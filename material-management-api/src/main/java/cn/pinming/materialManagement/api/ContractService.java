package cn.pinming.materialManagement.api;

import cn.pinming.materialManagement.dto.ContractNameVO;
import lombok.NonNull;

import java.util.List;

/**
 * 合同相关dubbo接口
 *
 * <AUTHOR>
 * @since 2022/8/5 13:37
 */
public interface ContractService {

    /**
     * 根据合同id列表查找合同信息`
     * @param ids 合同列表
     * @return 列表
     */
    List<ContractNameVO> findContractNameByIds(@NonNull List<String> ids);

    /**
     * 根据合同名称集合查找合同信息
     * @param names 合同名称列表
     * @return 列表
     */
    List<ContractNameVO> findContractByNames(@NonNull List<String> names);
}
