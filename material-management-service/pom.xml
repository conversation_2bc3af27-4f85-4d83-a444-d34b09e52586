<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>material-management</artifactId>
        <groupId>cn.pinming.microservice</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>material-management-service</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.pinming.microservice</groupId>
            <artifactId>login-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.pinming</groupId>
            <artifactId>core-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.pinming</groupId>
            <artifactId>common</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.pinming</groupId>
            <artifactId>probe-servlet</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.pinming</groupId>
            <artifactId>openapi-client</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>javassist</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.pinming</groupId>
            <artifactId>openapi-gateway-spring-boot-starter</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>cn.pinming</groupId>
            <artifactId>zhuang</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.pinming</groupId>
            <artifactId>model-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.pinming</groupId>
            <artifactId>platform-gateway-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.pinming</groupId>
            <artifactId>service-permission-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>3.4.9</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.4.2</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-launcher</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib-nodep</artifactId>
            <version>3.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.10.5</version>
            <exclusions>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-models</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>2.0.9</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.6.2</version>
        </dependency>
        <dependency>
            <groupId>cn.pinming</groupId>
            <artifactId>syn-showdoc-core</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>hutool-all</artifactId>
                    <groupId>cn.hutool</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.pinming</groupId>
            <artifactId>weaponx-wrapper</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.pinming.microservice</groupId>
            <artifactId>material-unit-api</artifactId>
            <version>0.0.3-ztcj-SNAPSHOT</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.alibaba</groupId>-->
<!--            <artifactId>fastjson</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>cn.pinming.microservice</groupId>
            <artifactId>material-management-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml-schemas</artifactId>
                </exclusion>
            </exclusions>
            <version>3.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.10.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.3.3</version>
        </dependency>
        <dependency>
            <groupId>cn.pinming</groupId>
            <artifactId>upload</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.pinming.ai</groupId>
            <artifactId>algModelApi-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>dubbo</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis-spring</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.pinming.microservice</groupId>
            <artifactId>plug-api</artifactId>
            <version>0.2.8-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>org.xhtmlrenderer</groupId>
            <artifactId>core-renderer</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>gui.ava</groupId>-->
<!--            <artifactId>html2image</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>cn.pinming.microservice</groupId>
            <artifactId>warehouse-management-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.pinming.yfzx.cxptz</groupId>
            <artifactId>thirdbutt-api</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>cn.pinming</groupId>-->
<!--            <artifactId>weaponx-permission</artifactId>-->
<!--            <version>0.1.0-SNAPSHOT</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>cn.pinming.material</groupId>-->
<!--            <artifactId>pinming-sdk-material</artifactId>-->
<!--            <version>1.1.8.240403_release</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>cn.pinming.material</groupId>
            <artifactId>material-client-sdk-spring-boot-starter</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>harbor</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <docker.image.prefix>reg.pinming.org/pms</docker.image.prefix>
                <docker.image.target>${project.artifactId}-${project.version}-${maven.build.timestamp}
                </docker.image.target>
                <docker.server.id>harbor</docker.server.id>
                <docker.registry.url>http://reg.pinming.org</docker.registry.url>
            </properties>
        </profile>
        <profile>
            <id>production</id>
            <properties>
                <docker.image.prefix>registry.cn-hangzhou.aliyuncs.com/pinming</docker.image.prefix>
                <docker.image.target>${project.artifactId}-${maven.build.timestamp}</docker.image.target>
                <docker.server.id>production</docker.server.id>
                <docker.registry.url>registry.cn-hangzhou.aliyuncs.com</docker.registry.url>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>material-management-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>${project.basedir}/src/main/resources/client/</directory>
                <includes>
                    <include>**/*.jar</include>
                    <include>**/*.yml</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>