package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.entity.MaterialHandler;
import cn.pinming.microservice.material.management.biz.form.MaterialHandlerForm;
import cn.pinming.microservice.material.management.biz.vo.HandlerVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 处理人信息 服务类
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 16:38:04
 */
public interface IMaterialHandlerService extends IService<MaterialHandler> {

    /**
     * 获取处理人列表
     * @return
     */
    List<HandlerVO> listHandler(Byte handleType);

    /**
     * 保存处理人列表
     * @param
     */
    void saveHandler(MaterialHandlerForm materialHandlerForm);

    /**
     * 判断用户是否有处理权限
     * @return
     */
     Boolean enableHandle(String userId, Byte handleType);

    /**
     * 企业下所有项目层处理人列表
     * @param handleType
     * @return HandlerVO
     */
    List<HandlerVO> listProjectsHandler(Byte handleType);

}
