package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 拌合站配料申请明细
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 09:54:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_ingredient_apply_detail")
@ApiModel(value = "IngredientApplyDetail对象", description = "拌合站配料申请明细")
public class IngredientApplyDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "申请单id")
    @TableField("ingredient_apply_id")
    private String ingredientApplyId;

    @ApiModelProperty(value = "配比材料id")
    @TableField("material_id")
    private Integer materialId;

    @ApiModelProperty(value = "水专用字段（仅用于后续展示）")
    @TableField("material_name")
    private String materialName;

    @ApiModelProperty(value = "原料试验报告ID")
    @TableField(value = "report_id",updateStrategy = FieldStrategy.IGNORED)
    private String reportId;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
