package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.StatisticsUnitDTO;
import cn.pinming.microservice.material.management.biz.entity.StatisticsMaterialConfig;
import cn.pinming.microservice.material.management.biz.entity.StatisticsMaterialGroup;
import cn.pinming.microservice.material.management.biz.enums.StatisticsDefaultGroupEnum;
import cn.pinming.microservice.material.management.biz.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.biz.service.IStatisticsMaterialConfigService;
import cn.pinming.microservice.material.management.biz.service.IStatisticsMaterialGroupService;
import cn.pinming.microservice.material.management.biz.service.MaterialGroupConfigService;
import cn.pinming.microservice.material.management.biz.vo.StatisticsConfigVO;
import cn.pinming.microservice.material.management.common.util.IfBranchUtil;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialCategoryDto;
import cn.pinming.microservice.material_unit.api.material.service.MaterialService;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2022/4/11
 * @description
 */
@Slf4j
@Service
public class MaterialGroupConfigServiceImpl implements MaterialGroupConfigService {

    /**
     * 有分类的默认category
     */
    @NacosValue(value = "${statistics.tag.default.category}", autoRefreshed = true)
    public String tagCategory;

    /**
     * 无分类的默认category
     */
    @NacosValue(value = "${statistics.default.category}", autoRefreshed = true)
    public String noTagCategory;

    @Resource
    private AuthUserHolder authUserHolder;

    @Resource
    private IStatisticsMaterialConfigService materialConfigService;

    @Resource
    private IStatisticsMaterialGroupService materialGroupService;

    @Resource
    private MaterialDataMapper materialDataMapper;

    @Reference
    private MaterialService materialService;

    @Override
    public List<StatisticsConfigVO> materialGroupConfig(Boolean tag, Boolean unit) {
        List<StatisticsConfigVO> list = Lists.newArrayList();
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer currentCompanyId = currentUser.getCurrentCompanyId();
        // 当前企业下的材料分类
        List<MaterialCategoryDto> materialCategoryDtos = Optional.ofNullable(materialService.listAllCategory(currentCompanyId)).orElse(Lists.newArrayList());
        // 与默认的材料分类取交集
        List<String> materialCategoryIds = Lists.newArrayList();
        materialCategoryDtos = materialCategoryDtos.stream().filter(item -> item.getEnable()).collect(toList());
        if (CollectionUtil.isNotEmpty(materialCategoryDtos)) {
            materialCategoryIds = materialCategoryDtos.stream().map(item -> String.valueOf(item.getMaterialCategoryId())).distinct().collect(toList());
        } else {
            log.info("materialService.listAllCategory is empty, currentCompanyId: {}", currentCompanyId);
        }
        if (ObjectUtil.isNotNull(currentUser)) {
            String userid = currentUser.getId();
            QueryWrapper<StatisticsMaterialConfig> configWrapper = new QueryWrapper<>();
            if (tag) {
                Map<String, String> tagCategoryMap = StatisticsDefaultGroupEnum.keyTransition(JSONObject.parseObject(tagCategory, Map.class));
                // 与默认的材料分类取交集
                for(Map.Entry<String, String> entry : tagCategoryMap.entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    String category = defaultCategory(value, materialCategoryIds);
                    tagCategoryMap.put(key, category);
                }
                // 配置数据
                configWrapper.lambda().eq(StatisticsMaterialConfig::getCreateId, userid).last(" and name is not null and name <> ''");
                List<StatisticsMaterialConfig> configs = materialConfigService.list(configWrapper);
                // 默认分组数据
                QueryWrapper<StatisticsMaterialGroup> groupWrapper = new QueryWrapper<>();
                groupWrapper.lambda().eq(StatisticsMaterialGroup::getCreateId, userid);
                List<StatisticsMaterialGroup> groups = materialGroupService.list(groupWrapper);
                // 合并
                mergeStatisticsMaterialConfig(tagCategoryMap, list, configs, groups);
            } else {
                // 与默认的材料分类取交集
                String category = defaultCategory(noTagCategory, materialCategoryIds);
                // 配置数据
                configWrapper.lambda().eq(StatisticsMaterialConfig::getCreateId, userid).last(" and (name is null or name = '')");
                StatisticsMaterialConfig config = materialConfigService.getOne(configWrapper);
                IfBranchUtil.isTureOrFalse(ObjectUtil.isNull(config)).trueOrFalseHandle(
                        () -> {
                            // 默认
                            StatisticsConfigVO configVO = StatisticsConfigVO.builder()
                                    .id(UUID.randomUUID().toString())
                                    .categoryIds(category.split(","))
                                    .build();
                            list.add(configVO);
                        },
                        () -> {
                            StatisticsConfigVO configVO = StatisticsConfigVO.builder()
                                    .id(config.getId())
                                    .categoryIds(Optional.ofNullable(config.getCategoryId()).orElse("").split(","))
                                    .build();
                            list.add(configVO);
                        }
                );
            }
        }
        // 单位
        if (unit) {
            if (CollectionUtil.isNotEmpty(list)) {
                List<String> categoryIdList = list.stream().map(StatisticsConfigVO::getCategoryIds).flatMap(Arrays::stream).collect(toList());
                // 地磅&移动收料-单位
                List<StatisticsUnitDTO> materialDataUnit = materialDataMapper.listUnitByCategoryIds(categoryIdList);
                // 分组
                Map<String, List<StatisticsUnitDTO>> listMap = Optional.ofNullable(materialDataUnit).orElse(Lists.newArrayList())
                        .stream().collect(Collectors.groupingBy(StatisticsUnitDTO::getCategoryId));
                for (StatisticsConfigVO configVO : list) {
                    List<String> units = Lists.newArrayList();
                    String[] categoryIds = configVO.getCategoryIds();
                    for (String categoryId : categoryIds) {
                        units.addAll(listMap.getOrDefault(categoryId, Lists.newArrayList()).stream().map(StatisticsUnitDTO::getUnit).collect(toList()));
                    }
                    // 去重
                    List<String> unitList = units.stream().distinct().collect(toList());
                    configVO.setUnits(unitList);
                }
            }
        }
        return list;
    }

    @Override
    public List<String> categoryIds(Boolean tag) {
        List<StatisticsConfigVO> configVOS = materialGroupConfig(tag, false);
        return configVOS.stream().map(StatisticsConfigVO::getCategoryIds).flatMap(Arrays::stream).filter(obj -> !obj.equals("0")).collect(toList());
    }

    /**
     * 默认材料分类与企业下的材料分类取交集
     * @param categoryIds           默认材料分类
     * @param materialCategoryIds   企业下材料分类
     */
    private String defaultCategory(String categoryIds, List<String> materialCategoryIds) {
        List<String> list = Lists.newArrayList();
        String[] categoryIdArr = categoryIds.split(",");
        for (String categoryId : categoryIdArr) {
            // 默认的材料分类不在企业材料分类里，则剔除
            if (materialCategoryIds.contains(categoryId)) {
                list.add(categoryId);
            }
        }
        return CollectionUtil.isEmpty(list) ? "" : list.stream().collect(Collectors.joining(","));
    }

    /**
     * 合并物料统计配置
     * @param tagCategoryMap    默认分类
     * @param list              返回数据
     * @param configs           配置数据
     * @param groups            删除的分组数据
     * @return
     */
    private void mergeStatisticsMaterialConfig(Map<String, String> tagCategoryMap,
                                               List<StatisticsConfigVO> list,
                                               List<StatisticsMaterialConfig> configs,
                                               List<StatisticsMaterialGroup> groups) {
        IfBranchUtil.isTureOrFalse(CollectionUtils.isEmpty(configs)).trueOrFalseHandle(
                // 默认配置
                () -> {
                    IfBranchUtil.isTureOrFalse(CollectionUtils.isEmpty(groups)).trueOrFalseHandle(
                            // 未删除默认配置
                            () -> {
                                for (StatisticsDefaultGroupEnum value : StatisticsDefaultGroupEnum.values()) {
                                    String val = value.val();
                                    String orDefault = tagCategoryMap.getOrDefault(val, "");
                                    StatisticsConfigVO configVO = StatisticsConfigVO.builder()
                                            .id(UUID.randomUUID().toString())
                                            .name(val).categoryIds(orDefault.split(",")).build();
                                    list.add(configVO);
                                }
                            },
                            // 存在删除默认配置记录
                            () -> {
                                List<String> delNames = groups.stream().map(StatisticsMaterialGroup::getName).collect(toList());
                                for (StatisticsDefaultGroupEnum value : StatisticsDefaultGroupEnum.values()) {
                                    String val = value.val();
                                    if (!delNames.contains(val)) {
                                        String orDefault = tagCategoryMap.getOrDefault(val, "");
                                        StatisticsConfigVO configVO = StatisticsConfigVO.builder()
                                                .id(UUID.randomUUID().toString())
                                                .name(val).categoryIds(orDefault.split(",")).build();
                                        list.add(configVO);
                                    }
                                }
                            }
                    );
                },
                // 自定义配置 + 默认配置
                () -> {
                    // 自定义配置包含的默认配置：遍历时将自定义配置添加到返回值中
                    List<String> containsDefaultNames = configs.stream()
                            .map(item -> {
                                // 添加自定义配置数据
                                StatisticsConfigVO configVO = StatisticsConfigVO.builder()
                                        .id(item.getId())
                                        .name(item.getName())
                                        .categoryIds(Optional.ofNullable(item.getCategoryId()).orElse("").split(","))
                                        .build();
                                list.add(configVO);
                                return item.getName();
                            })
                            .filter(name -> StatisticsDefaultGroupEnum.vals().contains(name))
                            .collect(toList());
                    // 自定义配置包含的默认配置标记为删除的默认配置，回调【默认配置】
                    List<StatisticsMaterialGroup> groupList = containsDefaultNames.stream().map(item -> {
                        StatisticsMaterialGroup group = new StatisticsMaterialGroup();
                        group.setName(item);
                        return group;
                    }).collect(toList());
                    groupList.addAll(groups);
                    // 回调
                    mergeStatisticsMaterialConfig(tagCategoryMap, list,null, groupList);
                }
        );
    }

}
