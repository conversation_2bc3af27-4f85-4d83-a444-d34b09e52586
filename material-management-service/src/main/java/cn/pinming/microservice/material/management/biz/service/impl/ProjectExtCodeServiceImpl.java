package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.management.biz.entity.MaterialWeighbridge;
import cn.pinming.microservice.material.management.biz.entity.ProjectExtCode;
import cn.pinming.microservice.material.management.biz.enums.DeleteEnum;
import cn.pinming.microservice.material.management.biz.mapper.ProjectExtCodeMapper;
import cn.pinming.microservice.material.management.biz.service.IProjectExtCodeService;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <p>
 * 外部编码 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-05-06 15:08:46
 */
@Service
public class ProjectExtCodeServiceImpl extends ServiceImpl<ProjectExtCodeMapper, ProjectExtCode> implements IProjectExtCodeService {

    @Resource
    private ProjectExtCodeMapper projectExtCodeMapper;

    @Override
    public String getExtCode(Integer companyId, Integer projectId) {
        return projectExtCodeMapper.queryExtCodeByProjectId(companyId, projectId);
    }

    @Override
    public void editExtCode(Integer companyId, Integer projectId, String extCode) {
        ProjectExtCode projectExtCode = this.lambdaQuery().eq(ProjectExtCode::getProjectId, projectId)
                .eq(ProjectExtCode::getCompanyId, companyId)
                .eq(ProjectExtCode::getIsDeleted, DeleteEnum.NORMAL).one();
        if (extCode != null) {
            extCode = StrUtil.cleanBlank(extCode);
        }
        if (projectExtCode == null) {
            projectExtCode = new ProjectExtCode();
            projectExtCode.setExtCode(extCode);
            projectExtCode.setProjectId(projectId);
            projectExtCode.setCompanyId(companyId);
            save(projectExtCode);
        } else {
            this.lambdaUpdate().eq(ProjectExtCode::getId, projectExtCode.getId()).set(ProjectExtCode::getExtCode, extCode).update();
        }
    }
}
