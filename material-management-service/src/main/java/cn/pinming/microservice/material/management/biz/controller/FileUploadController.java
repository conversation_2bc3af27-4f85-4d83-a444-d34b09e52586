package cn.pinming.microservice.material.management.biz.controller;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.upload.OssFile;
import cn.pinming.core.upload.UploadComponent;
import cn.pinming.core.upload.UploadConfig;
import cn.pinming.core.upload.domain.enums.FileTypeEnums;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.dto.PicDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialData;
import cn.pinming.microservice.material.management.biz.form.PicUploadForm;
import cn.pinming.microservice.material.management.biz.service.IMaterialDataService;
import cn.pinming.microservice.material.management.biz.service.IMaterialSendReceiveService;
import cn.pinming.microservice.material.management.proxy.FileServiceProxy;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;

/**
 * 地磅客户端文件上传oss
 */
@Api(tags = "地磅客户端文件上传oss")
@RestController
@RequestMapping("/api/common")
@Slf4j
public class FileUploadController {

    @Value("${temporary.path}")
    private String temporaryPath;
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private IMaterialSendReceiveService materialSendReceiveService;
    @Resource
    private IMaterialDataService materialDataService;

    /**
     * 上传图片
     *
     * @param form
     * @param file 图片内容
     * @return
     */
    @ApiOperation(value = "上传图片")
    @PostMapping("/upload")
    public ResponseEntity<Response> upload(PicUploadForm form, @RequestBody byte[] file) {
        log.info("接口上传图片开始,file大小为{}",file.length);
        if (file == null || file.length == 0 || StrUtil.isBlank(form.getWeighId())) {
            return ResponseEntity.ok(new SuccessResponse());
        }
        MaterialData data = materialDataService.lambdaQuery().eq(MaterialData::getWeighId, form.getWeighId()).one();

        Integer type = form.getType();
        if (data != null) {
            if (type == 1 && StrUtil.isNotBlank(data.getEnterPic())) {
                long size = StrUtil.split(data.getEnterPic(), CharUtil.COMMA).stream().filter(s -> !"null".equals(s) && StrUtil.isNotBlank(s) && !s.startsWith("D:")).count();
                if (size >= 4) {
                    return ResponseEntity.ok(new SuccessResponse(-1));
                }
            } else if (type == 2 && StrUtil.isNotBlank(data.getLeavePic())) {
                long size = StrUtil.split(data.getLeavePic(), CharUtil.COMMA).stream().filter(s -> !"null".equals(s) && StrUtil.isNotBlank(s) && !s.startsWith("D:")).count();
                if (size >= 4) {
                    return ResponseEntity.ok(new SuccessResponse(-1));
                }
            }
        }
        

        String uuid = uploadFile(form.getWeighId(),file);
        PicDTO dto = materialSendReceiveService.checkByWeighId(form.getWeighId());
        form.setUuid(uuid);
        materialDataService.saveDataForPhoto(dto, form);
        return ResponseEntity.ok(new SuccessResponse(uuid));
    }

    private String uploadFile(String weighId,byte[] file) {
        byte[] raw = URLDecoder.decode(file, true);
        log.info("weighId:{}, 原始文件字节数：{}， 解码文件字节数：{}",weighId, file.length, raw.length);
        String filePath = temporaryPath + StrUtil.format("{}.jpg", System.currentTimeMillis());
        try {
            FileOutputStream fos = new FileOutputStream(filePath);
            ByteArrayInputStream in = new ByteArrayInputStream(file);
            byte[] b = new byte[1024];
            int n;
            while ((n = in.read(b)) != -1) {
                fos.write(b, 0, n);
            }
            fos.flush();
            in.close();
            fos.close();
        } catch (Exception e) {
            log.error("文件存储到临时目录出错", e);
        }

        File tmp = new File(filePath);
        UploadComponent uploadComponent = fileServiceProxy.getDynamicUploadComponent();
        OssFile ossFile = new OssFile(tmp, "jpg", FileTypeEnums.IMAGE);
        UploadConfig uploadConfig = new UploadConfig("ff8080815afee284015afee408680001");
        String uuid = uploadComponent.uploadFile(ossFile, uploadConfig);
        fileServiceProxy.confirmFile(uuid, "material-management");
        try {
            uploadComponent.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return uuid;
    }

    @ApiOperation(value = "上传图片-base64")
    @PostMapping("/upload/base64")
    public ResponseEntity<Response> upload(@RequestBody(required = true) String base64) throws FileNotFoundException {
        BufferedImage bufferedImage = ImgUtil.toImage(base64);
        String filePath = temporaryPath + StrUtil.format("{}.jpg", System.currentTimeMillis());
        FileOutputStream outputStream = new FileOutputStream(filePath);
        ImgUtil.writeJpg(bufferedImage, outputStream);
        File file = new File(filePath);
        UploadComponent uploadComponent = fileServiceProxy.getDynamicUploadComponent();
        OssFile ossFile = new OssFile(file, "jpg", FileTypeEnums.IMAGE);
        UploadConfig uploadConfig = new UploadConfig("ff8080815afee284015afee408680001");
        String uuid = uploadComponent.uploadFile(ossFile, uploadConfig);
        fileServiceProxy.confirmFile(uuid, "material-management");
        return ResponseEntity.ok(new SuccessResponse(uuid));
    }


}
