package cn.pinming.microservice.material.management.biz.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MobileReceiveForm extends BaseForm{
    @ApiModelProperty(value = "采购单id")
    private String purchaseId;

    @ApiModelProperty(value = "收料类型 1有合同收料-按合同，2有合同收料-按采购单，3无合同收料")
    @NotNull(message = "收料类型不能为空")
    private Byte receiveType;

    @ApiModelProperty(value = "合同id")
    private String contractId;

    @ApiModelProperty(value = "车牌号")
    @NotBlank(message = "车牌号不能为空")
    private String truckNo;

    @ApiModelProperty(value = "车辆到场时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime truckTime;

    @ApiModelProperty(value = "车头车尾图片")
    @Size(min = 2,max = 2,message = "车头车尾照片必须为两张")
    private List<String> truckPic;

    @ApiModelProperty(value = "货/铭牌图片")
    @Size(min = 1,max = 6,message = "货/铭牌图片请选择1-6张")
    private List<String> goodsPic;

    @ApiModelProperty(value = "送货单图片")
    @Size(min = 1,max = 9,message = "送货单图片请选择1-9张")
    private List<String> sendPic;

    @ApiModelProperty(value = "收货状态（验收结果）1,合格进场 2,不合格进场")
    @NotNull(message = "验收结果不能为空")
    private Byte receiveStatus;

    @ApiModelProperty(value = "实际收货人")
    private String receiver;

    @ApiModelProperty(value= "供应商id")
    private Integer supplierId;

    @ApiModelProperty(value = "验收意见")
    @Length(max = 200)
    private String comment;

    @ApiModelProperty(value = "验收材料总计")
    @Valid
    private List<MobileReceiveMaterialForm> list;

    @ApiModelProperty(value = "经度")
    @NotBlank(message = "经度不能为空")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    @NotBlank(message = "纬度不能为空")
    private String latitude;

    @ApiModelProperty(value = "地址详情")
    @NotBlank(message = "地址详情不能为空")
    private String location;

    @ApiModelProperty(value = "传数据方式 null 移动收料原始版本 1 移动收料简易版")
    private Byte type;
}
