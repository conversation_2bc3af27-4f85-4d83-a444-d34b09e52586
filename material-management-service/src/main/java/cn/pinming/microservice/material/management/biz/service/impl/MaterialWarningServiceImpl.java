package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.HandleAdviceDTO;
import cn.pinming.microservice.material.management.biz.dto.WarningDetailDTO;
import cn.pinming.microservice.material.management.biz.dto.WarningInfoDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialWarning;
import cn.pinming.microservice.material.management.biz.entity.WarningHandlerAdvice;
import cn.pinming.microservice.material.management.biz.enums.HandleTypeEnum;
import cn.pinming.microservice.material.management.biz.enums.WarningSourceEnum;
import cn.pinming.microservice.material.management.biz.enums.WarningStatusEnum;
import cn.pinming.microservice.material.management.biz.enums.WarningTypeEnum;
import cn.pinming.microservice.material.management.biz.form.HandleAdviceForm;
import cn.pinming.microservice.material.management.biz.form.MaterialWarningForm;
import cn.pinming.microservice.material.management.biz.mapper.MaterialWarningMapper;
import cn.pinming.microservice.material.management.biz.query.WarningDetailQuery;
import cn.pinming.microservice.material.management.biz.query.WarningInfoQuery;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.NoUtil;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 预警信息 服务实现类
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 10:46:38
 */
@Service
public class MaterialWarningServiceImpl extends ServiceImpl<MaterialWarningMapper, MaterialWarning> implements IMaterialWarningService {

    @Resource
    private ProjectServiceProxy projectServiceProxy;

    @Resource
    private IWarningHandlerAdviceService handlerAdviceService;

    @Resource
    private AuthUserHolder authUserHolder;

    @Resource IMaterialDataService materialDataService;

    @Resource
    private NoUtil noUtil;

    @Resource
    private IMobileReceiveService mobileReceiveService;

    @Resource
    private IMaterialHandlerService materialHandlerService;

    @Resource
    private IMaterialWarningConfigService warningConfigService;

    @Override
    public IPage<WarningInfoVO> pageListByQuery(WarningInfoQuery warningInfoQuery) {
        warningInfoQuery.setCompanyId(authUserHolder.getCurrentUser().getCurrentCompanyId());
        if (authUserHolder.getCurrentUser().getCurrentProjectId() != null) {
            Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();
            warningInfoQuery.setSourceProjectIds(Collections.singletonList(projectId));
        } else {
            warningInfoQuery.setSourceProjectIds(warningInfoQuery.getProjectIds());
        }
        IPage<WarningInfoDTO> warningInfoDTOPage = this.baseMapper.selectPageByQuery(warningInfoQuery);
        List<WarningInfoDTO> records = warningInfoDTOPage.getRecords();
//
//        List<Byte> warningTypeList = warningInfoQuery.getWarningTypeList();
//        if (CollUtil.isNotEmpty(warningTypeList)) {
//            Set<String> warningType = warningTypeList.stream().map(String::valueOf).collect(Collectors.toSet());
//            records = warningInfoDTOPage.getRecords().stream().filter(warningInfoDTO -> {
//                String dtoWarningType = warningInfoDTO.getWarningType();
//                Set<String> dtoWarningTypeSet = Arrays.stream(dtoWarningType.split(StrUtil.COMMA)).collect(Collectors.toSet());
//                long count = dtoWarningTypeSet.stream().filter(warningType::contains).count();
//                return count > 0;
//            }).collect(Collectors.toList());
//        }

        Boolean enable= materialHandlerService.enableHandle(authUserHolder.getCurrentUser().getId(), HandleTypeEnum.WARNING_HANDLE.value());
        // 预警id列表
        List<String> warningIds = records.stream().map(WarningInfoDTO::getWarningId).collect(Collectors.toList());
        List<HandleAdviceDTO> lastHandlerAdvice = Optional.ofNullable(handlerAdviceService.getLastHandlerAdvice(warningIds)).orElse(Lists.newArrayList());
        Map<String, HandleAdviceDTO> adviceMap = lastHandlerAdvice.stream().collect(Collectors.toMap(HandleAdviceDTO::getWarningId, e -> e, (t1, t2) -> t2));

        // 预警来源项目列表
        List<Integer> projectIds = records.stream().map(WarningInfoDTO::getSourceProjectId).filter(Objects::nonNull).collect(Collectors.toList());
        List<ProjectVO> simpleProjects = projectServiceProxy.getSimpleProjects(projectIds);
        Map<Integer, ProjectVO> projectVOMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(simpleProjects)) {
            projectVOMap = simpleProjects.stream().collect(Collectors.toMap(ProjectVO::getProjectId, e -> e, (t1, t2) -> t1));
        }

        // 返回结果信息
        Map<Integer, ProjectVO> finalProjectMap = projectVOMap;
        List<WarningInfoVO> result = records.stream().map(warningInfoDTO -> {
            WarningInfoVO warningInfoVO = new WarningInfoVO();
            BeanUtils.copyProperties(warningInfoDTO, warningInfoVO);

            warningInfoVO.setEnableHandle(enable);
            // 封装发生项目名称
            ProjectVO projectVO = finalProjectMap.get(warningInfoDTO.getSourceProjectId());
            Optional.ofNullable(projectVO).ifPresent(project -> warningInfoVO.setSourceProject(project.getProjectTitle()));
            // 查询处理建议
            HandleAdviceDTO handleAdviceDTO = adviceMap.get(warningInfoDTO.getWarningId());
            Optional.ofNullable(handleAdviceDTO).ifPresent(handlerAdvice -> warningInfoVO.setHandlerAdvice(handlerAdvice.getAdvice()));
            // 预警类型
            String warningType = warningInfoDTO.getWarningType();
            if (StrUtil.isNotEmpty(warningType)) {
                warningInfoVO.setWarningType(Arrays.stream(warningType.split(StrUtil.COMMA)).map(Byte::new).collect(Collectors.toSet()));
            }

            // 移动收料有收料类型 判断是否是移动收料
            if (warningInfoDTO.getWarningSource().equals(WarningSourceEnum.MOBilE_RECEIVE.description())) {
                Byte type = mobileReceiveService.getReceiveTypeByReceiveId(warningInfoDTO.getWarningSourceId());
                warningInfoVO.setReceiveType(type);
            }
            return warningInfoVO;
        }).collect(Collectors.toList());

        IPage<WarningInfoVO> list = new Page<>();
        BeanUtils.copyProperties(warningInfoDTOPage, list);
        list.setRecords(result);
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void warningHandle(HandleAdviceForm handleAdviceForm) {
        String warningSourceNo = handleAdviceForm.getWarningSourceNo();
        String handlerId = authUserHolder.getCurrentUser().getId();
        String handlerName = authUserHolder.getCurrentUser().getMemberName();
        // 处理该编号下的所有预警
        List<MaterialWarning> materialWarningList = this.lambdaQuery().eq(MaterialWarning::getWarningSourceNo, warningSourceNo).list();
        // 保存处理建议信息
        List<WarningHandlerAdvice> warningHandlerAdviceList = Lists.newArrayList();
        materialWarningList.forEach(materialWarning -> {
            WarningHandlerAdvice warningHandlerAdvice = new WarningHandlerAdvice();
            warningHandlerAdvice.setWarningId(materialWarning.getId());
            warningHandlerAdvice.setHandlerAdvice(handleAdviceForm.getHandlerAdvice());
            warningHandlerAdvice.setHandlerId(handlerId);
            warningHandlerAdvice.setHandlerName(handlerName);
            warningHandlerAdvice.setHandlerTime(LocalDateTime.now());
            warningHandlerAdvice.setWarningStatus(handleAdviceForm.getWarningStatus());
            warningHandlerAdviceList.add(warningHandlerAdvice);
        });
        handlerAdviceService.saveBatch(warningHandlerAdviceList);

        // 预警表更新建议信息
        List<MaterialWarning> updateMaterialWarningList = Lists.newArrayList();
        materialWarningList.forEach(materialWarning -> {
            materialWarning.setHandlerId(handlerId);
            materialWarning.setHandlerName(handlerName);
            materialWarning.setHandlerTime(LocalDateTime.now());
            materialWarning.setWarningStatus(handleAdviceForm.getWarningStatus());
            updateMaterialWarningList.add(materialWarning);
        });
        updateBatchById(updateMaterialWarningList);
    }

    @Override
    public void saveMaterialWarning(MaterialWarningForm materialWarningForm) {
        if (materialWarningForm.getWarningType() == null) {
            throw new BOException(BOExceptionEnum.WARNING_TYPE_NOT_EMPTY);
        }

        if (materialWarningForm.getWarningSource() == null) {
            throw new BOException(BOExceptionEnum.WARNING_SOURCE_NOT_EMPTY);
        }

        if (materialWarningForm.getWarningSourceId() == null) {
            throw new BOException(BOExceptionEnum.WARNING_SOURCE_ID_NOT_EMPTY);
        }

        if (materialWarningForm.getSourceProjectId() == null) {
            throw new BOException(BOExceptionEnum.WARNING_SOURCE_PROJECT_NOT_EMPTY);
        }

        if (materialWarningForm.getWarningSourceNo() == null) {
            throw new BOException(BOExceptionEnum.WARNING_SOURCE_NO_NOT_EMPTY);
        }
        MaterialWarning materialWarning = new MaterialWarning();
        BeanUtils.copyProperties(materialWarningForm, materialWarning);
        for (WarningSourceEnum v : WarningSourceEnum.values()) {
            if (materialWarningForm.getWarningSource().equals(v.value())) {
                materialWarning.setWarningSource(v.description());
            }
        }
        materialWarning.setWarningNo(noUtil.getWarningNo(materialWarningForm.getSourceProjectId()));
        materialWarning.setWarningStatus(WarningStatusEnum.UNHANDLED.value());
        save(materialWarning);
    }

    @Override
    public List<WarningStatusVO> listWarningStatus() {
        // 预警状态列表
        List<WarningStatusVO> warningStatusVOS = Arrays.stream(WarningStatusEnum.values()).map(v -> {
            WarningStatusVO warningStatusVO = new WarningStatusVO();
            warningStatusVO.setWarningStatus(v.value());
            warningStatusVO.setDescription(v.description());
            return warningStatusVO;
        }).collect(Collectors.toList());
        return warningStatusVOS;
    }

    @Override
    public List<WarningTypeVO> listWarningType() {
        // 预警类型列表
        List<WarningTypeVO> warningTypeVOList = Arrays.stream(WarningTypeEnum.values()).map(v -> {
            WarningTypeVO warningTypeVO = new WarningTypeVO();
            warningTypeVO.setWarningType(v.value());
            warningTypeVO.setDescription(v.description());
            return warningTypeVO;
        }).collect(Collectors.toList());
        return warningTypeVOList;
    }

    @Override
    public List<WarningSourceVO> listWarningSource() {
        List<WarningSourceVO> warningSourceVOS = Arrays.stream(WarningSourceEnum.values()).map(v -> {
            WarningSourceVO warningSourceVO = new WarningSourceVO();
            warningSourceVO.setWarningSource(v.description());
            return warningSourceVO;
        }).collect(Collectors.toList());
        return warningSourceVOS;
    }

    @Override
    public List<WarningDetailVO> getWarningDetail(WarningDetailQuery warningDetailQuery) {
        List<WarningDetailDTO> warningDetailDTOList = this.baseMapper.listWarningDetail(warningDetailQuery);
        List<WarningDetailVO> warningDetailVOList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(warningDetailDTOList)) {
            Map<Byte, List<String>> warningDetailMap = warningDetailDTOList.stream()
                    .collect(Collectors.groupingBy(WarningDetailDTO::getWarningType,
                            Collectors.mapping(WarningDetailDTO::getWarningInfo, Collectors.toList())));
            warningDetailMap.forEach((k, v) -> {
                WarningDetailVO warningDetailVO = new WarningDetailVO();
                warningDetailVO.setType(k);
                warningDetailVO.setInfo(v);
                warningDetailVOList.add(warningDetailVO);
            });
        }
        return warningDetailVOList;
    }

}
