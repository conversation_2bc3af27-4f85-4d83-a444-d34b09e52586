package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.form.PurchaseContractForm;
import cn.pinming.microservice.material.management.biz.form.PushConfigForm;
import cn.pinming.microservice.material.management.biz.mapper.PurchaseContractDetailMapper;
import cn.pinming.microservice.material.management.biz.query.PurchaseContractQuery;
import cn.pinming.microservice.material.management.biz.service.IPurchaseContractService;
import cn.pinming.microservice.material.management.biz.vo.ContractMaterialVO;
import cn.pinming.microservice.material.management.biz.vo.ProjectVO;
import cn.pinming.microservice.material.management.biz.vo.PurchaseContractVO;
import cn.pinming.microservice.material.management.biz.vo.PushConfigVO;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * <p>
 * 采购合同 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Api(tags = "采购合同", value = "zh")
@RestController
@RequestMapping("/api/contract")
@AllArgsConstructor
public class PurchaseContractController {

    public final IPurchaseContractService contractService;
    @Resource
    private PurchaseContractDetailMapper purchaseContractDetailMapper;
    @Resource
    private AuthUserHolder authUserHolder;

    @ApiOperation(value = "列表", response = PurchaseContractVO.class)
    @Log(title = "列表", businessType = BusinessType.QUERY)
    @PostMapping("/list")
    public ResponseEntity<Response> list(@RequestBody PurchaseContractQuery query) {
        IPage<?> page = contractService.pageListByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "合同项目列表", response = ProjectVO.class)
    @Log(title = "列表", businessType = BusinessType.QUERY)
    @GetMapping("/project/list")
    public ResponseEntity<Response> projectList() {
        List<ProjectVO> list = contractService.projectList();
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation("保存")
    @Log(title = "保存", businessType = BusinessType.UPDATE)
    @PostMapping("/save")
    public ResponseEntity<Response> save(@Validated @Valid @RequestBody PurchaseContractForm form) {
        contractService.savePurchaseContract(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "详情", response = PurchaseContractVO.class)
    @Log(title = "详情", businessType = BusinessType.QUERY)
    @PostMapping("/{id}/detail")
    public ResponseEntity<Response> detail(@PathVariable String id) {
        AuthUser user = authUserHolder.getCurrentUser();
        PurchaseContractVO resultVO = contractService.queryDetailById(id,user.getCurrentCompanyId());
        return ResponseEntity.ok(new SuccessResponse(resultVO));
    }

    @ApiOperation("删除")
    @Log(title = "删除", businessType = BusinessType.DELETE)
    @PostMapping("/{id}/del")
    public ResponseEntity<Response> del(@PathVariable String id) {
        contractService.deletePurchaseContractById(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "采购合同-所属项目", response = ProjectVO.class)
    @Log(title = "采购合同-所属项目", businessType = BusinessType.QUERY)
    @GetMapping("/belong/{id}")
    public ResponseEntity<Response> belongProjects(@PathVariable String id) {
        List<ProjectVO> list = contractService.listBelongProjects(id);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "根据合同id和材料id获取信息",response = ContractMaterialVO.class)
    @GetMapping("/seek/{contractId}/{materialId}")
    public ResponseEntity<Response> seek(@PathVariable("contractId")String contractId,@PathVariable("materialId")Integer materialId) {
        ContractMaterialVO vo = purchaseContractDetailMapper.selectInfo(contractId,materialId);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "合同设置详情")
    @GetMapping("/pushConfig")
    public ResponseEntity<Response> pushConfig(@RequestParam String contractId) {
        PushConfigVO result = contractService.queryPushConfig(contractId);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "合同设置")
    @PostMapping("/pushConfig")
    public ResponseEntity<Response> pushConfig(@Validated @RequestBody PushConfigForm form) {
        contractService.savePushConfig(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

}
