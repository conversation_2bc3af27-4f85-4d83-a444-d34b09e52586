package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.MaterialSendReceive;
import cn.pinming.microservice.material.management.biz.entity.PurchaseOrder;
import cn.pinming.microservice.material.management.biz.enums.*;
import cn.pinming.microservice.material.management.biz.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.biz.mapper.MaterialSendReceiveMapper;
import cn.pinming.microservice.material.management.biz.mapper.MobileReceiveMapper;
import cn.pinming.microservice.material.management.biz.mapper.PurchaseOrderMapper;
import cn.pinming.microservice.material.management.biz.query.*;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.PicEchoUtil;
import cn.pinming.microservice.material.management.proxy.CooperateServiceProxy;
import cn.pinming.microservice.material.management.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material.management.wrapper.SupplierWrapper;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.v2.company.api.dto.EmployeeDetailDto;
import cn.pinming.v2.company.api.dto.EmployeeDetailQueryDto;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import cn.pinming.weaponx.wrapper.wrapper.ProjectTitleWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 收货/发货单 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Service
public class MaterialSendReceiveServiceImpl extends ServiceImpl<MaterialSendReceiveMapper, MaterialSendReceive> implements IMaterialSendReceiveService {

    @Resource
    private IPurchaseOrderService orderService;
    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;
    @Resource
    private MaterialSendReceiveMapper sendReceiveMapper;
    @Resource
    private MaterialDataMapper materialDataMapper;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private CooperateServiceProxy cooperateServiceProxy;
    @Resource
    private SupplierWrapper supplierWrapper;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private ProjectTitleWrapper projectTitleWrapper;
    @Resource
    private MobileReceiveMapper mobileReceiveMapper;
    @Resource
    private IPurchaseOrderService purchaseOrderService;
    @Resource
    private IMaterialReviseService materialReviseService;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private IMaterialHandlerService materialHandlerService;
    @Resource
    private IMaterialVerifyService materialVerifyService;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;
    @Resource
    private PicEchoUtil picEchoUtil;

    /**
     * 地磅收货列表
     *
     * @param query
     * @return
     */
    @Override
    public IPage<MaterialWeighInfoVO> showWeightInfo(MaterialWeighQuery query) {
        IPage<MaterialWeighInfoDTO> materialWeighInfoDTOS = materialDataMapper.queryWeightReceiveInfo(query);
        return this.getVO(materialWeighInfoDTOS, query);

    }

    private IPage<MaterialWeighInfoVO> getVO(IPage<MaterialWeighInfoDTO> materialWeighInfoDTOS, MaterialWeighQuery query) {
        IPage<MaterialWeighInfoVO> materialWeighInfoVOS = new Page<>();

        List<MaterialWeighInfoVO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(materialWeighInfoDTOS.getRecords())) {

            List<Integer> materialIdList = materialWeighInfoDTOS.getRecords().stream().map(MaterialWeighInfoDTO::getMaterialId)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList());

            List<Integer> receiveProjectList = materialWeighInfoDTOS.getRecords().stream().map(MaterialWeighInfoDTO::getReceiveProject)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList());

            String supplierIds = materialWeighInfoDTOS.getRecords().stream().map(MaterialWeighInfoDTO::getSupplierId)
                    .filter(Objects::nonNull).distinct().collect(Collectors.joining(","));

            List<MaterialDto> materialList = materialServiceProxy.listMaterialByIds(materialIdList);
            List<ProjectVO> receiveProjectInfoList = projectServiceProxy.getSimpleProjects(receiveProjectList);
            List<CooperateVO> supplierList = cooperateServiceProxy.findCooperateByIds(query.getCompanyId(), supplierIds);

            Map<Integer, MaterialDto> materialMap = materialList.stream()
                    .collect(Collectors.toMap(MaterialDto::getMaterialId, obj -> obj));

            Map<Integer, ProjectVO> receiveProjectInfoMap = receiveProjectInfoList.stream()
                    .collect(Collectors.toMap(ProjectVO::getProjectId, obj -> obj));

            Map<String, CooperateVO> supplierMap = supplierList.stream()
                    .collect(Collectors.toMap(obj -> String.valueOf(obj.getId()), obj -> obj));

            // 判断当前用户能否修订数据
            Boolean enableHandle = false;
            if (ObjectUtil.isNotEmpty(authUserHolder.getCurrentUser())) {
                String userId = authUserHolder.getCurrentUser().getId();
                if (userId != null) {
                    enableHandle = materialHandlerService.enableHandle(userId, HandleTypeEnum.REVISE_HANDLE.value());
                }
            }

            Boolean finalEnableHandle = enableHandle;
            materialWeighInfoDTOS.getRecords().forEach(e -> {
                MaterialWeighInfoVO vo = new MaterialWeighInfoVO();
                BeanUtils.copyProperties(e, vo);
                vo.setEnableRevise(finalEnableHandle);

                ReceiveModeEnum receiveModeEnum = ReceiveModeEnum.getReceiveMode(e.getReceiveMode(), e.getMaterialValidity());
                vo.setReceiveMode(receiveModeEnum.value());
                vo.setIsAdditionStr(AdditionalRecordEnum.desc(e.getIsAddition()));
                vo.setPushStateStr(PushStatusEnum.desc(vo.getPushState()));
                // 偏差率
                if (e.getDeviationRate() != null) {
                    vo.setDeviation(e.getDeviationRate());
                }

                // 偏差量
                vo.setDeviationCount(NumberUtil.sub(vo.getActualCount(),vo.getWeightSend()));

                // 材料类目
                if (e.getMaterialId() != null || StrUtil.isNotEmpty(e.getCategoryName())) {
                    vo.setMaterialCategoryName(e.getCategoryName());
                    MaterialDto materialDto = materialMap.get(e.getMaterialId());
                    if (ObjectUtil.isNotEmpty(materialDto)) {
                        if (StrUtil.isEmpty(e.getCategoryName())) {
                            vo.setMaterialCategoryName(materialDto.getMaterialCategoryName());
                        }
                        vo.setMaterialName(materialDto.getMaterialName());
                        vo.setMaterialSpec(materialDto.getMaterialSpec());
                    }
                } else {
                    if (StrUtil.isNotBlank(vo.getMaterialName())) {
                        vo.setMaterialCategoryName(vo.getMaterialName());
                        vo.setMaterialName(null);
                    }
                }

                // 收货项目
                if (e.getReceiveProject() != null) {
                    ProjectVO dto = receiveProjectInfoMap.get(e.getReceiveProject());
                    if (ObjectUtil.isNotNull(dto)) {
                        vo.setProjectTitle(dto.getProjectTitle());
                    }
                }

                // 供应商
                if (StrUtil.isNotBlank(e.getSupplierId())) {
                    CooperateVO supplier = supplierMap.get(e.getSupplierId());
                    if (ObjectUtil.isNotNull(supplier)) {
                        vo.setSupplierName(supplier.getName());
                    }
                }

                if (StringUtils.isNotBlank(e.getPurchaseId())) {
                    PurchaseOrder purchaseOrder = purchaseOrderService.getById(e.getPurchaseId());
                    if (ObjectUtil.isNotEmpty(purchaseOrder)) {
                        vo.setIsUsedPurchaseOrder("是");
                    } else {
                        vo.setIsUsedPurchaseOrder("否");
                    }
                } else {
                    vo.setIsUsedPurchaseOrder("否");
                }

                vo.setDeviationStatus(DeviationStatusEnum.chooseDeviationStatus(e.getDeviationStatus()));
                vo.setDeviationType(e.getDeviationStatus());
                list.add(vo);
                enumDescription(list);
            });

            BeanUtils.copyProperties(materialWeighInfoDTOS, materialWeighInfoVOS);
            materialWeighInfoVOS.setRecords(list);
        }
        return materialWeighInfoVOS;
    }

    private List<MaterialWeighInfoVO> enumDescription(List<MaterialWeighInfoVO> list) {
        list.stream().forEach(e -> {
            e.setWeightIntegrality(IsWeightIntegralityEnum.desc(e.getIsWeightIntegrality()));
            e.setContractRateUsed(IsContractRateUsedEnum.desc(e.getIsContractRateUsed()));
            e.setContractUnitUsed(IsContractUnitUsedEnum.desc(e.getIsContractUnitUsed()));
            e.setMaterialExistStr(MaterialExistEnum.desc(e.getMaterialExist()));
            e.setRevise(MaterialReviseEnum.desc(e.getIsRevise()));
            e.setVerify(VerifyEnum.desc(e.getIsVerify()));
            e.setReceiveModeStr(ReceiveModeEnum.desc(e.getReceiveMode()));
        });
        return list;
    }

    /**
     * 关联采购单
     *
     * @param receiveId
     * @param orderNo
     * @param user
     */
    @Override
    public void relatePurchaseOrder(String receiveId, String orderNo, AuthUser user) {

        MaterialSendReceive materialSendReceive = this.lambdaQuery()
                .eq(MaterialSendReceive::getId, receiveId)
                .eq(MaterialSendReceive::getCompanyId, user.getCurrentCompanyId())
                .one();
        if (ObjectUtil.isEmpty(materialSendReceive)) {
            throw new BOException(BOExceptionEnum.RECEIVE_IS_NOT_EXIST);
        }
        PurchaseOrder purchaseOrder = orderService.lambdaQuery()
                .select(PurchaseOrder::getId)
                .eq(PurchaseOrder::getOrderNo, orderNo)
                .eq(PurchaseOrder::getCompanyId, user.getCurrentCompanyId())
                .one();
        if (ObjectUtil.isEmpty(purchaseOrder)) {
            throw new BOException(BOExceptionEnum.ORDER_IS_NOT_EXIST);
        }

        this.lambdaUpdate()
                .eq(MaterialSendReceive::getId, receiveId)
                .set(MaterialSendReceive::getPurchaseId, purchaseOrder.getId())
                .update();
    }

    /**
     * 过磅情况
     *
     * @param query
     * @return
     */
    @Override
    public WeighInfoVO selectWeighInfo(WeighInfoQuery query) {
        this.getProjectIdList(query);
        if (CollUtil.isEmpty(query.getProjectIdList())) { return null; }

        WeighInfoVO weighInfoVO = new WeighInfoVO();
        Map<String, WeighTruckChangeDTO> map = new HashMap<>();

        WeighInfoDTO weighInfoDTO = sendReceiveMapper.selectWeightInfo(query);
        if (ObjectUtil.isNotEmpty(weighInfoDTO)) {
            BeanUtil.copyProperties(weighInfoDTO, weighInfoVO);

//          过磅车次变化
            List<WeighTruckChangeDTO> list = sendReceiveMapper.selectWeightInfoByDay(query);
            if (CollUtil.isNotEmpty(list)) {
                map = list.parallelStream().collect(Collectors.toMap(WeighTruckChangeDTO::getWeighTime, e -> e));
            }

            List<WeighTruckChangeDTO> result = getList(query, map);

            weighInfoVO.setList(result);
        } else {
            List<WeighTruckChangeDTO> result = getList(query, map);

            weighInfoVO.setList(result);
        }

        return weighInfoVO;
    }

    private List<WeighTruckChangeDTO> getList(WeighInfoQuery query, Map<String, WeighTruckChangeDTO> map) {
        Date start = Date.from(query.getStartDate().atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
        Date end = Date.from(query.getEndDate().atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
        List<DateTime> dateTimes = DateUtil.rangeToList(start, end, DateField.MONTH);
        DateFormat df = new SimpleDateFormat("yyyy-MM");
        List<WeighTruckChangeDTO> result = new ArrayList<>();
        for (DateTime dateTime : dateTimes) {
            WeighTruckChangeDTO dto = new WeighTruckChangeDTO();
            WeighTruckChangeDTO data = map.get(df.format(dateTime));
            if (data != null) {
                BeanUtils.copyProperties(data, dto);
            } else {
                dto.setReceiveNum(0);
                dto.setSendNum(0);
            }
            dto.setWeighTime(df.format(dateTime));
            result.add(dto);
        }
        return result;
    }

    /**
     * 过磅情况-更多情况
     *
     * @param query
     * @return
     */
    @Override
    public Map<String, WeighInfoVO> selectWeighInfos(WeighInfoQuery query) {
        Map<String,WeighInfoVO> result = new HashMap<>();
        AuthUser user = authUserHolder.getCurrentUser();
        Map<String, List<Integer>> map = new HashMap<>();
        query.setCompanyId(user.getCurrentCompanyId());
//      用户在项目层，顶点也只能是项目（组织树选分公司，最外面就是项目）
        if (user.getCurrentProjectId() != null) {
            query.setProjectIdList(Arrays.asList(user.getCurrentProjectId()));
            SimpleConstructionProjectDto simpleProject = projectServiceProxy.findSimpleProject(user.getCurrentProjectId());
            if(ObjectUtil.isNotEmpty(simpleProject)){
                map.put(user.getCurrentProjectId() + "-" + simpleProject.getProjectTitle(),Arrays.asList(user.getCurrentProjectId()));
            }
        } else {
//          直属节点 -> 项目列表 （已包含用户设置项目范围）
            map = projectServiceProxy.directlyUnderDeptOrProject(user.getCurrentCompanyId(), query.getPoint());
            if (CollUtil.isEmpty(map)) { return null; }
            List<Integer> projectIdList = map.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
            query.setProjectIdList(projectIdList);
        }
        if (CollUtil.isEmpty(map)) { return null; }

        List<WeighInfosDTO> list = sendReceiveMapper.selectWeightInfos(query);
        if (CollUtil.isNotEmpty(list)) {
            Map<String, List<Integer>> finalMap1 = map;
            finalMap1.keySet().forEach(e -> {
                List<WeighInfosDTO> listResult = new ArrayList<>();
                list.stream().forEach(m -> {
                   if(finalMap1.get(e).contains(m.getProjectId())){
                       listResult.add(m);
                   }
               });

                if(CollUtil.isNotEmpty(listResult)){
                    WeighInfoVO vo = new WeighInfoVO();
//                  找出时间最大的
                    LocalDateTime max = listResult.stream().map(WeighInfosDTO::getLastUsedTime).distinct().max(LocalDateTime::compareTo).get();
//                  找出时间最小的
                    LocalDateTime min = listResult.stream().map(WeighInfosDTO::getFirstUsedTime).distinct().min(LocalDateTime::compareTo).get();
//                  收料过磅车次总和
                    int weighingCarNum = listResult.stream().mapToInt(WeighInfosDTO::getWeighingCarNumber).sum();
//                  发料过磅车次总和
                    int sendingCarNum = listResult.stream().mapToInt(WeighInfosDTO::getSendingCarNumber).sum();
//                  过磅重量总和
                    BigDecimal weighWeight = listResult.stream().map(WeighInfosDTO::getWeighWeight).reduce(BigDecimal.ZERO, BigDecimal::add);

                    vo.setWeighingCarCount(weighingCarNum + sendingCarNum);
                    vo.setWeighingCarNumber(weighingCarNum);
                    vo.setSendingCarNumber(sendingCarNum);
                    vo.setWeighWeight(weighWeight);
                    vo.setFirstUsedTime(min);
                    vo.setLastUsedTime(max);

                    result.put(e,vo);
                }
            });
        }
        return result;
    }

    /**
     * 过磅收料偏差统计
     *
     * @param query
     * @return
     */
    @Override
    public WeighDeviationVO selectWeighDeviation(DeviationInfoQuery query) {
        WeighDeviationVO vo = new WeighDeviationVO();
        BigDecimal m = new BigDecimal("100");
        BigDecimal zero = BigDecimal.ZERO;
        BigDecimal weighSend = new BigDecimal("0");
        BigDecimal weighReceive = new BigDecimal("0");
        BigDecimal weighActualReceive = new BigDecimal("0");
        BigDecimal mobileSend = new BigDecimal("0");
        BigDecimal mobileReceive = new BigDecimal("0");
        BigDecimal mobileActualReceive = new BigDecimal("0");
        BigDecimal deviationSum = new BigDecimal("0");
        BigDecimal deviationRateSum = new BigDecimal("0");

        AuthUser user = authUserHolder.getCurrentUser();
        query.setCompanyId(user.getCurrentCompanyId());
        query.setPages(0);
        query.setSize(Integer.MAX_VALUE);
//      用户在项目层，顶点也只能是项目（组织树选分公司，最外面就是项目）
        if (user.getCurrentProjectId() != null) {
            query.setProjectIdList(Arrays.asList(user.getCurrentProjectId()));
        } else {
//          直属节点 -> 项目列表 （已包含用户设置项目范围）
            List<Integer> list = projectServiceProxy.statisticsProjectIds(user.getCurrentCompanyId(), query.getPoint());
            if (CollUtil.isEmpty(list)) { return null; }
            query.setProjectIdList(list);
        }

//      累计下单量
        BigDecimal purchaseSum = purchaseOrderMapper.selectPurchaseAccount(query);
        if (purchaseSum == null) {
            vo.setPurchaseSum(zero);
        } else {
            vo.setPurchaseSum(purchaseSum);
        }
        if (query.getReceiveType() == ReceiveTypeEnum.NULL.value() || query.getReceiveType() == ReceiveTypeEnum.NONE.value() || query.getReceiveType() == ReceiveTypeEnum.WEIGHWITHOUTPURCHASE.value()) {
            vo.setPurchaseSum(zero);
        }

//      地磅收料    全部，地磅收料-按采购单，地磅收料-无采购单
        if (query.getReceiveType() == ReceiveTypeEnum.WEIGHWITHPURCHASE.value() || query.getReceiveType() == ReceiveTypeEnum.WEIGHWITHOUTPURCHASE.value() || query.getReceiveType() == ReceiveTypeEnum.NULL.value() || query.getReceiveType() == ReceiveTypeEnum.HAVE.value()) {
            StatisticsDataDTO dto = sendReceiveMapper.selectStatistics(query);
            if (ObjectUtil.isNotEmpty(dto)) {
                weighSend = dto.getSendAmount();
                weighReceive = dto.getReceiveAmount();
                weighActualReceive = dto.getActualReceive();
            }
        }

//      移动收料   全部，移动收料-按采购单、合同、无采购单、无合同
        if (query.getReceiveType() != ReceiveTypeEnum.WEIGHWITHPURCHASE.value() && query.getReceiveType() != ReceiveTypeEnum.WEIGHWITHOUTPURCHASE.value()) {
            StatisticsDataDTO dto = mobileReceiveMapper.selectStatistics(query);
            if (ObjectUtil.isNotEmpty(dto)) {
                mobileSend = dto.getSendAmount();
                mobileReceive = dto.getReceiveAmount();
                mobileActualReceive = mobileReceive;
            }
        }

//      统计
        BigDecimal sendSum = weighSend.add(mobileSend);
        BigDecimal receiveSum = weighReceive.add(mobileReceive);
        if (ObjectUtil.isNull(weighActualReceive)) {weighActualReceive = BigDecimal.ZERO;}
        BigDecimal actualReceiveSum = weighActualReceive.add(mobileActualReceive);
        if (sendSum.compareTo(zero) != 0) {
            deviationSum = receiveSum.subtract(sendSum);
            deviationRateSum = deviationSum.divide(sendSum, 4, RoundingMode.HALF_UP).multiply(m);
        }

        vo.setSendSum(sendSum);
        vo.setReceiveSum(receiveSum);
        vo.setDeviationRateSum(deviationRateSum);
        vo.setDeviationSum(deviationSum);
        vo.setActualReceive(actualReceiveSum);

        return vo;
    }

    @Override
    public PicDTO checkByWeighId(String weighId) {
        return materialDataMapper.checkByWeighId(weighId);
    }

    @Override
    public IPage<WeighbridgeSendVO> selectWeighbridgeSendInfo(WeighbridgeSendQuery query) {
        IPage<WeighbridgeSendDTO> weighbridgeSendDTOList = materialDataMapper.queryWeighbridgeSendInfo(query);
        List<WeighbridgeSendDTO> records = weighbridgeSendDTOList.getRecords();
        Map<String, String> mimMap = new HashMap<>();
        Map<Integer, String> proMap = new HashMap<>();
        Map<Integer, MaterialDto> materialDtoMap = new HashMap<>();

        List<WeighbridgeSendVO> weighbridgeSendVOS = null;
        if (CollUtil.isNotEmpty(records)) {
            List<String> mimList = records.stream().map(WeighbridgeSendDTO::getCreateId).distinct().collect(Collectors.toList());
            EmployeeDetailQueryDto queryDto = new EmployeeDetailQueryDto();
            queryDto.setMemberIdList(mimList);
            queryDto.setCompanyId(authUserHolder.getCurrentUser().getCurrentCompanyId());
            List<EmployeeDetailDto> employeeDetailDtos = employeeServiceProxy.employeeList(queryDto);
            if(CollUtil.isNotEmpty(employeeDetailDtos)){
                mimMap = employeeDetailDtos.stream().collect(Collectors.toMap(EmployeeDetailDto::getMemberId, EmployeeDetailDto::getMemberName));
            }

            List<Integer> proList = records.stream().map(WeighbridgeSendDTO::getSendProjectId).distinct().collect(Collectors.toList());
            List<ProjectVO> simpleProjects = projectServiceProxy.getSimpleProjects(proList);
            if(CollUtil.isNotEmpty(simpleProjects)){
                proMap = simpleProjects.stream().collect(Collectors.toMap(ProjectVO::getProjectId, ProjectVO::getProjectTitle));
            }

            List<Integer> materialIdList = records.stream().map(WeighbridgeSendDTO::getMaterialId).distinct().collect(Collectors.toList());
            List<MaterialDto> materialDtos = materialServiceProxy.listMaterialByIds(materialIdList);
            if(CollUtil.isNotEmpty(materialDtos)){
                materialDtoMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, e -> e));
            }

            Map<String, String> finalMimMap = mimMap;
            Map<Integer, String> finalProMap = proMap;
            Map<Integer, MaterialDto> finalMaterialMap = materialDtoMap;
            weighbridgeSendVOS = records.stream().map(weighbridgeSendDTO -> {
                WeighbridgeSendVO weighbridgeSendVO = new WeighbridgeSendVO();
                BeanUtils.copyProperties(weighbridgeSendDTO, weighbridgeSendVO);
                weighbridgeSendVO.setIsAdditionStr(AdditionalRecordEnum.desc(weighbridgeSendVO.getIsAddition()));
//              接收方
                if(StrUtil.isNotBlank(weighbridgeSendDTO.getReceiverName())){weighbridgeSendVO.setReceiveProject(weighbridgeSendDTO.getReceiverName());}
                weighbridgeSendVO.setWeightUnit("吨");
                Integer materialId = weighbridgeSendDTO.getMaterialId();
                if (materialId != null) {
                    // 材料id存在二级分类名称
                    MaterialDto materialDto = finalMaterialMap.get(materialId);
                    if (materialDto != null) {
                        weighbridgeSendVO.setMaterialName(materialDto.getMaterialCategoryName());
                        weighbridgeSendVO.setMaterialSpec(StrUtil.format("{}/{}", materialDto.getMaterialName(), materialDto.getMaterialSpec()));
                    }
                }
//              发货单位
                weighbridgeSendVO.setSendProject(finalProMap.get(weighbridgeSendDTO.getSendProjectId()));
//              发货人
                weighbridgeSendVO.setSender(finalMimMap.get(weighbridgeSendDTO.getCreateId()));

                return weighbridgeSendVO;
            }).collect(Collectors.toList());
        }

        Page<WeighbridgeSendVO> weighbridgeSendVOPage = new Page<>();
        BeanUtils.copyProperties(weighbridgeSendDTOList, weighbridgeSendVOPage);
        weighbridgeSendVOPage.setRecords(weighbridgeSendVOS);
        return weighbridgeSendVOPage;
    }

    private void getProjectIdList(WeighInfoQuery query) {
        AuthUser user = authUserHolder.getCurrentUser();
        query.setCompanyId(user.getCurrentCompanyId());
//      用户在项目层，顶点也只能是项目（组织树选分公司，最外面就是项目）
        if (user.getCurrentProjectId() != null) {
            query.setProjectIdList(Arrays.asList(user.getCurrentProjectId()));
        } else {
//          直属节点 -> 项目列表 （已包含用户设置项目范围）
            List<Integer> list = projectServiceProxy.statisticsProjectIds(user.getCurrentCompanyId(), query.getPoint());
            if (CollUtil.isNotEmpty(list)) {
                query.setProjectIdList(list);
            }
        }
    }

    @Override
    public IPage<CrccMaterialWeighInfoDto> extWeightList(Integer projectId, Integer materialId, ztcjExtWeightListQuery query) {
        IPage<CrccMaterialWeighInfoDto> materialWeighInfoDTOS = materialDataMapper.queryCrccWeightReceiveInfo(projectId, materialId, query);
        if (CollUtil.isEmpty(materialWeighInfoDTOS.getRecords())) {
            return materialWeighInfoDTOS;
        }
        List<Integer> materialIdList = materialWeighInfoDTOS.getRecords().stream().map(CrccMaterialWeighInfoDto::getMaterialId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Integer> receiveProjectList = materialWeighInfoDTOS.getRecords().stream().map(CrccMaterialWeighInfoDto::getReceiveProject)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<String> receiverIdList = materialWeighInfoDTOS.getRecords().stream().map(CrccMaterialWeighInfoDto::getCreateId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());

        Map<Integer, MaterialDto> materialMap = materialServiceProxy.listMaterialByIds(materialIdList).stream()
                .collect(Collectors.toMap(MaterialDto::getMaterialId, obj -> obj));
        Map<Integer, ProjectVO> receiveProjectInfoMap = projectServiceProxy.getSimpleProjects(receiveProjectList).stream()
                .collect(Collectors.toMap(ProjectVO::getProjectId, obj -> obj));
        EmployeeDetailQueryDto queryDto = new EmployeeDetailQueryDto();
        queryDto.setMemberIdList(receiverIdList);
        queryDto.setCompanyId(materialWeighInfoDTOS.getRecords().get(0).getCompanyId());
        Map<String, String> receiverMap = employeeServiceProxy.employeeList(queryDto).stream().collect(Collectors.toMap(EmployeeDetailDto::getMemberId, EmployeeDetailDto::getMemberName));

        materialWeighInfoDTOS.getRecords().forEach(vo -> {
            ReceiveModeEnum receiveModeEnum = ReceiveModeEnum.getReceiveMode(vo.getReceiveMode(), vo.getMaterialValidity());
            vo.setReceiveMode(receiveModeEnum.value());
            // 材料类目
            if (vo.getMaterialId() != null || StrUtil.isNotEmpty(vo.getCategoryName())) {
                vo.setMaterialCategoryName(vo.getCategoryName());
                MaterialDto materialDto = materialMap.get(vo.getMaterialId());
                if (ObjectUtil.isNotEmpty(materialDto)) {
                    if (StrUtil.isEmpty(vo.getCategoryName())) {
                        vo.setMaterialCategoryName(materialDto.getMaterialCategoryName());
                    }
                    vo.setMaterialExtCode(materialDto.getExtCode());
                    vo.setMaterialName(materialDto.getMaterialName());
                    vo.setMaterialSpec(materialDto.getMaterialSpec());
                }
            } else {
                if (StrUtil.isNotBlank(vo.getMaterialName())) {
                    vo.setMaterialCategoryName(vo.getMaterialName());
                    vo.setMaterialName(null);
                }
            }
            // 收货项目
            if (vo.getReceiveProject() != null) {
                ProjectVO dto = receiveProjectInfoMap.get(vo.getReceiveProject());
                if (ObjectUtil.isNotNull(dto)) {
                    vo.setProjectTitle(dto.getProjectTitle());
                    vo.setProjectCode(dto.getProjectNum());
                }
            }
            if (vo.getCreateId() != null && CollUtil.isNotEmpty(receiverMap)) {
                vo.setReceiver(receiverMap.get(vo.getCreateId()));
            }
            //处理图片
            vo.setEnterPics(picEchoUtil.echo(vo.getEnterPic(),4));
            vo.setLeavePics(picEchoUtil.echo(vo.getLeavePic(),4));
            vo.setDocumentPics(picEchoUtil.echo(vo.getDocumentPic(),-1));
        });
        return materialWeighInfoDTOS;
    }
}
