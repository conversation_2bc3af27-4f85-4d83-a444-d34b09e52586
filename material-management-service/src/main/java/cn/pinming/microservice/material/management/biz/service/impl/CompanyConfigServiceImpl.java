package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.microservice.material.management.biz.entity.CompanyConfig;
import cn.pinming.microservice.material.management.biz.form.CompanyConfigForm;
import cn.pinming.microservice.material.management.biz.mapper.CompanyConfigMapper;
import cn.pinming.microservice.material.management.biz.service.ICompanyConfigService;
import cn.pinming.microservice.material.management.biz.vo.CompanyConfigVO;
import cn.pinming.microservice.material.management.biz.vo.TheoreticalInfoVO;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.material_unit.api.material.service.MaterialService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 项目配置表 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-13 10:28:23
 */
@Service
public class CompanyConfigServiceImpl extends ServiceImpl<CompanyConfigMapper, CompanyConfig> implements ICompanyConfigService {
    @DubboReference
    private MaterialService materialService;
    @Override
    public void saveOrUpdateConfig(CompanyConfigForm form) {
        if (form.getType() == 1 || form.getType() == 2) {
            Optional.ofNullable(form.getIsEnable()).orElseThrow(() -> new BOException(BOExceptionEnum.NO_PARAM));
        }

        CompanyConfig one = this.lambdaQuery()
                .eq(CompanyConfig::getCompanyId, form.getCompanyId())
                .eq(CompanyConfig::getType, form.getType())
                .one();

        CompanyConfig companyConfig = new CompanyConfig();
        BeanUtil.copyProperties(form, companyConfig);
        if (ObjectUtil.isNotNull(one)) {
            companyConfig.setId(one.getId());
        }
        this.saveOrUpdate(companyConfig);
    }

    @Override
    public CompanyConfigVO show(Byte type, AuthUser user) {
        CompanyConfig companyConfig = this.lambdaQuery()
                .eq(CompanyConfig::getCompanyId, user.getCurrentCompanyId())
                .eq(CompanyConfig::getType, type)
                .one();
        CompanyConfigVO vo = new CompanyConfigVO();
        BeanUtil.copyProperties(companyConfig,vo);
        return vo;
    }

    @Override
    public Boolean enable(Integer companyId, Integer projectId, byte type) {
        CompanyConfig one = this.lambdaQuery()
                .eq(CompanyConfig::getCompanyId, companyId)
                .eq(CompanyConfig::getType, type)
                .one();

        if (ObjectUtil.isNotNull(one)) {
            return one.getProjectIds().contains(String.valueOf(projectId));
        }
        return false;
    }

    public List<TheoreticalInfoVO> theoreticalInfoVOList(List<Integer> materialList) {
        try {
            return cache.get(materialList);
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    private LoadingCache<List<Integer>, List<TheoreticalInfoVO>> cache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(5 * 60, TimeUnit.SECONDS)
            .refreshAfterWrite(5 * 60, TimeUnit.SECONDS)
            .build(new CacheLoader<List<Integer>, List<TheoreticalInfoVO>>() {
                @Override
                public List<TheoreticalInfoVO> load(List<Integer> materialList) {
                    List<TheoreticalInfoVO> result = new ArrayList<>();
                    List<MaterialDto> materialDtos = materialService.listMaterialByIds(materialList);
                    Optional.ofNullable(materialDtos).ifPresent(list -> list.forEach(e -> {
                        if (StrUtil.isNotBlank(e.getWeighValue())) {
                            TheoreticalInfoVO vo = new TheoreticalInfoVO();
                            vo.setMaterialId(e.getMaterialId());
                            vo.setTheoreticalUnitWeight(new BigDecimal(e.getWeighValue()));
                            result.add(vo);
                        }
                    }));
                    return result;
                }
            });
}
