package cn.pinming.microservice.material.management.biz.runner;

import cn.pinming.microservice.material.management.biz.service.ISupplierService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 启动前处理相关逻辑.
 *
 * <AUTHOR>
 * @version 2022/3/4 9:53 AM
 */
@Slf4j
@Component
public class ApplicationCommandLineRunner implements CommandLineRunner {

    @Resource
    private ISupplierService supplierService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void run(String... args) throws Exception {
        log.info("项目启动前置处理开始");
        supplierService.initDataList();
        log.info("项目启动前置处理结束");
    }
}
