package cn.pinming.microservice.material.management.biz.timetask;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpUtil;
import cn.pinming.microservice.material.management.biz.entity.MaterialData;
import cn.pinming.microservice.material.management.biz.entity.MaterialPushLog;
import cn.pinming.microservice.material.management.biz.entity.MaterialSendReceive;
import cn.pinming.microservice.material.management.biz.form.AlgsPushForm;
import cn.pinming.microservice.material.management.biz.service.IMaterialDataService;
import cn.pinming.microservice.material.management.biz.service.IMaterialPushLogService;
import cn.pinming.microservice.material.management.biz.service.IMaterialSendReceiveService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;


/**
 * 安罗黄河高速定时推送
 *
 * <AUTHOR>
 * @since 2022/9/7 14:23
 */
@Component
@Slf4j
public class AlgsPushTimedTask {

    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private IMaterialSendReceiveService sendReceiveService;
    @Resource
    private IMaterialPushLogService materialPushLogService;

    @Value("${algs.push.address}")
    private String url;
    @Value("${algs.push.switch}")
    private Boolean enable;
    @Value("${algs.companyId}")
    private Integer companyId;
    @Value("${algs.projectId}")
    private Integer projectId;

//    @Scheduled(cron = "0 0/5 * * * ? ")
    public void algsPushData() {
        if (!enable) {
            return;
        }
        log.info("安罗黄河高速开启数据推送.......");
        // 需推送的数据
        List<MaterialData> materialDataList = materialDataService.algsNeedPushData(companyId, projectId);
        ;
        if (CollUtil.isEmpty(materialDataList)) {
            log.info("安罗黄河高速无需要推送的数据......");
            return;
        }
        for (MaterialData materialData : materialDataList) {
            MaterialSendReceive materialSendReceive = sendReceiveService.getById(materialData.getReceiveId());
            try {
                AlgsPushForm algsPushForm = new AlgsPushForm();
                algsPushForm.setId(materialData.getId());
                algsPushForm.setOrganizationId("d2b07f90-1ed8-4363-81b6-194a7f5083b3");
                algsPushForm.setSectionId("21250");
                algsPushForm.setSectionName("中交二航局安罗项目部");
                algsPushForm.setMixingStationCode("a7ff1473a4f4da4b6ae8f7a18d778f83");
                algsPushForm.setMixingStationName("中交二航局");
                algsPushForm.setEquipmentNumber("zjeh-alxm");
                algsPushForm.setMaterialVendor(materialData.getSupplierName() + "(" + materialData.getSupplierName() + ")");
                algsPushForm.setMaterialType(materialData.getMaterialName());
                algsPushForm.setMaterialBatch(materialSendReceive.getReceiveNo());
                algsPushForm.setProductionDate(DateUtil.format(materialData.getEnterTime(), DatePattern.NORM_DATETIME_PATTERN));
                algsPushForm.setSendingPlace(materialData.getSupplierName());
                if (materialData.getWeightGross() != null) {
                    algsPushForm.setGrossWeight(materialData.getWeightGross().multiply(new BigDecimal(1000)));
                }
                if (materialData.getWeightTare() != null) {
                    algsPushForm.setTareWeight(materialData.getWeightTare().multiply(new BigDecimal(1000)));
                }
                if (materialData.getWeightNet() != null) {
                    algsPushForm.setNetWeight(materialData.getWeightNet().multiply(new BigDecimal(1000)));
                }
                if (materialData.getWeightDeduct() != null) {
                    algsPushForm.setDeduction(materialData.getWeightDeduct().multiply(new BigDecimal(1000)));
                }
                if (materialData.getWeightActual() != null) {
                    algsPushForm.setCountingWeight(materialData.getWeightActual().multiply(new BigDecimal(1000)));
                }
                BigDecimal ratio = materialData.getRatio();
                if (ratio != null && materialData.getActualCount() == null) {
                    // 跳过转换系数不为空 实际数量为空的数据
                    continue;
                }
                if (ratio == null) {
                    algsPushForm.setActualWeight(materialData.getWeightActual().multiply(new BigDecimal(1000)));
                } else {
                    algsPushForm.setActualWeight(materialData.getActualCount().multiply(new BigDecimal(1000)));
                }
                algsPushForm.setCarNumber(materialSendReceive.getTruckNo());
                algsPushForm.setEntryTime(DateUtil.format(materialData.getEnterTime(), DatePattern.NORM_DATETIME_PATTERN));
                algsPushForm.setExitTime(DateUtil.format(materialData.getLeaveTime(), DatePattern.NORM_DATETIME_PATTERN));
                algsPushForm.setReceivingPlace("黄河高速项目地磅");
                algsPushForm.setMaterialSpec(materialData.getMaterialName());
                algsPushForm.setSerialNumber(materialSendReceive.getReceiveNo());
                algsPushForm.setCreator(materialSendReceive.getReceiver());

                String pushData = JSONObject.toJSONString(algsPushForm);
                log.info("push data:{}", pushData);
                try {
                    String result = HttpUtil.post(url, pushData, 10000);
                    log.info("push result:{}", result);
                    JSONObject jsonObject = JSONObject.parseObject(result);
                    Integer code = (Integer) jsonObject.get("Code");
                    String message = (String) jsonObject.get("Message");

                    // 更改数据的推送状态
                    if (code == 200) {
                        materialDataService.updatePushDataState(Collections.singletonList(algsPushForm.getId()), (byte) 1);
                        log.info("成功推送,明细ID【{}】", algsPushForm.getId());
                    } else if (code == 500) {
                        materialDataService.updatePushDataState(Collections.singletonList(algsPushForm.getId()), (byte) 2);
                        log.info("失败推送,明细ID【{}】", algsPushForm.getId());
                        materialPushLogService.save(buildMaterialPushLog(algsPushForm.getMaterialBatch(), algsPushForm.getId(), message));
                    }
                } catch (HttpException e) {
                    log.error("接口请求超时:{}", e.getMessage());
                    materialDataService.updatePushDataState(Collections.singletonList(algsPushForm.getId()), (byte) 2);
                    log.info("失败推送,明细ID【{}】", algsPushForm.getId());
                    materialPushLogService.save(buildMaterialPushLog(algsPushForm.getMaterialBatch(), algsPushForm.getId(), "接口请求超时"));
                }
            } catch (Exception e) {
                log.error("失败推送,数据异常{}, 明细id:{}", e.getMessage(),materialData.getId());
                materialDataService.updatePushDataState(Collections.singletonList(materialData.getId()), (byte) 2);
                materialPushLogService.save(buildMaterialPushLog(materialSendReceive.getReceiveNo(), materialData.getId(), "失败推送,数据异常"));
            }

        }
    }

    private MaterialPushLog buildMaterialPushLog(String receiveNo, String dataId, String reason) {
        return MaterialPushLog.builder().receiveNo(receiveNo).materialDataId(dataId).reason(reason).build();
    }
}
