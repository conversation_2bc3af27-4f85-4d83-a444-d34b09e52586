package cn.pinming.microservice.material.management.biz.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.dto.QrcodeInfoDTO;
import cn.pinming.microservice.material.management.biz.entity.CompanyConfig;
import cn.pinming.microservice.material.management.biz.form.*;
import cn.pinming.microservice.material.management.biz.mapper.PurchaseOrderDetailMapper;
import cn.pinming.microservice.material.management.biz.mapper.PurchaseOrderMapper;
import cn.pinming.microservice.material.management.biz.query.ContractQuery;
import cn.pinming.microservice.material.management.biz.query.MobileCardQuery;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.annotation.Receive;
import cn.pinming.microservice.material.management.common.annotation.Send;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 移动收料表 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:30:10
 */
@Api(tags = "移动收料-controller", value = "zh")
@RestController
@RequestMapping("api/biz/mobile-receive")
@Slf4j
public class MobileReceiveController {

    @Resource
    private IMobileReceiveService mobileReceiveService;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;
    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;
    @Resource
    private IPurchaseOrderService purchaseOrderService;
    @Resource
    private IMobileReceiveDetailService detailService;
    @Resource
    private IPurchaseOrderService orderService;
    @Resource
    private IPurchaseContractService contractService;
    @Resource
    private IMobileReceiveTotalService totalService;
    @Resource
    private IMobileReceiveTruckService mobileReceiveTruckService;
    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private IMaterialReviseService materialReviseService;
    @Resource
    private ICompanyConfigService companyConfigService;
    @Resource
    private ProjectServiceProxy projectServiceProxy;

    @ApiOperation(value = "新增收料记录")
    @PostMapping("/add")
    public ResponseEntity<Response> add(@Validated @Valid @RequestBody MobileReceiveForm form) {
        AuthUser user = authUserHolder.getCurrentUser();
        form.setCompanyId(user.getCurrentCompanyId());
        form.setProjectId(user.getCurrentProjectId());
        form.setReceiver(user.getMemberName());
        mobileReceiveService.add(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "采购单编号动态模糊查询", response = OrderNoVO.class)
    @GetMapping("/seek/{orderNo}")
    public ResponseEntity<Response> seekOrderNo(@PathVariable("orderNo") String orderNo) {
        AuthUser user = authUserHolder.getCurrentUser();
        List<OrderNoVO> vos = purchaseOrderMapper.seek(orderNo, user);
        return ResponseEntity.ok(new SuccessResponse(vos));
    }

    @ApiOperation(value = "采购单查询与选择-根据供应商id", response = PrePurchaseVO.class)
    @GetMapping("/purchaseList/{supplierId}")
    public ResponseEntity<Response> purchaseList(@PathVariable("supplierId") Integer supplierId) {
        List<PrePurchaseVO> vos = orderService.listPurchase(supplierId);
        return ResponseEntity.ok(new SuccessResponse(vos));
    }

    @ApiOperation(value = "合同查询与选择-根据供应商id", response = PurchaseContractVO.class)
    @PostMapping("/contractList")
    public ResponseEntity<Response> contractList(@RequestBody ContractQuery query) {
        List<PurchaseContractVO> vos = contractService.listBySupplierId(query);
        return ResponseEntity.ok(new SuccessResponse(vos));
    }

    @ApiOperation(value = "有采购单-获取上下限和结算单位", response = ThresholdVO.class)
    @GetMapping("/box/{purchaseId}/{materialId}")
    public ResponseEntity<Response> selectThreshold(@PathVariable("purchaseId") String purchaseId, @PathVariable("materialId") Integer materialId) {
        ThresholdVO vo = purchaseOrderDetailMapper.selectThreshold(purchaseId, materialId);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "获取采购单信息", response = PurchaseSimpleVO.class)
    @GetMapping("/select/{orderId}")
    public ResponseEntity<Response> selectOrderInfo(@PathVariable("orderId") String orderId) {
        PurchaseSimpleVO vo = purchaseOrderService.selectOrderInfo(orderId, null);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = " 采购单编号校验")
    @GetMapping("/checkOut/{orderNo}")
    public ResponseEntity<Response> checkOrder(@PathVariable("orderNo") String orderNo) {
        AuthUser user = authUserHolder.getCurrentUser();
        purchaseOrderService.checkOut(orderNo, user);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = " 移动收料单卡片列表页", response = MobileReceiveCardVO.class)
    @PostMapping("/page")
    public ResponseEntity<Response> page(@RequestBody MobileCardQuery query) {
        AuthUser user = authUserHolder.getCurrentUser();
        query.setCompanyId(user.getCurrentCompanyId());
        if (user.getCurrentDepartmentId() != null) {
            query.setProjectIds(projectServiceProxy.getAllProjectIdByCompanyDeptId(user.getCurrentCompanyId(), user.getCurrentDepartmentId()));
        }
        query.setProjectId(user.getCurrentProjectId() == null ? query.getProjectId() : user.getCurrentProjectId());
        IPage<MobileReceiveCardVO> page = detailService.selectPage(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "收料单详情", response = ReceiveCardDetailVO.class)
    @GetMapping("/detail/{receiveId}")
    public ResponseEntity<Response> detail(@PathVariable String receiveId) {
        ReceiveCardDetailVO vo = mobileReceiveService.detail(receiveId);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "查看点验历史", response = MobileReceiveHistoryVO.class)
    @GetMapping("/history/{totalId}")
    public ResponseEntity<Response> history(@PathVariable("totalId") String totalId) {
        MobileReceiveHistoryVO vo = totalService.history(totalId);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "App-移动收料记录-根据采购单~~", response = MobileReceiveVO.class)
    @GetMapping("/list/{purchaseId}")
    public ResponseEntity<Response> list(@PathVariable("purchaseId") String purchaseId) {
        List<MobileReceiveVO> list = mobileReceiveTruckService.listByPurchaseId(purchaseId);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "移动收料单位列表")
    @Log(title = "移动收料单位列表")
    @GetMapping("/unit/lists")
    public ResponseEntity<Response> mobileReceiveUnitList() {
        AuthUser user = authUserHolder.getCurrentUser();
        List<String> list = totalService.listUnitByCompanyId(user.getCurrentCompanyId());
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "校验",response = QrcodeInfoDTO.class)
    @Log(title = "校验", businessType = BusinessType.QUERY)
    @GetMapping("/check")
    public ResponseEntity<Response> check(@RequestParam String input) {
        log.info(input);
        QrcodeInfoDTO qrcodeInfoDTO = mobileReceiveService.processQrcodeInfo(input);
        mobileReceiveService.checkBillQrcode(qrcodeInfoDTO);
        return ResponseEntity.ok(new SuccessResponse(qrcodeInfoDTO));
    }

    @ApiOperation(value = "批量收料提交/修订")
    @PostMapping("/batch")
    public ResponseEntity<Response> batch(@RequestBody @NotEmpty(message = "数据上传列表为空") List<MobileMaterialBatchForm> list) {
        if (list.size() > 200) {throw new BOException(BOExceptionEnum.OUTSIZE);}
        SuccessOrFailVO vo = mobileReceiveService.batch(list);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "收料提交", response = WeighReceiveResultVO.class)
    @Log(title = "收料提交", businessType = BusinessType.INSERT)
    @PostMapping("/commit")
    public ResponseEntity<Response> commit(@Validated(Receive.class) @Valid @RequestBody WeighReceiveCommitForm form) {
        form.setIsWebPushed((byte)1);
        WeighReceiveResultVO resultVO = mobileReceiveService.weighReceiveCommit(form);
        return ResponseEntity.ok(new SuccessResponse(resultVO));
    }

    @ApiOperation(value = "发料提交",response =  WeighReceiveResultVO.class)
    @Log(title = "发料提交", businessType = BusinessType.INSERT)
    @PostMapping("/sendCommit")
    public ResponseEntity<Response> sendCommit(@Validated(Send.class) @RequestBody WeighReceiveCommitForm form) {
        WeighReceiveResultVO resultVO = mobileReceiveService.weighReceiveCommit(form);
        return ResponseEntity.ok(new SuccessResponse(resultVO));
    }

    @ApiOperation(value = "收料修订")
    @Log(title = "修订", businessType = BusinessType.INSERT)
    @PostMapping("/fix")
    public ResponseEntity<Response> fix(@RequestBody WeighReceiveFixForm form) {
        form.setIsScanReceive(true);
        mobileReceiveService.billFix(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "发料修订")
    @Log(title = "发料修订", businessType = BusinessType.INSERT)
    @PostMapping("/sendFix")
    public ResponseEntity<Response> sendFix(@RequestBody WeighSendFixForm form) {
        materialReviseService.sendFix(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "上传单据照片")
    @Log(title = "修订", businessType = BusinessType.INSERTORUPDATE)
    @PostMapping("/pic")
    public ResponseEntity<Response> pic(@RequestBody PicForm form) {
        materialDataService.pic(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "检验-历史",response = PurchaseSimpleVO.class)
    @Log(title = "检验-历史", businessType = BusinessType.QUERY)
    @GetMapping("/check/history")
    public ResponseEntity<Response> history() {
        PurchaseSimpleVO vo = purchaseOrderService.history();
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "是否需要展示理重 true 需要 false 不需要")
    @GetMapping("/isShow")
    public ResponseEntity<Response> isShow() {
        boolean flag = false;
        AuthUser user = authUserHolder.getCurrentUser();
        if (user.getCurrentProjectId() == null) {
            flag = false;
        }
        CompanyConfig companyConfig = companyConfigService.lambdaQuery()
                .eq(CompanyConfig::getCompanyId, user.getCurrentCompanyId())
                .eq(CompanyConfig::getType, 3)
                .isNotNull(CompanyConfig::getProjectIds)
                .one();
        if (ObjectUtil.isNotNull(companyConfig)) {
            List<String> projectIdList = StrUtil.split(companyConfig.getProjectIds(), ",");
            if (projectIdList.contains(String.valueOf(user.getCurrentProjectId()))) {
                flag = true;
            }
        }
        return ResponseEntity.ok(new SuccessResponse(flag));
    }

    @ApiOperation(value = "扫码历史", response = MaterialReviseDetailVO.class)
    @GetMapping("/mobile/history")
    public ResponseEntity<Response> mobileHistory() {
        MaterialReviseDetailVO vo = materialDataService.mobileHistory();
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "扫码历史-收发料", response = MaterialReviseDetailVO.class)
    @GetMapping("/mobile/history/{type}")
    public ResponseEntity<Response> mobileHistoryByType(@PathVariable("type")Byte type,@RequestParam(value = "receiveType",required = false)Byte receiveType) {
        MaterialReviseDetailVO vo = materialDataService.mobileHistoryByType(type,receiveType);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }
}
