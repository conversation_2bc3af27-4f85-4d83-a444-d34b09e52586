package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.MobileGoodsCardDTO;
import cn.pinming.microservice.material.management.biz.dto.SimpleTransformDTO;
import cn.pinming.microservice.material.management.biz.entity.MobileReceiveDetail;
import cn.pinming.microservice.material.management.biz.form.MobileReceiveMaterialForm;
import cn.pinming.microservice.material.management.biz.mapper.MobileReceiveDetailMapper;
import cn.pinming.microservice.material.management.biz.mapper.MobileReceiveMapper;
import cn.pinming.microservice.material.management.biz.query.MobileCardQuery;
import cn.pinming.microservice.material.management.biz.service.IMobileReceiveDetailService;
import cn.pinming.microservice.material.management.biz.service.IMobileReceiveTotalService;
import cn.pinming.microservice.material.management.biz.vo.MobileGoodsCardVO;
import cn.pinming.microservice.material.management.biz.vo.MobileReceiveCardVO;
import cn.pinming.microservice.material.management.proxy.FileServiceProxy;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material.management.wrapper.ProjectNameWrapper;
import cn.pinming.microservice.material.management.wrapper.SupplierWrapper;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 移动收料细节表 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:31:23
 */
@Service
public class MobileReceiveDetailServiceImpl extends ServiceImpl<MobileReceiveDetailMapper, MobileReceiveDetail> implements IMobileReceiveDetailService {

    @Resource
    private IMobileReceiveTotalService totalService;
    @Resource
    private MobileReceiveMapper receiveMapper;
    @Resource
    private MobileReceiveDetailMapper detailMapper;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private SupplierWrapper supplierWrapper;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private ProjectNameWrapper projectTitleWrapper;

    /**
     * 各材料收货记录录入
     *
     * @param list, receiveId
     */
    @Override
    public void add(List<MobileReceiveMaterialForm> list, String receiveId, SimpleTransformDTO simpleTransformDTO) {

//      遍历每个材料的统计记录
        list.forEach(e -> {
            String totalUuid = IdUtil.simpleUUID();
            BeanUtils.copyProperties(e, simpleTransformDTO);
            simpleTransformDTO.setReceiveId(receiveId);
            simpleTransformDTO.setTotalId(totalUuid);
            Byte type = simpleTransformDTO.getType();

            if (type != null && type == 1) {
                // 简易版本
                MobileReceiveDetail detail = new MobileReceiveDetail();
                simpleTransform(detail, simpleTransformDTO);
                this.save(detail);
            } else {
                e.getDetailList().forEach(m -> {
                    MobileReceiveDetail detail = new MobileReceiveDetail();

                    BeanUtils.copyProperties(m, detail);
                    if (CollUtil.isNotEmpty(m.getPointsPic())) {
                        fileServiceProxy.confirmFiles(m.getPointsPic(),"material-management");
                        detail.setPointsPic(String.join(",", m.getPointsPic()));
                    }
                    detail.setMaterialId(e.getMaterialId());
                    detail.setReceiveId(receiveId);
                    // 结算合计 = 件数 * 每件含量 * 换算系数
                    double d = m.getNumber() * (m.getContent().multiply(m.getRatio()).doubleValue());
                    detail.setSettlementTotal(BigDecimal.valueOf(d));
                    detail.setTotalId(totalUuid);
                    //  各材料收料信息细节录入
                    this.save(detail);
                });
            }

//          各材料收料信息统计录入
            totalService.add(e, simpleTransformDTO);
        });
    }

    /**
     * 移动收料单卡片列表页
     *
     * @param query
     * @return
     */
    @Override
    public IPage<MobileReceiveCardVO> selectPage(MobileCardQuery query) {
        IPage<MobileReceiveCardVO> page = new Page<>();

//      根据条件找出所有收货单
        IPage<MobileReceiveCardVO> totalPage = receiveMapper.selectReceivePage(query);
        if (CollectionUtil.isNotEmpty(totalPage.getRecords())) {
            List<MobileReceiveCardVO> totalList = totalPage.getRecords();

            List<MobileReceiveCardVO> listWithSupplierId = totalList.stream().filter(e -> e.getSupplierId() != null).collect(Collectors.toList());
            supplierWrapper.wrap(listWithSupplierId, authUserHolder.getCurrentUser().getCurrentCompanyId());
            projectTitleWrapper.wrap(totalList);

            List<String> receiveIdList = totalList.stream().map(MobileReceiveCardVO::getId).collect(Collectors.toList());
            List<MobileGoodsCardDTO> materialList = detailMapper.selectReceiveDetail(receiveIdList);
            if (CollectionUtil.isEmpty(materialList)) {
                return null;
            }

//          收货单id -> 收货单材料收货信息
            Map<String, List<MobileGoodsCardDTO>> map = materialList.stream().collect(Collectors.groupingBy(MobileGoodsCardDTO::getReceiveId));

            totalList.forEach(m -> {
                List<MobileGoodsCardVO> goodsCardVOS = new ArrayList<>();

//              某一个收货单下的材料收货信息, 因为无采购收货，有些材料的偏差状态可能为空
                List<MobileGoodsCardDTO> goodsCardDTOList = map.get(m.getId());
                if (CollectionUtil.isNotEmpty(goodsCardDTOList)) {
                    List<Integer> materialIds = goodsCardDTOList.stream().map(MobileGoodsCardDTO::getMaterialId).distinct().collect(Collectors.toList());
                    List<MaterialDto> materialsInfo = materialServiceProxy.listMaterialByIds(materialIds);
                    Map<Integer, MaterialDto> materialDtoMap = materialsInfo.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, e -> e));

                    if (CollUtil.isNotEmpty(materialsInfo)) {
                        List<String> category = materialsInfo.stream().map(x -> {
                            String str1 = StrUtil.format("{}", x.getMaterialCategoryName());
                            return str1;
                        }).collect(Collectors.toList());
                        String result = String.join(",", category);
                        m.setCategory(result);
                    }

                    goodsCardDTOList.forEach(n -> {
                        MobileGoodsCardVO goodsCardVO = new MobileGoodsCardVO();

                        BeanUtils.copyProperties(n, goodsCardVO);
                        MaterialDto materialDto = materialDtoMap.get(n.getMaterialId());
                        if (ObjectUtil.isNotEmpty(materialDto)) {
                            String str = StrUtil.format("{}/{}/{}", materialDto.getMaterialCategoryName(), materialDto.getMaterialName(), materialDto.getMaterialSpec());
                            goodsCardVO.setMaterialName(str);
                        }

                        goodsCardVOS.add(goodsCardVO);
                    });

                    m.setList(goodsCardVOS);
                }
            });

            BeanUtils.copyProperties(totalPage, page);
            page.setRecords(totalList);
        }
        return page;
    }

    /**
     * 简易对象处理
     *
     * @param detail
     * @param dto
     */
    private void simpleTransform(MobileReceiveDetail detail, SimpleTransformDTO dto) {
        detail.setReceiveId(dto.getReceiveId());
        detail.setMaterialId(dto.getMaterialId());
        detail.setTotalId(dto.getTotalId());
        detail.setIsStandard((byte) 1);
        detail.setNumber(1);
        detail.setContent(dto.getActualSettlementTotal());
        detail.setUnit(dto.getSettlementUnit());
        detail.setRatio(BigDecimal.ONE);
        detail.setSettlementUnit(dto.getSettlementUnit());
        detail.setSettlementTotal(dto.getActualSettlementTotal());
    }


}
