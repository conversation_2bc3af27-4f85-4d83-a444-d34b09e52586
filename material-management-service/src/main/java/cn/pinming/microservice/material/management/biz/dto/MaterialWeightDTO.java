/**
  * Copyright 2021 json.cn 
  */
package cn.pinming.microservice.material.management.biz.dto;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;

@Data
public class MaterialWeightDTO {


    @ApiModelProperty(value = "终端地磅ID")
    private String weighbridgeId;

    @ApiModelProperty(value = "地磅名称")
    private String weighbridgeName;

    @ApiModelProperty(value = "过磅序号")
    private Integer weightNo;

    @ApiModelProperty(value = "过磅重量")
    private BigDecimal weight;

    @ApiModelProperty(value = "过磅时间")
    private LocalDateTime weightTime;

}