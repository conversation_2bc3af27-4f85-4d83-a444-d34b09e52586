package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.MaterialSimpleDTO;
import cn.pinming.microservice.material.management.biz.dto.PurchaseContractDTO;
import cn.pinming.microservice.material.management.biz.dto.SimpleSupplierDTO;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContract;
import cn.pinming.microservice.material.management.biz.query.ContractQuery;
import cn.pinming.microservice.material.management.biz.query.PurchaseContractQuery;
import cn.pinming.microservice.material.management.biz.vo.AppContractChooseVO;
import cn.pinming.microservice.material.management.biz.vo.ContractForReviseVO;
import cn.pinming.microservice.material.management.biz.vo.PurchaseOrderStatusVO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 采购合同 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface PurchaseContractMapper extends BaseMapper<PurchaseContract> {

    @InterceptorIgnore(tenantLine = "true")
    IPage<PurchaseContractDTO> selectPageDTO(@Param("query") PurchaseContractQuery query);

    @InterceptorIgnore(tenantLine = "true")
    List<String> selectBelongProjects(@Param("id") String contractId);

    List<PurchaseContract> selectContract(@Param("projectId")Integer projectId);

    List<PurchaseContract> selectContractAndExtCode(@Param("projectId")Integer projectId, @Param("extCodeList") Collection<String> extCodeList);

    List<PurchaseOrderStatusVO> selectOrderStatus(@Param("projectIds") List<Integer> projectIds);

    List<SimpleSupplierDTO> selectSupplierIdsById(@Param("projectId") Integer projectId,@Param("companyId")Integer companyId);

    @InterceptorIgnore(tenantLine = "true")
    PurchaseContract getContract(@Param("id")String id);

    @InterceptorIgnore(tenantLine = "true")
    void updateInfo(PurchaseContract contract);

    List<PurchaseContractDTO> listBySupplierId(ContractQuery query, @Param("projectId")Integer projectId, @Param("companyId")Integer companyId);

    MaterialSimpleDTO selectMaterial(@Param("contractId")String contractId,@Param("materialId")Integer materialId);

    List<ContractForReviseVO> listForRevise(@Param("supplierId") Integer supplierId,@Param("companyId") Integer companyId,@Param("projectId") Integer projectId);

    Set<Integer> listUsedSupplierId();

    List<AppContractChooseVO> appchoose(@Param("contractId") String contractId,@Param("categoryId") String categoryId);

    List<PurchaseContract> selectContractAuto();

    PurchaseContract ocrCheck(@Param("companyId")Integer currentCompanyId,@Param("projectId") Integer currentProjectId,@Param("id") String otherId);

}
