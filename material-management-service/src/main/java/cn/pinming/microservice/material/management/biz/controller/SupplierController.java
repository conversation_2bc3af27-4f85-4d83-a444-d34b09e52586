package cn.pinming.microservice.material.management.biz.controller;


import cn.hutool.core.util.StrUtil;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.form.SupplierForm;
import cn.pinming.microservice.material.management.biz.listener.SupplierDataListener;
import cn.pinming.microservice.material.management.biz.query.SupplierQuery;
import cn.pinming.microservice.material.management.biz.service.ISupplierService;
import cn.pinming.microservice.material.management.biz.vo.ImportResultVO;
import cn.pinming.microservice.material.management.biz.vo.SupplierVO;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.apache.poi.util.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * <p>
 * 供应商表 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-04-13 13:17:24
 */
@Api(tags = "供应商", value = "lh")
@RestController
@RequestMapping("/api/supplier")
@AllArgsConstructor
public class SupplierController {

    private final ISupplierService supplierService;

    @ApiOperation(value = "列表", response = SupplierVO.class)
    @Log(title = "列表", businessType = BusinessType.QUERY)
    @PostMapping("/list")
    public ResponseEntity<Response> list(@RequestBody SupplierQuery query) {
        Page<SupplierVO> page = supplierService.listByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "保存")
    @Log(title = "保存", businessType = BusinessType.INSERTORUPDATE)
    @PostMapping("/save")
    public ResponseEntity<Response> add(@Validated @RequestBody SupplierForm form) {
        supplierService.saveSupplier(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "启用/禁用")
    @Log(title = "启用/禁用", businessType = BusinessType.UPDATE)
    @PostMapping("/enable/{id}")
    public ResponseEntity<Response> enable(@PathVariable String id) {
        supplierService.updateSupplierStatus(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "删除")
    @Log(title = "删除", businessType = BusinessType.UPDATE)
    @PostMapping("/delete/{id}")
    public ResponseEntity<Response> delete(@PathVariable Integer id) {
        supplierService.deleteSupplierById(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation("导入模板下载")
    @GetMapping("/download")
    @SneakyThrows
    public void download(HttpServletResponse response) {
        String name = "供应商导入模板";
        ClassPathResource resource = new ClassPathResource(StrUtil.format("templates/{}.xlsx", name));
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(name, "utf-8") + ".xlsx");
        ServletOutputStream outputStream = response.getOutputStream();
        IOUtils.copy(resource.getInputStream(), outputStream);
        response.flushBuffer();
    }

    @ApiOperation(value = "导入")
    @Log(title = "导入", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public ResponseEntity<Response> importSupplier(MultipartFile file) throws IOException {
        List<SupplierForm> list = EasyExcel.read(file.getInputStream(), SupplierForm.class, new SupplierDataListener()).sheet().doReadSync();
        //处理
        ImportResultVO result = supplierService.importSupplier(list);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "拌合站站点列表")
    @Log(title = "拌合站站点列表", businessType = BusinessType.QUERY)
    @GetMapping("/plant/list")
    public ResponseEntity<Response> plantList() {
        return ResponseEntity.ok(new SuccessResponse(supplierService.listMixPlantInfo()));
    }

}
