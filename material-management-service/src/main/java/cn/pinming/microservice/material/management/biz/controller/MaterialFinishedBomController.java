package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.microservice.material.management.biz.form.MaterialBomForm;
import cn.pinming.microservice.material.management.biz.query.BaseQuery;
import cn.pinming.microservice.material.management.biz.service.IMaterialFinishedBomService;
import cn.pinming.microservice.material.management.biz.vo.IngredientListVO;
import cn.pinming.microservice.material.management.biz.vo.MaterialBomVO;
import cn.pinming.microservice.material.management.common.SuccessResponse;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 成品BOM包设置 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 14:31:34
 */
@Api(tags = "BOM包",value = "zh")
@RestController
@RequestMapping("/api/biz/bom")
@AllArgsConstructor
public class MaterialFinishedBomController {

    private final IMaterialFinishedBomService materialFinishedBomService;

    private final AuthUserHolder authUserHolder;

    @ApiOperation(value = "添加or更新")
    @PostMapping("/saveOrUpdate")
    public ResponseEntity<SuccessResponse> saveOrUpdateBom(@RequestBody MaterialBomForm materialBomForm) {
        materialFinishedBomService.saveOrUpdateBom(materialBomForm);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "bom列表", response = MaterialBomVO.class)
    @PostMapping("/list")
    public ResponseEntity<SuccessResponse> list(@RequestBody BaseQuery baseQuery) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer projectId = currentUser.getCurrentProjectId();
        baseQuery.setProjectId(projectId);
        IPage<MaterialBomVO> materialBomVOList = materialFinishedBomService.listMaterialFinishBom(baseQuery);
        return ResponseEntity.ok(new SuccessResponse(materialBomVOList));
    }

    @ApiOperation(value = "本拌合站已存在成品类别")
    @GetMapping("/exist/categories")
    public ResponseEntity<SuccessResponse> existCategories() {
        List<Integer> existCategories = materialFinishedBomService.listExistCategories();
        return ResponseEntity.ok(new SuccessResponse(existCategories));
    }

    @ApiOperation(value = "其他技术要求列表")
    @GetMapping("/other/parameter")
    public ResponseEntity<SuccessResponse> otherParameter() {
        List<String> otherParameter = materialFinishedBomService.listOtherParameter();
        return ResponseEntity.ok(new SuccessResponse(otherParameter));
    }

    @ApiOperation(value = "删除bom")
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<SuccessResponse> deleteBom(@PathVariable String id) {
        materialFinishedBomService.deleteBom(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    /**
     * 根据订单明细id渲染申请单
     * @return
     */
    @ApiOperation(value = "根据订单明细id渲染申请单",response = IngredientListVO.class)
    @Log(title = "根据订单明细id渲染申请单", businessType = BusinessType.QUERY)
    @GetMapping("/otherMaterial/{mixingPlantOrderDetailId}")
    public ResponseEntity<Response> otherMaterial(@PathVariable("mixingPlantOrderDetailId") String mixingPlantOrderDetailId) {
        IngredientListVO vo = materialFinishedBomService.seek(mixingPlantOrderDetailId);
        return ResponseEntity.ok(new cn.pinming.microservice.material.management.common.SuccessResponse(vo));
    }

}
