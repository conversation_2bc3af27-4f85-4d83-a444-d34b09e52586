package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 进出场预报备
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-11-16 15:07:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_pre_weigh_report")
@ApiModel(value = "PreWeighReport对象", description = "进出场预报备")
public class PreWeighReport implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "报备记录编号")
    private String no;

    @ApiModelProperty(value = "采购单id")
    @TableField("purchase_order_id")
    private String purchaseOrderId;

    @ApiModelProperty(value = "类型 1:进场 2:出场")
    private Byte type;

    @ApiModelProperty(value = "拌合站订单明细id")
    @TableField("order_detail_id")
    private String orderDetailId;

    @ApiModelProperty(value = "出场单id")
    @TableField("exit_ticket_id")
    private String exitTicketId;

    @ApiModelProperty(value = "预进场类型 1:单一规格过磅 2:多规格过磅 3:非过磅")
    @TableField("weigh_type")
    private Byte weighType;

    @ApiModelProperty(value = "报备记录状态 1:草稿 2:已提交")
    private Byte status;

    @ApiModelProperty(value = "公司id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
