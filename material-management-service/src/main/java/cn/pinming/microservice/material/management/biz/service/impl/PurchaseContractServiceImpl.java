package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.material.v2.Material;
import cn.pinming.material.v2.MaterialClientBuilder;
import cn.pinming.material.v2.model.form.PurchaseForm;
import cn.pinming.material.v2.model.form.PurchaseItemForm;
import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.MaterialVerify;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContract;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContractDetail;
import cn.pinming.microservice.material.management.biz.entity.PurchaseOrder;
import cn.pinming.microservice.material.management.biz.enums.PurchaseOrderStatusEnum;
import cn.pinming.microservice.material.management.biz.form.PurchaseContractDetailForm;
import cn.pinming.microservice.material.management.biz.form.PurchaseContractForm;
import cn.pinming.microservice.material.management.biz.form.PushConfigForm;
import cn.pinming.microservice.material.management.biz.mapper.MaterialVerifyMapper;
import cn.pinming.microservice.material.management.biz.mapper.PurchaseContractDetailMapper;
import cn.pinming.microservice.material.management.biz.mapper.PurchaseContractMapper;
import cn.pinming.microservice.material.management.biz.mapper.PurchaseOrderMapper;
import cn.pinming.microservice.material.management.biz.query.ContractQuery;
import cn.pinming.microservice.material.management.biz.query.PurchaseContractQuery;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.MaterialToMapUtil;
import cn.pinming.microservice.material.management.common.util.NoUtil;
import cn.pinming.microservice.material.management.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material.management.wrapper.CreateNameWrapper;
import cn.pinming.microservice.material.management.wrapper.SupplierWrapper;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.material_unit.api.unit.dto.UnitDto;
import cn.pinming.microservice.material_unit.api.unit.service.UnitService;
import cn.pinming.weaponx.wrapper.wrapper.ProjectTitleWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.joining;

/**
 * <p>
 * 采购合同 服务实现类.
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Slf4j
@Service
public class PurchaseContractServiceImpl extends ServiceImpl<PurchaseContractMapper, PurchaseContract> implements IPurchaseContractService {

    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private IPurchaseContractDetailService contractDetailService;
    @Resource
    private IPurchaseOrderService purchaseOrderService;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private ProjectTitleWrapper projectTitleWrapper;
    @Resource
    private SupplierWrapper supplierWrapper;
    @Resource
    private PurchaseOrderMapper orderMapper;
    @Resource
    private PurchaseContractDetailMapper contractDetailMapper;
    @Resource
    private PurchaseContractMapper purchaseContractMapper;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;
    @Resource
    private IPurchaseOrderDetailService purchaseOrderDetailService;
    @Resource
    private CreateNameWrapper createNameWrapper;
    @Resource
    private MaterialToMapUtil materialToMapUtil;
    @Resource
    private VerifyService verifyService;
    @Resource
    private MaterialVerifyMapper materialVerifyMapper;
    @DubboReference
    private UnitService unitService;
    @Resource
    private NoUtil noUtil;
    @Resource
    private IMaterialVerifyService materialVerifyService;
    @Resource
    private CompanyConfigServiceImpl companyConfigServiceImpl;
    @Resource
    private ISdkConfigService sdkConfService;

    private AuthUser getAuthUser() {
        return authUserHolder.getCurrentUser();
    }

    @Override
    public IPage<?> pageListByQuery(PurchaseContractQuery query) {
        if (ObjectUtil.isNotEmpty(authUserHolder.getCurrentUser())) {
            Integer pjId = getAuthUser().getCurrentProjectId();
            if (pjId != null) {
                query.setProjectId(pjId);
            }
            query.setCompanyId(authUserHolder.getCurrentUser().getCurrentCompanyId());
        }

        if (CollUtil.isNotEmpty(query.getBelongProjectId())) {
            String pjIds = query.getBelongProjectId().stream().map(String::valueOf).collect(Collectors.joining("|"));
            query.setPjIds(pjIds);
        }
        IPage<PurchaseContractDTO> page = this.getBaseMapper().selectPageDTO(query);
        List<PurchaseContractDTO> records = page.getRecords();
        List<PurchaseContractVO> voList = new ArrayList<>();

        if (CollUtil.isNotEmpty(records)) {
            if (ObjectUtil.isNotEmpty(authUserHolder.getCurrentUser())) {
                supplierWrapper.wrap(records, getAuthUser().getCurrentCompanyId());
            } else {
                supplierWrapper.wrap(records, query.getCompanyId());
            }
            //项目去重
            Map<Integer, String> projectMap = this.getProjectMap(records);
            voList = records.stream().map(dto -> {
                PurchaseContractVO vo = new PurchaseContractVO();
                BeanUtils.copyProperties(dto, vo);
                //所属项目
                String belongProjectId = dto.getBelongProjectId();
                if (StrUtil.isNotBlank(belongProjectId)) {
                    List<String> projectIds = StrUtil.split(belongProjectId, ",", true, true);
                    List<String> belongProject = new ArrayList<>();
                    for (String projectId : projectIds) {
                        if (projectMap.containsKey(Integer.parseInt(projectId))) {
                            belongProject.add(projectMap.get(Integer.parseInt(projectId)));
                        }
                    }
                    vo.setBelongProject(StrUtil.join("、", belongProject));
                }
                return vo;
            }).collect(Collectors.toList());
        }

        IPage<PurchaseContractVO> result = new Page<>();
        BeanUtils.copyProperties(page, result);
        result.setRecords(voList);
        return result;
    }


    @NotNull
    private Map<Integer, String> getProjectMap(List<PurchaseContractDTO> records) {
        List<Integer> allProjectIds = records.stream().
                filter(s -> StrUtil.isNotBlank(s.getBelongProjectId())).
                flatMap(s -> Arrays.stream(s.getBelongProjectId().split(",")))
                .filter(StrUtil::isNotBlank).map(Integer::parseInt).sorted().
                collect(Collectors.toList());
        List<ProjectVO> projects = projectServiceProxy.getSimpleProjects(allProjectIds);
        return projects.stream().collect(Collectors.toMap(ProjectVO::getProjectId, ProjectVO::getProjectTitle));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String savePurchaseContract(PurchaseContractForm form) {
        String contractId = form.getId();
//        if (StrUtil.isNotBlank(contractId)) {
//            int count = purchaseOrderService.count(new LambdaQueryWrapper<PurchaseOrder>().eq(PurchaseOrder::getContractId, contractId));
//            if (count > 0) {
//                throw new BOException(BOExceptionEnum.PURCHASE_CONTRACT_CAN_NOT_EDIT);
//            }
//        }

        List<PurchaseContractDetailForm> list = form.getList();
        //校验材料选取是否重复
        List<Integer> materialIdList = checkMaterialList(list);

//        if (StrUtil.isNotBlank(contractId)) {
//            //删除采购合同物料明细列表
//            List<String> removeIds = contractDetailService.listRemoveIdsById(contractId, null);
//            if (CollUtil.isNotEmpty(removeIds)) {
//                contractDetailService.removeByIds(removeIds);
//            }
//        }

        //保存采购合同
        PurchaseContract contract = new PurchaseContract();
        BeanUtils.copyProperties(form, contract);
        if (ObjectUtil.isNotEmpty(authUserHolder.getCurrentUser())) {
            contract.setCreateId(authUserHolder.getCurrentUser().getId());
            contract.setUpdateId(authUserHolder.getCurrentUser().getId());
        }
        String category = list.parallelStream()
                .map(obj -> StrUtil.format("{}/{}", obj.getCategoryName(), obj.getMaterialName()))
                .distinct().collect(Collectors.joining("、"));
        contract.setCategory(category);
        this.saveOrUpdate(contract);

        QueryWrapper<PurchaseContractDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PurchaseContractDetail::getContractId, contract.getId());
        List<PurchaseContractDetail> details = contractDetailMapper.selectList(queryWrapper);
        List<String> formIds = list.stream().filter(e -> e.getContractDetailId() != null).map(PurchaseContractDetailForm::getContractDetailId).collect(Collectors.toList());
        List<String> DBIds = details.stream().map(PurchaseContractDetail::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(DBIds)) {
            DBIds.removeAll(formIds);
            if (CollUtil.isNotEmpty(DBIds)) {
                contractDetailMapper.deleteBatchIds(DBIds);
            }
        }

//        保存采购合同物料明细
        List<PurchaseContractDetail> contractDetailList =
                list.stream().map(obj -> {
                    PurchaseContractDetail detail = new PurchaseContractDetail();
                    BeanUtils.copyProperties(obj, detail);
                    detail.setId(obj.getContractDetailId());
                    if (ObjectUtil.isNotEmpty(authUserHolder.getCurrentUser())) {
                        detail.setCreateId(authUserHolder.getCurrentUser().getId());
                        detail.setUpdateId(authUserHolder.getCurrentUser().getId());
                    } else {
                        detail.setCreateId(form.getCreateId());
                        detail.setUpdateId(form.getUpdateId());
                    }
                    if (form.getCompanyId() != null && form.getCompanyId() != 0) {
                        detail.setCompanyId(form.getCompanyId());
                    }
                    if (form.getProjectId() != null && form.getProjectId() != 0) {
                        detail.setProjectId(form.getProjectId());
                    }
                    if (StrUtil.isBlank(obj.getBrand())) {
                        detail.setBrand("无");
                    }
                    detail.setContractId(contract.getId());
                    return detail;
                }).collect(Collectors.toList());
        contractDetailService.saveOrUpdateBatch(contractDetailList);

        // 自动创建"已归档"的对账批次
        if (StrUtil.isBlank(form.getId())) {
            List<String> projectIdList = StrUtil.split(form.getBelongProjectId(), ",");

            List<MaterialVerify> collect = projectIdList.stream().map(e -> {
                MaterialVerify entity = new MaterialVerify();

                String verifyNo = noUtil.getAutoVerifyNo(Integer.valueOf(e), contract.getName());
                entity.setVerifyNo(verifyNo);
                entity.setVerifyProjectId(Integer.valueOf(e));
                entity.setContractId(contract.getId());
                entity.setSupplierId(String.valueOf(form.getSupplierId()));
                entity.setStatus((byte) 0);
                entity.setFileTime(LocalDateTime.now());
                entity.setIsOrigin((byte) 1);
                entity.setProjectId(Integer.valueOf(e));

                return entity;
            }).collect(Collectors.toList());

            materialVerifyService.saveBatch(collect);
        }

        return contract.getId();
    }

    private List<Integer> checkMaterialList(List<PurchaseContractDetailForm> list) {
        List<Integer> materialIdList = list.stream().map(PurchaseContractDetailForm::getMaterialId).distinct().collect(Collectors.toList());
        if (list.size() > materialIdList.size()) {
            throw new BOException(BOExceptionEnum.MATERIAL_ID_REPEAT);
        }
        return materialIdList;
    }

    @Override
    public PurchaseContractVO queryDetailById(String id, Integer companyId) {
        PurchaseContract contract = this.getBaseMapper().getContract(id);
        Optional.ofNullable(contract).orElseThrow(() -> new BOException(BOExceptionEnum.PURCHASE_CONTRACT_NOT_EXISTS));

        PurchaseContractVO resultVO = new PurchaseContractVO();
        BeanUtils.copyProperties(contract, resultVO);

        if (ObjectUtil.isNotEmpty(authUserHolder.getCurrentUser())) {
            supplierWrapper.wrap(resultVO, getAuthUser().getCurrentCompanyId());
        } else {
            supplierWrapper.wrap(resultVO, companyId);
        }

        List<PurchaseContractDetailVO> list = contractDetailMapper.selectContractDetails(id);
        if (CollUtil.isNotEmpty(list)) {

            Map<Integer, List<UnitDto>> map = new HashMap<>();
            Set<Integer> categoryIdList = list.stream().map(e -> e.getCategoryId()).collect(Collectors.toSet());
            categoryIdList.stream().forEach(e -> {
                List<UnitDto> unitDtos = unitService.getMaterialUnitList(companyId, e);
                if (CollUtil.isNotEmpty(unitDtos)) {
                    map.put(e, unitDtos);
                }
            });

            list.stream().forEach(e -> {
                e.setBrand(Arrays.asList(e.getBrandStr()));
                if (CollUtil.isNotEmpty(map)) {
                    List<UnitDTO> collect = map.get(e.getCategoryId()).stream().map(m -> {
                        UnitDTO dto = new UnitDTO();
                        dto.setId(m.getUnitId());
                        dto.setConversionRate(m.getConversionRate());
                        dto.setUnitName(m.getUnitName());

                        return dto;
                    }).collect(Collectors.toList());
                    e.setUnitList(collect);
                }
            });
            resultVO.setList(list);
        }

        return resultVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePurchaseContractById(String id) {
        int count = purchaseOrderService.count(new LambdaQueryWrapper<PurchaseOrder>().eq(PurchaseOrder::getContractId, id));
        if (count > 0) {
            throw new BOException(BOExceptionEnum.PURCHASE_CONTRACT_CAN_NOT_REMOVE);
        }
        this.removeById(id);

    }

    @Override
    public List<ProjectVO> listBelongProjects(String id) {
        List<String> list = this.getBaseMapper().selectBelongProjects(id);
        List<ProjectVO> belongProjects = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            List<Integer> projectIds = list.stream().flatMap(str -> Arrays.stream(str.split(","))).map(Integer::parseInt).sorted().collect(Collectors.toList());
            belongProjects = projectServiceProxy.getSimpleProjects(projectIds);
        }
        return belongProjects;
    }

    @Override
    public List<PurchaseContractVO> listContract(String contractName) {
        List<PurchaseContract> list = this.getBaseMapper().selectContract(getAuthUser().getCurrentProjectId());
        List<PurchaseContractVO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            result = list.stream().map(obj -> {
                PurchaseContractVO vo = new PurchaseContractVO();
                BeanUtils.copyProperties(obj, vo);
                return vo;
            }).collect(Collectors.toList());
        }
        supplierWrapper.wrap(result, getAuthUser().getCurrentCompanyId());
        if (StrUtil.isNotBlank(contractName)) {
            return result.parallelStream().filter(purchaseContractVO -> purchaseContractVO.getName().contains(contractName)).collect(Collectors.toList());
        } else {
            return result;
        }

    }

    @Override
    public List<PurchaseOrderStatusVO> listOrderStatus() {
        Integer projectId = getAuthUser().getCurrentProjectId();

        AuthUser user = authUserHolder.getCurrentUser();
        List<Integer> projectIds = new ArrayList<>();
        Map<Byte, Integer> statusVOMap = new HashMap<>();
        int count = 0;
        if (user.getCurrentDepartmentId() != null) {
            projectIds = projectServiceProxy.getAllProjectIdByCompanyDeptId(user.getCurrentCompanyId(), user.getCurrentDepartmentId());
        }
        if (projectId != null) {
            projectIds.add(projectId);
        }
        if (CollUtil.isNotEmpty(projectIds)) {
            List<PurchaseOrderStatusVO> list = this.getBaseMapper().selectOrderStatus(projectIds);
            if (CollUtil.isNotEmpty(list)) {
                statusVOMap = list.stream().collect(Collectors.toMap(PurchaseOrderStatusVO::getStatus, PurchaseOrderStatusVO::getNum));
                count = list.stream().map(PurchaseOrderStatusVO::getNum).reduce(Integer::sum).orElse(0);
            }
        }

        List<PurchaseOrderStatusVO> result = new ArrayList<>();
        PurchaseOrderStatusVO total = new PurchaseOrderStatusVO();
        total.setName("全部");
        total.setNum(count);
        result.add(total);
        for (PurchaseOrderStatusEnum value : PurchaseOrderStatusEnum.values()) {
            PurchaseOrderStatusVO vo = new PurchaseOrderStatusVO();
            vo.setName(value.description());
            vo.setStatus(value.value());
            vo.setNum(statusVOMap.getOrDefault(value.value(), 0));
            result.add(vo);
        }
        return result;
    }

    @Override
    public List<CooperateVO> getCooperateList() {
        Integer projectId = getAuthUser().getCurrentProjectId();
        List<SimpleSupplierDTO> list = this.getBaseMapper().selectSupplierIdsById(projectId, getAuthUser().getCurrentCompanyId());
        supplierWrapper.wrap(list, getAuthUser().getCurrentCompanyId());
        List<CooperateVO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            result = list.stream().filter(obj -> StrUtil.isNotBlank(obj.getSupplierTitle())).map(obj -> {
                CooperateVO vo = new CooperateVO();
                vo.setId(obj.getSupplierId());
                vo.setName(obj.getSupplierTitle());
                return vo;
            }).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public List<CooperateVO> listSupplier(String supplierName) {
        Integer projectId = getAuthUser().getCurrentProjectId();
        List<SimpleSupplierDTO> list = this.getBaseMapper().selectSupplierIdsById(projectId, getAuthUser().getCurrentCompanyId());
        supplierWrapper.wrap(list, getAuthUser().getCurrentCompanyId());
        List<CooperateVO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            result = list.stream().map(obj -> {
                CooperateVO vo = new CooperateVO();
                vo.setId(obj.getSupplierId());
                vo.setName(obj.getSupplierTitle());
                return vo;
            }).collect(Collectors.toList());
        }
        if (StrUtil.isNotBlank(supplierName)) {
            List<CooperateVO> lastResult = result.stream().filter(e -> e.getName().contains(supplierName)).collect(Collectors.toList());
            return lastResult;
        }
        return result;
    }

    @Override
    public List<PurchaseContractVO> listBySupplierId(ContractQuery query) {
        List<PurchaseContractVO> vos = new ArrayList<>();
        AuthUser user = authUserHolder.getCurrentUser();
        List<PurchaseContractDTO> list = this.getBaseMapper().listBySupplierId(query, user.getCurrentProjectId(), user.getCurrentCompanyId());

        if (CollUtil.isNotEmpty(list)) {
            createNameWrapper.wrap(list, user.getCurrentCompanyId());
            supplierWrapper.wrap(list, getAuthUser().getCurrentCompanyId());
            Map<Integer, String> projectMap = this.getProjectMap(list);

            list.forEach(e -> {
                PurchaseContractVO vo = new PurchaseContractVO();
                BeanUtil.copyProperties(e, vo);

                String belongProjectId = e.getBelongProjectId();
                if (StrUtil.isNotBlank(belongProjectId)) {
                    List<String> projectIds = StrUtil.split(belongProjectId, ",", true, true);
                    List<String> belongProject = new ArrayList<>();
                    for (String projectId : projectIds) {
                        belongProject.add(projectMap.get(Integer.parseInt(projectId)));
                    }
                    vo.setBelongProject(StrUtil.join("、", belongProject));
                }

                List<ContractMaterialDetailDTO> details = contractDetailMapper.listMaterial(e.getId());
                if (CollUtil.isNotEmpty(details)) {
                    Map<Integer, BigDecimal> map = new HashMap<>();

                    List<Integer> materialIdList = details.stream().map(ContractMaterialDetailDTO::getMaterialId).distinct().collect(Collectors.toList());
                    List<TheoreticalInfoVO> theoreticalInfoVOS = companyConfigServiceImpl.theoreticalInfoVOList(materialIdList);
                    if (CollUtil.isNotEmpty(theoreticalInfoVOS)) {
                        map = theoreticalInfoVOS.stream().collect(Collectors.toMap(TheoreticalInfoVO::getMaterialId, TheoreticalInfoVO::getTheoreticalUnitWeight));
                    }
                    List<String> category = details.stream().map(ContractMaterialDetailDTO::getCategoryName).distinct().collect(Collectors.toList());
                    Optional.ofNullable(category).ifPresent(col -> vo.setCategory(StrUtil.join(",", col)));

                    Map<Integer, BigDecimal> finalMap = map;
                    List<PurchaseContractDetailVO> detailVOS = details.stream().map(m -> {
                        PurchaseContractDetailVO detailVO = new PurchaseContractDetailVO();
                        BeanUtil.copyProperties(m, detailVO);
                        detailVO.setConversionRate(detailVO.getRatio());
                        if (CollUtil.isNotEmpty(finalMap) && ObjectUtil.isNotNull(finalMap.get(m.getMaterialId()))) {
                            TheoreticalInfoVO theoreticalInfoVO = new TheoreticalInfoVO();
                            theoreticalInfoVO.setMaterialId(m.getMaterialId());
                            theoreticalInfoVO.setTheoreticalUnitWeight(finalMap.get(m.getMaterialId()));
                            detailVO.setTheoreticalInfoVO(theoreticalInfoVO);
                        }
                        if (StrUtil.isNotBlank(m.getBrand())) {
                            List<String> brandList = StrUtil.split(m.getBrand(), ",", true, true);
                            detailVO.setBrand(brandList);
                        }
                        String str = StrUtil.format("{}/{}/{}", m.getCategoryName(), m.getMaterialName(), m.getMaterialSpec());
                        detailVO.setName(str);
                        detailVO.setType(str);
                        return detailVO;
                    }).collect(Collectors.toList());
                    vo.setList(detailVOS);
                    Map<String, List<PurchaseContractDetailVO>> resultMap = detailVOS.stream().collect(Collectors.groupingBy(PurchaseContractDetailVO::getMaterialName));
                    List<ContractDetailVO> collect = resultMap.entrySet().stream().map(m -> {
                        ContractDetailVO detailVO = new ContractDetailVO();
                        detailVO.setName(m.getKey());
                        detailVO.setList(m.getValue());
                        return detailVO;
                    }).collect(Collectors.toList());
                    vo.setListApp(collect);
                }

                vos.add(vo);
            });
        }

        return vos;
    }

    @Override
    public List<ContractForReviseVO> getContractForRevise(Integer supplierId) {
        // TODO: 2022/6/14  可能会有大量的查询-优化 
        AuthUser user = authUserHolder.getCurrentUser();
//      根据供应商id拿所有服务本项目的合同
        List<ContractForReviseVO> contractList = this.getBaseMapper().listForRevise(supplierId, user.getCurrentCompanyId(), user.getCurrentProjectId());
        if (CollUtil.isNotEmpty(contractList)) {
            contractList.stream().forEach(e -> {
//              每个合同的采购单
                List<PurchaseForReviseVO> purchaseList = purchaseOrderService.listForRevise(e.getContractId());
                if (CollUtil.isNotEmpty(purchaseList)) {
                    e.setPurchaseList(purchaseList);
                    purchaseList.stream().forEach(m -> {
//                      每个采购单的物料明细
                        List<GoodsForReviseVO> goodList = purchaseOrderDetailService.listForRevise(m.getPurchaseOrderId());
                        if (CollUtil.isNotEmpty(goodList)) {
                            List<String> categoryNameList = goodList.stream().map(GoodsForReviseVO::getCategoryName).distinct().collect(Collectors.toList());
                            String str = categoryNameList.stream().collect(joining(","));
                            m.setCategoryName(str);
                            goodList.stream().forEach(n -> n.setType(StrUtil.format("{}/{}/{}", n.getCategoryName(), n.getMaterialName(), n.getMaterialSpec())));
                            m.setList(goodList);
                        }
                    });
                }
            });
        }
        return contractList;
    }

    @Override
    public Set<Integer> listUsedSupplierId() {
        return this.baseMapper.listUsedSupplierId();
    }

    @Override
    public List<AppContractChooseVO> appchoose(String contractId, String categoryId) {
        List<AppContractChooseVO> list = purchaseContractMapper.appchoose(contractId, categoryId);

        supplierWrapper.wrap(list, authUserHolder.getCurrentUser().getCurrentCompanyId());

        Map<Integer, MaterialDto> map = materialToMapUtil.materialDtoMap(list.stream().map(e -> e.getMaterialId()).distinct().collect(Collectors.toList()));
        if (CollUtil.isNotEmpty(map)) {
            list.stream().forEach(e -> {
                MaterialDto materialDto = map.get(e.getMaterialId());
                if (ObjectUtil.isNotNull(materialDto)) {
                    e.setCategoryId(materialDto.getMaterialCategoryId());
                    e.setCategoryName(materialDto.getMaterialCategoryName());
                    e.setMaterialName(materialDto.getMaterialName());
                    e.setMaterialSpec(materialDto.getMaterialSpec());
                    e.setName(StrUtil.format("{}/{}/{}", e.getCategoryName(), e.getMaterialName(), e.getMaterialSpec()));
                }
            });
        }
        return list;
    }

    @Override
    public List<ProjectVO> projectList() {
        Integer companyId = getAuthUser().getCurrentCompanyId();
        Integer projectId = getAuthUser().getCurrentProjectId();
        List<ProjectVO> list = new ArrayList<>();
        if (Objects.nonNull(getAuthUser().getCurrentProjectId())) {
            ProjectVO project = projectServiceProxy.getProjectById(projectId);
            list.add(project);
        } else {
            List<PurchaseContract> belongProjectIdList = this.lambdaQuery().select(PurchaseContract::getBelongProjectId).eq(PurchaseContract::getCompanyId, companyId).list();
            if (CollUtil.isNotEmpty(belongProjectIdList)) {
                List<Integer> allProjectIds = belongProjectIdList.stream().
                        filter(s -> StrUtil.isNotBlank(s.getBelongProjectId())).
                        flatMap(s -> Arrays.stream(s.getBelongProjectId().split(",")))
                        .filter(StrUtil::isNotBlank).map(Integer::parseInt).sorted().
                        collect(Collectors.toList());
                return projectServiceProxy.getSimpleProjects(allProjectIds);
            }
        }
        return list;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePushConfig(PushConfigForm form) {
        Integer companyId = getAuthUser().getCurrentCompanyId();
        Integer projectId = getAuthUser().getCurrentProjectId();
        if (projectId == null) {
            throw new BOException(BOExceptionEnum.LOGIC_ERROR.errorCode(), "请切换到项目层");
        }
        PurchaseContract contract = lambdaQuery().eq(PurchaseContract::getId, form.getContractId()).oneOpt().orElseThrow(() -> new BOException(BOExceptionEnum.PURCHASE_CONTRACT_NOT_EXISTS));
        if (form.getIsPush() == 2 && CollUtil.isEmpty(form.getContractDetailIdList())) {
            throw new BOException(BOExceptionEnum.LOGIC_ERROR.errorCode(), "请选择合同明细");
        }

        PurchaseContract pushConfig = new PurchaseContract();
        pushConfig.setId(form.getContractId());
        pushConfig.setIsPush(form.getIsPush());
        pushConfig.setPushDetailId(StrUtil.join(StrUtil.COMMA, form.getContractDetailIdList()));
        updateById(pushConfig);
        // 推送到基石订单发货平台
        if (form.getIsPush() == 2) {
            try {
                pushMaterialClientPlatform(contract,form.getContractDetailIdList());
            } catch (Exception e) {
                log.error("推送到基石订单发货平台失败", e);
            }
        }else {
            SdkConfigDTO sdkConfig = sdkConfService.getSdkConfig(companyId, projectId);
            Material materialClient = new MaterialClientBuilder().build(sdkConfig.getHost(), sdkConfig.getAppKey(), sdkConfig.getAppSecretKey());
            materialClient.purchaseOrderFinish(form.getContractId());
        }
    }

    @Override
    public PushConfigVO queryPushConfig(String contractId) {
        Integer companyId = getAuthUser().getCurrentCompanyId();
        Integer projectId = getAuthUser().getCurrentProjectId();
        if (projectId == null) {
            throw new BOException(BOExceptionEnum.LOGIC_ERROR.errorCode(), "请切换到项目层");
        }
        PurchaseContract contract = lambdaQuery().eq(PurchaseContract::getId, contractId).oneOpt().orElseThrow(() -> new BOException(BOExceptionEnum.PURCHASE_CONTRACT_NOT_EXISTS));

        String pushDetailId = contract.getPushDetailId();
        List<String> contractDetailIds = new ArrayList<>();
        if (StrUtil.isNotBlank(pushDetailId)) {
            contractDetailIds = StrUtil.split(pushDetailId, StrUtil.COMMA);
        }
        List<PurchaseContractDetail> contractDetailList = contractDetailService.lambdaQuery().eq(PurchaseContractDetail::getContractId, contractId).list();
        List<ContractDetailSimpleVO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(contractDetailList)) {
            List<String> finalContractDetailIds = contractDetailIds;
            list = contractDetailList.stream().map(obj -> {
                ContractDetailSimpleVO vo = new ContractDetailSimpleVO();
                BeanUtils.copyProperties(obj, vo);
                if (finalContractDetailIds.contains(obj.getId())) {
                    vo.setIsChecked(true);
                } else {
                    vo.setIsChecked(false);
                }
                return vo;
            }).collect(Collectors.toList());
        }
        PushConfigVO result = new PushConfigVO();
        result.setContractId(contractId);
        result.setIsPush(contract.getIsPush());
        result.setList(list);
        return result;
    }

    public void pushMaterialClientPlatform(PurchaseContract order, List<String> contractDetailIdList) {
        Integer companyId = getAuthUser().getCurrentCompanyId();
        Integer projectId = getAuthUser().getCurrentProjectId();

        // 推送到基石订单发货平台
        SdkConfigDTO sdkConfig = sdkConfService.getSdkConfig(companyId, projectId);
        Material materialClient = new MaterialClientBuilder().build(sdkConfig.getHost(), sdkConfig.getAppKey(), sdkConfig.getAppSecretKey());

        PurchaseForm purchaseForm = new PurchaseForm();
        purchaseForm.setSource("品茗合同");
        purchaseForm.setAttributionCode(String.valueOf(projectId));
        purchaseForm.setOrderExtId(order.getId());
        purchaseForm.setSupplierExtId(String.valueOf(order.getSupplierId()));
        ProjectVO project = projectServiceProxy.getProjectById(projectId);
        if (project != null) {
            purchaseForm.setProject(project.getProjectTitle());
            purchaseForm.setAddress(project.getAddress());
        }
        purchaseForm.setReceiveDate(order.getCreateTime().toLocalDate());
        List<SimpleContractDetailDTO> contractDetailList = contractDetailService.querySimpleContractDetails(contractDetailIdList);

        List<PurchaseItemForm> list = contractDetailList.stream().map(obj -> {
            PurchaseItemForm item = new PurchaseItemForm();
            item.setExtId(obj.getContractDetailId());
            item.setAmount(BigDecimal.ONE);
            item.setDeductRatio(BigDecimal.ZERO);
            item.setScaleFactor(BigDecimal.ZERO);
            item.setName(obj.getMaterialName());
            item.setSpec(obj.getMaterialSpec());
            item.setUnit(obj.getUnit());
            item.setScaleFactor(obj.getConversionRate());
            item.setUnitType(1);// 桩桩系统都是吨
            return item;
        }).collect(Collectors.toList());
        purchaseForm.setList(list);
        materialClient.purchaseOrderAdd(purchaseForm);
    }

    @Override
    public SimpleContractDetailDTO querySimpleContractDetail(String contractDetailId) {
        PurchaseContractDetail contractDetail = contractDetailService.getById(contractDetailId);
        SimpleContractDetailDTO result = new SimpleContractDetailDTO();
        if (contractDetail != null) {
            BeanUtils.copyProperties(contractDetail, result);
        }
        return result;
    }
}
