package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.upload.OssFile;
import cn.pinming.core.upload.UploadComponent;
import cn.pinming.core.upload.UploadConfig;
import cn.pinming.core.upload.domain.enums.FileTypeEnums;
import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.MaterialFinishedBom;
import cn.pinming.microservice.material.management.biz.entity.MixingPlantExitTicket;
import cn.pinming.microservice.material.management.biz.entity.MixingPlantOrder;
import cn.pinming.microservice.material.management.biz.entity.MixingPlantOrderDetail;
import cn.pinming.microservice.material.management.biz.enums.ParameterRequirementsTypeEnum;
import cn.pinming.microservice.material.management.biz.enums.PlantOrderDetailStatusEnum;
import cn.pinming.microservice.material.management.biz.form.IngredientAutoAddForm;
import cn.pinming.microservice.material.management.biz.form.PlantOrderDetailForm;
import cn.pinming.microservice.material.management.biz.form.PlantOrderForm;
import cn.pinming.microservice.material.management.biz.mapper.MixingPlantExitTicketMapper;
import cn.pinming.microservice.material.management.biz.mapper.MixingPlantOrderDetailMapper;
import cn.pinming.microservice.material.management.biz.mapper.MixingPlantOrderMapper;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.util.IfBranchUtil;
import cn.pinming.microservice.material.management.common.util.NoUtil;
import cn.pinming.microservice.material.management.common.util.UUIDUtil;
import cn.pinming.microservice.material.management.common.util.WordUtils;
import cn.pinming.microservice.material.management.proxy.*;
import cn.pinming.microservice.material.management.wrapper.CreateNameWrapper;
import cn.pinming.microservice.material.management.wrapper.ProjectNameWrapper;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.model.dto.FilePreviewDto;
import cn.pinming.v2.company.api.dto.EmployeeDetailDto;
import cn.pinming.v2.company.api.dto.EmployeeDetailQueryDto;
import cn.pinming.v2.company.api.dto.EmployeeDto;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 拌合站订单 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-28 15:26:17
 */
@Service
public class MixingPlantOrderServiceImpl extends ServiceImpl<MixingPlantOrderMapper, MixingPlantOrder> implements IMixingPlantOrderService {

    @Value("${temporary.path}")
    private String temporaryPath;

    @Resource
    private FileServiceProxy fileServiceProxy;

    @Resource
    private MixingPlantOrderDetailMapper mixingPlantOrderDetailMapper;

    @Resource
    private IMixingPlantOrderDetailService mixingPlantOrderDetailService;

    @Resource
    private MixingPlantOrderMapper mixingPlantOrderMapper;

    @Resource
    private MaterialServiceProxy materialServiceProxy;

    @Resource
    private AuthUserHolder authUserHolder;

    @Resource
    private IPurchaseOrderService purchaseOrderService;

    @Resource
    private NoUtil noUtil;

    @Resource
    private EmployeeServiceProxy employeeServiceProxy;

    @Resource
    private ProjectServiceProxy projectServiceProxy;

    @Resource
    private DepartmentTreeServiceProxy departmentTreeServiceProxy;

    @Resource
    private ProjectNameWrapper projectNameWrapper;

    @Resource
    private CreateNameWrapper createNameWrapper;

    @Resource
    private IMixingPlantExitTicketService mixingPlantExitTicketService;

    @Resource
    private MixingPlantExitTicketMapper mixingPlantExitTicketMapper;

    @Resource
    private AuthUserHolder siteContextHolder;

    @Resource
    private IngredientListServiceImpl ingredientListService;

    @Resource
    private IMaterialFinishedBomService materialFinishedBomService;

    @Override
    public IPage<MixingPlantOrderVO> plantOrderPage(Byte status, int pageNum, int pageSize) {
        Integer currentProjectId = authUserHolder.getCurrentUser().getCurrentProjectId();
        IPage<MixingPlantOrderVO> page = new Page<>(pageNum, pageSize);
        Long count = mixingPlantOrderMapper.plantOrderCount(status, currentProjectId);
        if (ObjectUtil.isNotNull(count) && count > 0) {
            List<MixingPlantOrderVO> list = mixingPlantOrderMapper.plantOrderPage(status, currentProjectId, (pageNum - 1) * pageSize, pageSize);
            if (CollectionUtil.isNotEmpty(list)) {
                Set<Integer> materialIds = list.stream().map(MixingPlantOrderVO::getMaterialId).collect(Collectors.toSet());
                Map<Integer, String> materialSpecMap = materialServiceProxy.materialSpec(materialIds);
                list.forEach(item -> {
                    item.setMaterialSpec(materialSpecMap.get(item.getMaterialId()));
                    // 参数要求解析
                    String parameterRequirements = item.getParameterRequirements();
                    String requirements = purchaseOrderService.parseParameterRequirements(item.getCategoryId(), parameterRequirements);
                    item.setParameterRequirementsDesc(requirements);
                });
            }
            page.setTotal(count);
            page.setPages(count / pageSize + 1);
            page.setRecords(list);
        }
        return page;
    }

    @Override
    public void updateStatus(String id, Byte status) {
        MixingPlantOrderDetail entity = new MixingPlantOrderDetail();
        entity.setId(id);
        entity.setStatus(status);
        IfBranchUtil.isTrue(status == PlantOrderDetailStatusEnum.FIVE.value()).trueHandle(() -> entity.setFinishTime(LocalDateTime.now()));
        mixingPlantOrderDetailMapper.updateById(entity);
    }

    @Override
    public void addPlantOrder(PlantOrderForm plantOrderForm) {
        Integer receiverProject = plantOrderForm.getReceiverProject();
        SimpleConstructionProjectDto simpleProject = projectServiceProxy.findSimpleProject(receiverProject);
        MixingPlantOrder order = new MixingPlantOrder();
        BeanUtils.copyProperties(plantOrderForm, order);
        String orderId = UUIDUtil.randomUUIDWithoutConnector();
        order.setId(orderId);
        IfBranchUtil.isTrue(ObjectUtil.isNotNull(simpleProject)).trueHandle(() -> order.setReceiverProjectTitle(simpleProject.getProjectTitle()));
        String orderNo = plantOrderForm.getOrderNo();
        if (StringUtils.isBlank(orderNo)) {
            order.setOrderNo(noUtil.getBizNo(NoUtil.PLANT_KEY_PREFIX, plantOrderForm.getReceiverProject()));
        }
        AuthUser currentUser = siteContextHolder.getCurrentUser();
        order.setPlantId(currentUser.getCurrentProjectId());

        this.save(order);
        List<MixingPlantOrderDetail> details = plantOrderForm.getDetailFormList().stream().map(item -> {
            MixingPlantOrderDetail detail = new MixingPlantOrderDetail();
            BeanUtils.copyProperties(item, detail);
            detail.setId(UUIDUtil.randomUUIDWithoutConnector());
            detail.setMixingPlantOrderId(orderId);
            List<ParameterRequirementsDTO> parameterRequirementsModel = item.getParameterRequirementsModel();
            IfBranchUtil.isTrue(CollectionUtil.isNotEmpty(parameterRequirementsModel)).trueHandle(() -> detail.setParameterRequirements(JSONObject.toJSONString(parameterRequirementsModel)));
            return detail;
        }).collect(Collectors.toList());
        mixingPlantOrderDetailService.saveBatch(details);

        // 自动生成配料单
        List<IngredientAutoAddForm> autoAddFormList = details.stream().map(e -> {
            IngredientAutoAddForm form = new IngredientAutoAddForm();
            BeanUtil.copyProperties(e, form);
            form.setMixingPlantOrderDetailId(e.getId());
            return form;
        }).collect(Collectors.toList());
        ingredientListService.autoAdd(autoAddFormList, null);
    }

    @Override
    public void updatePlantOrder(PlantOrderForm plantOrderForm) {
        // 订单明细ID
        String id = plantOrderForm.getId();
        // 订单ID
        String orderId = this.queryOrderIdByOrderDetailId(id);

        Integer receiverProject = plantOrderForm.getReceiverProject();
        SimpleConstructionProjectDto simpleProject = projectServiceProxy.findSimpleProject(receiverProject);
        // 修改订单信息
        MixingPlantOrder entity = new MixingPlantOrder();
        BeanUtils.copyProperties(plantOrderForm, entity);
        IfBranchUtil.isTrue(ObjectUtil.isNotNull(simpleProject)).trueHandle(() -> entity.setReceiverProjectTitle(simpleProject.getProjectTitle()));
        entity.setId(orderId);
        this.updateById(entity);
        // 修改订单明细
        List<PlantOrderDetailForm> detailFormList = plantOrderForm.getDetailFormList();
        if (CollectionUtil.isNotEmpty(detailFormList)) {
            List<MixingPlantOrderDetail> details = detailFormList.stream().map(item -> {
                MixingPlantOrderDetail detail = new MixingPlantOrderDetail();
                BeanUtils.copyProperties(item, detail);
                List<ParameterRequirementsDTO> parameterRequirementsModel = item.getParameterRequirementsModel();
                IfBranchUtil.isTrue(CollectionUtil.isNotEmpty(parameterRequirementsModel)).trueHandle(() -> detail.setParameterRequirements(JSONObject.toJSONString(parameterRequirementsModel)));
                detail.setMixingPlantOrderId(orderId);
                return detail;
            }).collect(Collectors.toList());
            // 为保证订单ID和订单明细ID不变：添加新增的，修改存在的，减去删除的。若业务上关联订单信息的数据不存在【关联后还能编辑】，则不需要考虑这3个case
            QueryWrapper<MixingPlantOrderDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(MixingPlantOrderDetail::getIsDeleted, 0).eq(MixingPlantOrderDetail::getMixingPlantOrderId, orderId);
            // 已存在的
            List<MixingPlantOrderDetail> list = mixingPlantOrderDetailService.list(queryWrapper);
            List<String> updateDataIds = details.stream().filter(item -> ObjectUtil.isNotNull(item.getId())).map(MixingPlantOrderDetail::getId).collect(Collectors.toList());
            List<String> dataIds = list.stream().map(MixingPlantOrderDetail::getId).collect(Collectors.toList());
            // 1. 添加新增的
            List<MixingPlantOrderDetail> addList = details.stream().filter(item -> ObjectUtil.isNull(item.getId())).collect(Collectors.toList());
            IfBranchUtil.isTrue(CollectionUtil.isNotEmpty(addList)).trueHandle(() -> {
                addList.forEach(item -> {
                    item.setId(UUIDUtil.randomUUIDWithoutConnector());
                    item.setMixingPlantOrderId(orderId);
                });
                mixingPlantOrderDetailService.saveBatch(addList);
            });
            // 2. 修改存在的
            List<String> updateIds = dataIds.stream().filter(dataId -> updateDataIds.contains(dataId)).collect(Collectors.toList());
            IfBranchUtil.isTrue(CollectionUtil.isNotEmpty(updateIds)).trueHandle(() -> {
                List<MixingPlantOrderDetail> updateList = details.stream().filter(detail -> updateIds.contains(detail.getId())).collect(Collectors.toList());
                mixingPlantOrderDetailService.updateBatchById(updateList);
            });
            // 3. 减去删除的
            List<String> deleteIds = dataIds.stream().filter(dataId -> !updateDataIds.contains(dataId)).collect(Collectors.toList());
            IfBranchUtil.isTrue(CollectionUtil.isNotEmpty(deleteIds)).trueHandle(() -> mixingPlantOrderDetailService.removeByIds(deleteIds));
        }
    }

    @Override
    public MixingPlantOrderUpdateVO queryUpdateVO(String orderId) {
        MixingPlantOrderUpdateVO mixingPlantOrderUpdateVO = mixingPlantOrderMapper.queryOrderUpdateVO(orderId);
        List<MixingPlantOrderDetailUpdateVO> mixingPlantOrderDetailUpdateVOS = mixingPlantOrderMapper.queryOrderDetailUpdateVO(orderId);
        if (CollectionUtil.isNotEmpty(mixingPlantOrderDetailUpdateVOS)) {
            Set<Integer> materialIds = mixingPlantOrderDetailUpdateVOS.stream()
                    .filter(item -> ObjectUtil.isNotNull(item.getMaterialId()))
                    .map(MixingPlantOrderDetailUpdateVO::getMaterialId)
                    .collect(Collectors.toSet());
            List<Integer> categoryIds = mixingPlantOrderDetailUpdateVOS.stream()
                    .filter(item -> ObjectUtil.isNotNull(item.getCategoryId()))
                    .map(MixingPlantOrderDetailUpdateVO::getCategoryId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Integer, String> materialSpecMap = materialServiceProxy.materialSpec(materialIds);
            Map<Integer, List<String>> unitMap = materialServiceProxy.unitMapByCategoryIds(mixingPlantOrderUpdateVO.getCompanyId(), categoryIds);
            mixingPlantOrderDetailUpdateVOS.forEach(item -> {
                // 规格型号
                item.setMaterialSpec(materialSpecMap.get(item.getMaterialId()));
                // 计量单位
                item.setUnits(unitMap.get(item.getCategoryId()));
                // 参数要求解析
                String parameterRequirements = item.getParameterRequirements();
                item.setParameterRequirementsModel(JSONObject.parseArray(parameterRequirements, ParameterRequirementsDTO.class));
                String requirements = purchaseOrderService.parseParameterRequirements(item.getCategoryId(), parameterRequirements);
                item.setParameterRequirementsDesc(requirements);
            });
        }
        mixingPlantOrderUpdateVO.setDetailList(mixingPlantOrderDetailUpdateVOS);
        return mixingPlantOrderUpdateVO;
    }

    @Override
    public MixingPlantOrderDetailVO queryOrderDetailById(String orderDetailId) {
        MixingPlantOrderDetailVO detail = mixingPlantOrderMapper.queryOrderDetailByDetailId(orderDetailId);
        Integer plantId = detail.getPlantId();
        Integer receiverProjectId = detail.getReceiverProjectId();
        String createId = detail.getCreateId();
        Integer companyId = detail.getCompanyId();
        EmployeeDto employee = employeeServiceProxy.findEmployee(companyId, createId);
        // 客户下单人
        IfBranchUtil.isTrue(ObjectUtil.isNotNull(employee)).trueHandle(() -> detail.setCustomerName(employee.getMemberName()));
        // 接单站点
        SimpleConstructionProjectDto plant = projectServiceProxy.findSimpleProject(plantId);
        IfBranchUtil.isTrue(ObjectUtil.isNotNull(plant)).trueHandle(() -> detail.setPlantName(plant.getProjectTitle()));
        // 要货项目
        SimpleConstructionProjectDto project = projectServiceProxy.findSimpleProject(receiverProjectId);
        IfBranchUtil.isTrue(ObjectUtil.isNotNull(project)).trueHandle(() -> {
            // 项目名称
            detail.setReceiverProject(project.getProjectTitle());
            // 项目地址
            detail.setProjectAddress(project.getAddress());
            // 要货单位
            Integer relationDepartmentId = project.getRelationDepartmentId();
            if (ObjectUtil.isNotNull(relationDepartmentId)) {
                String departmentParent = departmentTreeServiceProxy.getDepartmentParent(relationDepartmentId);
                detail.setReceiverCorp(departmentParent);
            }
        });
        // 订单明细
        List<MixingPlantOrderDetailItemVO> items = mixingPlantOrderMapper.queryOrderDetailItemsById(orderDetailId);
        if (CollectionUtil.isNotEmpty(items)) {
            Set<Integer> materialIds = items.stream().map(MixingPlantOrderDetailItemVO::getMaterialId).collect(Collectors.toSet());
            Map<Integer, String> materialSpecMap = materialServiceProxy.materialSpec(materialIds);
            items.forEach(item -> {
                // 规格型号
                item.setMaterialSpec(materialSpecMap.get(item.getMaterialId()));
                // 参数要求解析
                String requirements = purchaseOrderService.parseParameterRequirements(item.getCategoryId(), item.getParameterRequirements());
                item.setParameterRequirementsDesc(requirements);
            });
            detail.setOrderDetailItemVOS(items);
        }
        // 发货完毕状态
        detail.setOrderOver(orderOver(items));
        return detail;
    }

    private Boolean orderOver(List<MixingPlantOrderDetailItemVO> detailItemVOS) {
        if (CollectionUtil.isEmpty(detailItemVOS)) {
            return false;
        }
        Set<Byte> statusSet = detailItemVOS.stream().filter(item -> ObjectUtil.isNotNull(item.getStatus())).map(MixingPlantOrderDetailItemVO::getStatus).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(statusSet)) {
            return false;
        }
        /**
         * 发货完毕：除”发货完毕、已作废“状态之外，均可用
         * 1、明细状态中有一个“发货中”，订单状态即为“发货中”；
         * 2、明细状态全部为“发货完毕”，或者同时只有“发货完毕”和“已作废”时，订单状态即为“发货完毕”
         * 3、明细状态全部为“已作废”，订单状态即为“已作废”；
         * 4、其他情况为“生产中”
         */
        // case 2、3
        boolean overStatus = statusSet.stream().allMatch(status ->
                status == PlantOrderDetailStatusEnum.FIVE.value() ||
                        status == PlantOrderDetailStatusEnum.SEVEN.value()
        );
        if (overStatus) {
            return false;
        }
        return true;
    }

    @Override
    public List<MixingPlantOrderDetailVO> choose(String name, String no) {
        List<MixingPlantOrderDetailVO> result = new ArrayList<>();
        List<MixingPlantOrderDetailVO> voList = new ArrayList<>();
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
        Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();

        List<MixingPlantOrderDetailItemDTO> dtoList = mixingPlantOrderDetailMapper.choose(companyId, projectId, name, no);
        if (CollUtil.isNotEmpty(dtoList)) {
            Map<Integer, MaterialDto> materialDtoMap = null;
            Map<Integer, SimpleConstructionProjectDto> projectDtoMap = null;
            Map<String, ExistTicketDetailStatisticsDTO> statisticsDTOMap = null;
            Map<String, String> employeeMap = null;
            Map<String, ExistTicketHistoryVO> historyVOMap = null;
            Map<Integer, MaterialFinishedBom> bomMap = null;

            // 客户下单人
            List<String> employeeList = dtoList.stream().map(MixingPlantOrderDetailItemDTO::getCreateId).distinct().collect(Collectors.toList());
            EmployeeDetailQueryDto queryDto = new EmployeeDetailQueryDto();
            queryDto.setMemberIdList(employeeList);
            queryDto.setCompanyId(companyId);
            List<EmployeeDetailDto> employeeDetailDtos = employeeServiceProxy.employeeList(queryDto);
            if (CollUtil.isNotEmpty(employeeDetailDtos)) {
                employeeMap = employeeDetailDtos.stream().collect(Collectors.toMap(EmployeeDetailDto::getMemberId, EmployeeDetailDto::getMemberName));
            }
            // 材料
            List<Integer> materialList = dtoList.stream().map(MixingPlantOrderDetailItemDTO::getMaterialId).distinct().collect(Collectors.toList());
            List<MaterialDto> materialDtos = materialServiceProxy.listMaterialByIds(materialList);
            if (CollUtil.isNotEmpty(materialDtos)) {
                materialDtoMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, e -> e));
            }
            // 要货项目 + 接单站点
            List<Integer> receiveProjectList = dtoList.stream().map(MixingPlantOrderDetailItemDTO::getReceiverProject).distinct().collect(Collectors.toList());
            List<Integer> projectList = dtoList.stream().map(MixingPlantOrderDetailItemDTO::getProjectId).distinct().collect(Collectors.toList());
            List<Integer> collect = Stream.concat(receiveProjectList.stream(), projectList.stream()).distinct().collect(Collectors.toList());
            List<SimpleConstructionProjectDto> projectsByProjectIds = projectServiceProxy.findProjectsByProjectIds(collect);
            if (CollUtil.isNotEmpty(projectsByProjectIds)) {
                projectDtoMap = projectsByProjectIds.stream().collect(Collectors.toMap(SimpleConstructionProjectDto::getProjectId, e -> e));
            }
            // 累计发货量 + 累计发货车次
            List<String> detailIdS = dtoList.stream().map(MixingPlantOrderDetailItemDTO::getOrderDetailId).collect(Collectors.toList());
            List<ExistTicketDetailStatisticsDTO> statisticsDTOS = mixingPlantExitTicketMapper.statisticsByIdS(detailIdS);
            if (CollUtil.isNotEmpty(statisticsDTOS)) {
                statisticsDTOMap = statisticsDTOS.stream().collect(Collectors.toMap(ExistTicketDetailStatisticsDTO::getId, e -> e));
            }
            // 历史车次信息
            List<String> orderDetailIdList = dtoList.stream().map(MixingPlantOrderDetailItemDTO::getOrderDetailId).collect(Collectors.toList());
            List<ExistTicketHistoryVO> historyVOS = mixingPlantExitTicketMapper.historys(orderDetailIdList);
            if (CollUtil.isNotEmpty(historyVOS)) {
                historyVOMap = historyVOS.stream().collect(Collectors.toMap(ExistTicketHistoryVO::getMixingPlantOrderDetailId, e -> e));
            }
            // BOM包技术要求
            if (CollUtil.isNotEmpty(materialDtos)) {
                List<Integer> categoryIdList = materialDtos.stream().map(e -> e.getMaterialCategoryId()).distinct().collect(Collectors.toList());
                List<MaterialFinishedBom> finishedBomList = materialFinishedBomService.lambdaQuery()
                        .eq(MaterialFinishedBom::getCompanyId, companyId)
                        .eq(MaterialFinishedBom::getProjectId, projectId)
                        .in(MaterialFinishedBom::getCategoryId, categoryIdList)
                        .list();
                if (CollUtil.isNotEmpty(finishedBomList)) {
                    bomMap = finishedBomList.stream().collect(Collectors.toMap(MaterialFinishedBom::getCategoryId, e -> e));
                }
            }

            Map<String, List<MixingPlantOrderDetailItemDTO>> map = dtoList.stream().collect(Collectors.groupingBy(MixingPlantOrderDetailItemDTO::getOrderId));
            Map<Integer, MaterialDto> finalMaterialDtoMap = materialDtoMap;
            Map<Integer, SimpleConstructionProjectDto> finalProjectDtoMap = projectDtoMap;
            Map<String, ExistTicketDetailStatisticsDTO> finalStatisticsDtoMap = statisticsDTOMap;
            Map<String, String> finalEmployeeDtoMap = employeeMap;
            Map<String, ExistTicketHistoryVO> finalHistoryVOMap = historyVOMap;
            Map<Integer, MaterialFinishedBom> finalBomMap = bomMap;
            map.keySet().forEach(e -> {
                MixingPlantOrderDetailVO orderVo = new MixingPlantOrderDetailVO();

                orderVo.setId(e);
                orderVo.setMixingPlantOrderId(e);
                MixingPlantOrderDetailItemDTO dto = map.get(e).get(0);
                orderVo.setOrderNo(dto.getOrderNo());
                if (CollUtil.isNotEmpty(finalEmployeeDtoMap) && StrUtil.isNotBlank(finalEmployeeDtoMap.get(dto.getCreateId()))) {
                    orderVo.setCustomerName(finalEmployeeDtoMap.get(dto.getCreateId()));
                }
                if (CollUtil.isNotEmpty(finalProjectDtoMap)) {
                    SimpleConstructionProjectDto projectDto = finalProjectDtoMap.get(dto.getReceiverProject());
                    if (ObjectUtil.isNotNull(projectDto)) {
                        orderVo.setProjectAddress(projectDto.getAddress());
                        orderVo.setReceiverProject(projectDto.getProjectTitle());
                        Integer relationDepartmentId = projectDto.getRelationDepartmentId();
                        if (ObjectUtil.isNotNull(relationDepartmentId)) {
                            String departmentParent = departmentTreeServiceProxy.getDepartmentParent(relationDepartmentId);
                            orderVo.setReceiverCorp(departmentParent);
                        }
                    }
                    SimpleConstructionProjectDto simpleConstructionProjectDto = finalProjectDtoMap.get(dto.getProjectId());
                    if (ObjectUtil.isNotNull(simpleConstructionProjectDto)) {
                        orderVo.setPlantName(simpleConstructionProjectDto.getProjectTitle());
                    }
                }
                orderVo.setPurchaseOrderNo(dto.getPurchaseOrderNo());
                orderVo.setReceiveTime(dto.getReceiveTime());
                orderVo.setOrderDate(dto.getOrderDate());
                orderVo.setReceiverAddress(dto.getReceiverAddress());
                orderVo.setReceiver(dto.getReceiver());
                orderVo.setReceiverTel(dto.getReceiverTel());
                orderVo.setRemark(dto.getRemark());

                List<MixingPlantOrderDetailItemVO> detailVOS = map.get(e).stream().map(m -> {
                    MixingPlantOrderDetailItemVO detailVO = new MixingPlantOrderDetailItemVO();

                    BeanUtil.copyProperties(m, detailVO);
                    detailVO.setId(m.getOrderDetailId());
                    // bom包的技术要求追加到ParameterRequirements(最初只存放字典中参数要求)中

                    if (CollUtil.isNotEmpty(finalMaterialDtoMap) && ObjectUtil.isNotNull(finalMaterialDtoMap.get(m.getMaterialId()))) {
                        MaterialDto materialDto = finalMaterialDtoMap.get(m.getMaterialId());
                        if (CollUtil.isNotEmpty(finalBomMap) && ObjectUtil.isNotNull(finalBomMap.get(materialDto.getMaterialCategoryId()))) {
                            MaterialFinishedBom bom = finalBomMap.get(materialDto.getMaterialCategoryId());
                            List<ParameterRequirementsDTO> parameterResult = new ArrayList<>();
                            List<String> param = StrUtil.split(bom.getOtherParameter(), ",");

                            if (StrUtil.isNotBlank(m.getParameterRequirements())) {
                                List<ParameterRequirementsDTO> parseArray = JSONObject.parseArray(m.getParameterRequirements(), ParameterRequirementsDTO.class);
                                param.stream().forEach(n -> {
                                    ParameterRequirementsDTO parameterRequirementsDTO = new ParameterRequirementsDTO();
                                    parameterRequirementsDTO.setParamName(n);
                                    parameterRequirementsDTO.setParamType(ParameterRequirementsTypeEnum.INPUT.code());
                                    parameterRequirementsDTO.setKind((byte) 1);

                                    parseArray.add(parameterRequirementsDTO);
                                });
                                parameterResult = parseArray;
                            } else {
                                parameterResult = param.stream().map(n -> {
                                    ParameterRequirementsDTO parameterRequirementsDTO = new ParameterRequirementsDTO();
                                    parameterRequirementsDTO.setParamName(n);
                                    parameterRequirementsDTO.setParamType(ParameterRequirementsTypeEnum.INPUT.code());
                                    parameterRequirementsDTO.setKind((byte) 1);
                                    return parameterRequirementsDTO;
                                }).collect(Collectors.toList());
                            }
                            detailVO.setParameterRequirements(JSON.toJSONString(parameterResult));
                        }

                        detailVO.setType(StrUtil.format("{}/{}", finalMaterialDtoMap.get(m.getMaterialId()).getMaterialName(), finalMaterialDtoMap.get(m.getMaterialId()).getMaterialSpec()));
                        detailVO.setParameterRequirementsDesc(purchaseOrderService.parseParameterRequirements(finalMaterialDtoMap.get(m.getMaterialId()).getMaterialCategoryId(), m.getParameterRequirements()));
                        detailVO.setCategoryId(finalMaterialDtoMap.get(m.getMaterialId()).getMaterialCategoryId());
                    }
                    if (CollUtil.isNotEmpty(finalStatisticsDtoMap) && ObjectUtil.isNotNull(finalStatisticsDtoMap.get(m.getOrderDetailId()))) {
                        detailVO.setSendCount(finalStatisticsDtoMap.get(m.getOrderDetailId()).getSendCount());
                        detailVO.setTruckCount(finalStatisticsDtoMap.get(m.getOrderDetailId()).getTruckCount());
                    }
                    if (CollUtil.isNotEmpty(finalHistoryVOMap) && ObjectUtil.isNotNull(finalHistoryVOMap.get(m.getOrderDetailId()))) {
                        detailVO.setHistoryVO(finalHistoryVOMap.get(m.getOrderDetailId()));
                    }

                    return detailVO;
                }).collect(Collectors.toList());

                orderVo.setOrderDetailItemVOS(detailVOS);
                result.add(orderVo);
            });
            voList = result.stream().sorted(Comparator.comparing(MixingPlantOrderDetailVO::getReceiveTime, Comparator.nullsLast(Comparator.reverseOrder()))).collect(Collectors.toList());
        }

        return voList;
    }

    @Override
    public List<String> customer() {
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
        Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();

        List<String> list = this.getBaseMapper().customer(companyId, projectId);
        List<String> result = list.stream().distinct().collect(Collectors.toList());

        return result;
    }

    @Override
    public ExistTicketDetailVO orderDetail() {
        ExistTicketDetailVO vo = new ExistTicketDetailVO();
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();

        MixingPlantExitTicket entity = mixingPlantExitTicketService.lambdaQuery()
                .select(MixingPlantExitTicket::getMixingPlantOrderDetailId)
                .eq(MixingPlantExitTicket::getCompanyId, authUserHolder.getCurrentUser().getCurrentCompanyId())
                .eq(MixingPlantExitTicket::getProjectId, authUserHolder.getCurrentUser().getCurrentProjectId())
                .eq(MixingPlantExitTicket::getCreateId, authUserHolder.getCurrentUser().getId())
                .orderByDesc(MixingPlantExitTicket::getCreateTime)
                .last("limit 1")
                .one();
        if (ObjectUtil.isNotNull(entity)) {
            ExistTicketDetailDTO dto = mixingPlantOrderDetailMapper.selectOrderDetail(entity.getMixingPlantOrderDetailId());
            if (ObjectUtil.isNotNull(dto)) {
                MixingPlantOrderDetailItemVO detailItemVO = new MixingPlantOrderDetailItemVO();

                projectNameWrapper.wrap(dto);
                createNameWrapper.wrap(dto, companyId);
                MaterialDto materialDto = materialServiceProxy.materialById(dto.getMaterialId());
                SimpleConstructionProjectDto simpleProject = projectServiceProxy.findSimpleProject(dto.getReceiverProject());

                BeanUtil.copyProperties(dto, vo);
                vo.setPlantName(vo.getProjectTitle());
                vo.setCustomerName(vo.getCreateName());
                vo.setOrderDate(vo.getCreateTime());
                BeanUtil.copyProperties(dto, detailItemVO);

                detailItemVO.setId(dto.getMixingPlantOrderDetailId());
                if (ObjectUtil.isNotNull(materialDto)) {
                    detailItemVO.setType(StrUtil.format("{}/{}", materialDto.getMaterialName(), materialDto.getMaterialSpec()));
                    detailItemVO.setParameterRequirementsDesc(purchaseOrderService.parseParameterRequirements(materialDto.getMaterialCategoryId(), dto.getParameterRequirements()));
                }
                if (ObjectUtil.isNotNull(simpleProject)) {
                    vo.setReceiverProject(simpleProject.getProjectTitle());
                    vo.setProjectAddress(simpleProject.getAddress());
                    Integer relationDepartmentId = simpleProject.getRelationDepartmentId();
                    if (ObjectUtil.isNotNull(relationDepartmentId)) {
                        String departmentParent = departmentTreeServiceProxy.getDepartmentParent(relationDepartmentId);
                        vo.setReceiverCorp(departmentParent);
                    }
                }

                // 累计发货量和累计发货车次
                ExistTicketDetailStatisticsDTO statisticsDTO = mixingPlantExitTicketMapper.statistics(dto.getMixingPlantOrderDetailId());
                if (ObjectUtil.isNotNull(statisticsDTO)) {
                    detailItemVO.setSendCount(statisticsDTO.getSendCount());
                    detailItemVO.setTruckCount(statisticsDTO.getTruckCount());
                }

                ExistTicketHistoryVO history = mixingPlantExitTicketMapper.history(dto.getMixingPlantOrderDetailId());
                detailItemVO.setHistoryVO(history);

                vo.setOrderDetailItemVOS(Arrays.asList(detailItemVO));
            }
        }

        return vo;
    }

    @Override
    public MixingPlantOrderDTO getSimpleMixingPlantOrder(String plantOrderDetailId) {
        MixingPlantOrderDTO mixingPlantOrderDTO = new MixingPlantOrderDTO();
        MixingPlantOrderDetail one = mixingPlantOrderDetailService.lambdaQuery()
                .eq(MixingPlantOrderDetail::getId, plantOrderDetailId)
                .one();
        if (ObjectUtil.isNotNull(one)) {
            mixingPlantOrderDTO.setPurchaseOrderId(one.getMixingPlantOrderId());
            mixingPlantOrderDTO.setPurchaseOrderDetailId(one.getId());
            mixingPlantOrderDTO.setMixingType(1);
            return mixingPlantOrderDTO;
        }
        return null;

    }

    @Override
    public String getPrintPlantOrderUrl(String orderDetailId) {
        // 数据
        MixingPlantOrderDetailVO mixingPlantOrderDetailVO = this.queryOrderDetailById(orderDetailId);
        List<MixingPlantOrderSendDetailVO> mixingPlantOrderSendDetailVOS = this.queryPlantOrderSendDetailsById(orderDetailId);
        MixingPlantOrderPreviewVO previewVO = new MixingPlantOrderPreviewVO();
        BeanUtils.copyProperties(mixingPlantOrderDetailVO, previewVO);
        previewVO.setMaterialCount(CollectionUtil.isNotEmpty(mixingPlantOrderSendDetailVOS) ?
                mixingPlantOrderSendDetailVOS.size() : 0);
        previewVO.setOrderSendDetailVOS(mixingPlantOrderSendDetailVOS);
        //获取url
        return preview("打印拌合站订单详情", previewVO, previewVO.getOrderNo());
    }

    @Override
    public List<MixingPlantOrderSendDetailVO> queryPlantOrderSendDetailsById(String orderDetailId) {
        List<MixingPlantOrderSendDetailVO> mixingPlantOrderSendDetailVOS = mixingPlantOrderMapper.queryPlantOrderSendDetailsById(orderDetailId);
        if (CollectionUtil.isNotEmpty(mixingPlantOrderSendDetailVOS)) {
            Set<Integer> materialIds = mixingPlantOrderSendDetailVOS.stream().map(MixingPlantOrderSendDetailVO::getMaterialId).collect(Collectors.toSet());
            Map<Integer, String> materialSpecMap = materialServiceProxy.materialSpec(materialIds);
            mixingPlantOrderSendDetailVOS.forEach(item -> item.setMaterialSpec(materialSpecMap.get(item.getMaterialId())));
        }
        return mixingPlantOrderSendDetailVOS;
    }

    @Override
    public String queryOrderIdByOrderDetailId(String orderDetailId) {
        MixingPlantOrderDetail detail = mixingPlantOrderDetailMapper.selectById(orderDetailId);
        return detail.getMixingPlantOrderId();
    }

    @Override
    public List<MixingPlantOrderStatusCountVO> orderStatusCount() {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        List<MixingPlantOrderStatusCountVO> list = Lists.newArrayList();
        List<MixingPlantOrderStatusCountVO> statusCount = mixingPlantOrderMapper.orderStatusCount(currentUser.getCurrentProjectId());
        for (PlantOrderDetailStatusEnum value : PlantOrderDetailStatusEnum.values()) {
            if (CollectionUtil.isNotEmpty(statusCount)) {
                Optional<MixingPlantOrderStatusCountVO> any = statusCount.stream().filter(item -> item.getStatus() == value.value()).findAny();
                if (any.isPresent()) {
                    list.add(any.get());
                } else {
                    MixingPlantOrderStatusCountVO vo = new MixingPlantOrderStatusCountVO();
                    vo.setStatus(value.value());
                    list.add(vo);
                }
            }
        }
        return list;
    }

    /**
     * 预览pdf
     *
     * @param fileName 文件名称
     * @param data
     * @param orderNo
     * @return
     */
    private String preview(String fileName, Object data, String orderNo) {
        String finalPath = temporaryPath + StrUtil.format("{}.docx", orderNo + System.currentTimeMillis());
        InputStream inputStream = WordUtils.getTemplatePath(fileName);

        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        Configure config = Configure.builder().bind("orderDetailItemVOS", policy).bind("orderSendDetailVOS", policy).useSpringEL(false).build();
        //渲染word
        WordUtils.render(inputStream, finalPath, data, config);
        File file = null;
        try (UploadComponent uploadComponent = fileServiceProxy.getDynamicUploadComponent()) {
            file = new File(finalPath);
            OssFile ossFile = new OssFile(file, "application/octet-stream", FileTypeEnums.OTHER);
            UploadConfig uploadConfig = new UploadConfig("ff8080815afee284015afee408680001");
            //上传到oss
            String uuid = uploadComponent.uploadFile(ossFile, uploadConfig);
            //生产uuid
            fileServiceProxy.confirmFile(uuid, "material-management");
            FilePreviewDto filePreview = fileServiceProxy.findFilePreview(uuid);
            return filePreview.getFileUrl();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            file.deleteOnExit();
        }
        return null;
    }

}
