package cn.pinming.microservice.material.management.biz.controller;

import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.query.ReceiveOverviewCardQuery;
import cn.pinming.microservice.material.management.biz.query.ReceiveOverviewModalQuery;
import cn.pinming.microservice.material.management.biz.query.SummaryDeliveryQuery;
import cn.pinming.microservice.material.management.biz.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.biz.service.ISummaryAnalysisService;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 收料总览/收料偏差总览
 *
 * <AUTHOR>
 * @version 2021/9/6 10:37 上午
 */
@Api(tags = "总览分析", value = "lh")
@RestController
@RequestMapping("/api/summary/analysis")
@AllArgsConstructor
public class SummaryAnalysisController {

    public final ISummaryAnalysisService summaryAnalysisService;

    @Resource
    private AuthUserHolder authUserHolder;

    @ApiOperation(value = "汇总",response = SummaryAnalysisVO.class)
    @Log(title = "汇总", businessType = BusinessType.QUERY)
    @PostMapping
    public ResponseEntity<Response> summary(@RequestBody SummaryDeliveryQuery query) {
        SummaryAnalysisVO result = summaryAnalysisService.querySummary(query);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "收料总览 - 地磅收料",response = CategoryReceiveVO.class)
    @Log(title = "收料总览 - 地磅收料", businessType = BusinessType.QUERY)
    @PostMapping("/receive")
    public ResponseEntity<Response> receive(@RequestBody SupplierAnalysisQuery query) {
        List<CategoryReceiveVO> list = summaryAnalysisService.listReceiveByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "标题-收料总览 iteration  二级下钻页",response = SummaryDeliverySecondVO.class)
    @Log(title = "收料总览 iteration  二级下钻页", businessType = BusinessType.QUERY)
    @PostMapping("/overview/receive/second")
    public ResponseEntity<Response> receiveIterationSecond(@RequestBody SummaryDeliveryQuery query) {
        List<ReceiveOverviewSecondVO> list = summaryAnalysisService.listReceiveOverviewSecondByQuery(query, (byte) 1);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "标题-发料总览 iteration  二级下钻页",response = SummaryDeliverySecondVO.class)
    @Log(title = "收料总览 iteration  二级下钻页", businessType = BusinessType.QUERY)
    @PostMapping("/overview/send/second")
    public ResponseEntity<Response> sendIterationSecond(@RequestBody SummaryDeliveryQuery query) {
        List<ReceiveOverviewSecondVO> list = summaryAnalysisService.listReceiveOverviewSecondByQuery(query, (byte) 2);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "收料总览 - 移动收料",response = CategoryReceiveVO.class)
    @Log(title = "收料总览 - 移动收料", businessType = BusinessType.QUERY)
    @PostMapping("/mobileReceive")
    public ResponseEntity<Response> mobileReceive(@RequestBody SupplierAnalysisQuery query) {
        List<CategoryReceiveVO> list = summaryAnalysisService.countMobileReceiveNum(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "收料偏差总览 - 偏差占比趋势",response = ReceiveDeviationVO.class)
    @Log(title = "收料偏差总览 - 偏差占比趋势", businessType = BusinessType.QUERY)
    @PostMapping("/deviation")
    public ResponseEntity<Response> deviation(@RequestBody SupplierAnalysisQuery query) {
        List<ReceiveDeviationVO> list = summaryAnalysisService.listDeviationByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "收料偏差总览 - 变差占比累计",response = ReceiveDeviationSummaryVO.class)
    @Log(title = "收料偏差总览 - 变差占比累计", businessType = BusinessType.QUERY)
    @PostMapping("/deviation/summary")
    public ResponseEntity<Response> deviationSummary(@RequestBody SupplierAnalysisQuery query) {
        List<ReceiveDeviationSummaryVO> list = summaryAnalysisService.listDeviationSummaryByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "发料总览",response = SummaryDeliveryVO.class)
    @Log(title = "发料总览", businessType = BusinessType.QUERY)
    @PostMapping("/delivery")
    public ResponseEntity<Response> summaryDelivery(@RequestBody SummaryDeliveryQuery query) {
        List<SummaryDeliveryVO> list = summaryAnalysisService.listSummaryDeliveryByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "发料总览-二级下钻页",response = SummaryDeliverySecondVO.class)
    @Log(title = "发料总览-二级下钻页", businessType = BusinessType.QUERY)
    @PostMapping("/delivery/second")
    public ResponseEntity<Response> summaryDeliverySecond(@RequestBody SummaryDeliveryQuery query) {
        List<SummaryDeliverySecondVO> list = summaryAnalysisService.listSummaryDeliverySecondByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "预警总览",response = SummaryWarningVO.class)
    @Log(title = "预警总览", businessType = BusinessType.QUERY)
    @PostMapping("/warning")
    public ResponseEntity<Response> summaryWarning(@RequestBody SummaryDeliveryQuery query) {
        List<SummaryWarningVO> list = summaryAnalysisService.listSummaryWarningByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "预警总览-二级下钻页")
    @Log(title = "预警总览-二级下钻页", businessType = BusinessType.QUERY)
    @PostMapping("/warning/second")
    public ResponseEntity<Response> summaryWarningSecond(@RequestBody SummaryDeliveryQuery query) {
        List<WarningSecondVO> list = summaryAnalysisService.listWarningSecondByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }


    @ApiOperation(value = "标题-预警总览-overview-二级下钻页")
    @Log(title = "预警总览-二级下钻页", businessType = BusinessType.QUERY)
    @PostMapping("/overview/warning/second")
    public ResponseEntity<Response> overviewWarningSecond(@RequestBody SummaryDeliveryQuery query) {
        List<WarningOverviewSecondVO> list = summaryAnalysisService.listWarningOverviewSecondByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "标题-收料偏差总览-二级下钻页")
    @Log(title = "标题-收料偏差总览-二级下钻页", businessType = BusinessType.QUERY)
    @PostMapping("/overview/deviation/second")
    public ResponseEntity<Response> deviationOverviewSecond(@RequestBody SummaryDeliveryQuery query) {
        List<DeviationOverviewSecondVO> list = summaryAnalysisService.listDeviationOverviewSecondByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "收料偏差总览-二级下钻页")
    @Log(title = "收料偏差总览-二级下钻页", businessType = BusinessType.QUERY)
    @PostMapping("/deviation/second")
    public ResponseEntity<Response> deviationSecond(@RequestBody SupplierAnalysisQuery query) {
            List<DeviationSecondVO> list = summaryAnalysisService.listDeviationSecondByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "收料偏差总览-二级下钻页-偏差累计")
    @Log(title = "收料偏差总览-二级下钻页-偏差累计", businessType = BusinessType.QUERY)
    @PostMapping("/deviation/second/count")
    public ResponseEntity<Response> deviationSecondCount(@RequestBody SupplierAnalysisQuery query) {
        List<DeviationSecondSummaryVO> list = summaryAnalysisService.listDeviationSecondSummaryByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "总览 - 收料总览 - 卡片", response = ReceiveOverviewCardVO.class)
    @PostMapping("/overview/receive/card")
    public ResponseEntity<Response> receiveOverviewCard(@RequestBody ReceiveOverviewCardQuery query) {
        List<ReceiveOverviewCardVO> list = summaryAnalysisService.receiveOverviewCard(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "总览 - 收料总览 - 其他物料", response = ReceiveOverviewCardVO.class)
    @PostMapping("/overview/receive/other")
    public ResponseEntity<Response> receiveOverviewOther(@RequestBody ReceiveOverviewCardQuery query) {
        Map<String, List<ReceiveOverviewHistogram>> map = summaryAnalysisService.receiveOverviewOther(query);
        return ResponseEntity.ok(new SuccessResponse(map));
    }

    @ApiOperation(value = "总览 - 收料总览 - 柱状图下钻")
    @PostMapping("/overview/receive/card/second")
    public ResponseEntity<Response> receiveOverviewCardSecond(@RequestBody ReceiveOverviewModalQuery query) {
        Map<String, Object> map = summaryAnalysisService.receiveOverviewModal(query);
        return ResponseEntity.ok(new SuccessResponse(map));
    }

    @ApiOperation(value = "总览 - 异常提示", response = WarningOverviewTipsVO.class)
    @Log(title = "总览 - 异常提示", businessType = BusinessType.QUERY)
    @GetMapping("/overview/tips/{deptId}")
    public ResponseEntity<Response> overviewWarningTips(@PathVariable Integer deptId) {
        WarningOverviewTipsVO warningOverviewTipsVO = summaryAnalysisService.warningOverviewTip(deptId);
        return ResponseEntity.ok(new SuccessResponse(warningOverviewTipsVO));
    }

}
