package cn.pinming.microservice.material.management.biz.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WeighbridgeSendQuery extends BaseQuery{

    @ApiModelProperty(value = "明细id")
    private String id;

    @ApiModelProperty(value = "发货单id")
    private String sendId;

    @ApiModelProperty(value = "发货单号")
    private String sendNo;

    @ApiModelProperty(value = "车牌号")
    private String truckNo;

    @ApiModelProperty(value = "发货单位")
    private String sendProject;

    @ApiModelProperty(value = "收货单位")
    private String receiveProject;

    @ApiModelProperty(value = "物资名称")
    private String materialName;

    @ApiModelProperty(value = "品种及规格")
    private String materialSpec;

    @ApiModelProperty(value = "毛皮重完整性:1 完整 2 不完整")
    private Byte isWeightIntegrality;

    @ApiModelProperty(value = "有效性:1 有效 2 无效")
    private Byte materialValidity;

    @ApiModelProperty(value = "是否为手工补单 1 否 2 是")
    private Byte isAddition;
}

