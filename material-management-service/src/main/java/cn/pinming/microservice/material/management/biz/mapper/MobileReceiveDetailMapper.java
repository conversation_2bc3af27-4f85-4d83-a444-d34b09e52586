package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.MobileGoodsCardDTO;
import cn.pinming.microservice.material.management.biz.dto.ReceiveCardMaterialDetailDTO;
import cn.pinming.microservice.material.management.biz.entity.MobileReceiveDetail;
import cn.pinming.microservice.material.management.biz.vo.MobileReceiveDetailHistoryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 移动收料细节表 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:31:23
 */
public interface MobileReceiveDetailMapper extends BaseMapper<MobileReceiveDetail> {

    /**
     * 获取收货单下各材料收料合计
     *
     * @param receiveIdList,categoryId
     * @return
     */
    List<MobileGoodsCardDTO> selectReceiveDetail(List<String> receiveIdList);

    /**
     * 获取各材料收料信息 根据收货单id
     *
     * @param receiveId
     * @return
     */
    List<ReceiveCardMaterialDetailDTO> detail(@Param("receiveId") String receiveId);

    List<MobileReceiveDetailHistoryVO> selectHistory(@Param("totalId")String totalId);

    List<String> getPointsPic(String receiveId);
}
