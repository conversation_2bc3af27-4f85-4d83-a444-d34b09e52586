package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.entity.LeaveTemplate;
import cn.pinming.microservice.material.management.biz.enums.LeaveParamEnum;
import cn.pinming.microservice.material.management.biz.enums.TemplateTypeEnum;
import cn.pinming.microservice.material.management.biz.enums.WeightParamEnum;
import cn.pinming.microservice.material.management.biz.form.LeaveTemplateContentForm;
import cn.pinming.microservice.material.management.biz.form.LeaveTemplateImageForm;
import cn.pinming.microservice.material.management.biz.form.LeaveTemplateNameForm;
import cn.pinming.microservice.material.management.biz.form.TemplateImageForm;
import cn.pinming.microservice.material.management.biz.mapper.LeaveTemplateMapper;
import cn.pinming.microservice.material.management.biz.service.ILeaveTemplateService;
import cn.pinming.microservice.material.management.biz.vo.LeaveTemplateNameVO;
import cn.pinming.microservice.material.management.biz.vo.LeaveTemplateParamVO;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.proxy.FileServiceProxy;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 * 出场单模板 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-28 13:58:49
 */
@Service
public class LeaveTemplateServiceImpl extends ServiceImpl<LeaveTemplateMapper, LeaveTemplate> implements ILeaveTemplateService {


    @Resource
    private FileServiceProxy fileServiceProxy;

    @Resource
    private AuthUserHolder authUserHolder;

    @Override
    public void saveLeaveTemplate(LeaveTemplateContentForm leaveTemplateForm) {
        String templateName = leaveTemplateForm.getTemplateName();
        Byte templateType = leaveTemplateForm.getTemplateType();
        List<LeaveTemplate> leaveTemplateList = getLeaveTemplateByName(templateName, templateType);
        if (CollUtil.isEmpty(leaveTemplateList)) {
            throw new BOException(BOExceptionEnum.TEMPLATE_NAME_NOT_EXIST);
        }
        LeaveTemplate leaveTemplate = leaveTemplateList.get(0);
        leaveTemplate.setTemplateContent(leaveTemplateForm.getTemplateContent());
        this.updateById(leaveTemplate);
    }

    @Override
    public void saveLeaveTemplateName(LeaveTemplateNameForm leaveTemplateNameForm) {
        String oldTemplateName = leaveTemplateNameForm.getOldTemplateName();
        String newTemplateName = leaveTemplateNameForm.getNewTemplateName();
        if (StringUtils.isEmpty(oldTemplateName)) {
            // 新增
            List<LeaveTemplate> leaveTemplates = getLeaveTemplateByName(StrUtil.cleanBlank(newTemplateName), leaveTemplateNameForm.getTemplateType());
            if (CollUtil.isNotEmpty(leaveTemplates)) {
                throw new BOException(BOExceptionEnum.TEMPLATE_NAME_REPEAT);
            }
            LeaveTemplate leaveTemplate = new LeaveTemplate();
            leaveTemplate.setTemplateType(leaveTemplateNameForm.getTemplateType());
            leaveTemplate.setTemplateName(leaveTemplateNameForm.getNewTemplateName());
            this.save(leaveTemplate);
        } else {
            // 更新
            List<LeaveTemplate> leaveTemplates = getLeaveTemplateByName(oldTemplateName, leaveTemplateNameForm.getTemplateType());
            if (!CollUtil.isNotEmpty(leaveTemplates)) {
                throw new BOException(BOExceptionEnum.TEMPLATE_NAME_NOT_EXIST);
            }
            LeaveTemplate leaveTemplate = leaveTemplates.get(0);
            leaveTemplate.setTemplateName(newTemplateName);
            this.updateById(leaveTemplate);
        }
    }

    @Override
    public String findLeaveTemplate(String templateName, Byte type) {
        List<LeaveTemplate> leaveTemplates = getLeaveTemplateByName(templateName, type);
        if (CollUtil.isEmpty(leaveTemplates)) {
            throw new BOException(BOExceptionEnum.TEMPLATE_NAME_NOT_EXIST);
        }
        LeaveTemplate leaveTemplate = leaveTemplates.get(0);
        return leaveTemplate.getTemplateContent();
    }

    @Override
    public void deleteLeaveTemplate(String templateName, Byte type) {
        List<LeaveTemplate> leaveTemplates = getLeaveTemplateByName(templateName, type);
        if (CollUtil.isEmpty(leaveTemplates)) {
            throw new BOException(BOExceptionEnum.TEMPLATE_NAME_NOT_EXIST);
        }
        List<LeaveTemplate> leaveTemplateList = listLeaveTemplateByProject(type);
        if (leaveTemplateList.size() == 1) {
            throw new BOException(BOExceptionEnum.TEMPLATE_NAME_NOT_DELETE);
        }
        LeaveTemplate leaveTemplate = leaveTemplates.get(0);
        this.removeById(leaveTemplate);
    }

    @Override
    public List<LeaveTemplateNameVO> listLeaveTemplate(Byte type) {
        List<LeaveTemplate> leaveTemplateList = this.listLeaveTemplateByProject(type);
        if (CollUtil.isEmpty(leaveTemplateList)) {
            LeaveTemplateNameForm leaveTemplateNameForm = new LeaveTemplateNameForm();
            if (type == TemplateTypeEnum.WEIGHT.value()) {
                leaveTemplateNameForm.setNewTemplateName("默认称重单模板");
            } else if (type == TemplateTypeEnum.LEAVE.value()) {
                leaveTemplateNameForm.setNewTemplateName("默认出场单单模板");
            }
            leaveTemplateNameForm.setTemplateType(type);
            this.saveLeaveTemplateName(leaveTemplateNameForm);
        }
        return this.listLeaveTemplateByProject(type).stream().map(leaveTemplate -> new LeaveTemplateNameVO(leaveTemplate.getId(), leaveTemplate.getTemplateName())).collect(Collectors.toList());
    }

    @Override
    public void saveLeaveTemplateImage(LeaveTemplateImageForm leaveTemplateForm) {
        // 确认上传图片
        CompletableFuture.runAsync(() ->  fileServiceProxy.confirmFile(leaveTemplateForm.getTemplateImage(), "material-management"));

        String templateName = leaveTemplateForm.getTemplateName();
        List<LeaveTemplate> leaveTemplateList;
        synchronized (this) {
            leaveTemplateList = getLeaveTemplateByName(templateName, leaveTemplateForm.getTemplateType());

            if (CollUtil.isEmpty(leaveTemplateList)) {
                throw new BOException(BOExceptionEnum.TEMPLATE_NAME_NOT_EXIST);
            }
            LeaveTemplate leaveTemplate = leaveTemplateList.get(0);
            List<String> uuids = Lists.newArrayList();
            String templateImage = leaveTemplateForm.getTemplateImage();
            String existImage = leaveTemplate.getTemplateImage();
            if (StrUtil.isNotEmpty(existImage)) {
                // 已存在追加
                uuids.add(leaveTemplate.getTemplateImage());
                uuids.add(templateImage);
                leaveTemplate.setTemplateImage(String.join(StrUtil.COMMA, uuids));
            } else {
                leaveTemplate.setTemplateImage(templateImage);
            }
            this.updateById(leaveTemplate);
        }
    }

    @Override
    public List<TemplateImageForm> findLeaveTemplateImage(String name, Byte type) {
        List<LeaveTemplate> leaveTemplateByName = getLeaveTemplateByName(name, type);
        List<TemplateImageForm> templateImageFormList = Lists.newArrayList();
        if (CollUtil.isEmpty(leaveTemplateByName)) {
            return templateImageFormList;
        }
        LeaveTemplate leaveTemplate = leaveTemplateByName.get(0);
        if (StrUtil.isNotEmpty(leaveTemplate.getTemplateImage())) {
            String templateImage = leaveTemplate.getTemplateImage();
            String[] imageUuids = templateImage.split(StrUtil.COMMA);
            return Arrays.stream(imageUuids).map(uuid -> {
                TemplateImageForm templateImageForm = new TemplateImageForm();
                templateImageForm.setImageUUID(uuid);
                templateImageForm.setImageUrl(fileServiceProxy.fileDownloadUrlByUUID(uuid));
                return templateImageForm;
            }).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    @Override
    public void deleteLeaveTemplateImage(LeaveTemplateImageForm leaveTemplateImageForm) {
        List<LeaveTemplate> leaveTemplateList = getLeaveTemplateByName(leaveTemplateImageForm.getTemplateName(), leaveTemplateImageForm.getTemplateType());
        if (CollUtil.isEmpty(leaveTemplateList)) {
            throw new BOException(BOExceptionEnum.TEMPLATE_NAME_NOT_EXIST);
        }
        LeaveTemplate leaveTemplate = leaveTemplateList.get(0);
        String templateImage = leaveTemplate.getTemplateImage();
        if (StrUtil.isNotEmpty(templateImage)) {
            String[] uuids = templateImage.split(StrUtil.COMMA);
            String newUuid = Arrays.stream(uuids).filter(uuid -> !uuid.equals(leaveTemplateImageForm.getTemplateImage())).collect(Collectors.joining(StrUtil.COMMA));
            leaveTemplate.setTemplateImage(newUuid);
            this.updateById(leaveTemplate);
        }
    }

    @Override
    public List<LeaveTemplateParamVO> paramList(Byte type) {
        if (type == TemplateTypeEnum.LEAVE.value()) {
            return Arrays.stream(LeaveParamEnum.values()).map(leaveParamEnum -> new LeaveTemplateParamVO(leaveParamEnum.value(), leaveParamEnum.description())).collect(Collectors.toList());
        } else if (type == TemplateTypeEnum.WEIGHT.value()) {
            return Arrays.stream(WeightParamEnum.values()).map(leaveParamEnum -> new LeaveTemplateParamVO(leaveParamEnum.value(), leaveParamEnum.description())).collect(Collectors.toList());
        }
        return null;
    }


    private List<LeaveTemplate> getLeaveTemplateByName(String templateName, Byte templateType) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        return this.lambdaQuery().eq(LeaveTemplate::getTemplateName, templateName)
                .eq(LeaveTemplate::getTemplateType, templateType)
                .eq(LeaveTemplate::getProjectId, currentUser.getCurrentProjectId()).list();
    }

    private List<LeaveTemplate> listLeaveTemplateByProject(Byte type) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        return this.lambdaQuery().eq(LeaveTemplate::getProjectId, currentUser.getCurrentProjectId())
                .eq(LeaveTemplate::getTemplateType, type)
                .orderByAsc(LeaveTemplate::getCreateTime).list();
    }

}
