package cn.pinming.microservice.material.management.common.exception;

import cn.pinming.core.common.exception.ErrorView;

/**
 * 运行时异常的基类
 */
public class BaseRuntimeException extends RuntimeException implements ErrorView {

    private static final long serialVersionUID = 9051275901375243959L;
    /**
     * 错误码
     */
    private final String errorCode;

    /**
     * 构造函数
     *
     * @param errorCode 错误码
     * @param errorMsg  错误信息
     */
    public BaseRuntimeException(String errorCode, String errorMsg) {
        super(errorMsg);
        this.errorCode = errorCode;
    }

    /**
     * 取错误码
     */
    @Override
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 取错误信息
     */
    @Override
    public String getErrorMsg() {
        return getMessage();
    }
}
