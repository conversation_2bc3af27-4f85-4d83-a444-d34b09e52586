package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.IngredientStatisticsDTO;
import cn.pinming.microservice.material.management.biz.entity.IngredientList;
import cn.pinming.microservice.material.management.biz.query.IngredientQuery;
import cn.pinming.microservice.material.management.biz.vo.IngredientListVO;
import cn.pinming.microservice.material.management.biz.vo.IngredientPageVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 拌合站配料单 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 09:54:01
 */
public interface IngredientListMapper extends BaseMapper<IngredientList> {

    IngredientList isFile(@Param("id") String id);

    IngredientList judge(@Param("id") String id);

    String applyNoCheck(@Param("companyId") Integer companyId,@Param("projectId") Integer projectId,@Param("no") String no);

    IngredientListVO selectIngredientListVO(@Param("id") String id);

    IPage<IngredientPageVO> selectPages(IngredientQuery query);

    List<IngredientStatisticsDTO> statistics(@Param("companyId") Integer companyId,@Param("projectId") Integer projectId);

    List<String> requirement(@Param("companyId") Integer companyId,@Param("projectId") Integer projectId);

    BigDecimal selectRequirement(@Param("ingredientListId")String ingredientListId);
}
