package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 采购合同
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_purchase_contract")
@ApiModel(value = "PurchaseContract对象", description = "采购合同")
public class PurchaseContract implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "合同ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "所属项目，多个")
    @TableField("belong_project_id")
    private String belongProjectId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编号")
    private String no;

    @ApiModelProperty(value = "类型， 1：混凝土采购合同；2：材料采购合同")
    private String type;

    @ApiModelProperty(value = "外部系统代码")
    @TableField("ext_code")
    private String extCode;

    @ApiModelProperty(value = "供应商ID")
    @TableField("supplier_id")
    private Integer supplierId;

    @ApiModelProperty(value = "物资品种")
    private String category;

    @ApiModelProperty(value = "对账归档后是否同步仓库 1 是 2 否")
    @TableField(value = "is_upload")
    private Byte isUpload;

    @ApiModelProperty(value = "所属机构")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;

    @ApiModelProperty("0 默认 1 采购单推送至基石'订单/发货'系统 2 合同物料明细推送至基石'订单/发货'系统 ")
    private Byte isPush;

    @ApiModelProperty("推送合同明细ID,多个用逗号分隔")
    private String pushDetailId;


}
