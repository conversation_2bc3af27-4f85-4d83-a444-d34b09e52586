package cn.pinming.microservice.material.management.proxy;

import cn.pinming.core.common.model.PageList;
import cn.pinming.microservice.material.management.biz.query.MaterialWeighbridgeQuery;
import cn.pinming.microservice.material.management.biz.vo.ProjectVO;
import cn.pinming.v2.project.api.dto.ConstructionProjectDto;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import lombok.NonNull;
import org.jetbrains.annotations.NotNull;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 项目相关dubbo.
 *
 * <AUTHOR>
 * @version 2021/9/1 4:44 下午
 */
public interface ProjectServiceProxy {

    String PLATE_PLUGIN_NO = "mix-plant";
    String WAREHOUSE_PLUGIN_NO = "warehouse-management";

    List<ProjectVO> getProjectsByCompanyId(@NonNull Integer companyId);

    List<ProjectVO> getAllProjectsByCompanyId(@NonNull Integer companyId);

    List<ProjectVO> getAllProjectsByCompanyDeptId(@NonNull Integer companyId, Integer deptId);

    //List<ProjectVO> getProjectsByProjectIds(@NonNull Integer companyId, @NonNull Collection<Integer> projectIds);

    List<ProjectVO> getProjectsByProjectIds(@NonNull Collection<Integer> projectIds);

    ProjectVO getProjectById(@NonNull Integer projectId);

    List<ProjectVO> getSimpleProjects(@NotNull List<Integer> projectIds);

    Integer getProjectIdByCode(@NotNull Integer companyId, Long projectCode);

    PageList<ConstructionProjectDto> getProjectsPageByQuery(MaterialWeighbridgeQuery query);

    SimpleConstructionProjectDto findSimpleProject(Integer projectId);

    List<SimpleConstructionProjectDto> findProjectsByProjectIds(List<Integer> projectIds);

    List<ProjectVO> getProjectsByDepartmentId(@NotNull List<Integer> departments);

    /**
     * 总览-统计设置-项目范围
     *
     * @param compId 企业ID
     * @param deptId 部门ID
     * @return 组织树节点下所有项目 和 项目范围配置 的交集项目列表
     */
    List<Integer> statisticsProjectIds(@NonNull Integer compId, Integer deptId);

    /**
     * 总览-统计设置-直属下级单位(分公司&项目) 和 项目范围配置 的交集项目列表
     *
     * @param compId 企业ID
     * @param deptId 部门ID
     * @return k: 单位或项目ID-单位或项目名称（例如：1-杭州西湖）名称截取第一个-后面的内容
     * v: 项目ID列表
     */
    Map<String, List<Integer>> directlyUnderDeptOrProject(@NonNull Integer compId, Integer deptId);

    /**
     * 总览-统计设置-项目范围
     *
     * @param compId 企业ID
     * @param deptId 部门ID
     * @return 组织树节点下所有项目的项目列表
     */
    List<Integer> statisticsDeptProjectIds(@NonNull Integer compId, Integer deptId);


    /**
     * 总览-统计设置-直属下级单位(分公司&项目)集项目列表
     *
     * @param compId 企业ID
     * @param deptId 部门ID
     * @return k: 单位或项目ID-单位或项目名称（例如：1-杭州西湖）名称截取第一个-后面的内容
     * v: 项目ID列表
     */
    Map<String, List<Integer>> allDirectlyUnderDeptOrProject(@NonNull Integer compId, Integer deptId);


    ConstructionProjectDto findProject(Integer projectId);

    /**
     * 查询开通插件的项目ID
     *
     * @param pluginNo
     * @return
     */
    List<Integer> getPluginProjectIds(String pluginNo);

    List<Integer> getAllProjectIdByCompanyDeptId(Integer currentCompanyId, Integer currentDepartmentId);
}
