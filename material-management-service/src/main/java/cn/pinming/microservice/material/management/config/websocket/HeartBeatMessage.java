package cn.pinming.microservice.material.management.config.websocket;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 推送信息：心跳包
 * {"action":"heartBeat","companyId":"11346","projectId":"5128","projectName":"测试项目","pushTime":1646197620358,"sn":"xxx123","version":"1.33"}
 */
@Data
public class HeartBeatMessage {

	/**
	 * 消息类型
	 */
	private ActionType action;

	/**
	 * 客户端版本
	 */
	private String version;

	/**
	 * 企业id
	 */
	private Integer companyId;

	/**
	 * 项目id
	 */
	private Integer projectId;

	/**
	 * 项目名称
	 */
	private String projectName;

	/**
	 * 地磅设备sn
	 */
	private String sn;

	/**
	 * 推送时间
	 */
	//protected LocalDateTime pushTime;

}
