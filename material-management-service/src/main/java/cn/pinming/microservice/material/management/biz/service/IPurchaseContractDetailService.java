package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.dto.ContractDetailDTO;
import cn.pinming.microservice.material.management.biz.dto.SimpleContractDetailDTO;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContractDetail;
import cn.pinming.microservice.material.management.biz.vo.GoodsSimpleVO;
import cn.pinming.microservice.material.management.biz.vo.PurchaseContractDetailVO;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 采购合同物料明细 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface IPurchaseContractDetailService extends IService<PurchaseContractDetail> {

    List<String> listRemoveIdsById(String contractId, List<Integer> materialIdList);

    List<PurchaseContractDetailVO> listContractDetailById(String contractId);

    List<ContractDetailDTO> selectByContractIds(List<String> contractIds);

    List<ContractDetailDTO> selectByContractDetailIds(List<String> contractIds);

    List<String> listUnitById(Integer companyId, Integer projectId);

    List<SimpleContractDetailDTO> querySimpleContractDetails(List<String> contractDetailId);

}
