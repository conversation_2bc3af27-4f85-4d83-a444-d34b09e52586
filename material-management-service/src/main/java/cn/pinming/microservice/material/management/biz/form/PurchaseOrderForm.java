package cn.pinming.microservice.material.management.biz.form;

import cn.pinming.microservice.material.management.common.OpenApiAdd;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 2021/8/25 2:08 下午
 */
@Data
@Builder
public class PurchaseOrderForm extends BaseForm {

    @ApiModelProperty(value = "项目id")
    private Integer projectId;

    @ApiModelProperty(value = "采购单ID")
    private String id;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "订单名称为空")
    private String orderName;

    @ApiModelProperty(value = "采购合同ID")
    @NotBlank(message = "所属合同为空")
    private String contractId;

    @ApiModelProperty(value = "供应商ID")
    @NotNull(message = "供应商为空")
    private Integer supplierId;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "下单日期")
    @NotNull(message = "下单日期为空")
    private LocalDate orderTime;

    @ApiModelProperty(value = "收货项目")
    @NotNull(message = "收货项目为空")
    private Integer receiverProject;

    @ApiModelProperty(value = "收货地址")
    @NotBlank(message = "收货地址为空")
    private String receiverAddress;

    @ApiModelProperty(value = "收货人")
    @NotBlank(message = "收货人为空")
    private String receiver;

    @ApiModelProperty(value = "收货电话")
    @NotBlank(message = "收货电话为空")
    private String receiverTel;

    @ApiModelProperty(value = "要货日期")
    @NotNull(message = "要货日期为空")
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "备注")
    @Length(max = 200, message = "备注字数超过200")
    private String remark;

    @ApiModelProperty(value = "采购明细")
    @Size(min = 1, message = "采购明细为空")
    private List<PurchaseOrderDetailForm> list;

    @ApiModelProperty(value = "创建者id")
    @NotBlank(groups = OpenApiAdd.class,message = "创建者id为空")
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @NotBlank(groups = OpenApiAdd.class,message = "修改者id为空")
    private String updateId;

    @ApiModelProperty(value = "是否推送至基石“订单/发货”系统 1 是 0 否")
//    @NotNull(message = "是否推送至基石系统为空")
    private Byte isPush;
}
