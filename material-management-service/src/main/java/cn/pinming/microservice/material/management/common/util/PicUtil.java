package cn.pinming.microservice.material.management.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.upload.OssFile;
import cn.pinming.core.upload.UploadComponent;
import cn.pinming.core.upload.UploadConfig;
import cn.pinming.core.upload.domain.enums.FileTypeEnums;
import cn.pinming.microservice.material.management.proxy.FileServiceProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class PicUtil {
    @Value("${temporary.path}")
    private String temporaryPath;
    @Resource
    private FileServiceProxy fileServiceV2Proxy;
//    @DubboReference
//    private InternalFileCenterService internalFileCenterService;

    /**
     * 通过base64获取uuid
     *
     * @param base64
     * @return
     * @throws FileNotFoundException
     */
    public String getUrlByBase64(String base64) throws FileNotFoundException {
        BufferedImage bufferedImage = ImgUtil.toImage(base64.split(StrUtil.COMMA)[1]);
        String filePath = temporaryPath + StrUtil.format("{}.jpg", System.currentTimeMillis());
        FileOutputStream outputStream = new FileOutputStream(filePath);
        ImgUtil.writeJpg(bufferedImage, outputStream);
        File file = new File(filePath);
        UploadComponent uploadComponent = fileServiceV2Proxy.getDynamicUploadComponent();
        OssFile ossFile = new OssFile(file, "jpg", FileTypeEnums.IMAGE);
        UploadConfig uploadConfig = new UploadConfig("ff8080815afee284015afee408680001");
        String uuid = uploadComponent.uploadFile(ossFile, uploadConfig);
        fileServiceV2Proxy.confirmFiles(Collections.singletonList(uuid),"material-management-service");
        return uuid;
    }

    /**
     * 通过批量base64获取uuid
     *
     * @param base64s
     * @return
     * @throws FileNotFoundException
     */
    public String getUrlByBase64S(List<String> base64s) {
        if (CollUtil.isNotEmpty(base64s)) {
            return base64s.stream().map(e -> {
                try {
                    return this.getUrlByBase64(e);
                } catch (FileNotFoundException ex) {
                    log.info("base64图片下载失败...");
                }
                return null;
            }).collect(Collectors.joining(","));
        }else {
            return null;
        }
    }

    /**
     * 通过下载地址获取uuid
     *
     * @param downloadUrl
     * @param memberId
     * @param fileName
     * @return
     */
    public String downloadAndUploadFile(String downloadUrl, String memberId, String fileName) throws IOException {
        if (StrUtil.isBlank(downloadUrl)) {return null;}
        // 下载图片字节流
        URL url = new URL(downloadUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            InputStream inputStream = connection.getInputStream();
            File file = File.createTempFile("image", ".jpg");
            FileOutputStream outputStream = new FileOutputStream(file);
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.close();
            inputStream.close();
            fileName = StrUtil.isBlank(fileName) ? memberId + ".jpg" : fileName + ".jpg";
            OssFile ossFile = new OssFile(file, URLConnection.guessContentTypeFromName(fileName), FileTypeEnums.IMAGE);
            UploadComponent dynamicUploadComponent = fileServiceV2Proxy.getDynamicUploadComponent();
            return dynamicUploadComponent.uploadFile(ossFile, new UploadConfig(memberId));
        }
        return null;
    }

    /**
     * 通过批量base64获取uuid
     *
     * @param
     * @return
     * @throws FileNotFoundException
     */
    public String getUrlByUrls(List<String> urls) {
        if (CollUtil.isNotEmpty(urls)) {
            return urls.stream().map(e -> {
                try {
                    return this.downloadAndUploadFile(e,UUIDUtil.randomUUID(),null);
                } catch (Exception ex) {
                    log.info("base64图片下载失败...");
                }
                return null;
            }).collect(Collectors.joining(","));
        }else {
            return null;
        }
    }

//    /**
//     * 批量删除文件
//     * @param fileIdList
//     */
//    public void deleteFiles(List<String> fileIdList) {
//        if (CollUtil.isNotEmpty(fileIdList)) {
//            fileIdList.forEach(this::deleteFile);
//        }
//    }
//
//    /**
//     * 删除文件
//     * @param fileId
//     */
//    public void deleteFile(String fileId) {
//        internalFileCenterService.deleteAllSameFileByFileUuid(fileId);
//    }
}
