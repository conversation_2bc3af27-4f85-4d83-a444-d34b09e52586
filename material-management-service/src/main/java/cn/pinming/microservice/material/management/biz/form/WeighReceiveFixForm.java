package cn.pinming.microservice.material.management.biz.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 收料修正form
 *
 * <AUTHOR>
 * @version 2022/4/15 16:02
 */
@Data
public class WeighReceiveFixForm extends MaterialReviseForm{

    @ApiModelProperty("称重记录UUID")
    @JsonProperty(value = "tUuid")
    private String tUuid;

    @ApiModelProperty(value = "合同约定转换系数")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "是否是扫码收料")
    private Boolean isScanReceive = false;
}
