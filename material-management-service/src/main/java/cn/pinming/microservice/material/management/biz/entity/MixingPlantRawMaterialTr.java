package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 拌合站—原料实验报告
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 16:30:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_mixing_plant_raw_material_tr")
@ApiModel(value = "MixingPlantRawMaterialTr对象", description = "拌合站—原料实验报告")
public class MixingPlantRawMaterialTr implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "实验报告id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "实验报告编号")
    @TableField("lab_report_no")
    private String labReportNo;

    @ApiModelProperty(value = "试验材料id列表(英文逗号分隔)")
    @TableField("lab_material_id")
    private String labMaterialId;

    @ApiModelProperty(value = "实验单位")
    @TableField("lab_unit")
    private String labUnit;

    @ApiModelProperty(value = "实验报告日期")
    @TableField("lab_report_time")
    private LocalDate labReportTime;

    @ApiModelProperty(value = "附件uuid")
    @TableField("accessory_uuid")
    private String accessoryUuid;

    @ApiModelProperty(value = "附件文件名")
    @TableField("accessory_file_name")
    private String accessoryFileName;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
