package cn.pinming.microservice.material.management.biz.timetask.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.management.biz.dto.MaterialNameOneDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialWarning;
import cn.pinming.microservice.material.management.biz.enums.WarningSubTypeEnum;
import cn.pinming.microservice.material.management.biz.enums.WarningTypeEnum;
import cn.pinming.microservice.material.management.biz.mapper.MaterialWarningMapper;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.material_unit.api.material.service.MaterialService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 物料名称异常预警自动处理方案
 * <AUTHOR>
 * @date 2022/6/14
 */
@Slf4j
@Component
public class WarningSevenStrategy extends WarningAutoProcessStrategy implements CommandLineRunner {

    @Resource
    private MaterialWarningMapper warningMapper;

    @Reference
    private MaterialService materialService;

    /**
     * 7.1 检查相应data的材料ID在企业材料库内是否存在存在则自动置为"已处理〞。仍不存在则不作处理
     * 7.2 检查相应data的材料ID与对应合同明细材料ID是否相同，相同则自动置为“已处理”。仍不相同，则不作处理
     * @param warnings
     * @return
     */
    @Override
    public void autoProcessWay(List<MaterialWarning> warnings) {
        List<String> warningIds = Lists.newArrayList();
        log.info("物料名称异常预警自动处理 start...");
        if (CollectionUtil.isEmpty(warnings)) {
            log.info("待处理的预警为空");
            log.info("物料名称异常预警自动处理 end...");
            return;
        }
        // 预警子分类分组
        Map<Byte, List<MaterialWarning>> subTypeMap = warnings.stream().filter(item -> ObjectUtil.isNotNull(item.getWarningSubType()))
                .collect(Collectors.groupingBy(MaterialWarning::getWarningSubType));
        List<MaterialWarning> materialWarningsOne = subTypeMap.get(WarningSubTypeEnum.MATERIAL_NAME_ONE.subType());
        if (CollectionUtil.isEmpty(materialWarningsOne)) {
            log.info("物料名称异常subType=1预警为空");
        } else {
            List<String> ids = materialWarningsOne.stream().map(MaterialWarning::getId).collect(Collectors.toList());
            List<MaterialNameOneDTO> materialNameOneDTOS = warningMapper.materialNameOne(ids);
            List<MaterialNameOneDTO> dtoList = materialNameOneDTOS.stream().filter(item -> ObjectUtil.isNotNull(item.getMaterialId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(dtoList)) {
                Set<Integer> materialIds = dtoList.stream().map(MaterialNameOneDTO::getMaterialId).collect(Collectors.toSet());
                List<MaterialDto> materialDtos = materialService.listMaterialByIds(materialIds);
                if (CollectionUtil.isNotEmpty(materialDtos)) {
                    // 企业材料库中存在的材料ID
                    List<Integer> materialIdList = materialDtos.stream().filter(item -> StringUtils.isNotBlank(item.getMaterialName()))
                            .map(MaterialDto::getMaterialId).collect(Collectors.toList());
                    warningIds.addAll(dtoList.stream().filter(item -> materialIdList.contains(item.getMaterialId())).map(MaterialNameOneDTO::getId).collect(Collectors.toList()));
                }
            }
        }
        List<MaterialWarning> materialWarningsTwo = subTypeMap.get(WarningSubTypeEnum.MATERIAL_NAME_TWO.subType());
        if (CollectionUtil.isEmpty(materialWarningsTwo)) {
            log.info("物料名称异常subType=2预警为空");
        } else {
            List<String> ids = materialWarningsTwo.stream().map(MaterialWarning::getId).collect(Collectors.toList());
            warningIds.addAll(Optional.ofNullable(warningMapper.invalidWeightOne(ids)).orElse(Lists.newArrayList()));
        }
        log.info("物料名称异常预警自动处理 end...");
        super.autoCloseWarning(warningIds);
    }

    @Override
    public void run(String... args) throws Exception {
        super.addChildClass(WarningTypeEnum.MATERIAL_NAME.value(), this);
    }
}
