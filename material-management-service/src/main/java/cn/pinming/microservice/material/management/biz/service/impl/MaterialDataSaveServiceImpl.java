package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.pinming.material.v2.Material;
import cn.pinming.material.v2.MaterialClientBuilder;
import cn.pinming.microservice.material.management.biz.dto.MaterialMatchingScore;
import cn.pinming.microservice.material.management.biz.dto.PicUrlDTO;
import cn.pinming.microservice.material.management.biz.dto.ReceiptRecyclePushDTO;
import cn.pinming.microservice.material.management.biz.dto.SimpleContractDetailDTO;
import cn.pinming.microservice.material.management.biz.entity.*;
import cn.pinming.microservice.material.management.biz.enums.DeviationStatusEnum;
import cn.pinming.microservice.material.management.biz.enums.OcrMappingEnum;
import cn.pinming.microservice.material.management.biz.form.WordsMatchForm;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.biz.vo.ProjectVO;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.NoUtil;
import cn.pinming.microservice.material.management.common.util.PicUtil;
import cn.pinming.microservice.material.management.common.util.UUIDUtil;
import cn.pinming.microservice.material.management.common.util.WeighDuplicationUtil;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MaterialDataSaveServiceImpl implements IMaterialDataSaveService {

    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private WeighDuplicationUtil weighDuplicationUtil;
    @Resource
    private NoUtil noUtil;
    @Resource
    private ISupplierService supplierServiceProxy;
    @NacosValue("${imatchocr.url:http://imatchocr-ztcj-local-prod:8080}")
    private String iMatchOcrUlr;
    @NacosValue("${sdk.host:https://weighmaster.pinming.cn}")
    private String sdkHost;
    @Resource
    private PicUtil picUtil;
    @Resource
    private ISdkConfigService sdkConfigService;
    @Resource
    private IMaterialSendReceiveService materialSendReceiveService;
    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private IPurchaseContractService purchaseContractService;
    @Resource
    private IPurchaseContractDetailService contractDetailService;

    @Override
    public void OCRSync(ReceiptRecyclePushDTO dto, List<String> fileIds,Integer companyId, Integer projectId) {
        List<ReceiptRecyclePushDTO.ReceiptRecycleWeighDTO> weighList = dto.getWeighList();
        ReceiptRecyclePushDTO.ReceiptRecycleWeighDTO record1 = weighList.get(0);
        ReceiptRecyclePushDTO.ReceiptRecycleWeighDTO record2 = weighList.get(1);
        weighDuplicationUtil.judge(Arrays.asList(record1.getRecordId(), record2.getRecordId()));

        ReceiptRecyclePushDTO.ReceiptRecycleWeighDTO gross;
        ReceiptRecyclePushDTO.ReceiptRecycleWeighDTO tare;
        byte type = 1;
        if (record1.getWeight().compareTo(record2.getWeight()) > 0) {
            gross = record1;
            tare = record2;
        } else {
            gross = record2;
            tare = record1;
        }
        if (gross.getWeighTime().isAfter(tare.getWeighTime())) {
            type = 2;
        }

        MaterialSendReceive materialSendReceive = new MaterialSendReceive();
        materialSendReceive.setType(type);
        if (materialSendReceive.getType() == 1) {
            materialSendReceive.setReceiveNo(noUtil.getOCRReceiveNo(projectId));
        } else {
            materialSendReceive.setReceiveNo(noUtil.getOCRSendNo(projectId));
        }
        materialSendReceive.setId(UUIDUtil.randomUUIDWithoutConnector());
        materialSendReceive.setTruckNo(dto.getTruckNo());
        materialSendReceive.setProjectId(projectId);
        materialSendReceive.setCompanyId(companyId);
        // 冗余ReceiptRecycleDO的id
        materialSendReceive.setConfirmExtNo(String.valueOf(dto.getId()));

        MaterialData materialData = new MaterialData();
        // OCR识别结果
        String ocrMaterialName = null;
        String ocrMaterialSpec = null;
        String ocrSupplierName = null;
        if (CollUtil.isNotEmpty(dto.getModuleList())) {
            for (ReceiptRecyclePushDTO.ReceiptRecycleModuleDTO e : dto.getModuleList()) {
                if (e.getKeyName().equals(OcrMappingEnum.POSITION.getMapping())) {
                    materialData.setPosition(e.getKeyValue());
                }
                if (e.getKeyName().equals(OcrMappingEnum.WEIGHT_SEND.getMapping())) {
                    if (ObjectUtil.isNotNull(e.getKeyValue())) {
                        materialData.setWeightSend(new BigDecimal(e.getKeyValue()));
                    }
                }
                if (e.getKeyName().equals(OcrMappingEnum.GOODS.getMapping())) {
                    ocrMaterialName = e.getKeyValue();
                }
                if (e.getKeyName().equals(OcrMappingEnum.SPEC.getMapping())) {
                    ocrMaterialSpec = e.getKeyValue();
                }
                if (e.getKeyName().equals(OcrMappingEnum.SUPPLIER_NAME.getMapping())) {
                    materialData.setSupplierName(e.getKeyValue());
                }
            }
        }
        SimpleContractDetailDTO detailDTO = null;
        if (StrUtil.isNotBlank(dto.getContractId())) {
            // 有合同id
            PurchaseContract purchaseContract = purchaseContractService.getById(dto.getContractId());
            if (ObjectUtil.isNull(purchaseContract) || ObjectUtil.isNull(purchaseContract.getSupplierId())) {
                throw new BOException(BOExceptionEnum.DOCUMENT_ERROR);
            }
            materialData.setSupplierId(purchaseContract.getSupplierId());
            Supplier supplierDTO = supplierServiceProxy.getById(purchaseContract.getSupplierId());
            if (ObjectUtil.isNotNull(supplierDTO)) {
                materialData.setSupplierName(supplierDTO.getName());
            }
//            List<SimpleContractDetailDTO> purchaseContractDetailList = querySimpleContractDetailsByContractId(dto.getContractId());
//            List<String> contractMaterial = new ArrayList<>();
//            if (CollUtil.isNotEmpty(purchaseContractDetailList)) {
//                contractMaterial = purchaseContractDetailList.stream().map(e -> e.getMaterialName() + e.getMaterialSpec()).collect(Collectors.toList());
//            }

            // 语义相似度过滤
//            try {
//                List<MaterialMatchingScore> materialMatchingScores = materialMatching(ocrMaterialName + ocrMaterialSpec, contractMaterial);
//                log.info(materialMatchingScores.toString());
//                if (CollUtil.isNotEmpty(materialMatchingScores)) {
//                    List<MaterialMatchingScore> scores = materialMatchingScores.stream().filter(e -> new BigDecimal(e.getScore()).compareTo(new BigDecimal("0.95")) > 0).collect(Collectors.toList());
//                    if (CollUtil.isNotEmpty(scores) && scores.size() == 1) {
//                        MaterialMatchingScore materialMatchingScore = scores.get(0);
//                        detailDTO = purchaseContractDetailList.stream().filter(e -> StrUtil.format("{}{}", e.getMaterialName(), e.getMaterialSpec()).equals(materialMatchingScore.getMaterialName())).collect(Collectors.toList()).get(0);
//                        materialData.setMaterialId(detailDTO.getMaterialId());
//                        materialData.setCategoryId(detailDTO.getCategoryId());
//                        materialData.setContractDetailId(detailDTO.getContractDetailId());
//                        materialData.setWeightUnit(detailDTO.getUnit());
//                        materialData.setRatio(detailDTO.getConversionRate());
//                    }
//                    if (CollUtil.isEmpty(scores) || (CollUtil.isNotEmpty(scores) && scores.size() != 1)) {
//                        materialData.setMaterialName(ocrMaterialName + ocrMaterialSpec);
//                    }
//                } else {
//                    materialData.setMaterialName(ocrMaterialName + ocrMaterialSpec);
//                }
//            } catch (Exception ignored) {
//                log.info("语义匹配失败");
//            }
            materialData.setMaterialName(ocrMaterialName + ocrMaterialSpec);
        } else {
            // 无other_id
            materialData.setMaterialName(ocrMaterialName + ocrMaterialSpec);
        }
        materialData.setReceiveId(materialSendReceive.getId());
        materialData.setWeightGross(gross.getWeight());
        materialData.setWeightTare(tare.getWeight());
        materialData.setWeightDeduct(BigDecimal.ZERO);
        materialData.setWeightNet(NumberUtil.sub(materialData.getWeightGross(), materialData.getWeightTare()));
        materialData.setMoistureContent(BigDecimal.ZERO);
        materialData.setWeightActual(materialData.getWeightNet());
        // 实际数量
        if (materialData.getRatio() != null) {
            materialData.setActualCount(NumberUtil.div(materialData.getWeightActual(), NumberUtil.div(materialData.getRatio(), 1000)));
            // 偏差率 + 偏差状态
            boolean flag2 = materialData.getWeightSend() != null && NumberUtil.isGreater(materialData.getWeightSend(), BigDecimal.ZERO);
            if (flag2) {
                BigDecimal deviation = NumberUtil.mul(NumberUtil.div(NumberUtil.sub(materialData.getActualCount(), materialData.getWeightSend()), materialData.getWeightSend(), 4), 100);
                materialData.setDeviationRate(deviation);
                if (ObjectUtil.isNotNull(detailDTO)) {
                    BigDecimal deviationCeiling = detailDTO.getDeviationCeiling();
                    BigDecimal deviationFloor = detailDTO.getDeviationFloor();

                    if (ObjectUtil.isNotNull(deviationCeiling) && ObjectUtil.isNotNull(deviationFloor) && ObjectUtil.isNotNull(detailDTO.getDeviationCalculate()) && detailDTO.getDeviationCalculate() == 1) {
                        int flag;
                        int flag1;
                        flag = deviation.compareTo(deviationFloor);
                        flag1 = deviation.compareTo(deviationCeiling);
                        if (flag < 0) {
                            materialData.setDeviationStatus(DeviationStatusEnum.NEGATIVEDIFFERENCE.value());
                        }
                        if (flag1 > 0) {
                            materialData.setDeviationStatus(DeviationStatusEnum.POSITIVEDIFFERENCE.value());
                        }
                        if (flag >= 0 && flag1 <= 0) {
                            materialData.setDeviationStatus(DeviationStatusEnum.NORMAL.value());
                        }
                    }
                }
            }
            // 实收数量
            if (materialData.getActualCount() != null && materialData.getActualReceive() == null) {
                materialData.setActualReceive(materialData.getActualCount());
                boolean flag = materialData.getDeviationStatus() != null && (materialData.getDeviationStatus() == DeviationStatusEnum.POSITIVEDIFFERENCE.value() || materialData.getDeviationStatus() == DeviationStatusEnum.NORMAL.value());
                if (flag) {
                    materialData.setActualReceive(materialData.getWeightSend());
                }
            }
        }
        materialData.setRecordId1(record1.getRecordId());
        materialData.setRecordId2(record2.getRecordId());
        materialData.setIsDevice((byte) 2);
        materialData.setWeighId(UUIDUtil.randomUUIDWithoutConnector());
//        materialData.setConfirmType(3);
        List<PicUrlDTO> recyclePicUrls = dto.getRecyclePicUrls();
        if (CollUtil.isNotEmpty(recyclePicUrls)) {
            List<String> uuids = recyclePicUrls.stream().map(e -> {
                try {
                    return picUtil.downloadAndUploadFile(e.getDownloadUrl(), UUIDUtil.randomUUID(), null);
                } catch (IOException ex) {
                    log.info("下载单据照片异常");
                    return null;
                }
            }).collect(Collectors.toList());
            fileIds.addAll(uuids);
            materialData.setDocumentPic(String.join(",", uuids));
        }
        materialData.setEnterTime(gross.getWeighTime().
                isBefore(tare.getWeighTime()) ? gross.getWeighTime() : tare.getWeighTime());
        materialData.setLeaveTime(gross.getWeighTime().
                isAfter(tare.getWeighTime()) ? gross.getWeighTime() : tare.getWeighTime());
        if (gross.getWeighTime().
                isBefore(tare.getWeighTime())) {
            if (CollUtil.isNotEmpty(gross.getFileIdUrls())) {
                List<String> uuids = gross.getFileIdUrls().stream().map(e -> {
                    try {
                        return picUtil.downloadAndUploadFile(e.getDownloadUrl(), UUIDUtil.randomUUID(), null);
                    } catch (IOException ex) {
                        log.info("下载进场照片异常");
                        return null;
                    }
                }).collect(Collectors.toList());
                fileIds.addAll(uuids);
                materialData.setEnterPic(StrUtil.join(",", uuids));
            }
            if (CollUtil.isNotEmpty(tare.getFileIdUrls())) {
                List<String> uuids = tare.getFileIdUrls().stream().map(e -> {
                    try {
                        return picUtil.downloadAndUploadFile(e.getDownloadUrl(), UUIDUtil.randomUUID(), null);
                    } catch (IOException ex) {
                        log.info("下载出场照片异常 {}", ex.getMessage());
                        return null;
                    }
                }).collect(Collectors.toList());
                fileIds.addAll(uuids);
                materialData.setLeavePic(StrUtil.join(",", uuids));
            }
        } else {
            if (CollUtil.isNotEmpty(tare.getFileIdUrls())) {
                List<String> uuids = tare.getFileIdUrls().stream().map(e -> {
                    try {
                        return picUtil.downloadAndUploadFile(e.getDownloadUrl(), UUIDUtil.randomUUID(), null);
                    } catch (IOException ex) {
                        log.info("下载进场照片异常 {}", ex.getMessage());
                        return null;
                    }
                }).collect(Collectors.toList());
                fileIds.addAll(uuids);
                materialData.setEnterPic(StrUtil.join(",", uuids));
            }
            if (CollUtil.isNotEmpty(gross.getFileIdUrls())) {
                List<String> uuids = gross.getFileIdUrls().stream().map(e -> {
                    try {
                        return picUtil.downloadAndUploadFile(e.getDownloadUrl(), UUIDUtil.randomUUID(), null);
                    } catch (IOException ex) {
                        log.info("下载出场照片异常");
                        return null;
                    }
                }).collect(Collectors.toList());
                fileIds.addAll(uuids);
                materialData.setLeavePic(StrUtil.join(",", uuids));
            }
        }
        materialData.setReceiveTime(materialData.getLeaveTime());
        materialData.setCompanyId(companyId);
        materialData.setProjectId(projectId);

        materialSendReceiveService.save(materialSendReceive);
        materialDataService.save(materialData);
        log.error("OCR保存数据完成 开始组装sdk数据");

    }

    /**
     * 语义相似度匹配
     *
     * @param material,materialList
     * @return
     */
    private List<MaterialMatchingScore> materialMatching(String material, List<String> materialList) {
        WordsMatchForm form = new WordsMatchForm();
        form.setWord(material);
        form.setWords(materialList);
        try {
            String res = HttpUtil.post(iMatchOcrUlr, JSON.toJSONString(form));
            return JSONObject.parseArray(res, MaterialMatchingScore.class);
        } catch (Exception e) {
            throw new BOException(BOExceptionEnum.WORDS_MATCH_ERROR);
        }
    }

    public List<SimpleContractDetailDTO> querySimpleContractDetailsByContractId(String contractId) {
        List<PurchaseContractDetail> list = contractDetailService.lambdaQuery()
                .in(PurchaseContractDetail::getContractId, contractId)
                .list();

        List<SimpleContractDetailDTO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            result = list.stream().map(e -> {
                SimpleContractDetailDTO simpleContractDetailDTO = new SimpleContractDetailDTO();
                BeanUtils.copyProperties(e, simpleContractDetailDTO);
                simpleContractDetailDTO.setContractDetailId(e.getId());
                return simpleContractDetailDTO;
            }).collect(Collectors.toList());
        }

        return result;
    }
}
