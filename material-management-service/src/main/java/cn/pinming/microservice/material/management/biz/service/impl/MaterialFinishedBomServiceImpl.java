package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.OrderDetailDTO;
import cn.pinming.microservice.material.management.biz.dto.ParameterRequirementsDTO;
import cn.pinming.microservice.material.management.biz.entity.IngredientList;
import cn.pinming.microservice.material.management.biz.entity.MaterialFinishedBom;
import cn.pinming.microservice.material.management.biz.enums.IngredientListEnum;
import cn.pinming.microservice.material.management.biz.form.MaterialBomForm;
import cn.pinming.microservice.material.management.biz.mapper.MaterialFinishedBomMapper;
import cn.pinming.microservice.material.management.biz.query.BaseQuery;
import cn.pinming.microservice.material.management.biz.service.IIngredientListService;
import cn.pinming.microservice.material.management.biz.service.IMaterialFinishedBomService;
import cn.pinming.microservice.material.management.biz.service.IPurchaseOrderService;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material_unit.api.material.dto.InfoItem;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialCategoryDto;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.material_unit.api.material.dto.SelectItem;
import cn.pinming.microservice.material_unit.api.material.service.MaterialService;
import cn.pinming.v2.company.api.dto.EmployeeDetailDto;
import cn.pinming.v2.company.api.dto.EmployeeDetailQueryDto;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.compress.utils.Sets;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Time;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 成品BOM包设置 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 14:31:34o
 */
@Service
public class MaterialFinishedBomServiceImpl extends ServiceImpl<MaterialFinishedBomMapper, MaterialFinishedBom> implements IMaterialFinishedBomService {

    @Resource
    private MaterialFinishedBomMapper materialFinishedBomMapper;

    @Resource
    private MaterialServiceProxy materialServiceProxy;

    @Resource
    private AuthUserHolder authUserHolder;

    @Resource
    private IPurchaseOrderService purchaseOrderService;

    @Resource
    private EmployeeServiceProxy employeeServiceProxy;

    @Resource
    private IIngredientListService ingredientListService;

    @Resource
    private IngredientListServiceImpl ingredientListServiceImpl;

    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Reference
    private MaterialService materialService;

    @Override
    public void saveOrUpdateBom(MaterialBomForm materialBomForm) {

        // 其他材料品种不能重复
        String[] otherCategoryMaterialsArray = materialBomForm.getCategoryMaterials().split(StrUtil.COMMA);
        int sum = Arrays.stream(otherCategoryMaterialsArray).distinct().mapToInt(i -> 1).sum();
        if (otherCategoryMaterialsArray.length != sum) {
            throw new BOException(BOExceptionEnum.FINISHED_CATEGORY_MATERIAL_REPEAT);
        }

        String bomId = materialBomForm.getId();
        if (StrUtil.isNotEmpty(bomId)) {
            MaterialFinishedBom materialFinishedBom = materialFinishedBomMapper.selectById(bomId);
            Optional.ofNullable(materialFinishedBom).ifPresent(finishedBom -> {
                Map<Integer, Boolean> relationIngredientApplyBillMap = relationIngredientApplyBillMap(Collections.singletonList(materialFinishedBom.getCategoryId()));
                Boolean relation = relationIngredientApplyBillMap.getOrDefault(materialFinishedBom.getCategoryId(), Boolean.FALSE);
                if (relation) {
                    // 关联配料单只能增加不能删
                    String categoryMaterials = materialFinishedBom.getCategoryMaterials();
                    String categoryMaterialsForm = materialBomForm.getCategoryMaterials();
                    String[] categoryMaterialsArray = categoryMaterials.split(StrUtil.COMMA);
                    String[] categoryMaterialsFormArray = categoryMaterialsForm.split(StrUtil.COMMA);
                    List<String> categoryMaterialsFormList = Arrays.asList(categoryMaterialsFormArray);
                    List<String> categoryMaterialsList = Arrays.asList(categoryMaterialsArray);
                    boolean allExist = categoryMaterialsFormList.containsAll(categoryMaterialsList);
                    if (!allExist) {
                        throw new BOException(BOExceptionEnum.UPDATE_RELATION_INGREDIENT);
                    }
                    // 关联配料单是否含水不可修改
                    if (!materialFinishedBom.getExistWater().equals(materialBomForm.getExistWater())) {
                        throw new BOException(BOExceptionEnum.FINISHED_CATEGORY_WATER_NO_UPDATE);
                    }
                }
                BeanUtils.copyProperties(materialBomForm, finishedBom);
                materialFinishedBomMapper.updateById(materialFinishedBom);
            });
        } else {
            MaterialFinishedBom materialFinishedBom = new MaterialFinishedBom();
            List<Integer> existCategories = listExistCategories();
            // 重复二级分类品种
            if (existCategories.contains(materialBomForm.getCategoryId())) {
                throw new BOException(BOExceptionEnum.FINISHED_CATEGORY_ID_REPEAT);
            }

            // 其他参数不能与必要参数重复
            String otherParameter = materialBomForm.getOtherParameter();
            List<String> otherParameterList = Lists.newArrayList();
            if (StrUtil.isNotEmpty(otherParameter)) {
                otherParameterList = Arrays.asList(otherParameter.split(StrUtil.COMMA));
            }
            // 分类必要参数列表
            List<InfoItem> requirementList = purchaseOrderService.parameterRequirements(materialBomForm.getCategoryId());
            List<String> finalOtherParameterList = otherParameterList;
            Optional.ofNullable(requirementList).ifPresent(requirements -> {
                boolean exist = requirements.stream().map(InfoItem::getParamName).filter(Objects::nonNull).anyMatch(finalOtherParameterList::contains);
                if (exist) {
                    throw new BOException(BOExceptionEnum.PARAMETER_REPEAT);
                }
            });

            BeanUtils.copyProperties(materialBomForm, materialFinishedBom);
            materialFinishedBomMapper.insert(materialFinishedBom);
        }
    }

    @Override
    public IPage<MaterialBomVO> listMaterialFinishBom(BaseQuery baseQuery) {

        IPage<MaterialFinishedBom> page = materialFinishedBomMapper.selectPageByQuery(baseQuery);
        List<MaterialFinishedBom> materialFinishedBomList = page.getRecords();

        if (CollUtil.isNotEmpty(materialFinishedBomList)) {
            List<Integer> categoryIds = materialFinishedBomList.stream().map(MaterialFinishedBom::getCategoryId).filter(Objects::nonNull).collect(Collectors.toList());
            List<MaterialCategoryDto> materialCategoryDtoList = materialServiceProxy.listCategoryByIds(authUserHolder.getCurrentUser().getCurrentCompanyId(), categoryIds);
            Map<Integer, MaterialCategoryDto> categoryDtoMap = materialCategoryDtoList.stream().collect(Collectors.toMap(MaterialCategoryDto::getMaterialCategoryId, e -> e));

            // 所有分类集合
            Set<Integer> otherCategoryIds = Sets.newHashSet();
            materialFinishedBomList.forEach(materialFinishedBom -> {
                String categoryMaterials = materialFinishedBom.getCategoryMaterials();
                if (StrUtil.isNotEmpty(categoryMaterials)) {
                    String[] categoryMaterial = categoryMaterials.split(StrUtil.COMMA);
                    Arrays.stream(categoryMaterial).forEach(categoryMaterialName -> {
                        String[] splitArr = categoryMaterialName.split(StrUtil.SLASH);
                        Integer categoryId = Integer.parseInt(splitArr[0]);
                        otherCategoryIds.add(categoryId);
                    });
                }
            });

            // 封装数据
            Map<Integer, MaterialCategoryDto> otherCategoryMap = Maps.newHashMap();
            if (CollUtil.isNotEmpty(otherCategoryIds)) {
                List<MaterialCategoryDto> categoryDtoList = materialServiceProxy.listCategoryByIds(authUserHolder.getCurrentUser().getCurrentCompanyId(), otherCategoryIds);
                otherCategoryMap = categoryDtoList.stream().collect(Collectors.toMap(MaterialCategoryDto::getMaterialCategoryId, e -> e));
            }

            List<String> creatorList = materialFinishedBomList.stream().map(MaterialFinishedBom::getCreateId).collect(Collectors.toList());
            List<String> updaterList = materialFinishedBomList.stream().map(MaterialFinishedBom::getUpdateId).collect(Collectors.toList());
            // 并集
            List<String> personId = Stream.of(creatorList, updaterList).flatMap(Collection::stream).distinct().collect(Collectors.toList());
            EmployeeDetailQueryDto employeeDetailQueryDto = new EmployeeDetailQueryDto();
            employeeDetailQueryDto.setMemberIdList(personId);
            employeeDetailQueryDto.setCompanyId(authUserHolder.getCurrentUser().getCurrentCompanyId());
            List<EmployeeDetailDto> employeeDetailDtos = employeeServiceProxy.employeeList(employeeDetailQueryDto);
            Map<String, String> personMap = Maps.newHashMap();
            if (CollUtil.isNotEmpty(employeeDetailDtos)) {
                personMap = employeeDetailDtos.stream().collect(Collectors.toMap(EmployeeDetailDto::getMemberId, EmployeeDetailDto::getMemberName));
            }

            Map<Integer, MaterialCategoryDto> finalOtherCategoryMap = otherCategoryMap;
            Map<String, String> finalPersonMap = personMap;
            Map<Integer, Boolean> relationIngredientApplyBillMap = relationIngredientApplyBillMap(categoryIds);
            List<MaterialBomVO> result = materialFinishedBomList.stream().map(materialFinishedBom -> {
                MaterialBomVO materialBomVO = new MaterialBomVO();
                BeanUtils.copyProperties(materialFinishedBom, materialBomVO);
                MaterialCategoryDto materialCategoryDto = categoryDtoMap.get(materialBomVO.getCategoryId());
                materialBomVO.setCategoryName(materialCategoryDto.getMaterialCategoryName());
                String categoryMaterials = materialBomVO.getCategoryMaterials();

                // 封装其他品种材料组合
                String[] allCategoryMaterialName = categoryMaterials.split(StrUtil.COMMA);
                List<String> categoryMaterialNameList = Lists.newArrayList();
                Arrays.stream(allCategoryMaterialName).forEach(categoryMaterialName -> {
                    String[] nameArr = categoryMaterialName.split(StrUtil.SLASH);
                    Integer categoryId = Integer.valueOf(nameArr[0]);
                    String materialName = nameArr[1];
                    MaterialCategoryDto categoryDto = finalOtherCategoryMap.get(categoryId);
                    categoryMaterialNameList.add(categoryDto.getMaterialCategoryName() + StrUtil.SLASH + materialName);
                });
                materialBomVO.setCategoryMaterialsName(categoryMaterialNameList);
                materialBomVO.setCreater(finalPersonMap.get(materialFinishedBom.getCreateId()));
                materialBomVO.setUpdater(finalPersonMap.get(materialFinishedBom.getUpdateId()));
                materialBomVO.setRelationIngredient(relationIngredientApplyBillMap.getOrDefault(materialFinishedBom.getCategoryId(), Boolean.FALSE));
                return materialBomVO;
            }).collect(Collectors.toList());

            IPage<MaterialBomVO> list = new Page<>();
            BeanUtils.copyProperties(page, list);
            list.setRecords(result);
            return list;
        }
        return new Page<>();
    }

    @Override
    public List<Integer> listExistCategories() {
        return listCurrentProjectMaterialFinishedBom().stream().map(MaterialFinishedBom::getCategoryId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> listOtherParameter() {
        return listCurrentProjectMaterialFinishedBom().stream().map(MaterialFinishedBom::getOtherParameter)
                .filter(Objects::nonNull).flatMap(s -> Arrays.stream(s.split(StrUtil.COMMA))).distinct().collect(Collectors.toList());
    }

    @Override
    public void deleteBom(String id) {
        MaterialFinishedBom materialFinishedBom = this.getById(id);
        Map<Integer, Boolean> relationIngredientApplyBillMap = relationIngredientApplyBillMap(Collections.singletonList(materialFinishedBom.getCategoryId()));
        Boolean exist = relationIngredientApplyBillMap.getOrDefault(materialFinishedBom.getCategoryId(), Boolean.FALSE);
        if (exist) {
            throw new BOException(BOExceptionEnum.FINISHED_CATEGORY_ALREADY_RELATION);
        }
        removeById(materialFinishedBom.getId());
    }

    @Override
    public IngredientListVO seek(String mixingPlantOrderDetailId) {
        IngredientListVO ingredientListVO = new IngredientListVO();
        ApplyVO applyVO = new ApplyVO();
        List<TechnicalRequirementVO> requirementVOList = new ArrayList<>();
        List<MaterialReportVO> reportVOList = new ArrayList<>();
        List<InfoItemVO> infoItems = new ArrayList<>();
        List<InfoItemVO> resultItems = new ArrayList<>();

        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
        Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();

        OrderDetailDTO bom = this.getBaseMapper().selectOrderDetail(mixingPlantOrderDetailId, companyId, projectId);
        if (ObjectUtil.isNotNull(bom)) {
            if (StrUtil.isNotBlank(bom.getCategoryMaterials())) {
                Map<Integer, MaterialCategoryDto> materialCategoryMap = new HashMap<>();
                List<String> bomMaterial = StrUtil.split(bom.getCategoryMaterials(), ",");

                List<Integer> categoryIdList = bomMaterial.stream().map(item -> Integer.valueOf(Arrays.stream(item.split("/")).reduce((first, second) -> first).orElse(null))).distinct().collect(Collectors.toList());
                List<MaterialCategoryDto> materialCategoryDtos = materialService.listCategoryByIds(companyId, categoryIdList);
                if (CollUtil.isNotEmpty(materialCategoryDtos)) {
                    materialCategoryMap = materialCategoryDtos.stream().filter(e -> e.getEnable() == true).collect(Collectors.toMap(MaterialCategoryDto::getMaterialCategoryId, e -> e));
                }

                Map<Integer, MaterialCategoryDto> finalMaterialCategoryMap = materialCategoryMap;
                reportVOList = bomMaterial.stream().map(e -> {
                    MaterialReportVO materialReportVO = new MaterialReportVO();

                    String a = Arrays.stream(e.split("/")).reduce((first, second) -> first).orElse(null);
                    String b = Arrays.stream(e.split("/")).reduce((first, second) -> second).orElse(null);
                    if (CollUtil.isNotEmpty(finalMaterialCategoryMap)) {
                        MaterialCategoryDto materialCategoryDto = finalMaterialCategoryMap.get(Integer.valueOf(a));
                        if (ObjectUtil.isNotNull(materialCategoryDto)) {
                            materialReportVO.setMaterialCategoryId(Integer.valueOf(a));
                            materialReportVO.setMaterialCategoryName(materialCategoryDto.getMaterialCategoryName());
                            materialReportVO.setMaterialName(b);
                        }
                    }

                    return materialReportVO;
                }).collect(Collectors.toList());

                applyVO.setMaterialReportVOList(reportVOList);
            }

            if (bom.getExistWater() == 1) {
                MaterialReportVO materialReportVO = new MaterialReportVO();
                materialReportVO.setMaterialName("水");
                reportVOList.add(materialReportVO);
                applyVO.setMaterialReportVOList(reportVOList);
            }

            SimpleConstructionProjectDto simpleProject = projectServiceProxy.findSimpleProject(bom.getProjectId());
            MaterialDto materialDto = materialServiceProxy.materialById(bom.getMaterialId());

            if (ObjectUtil.isNotNull(simpleProject)) {
                applyVO.setCompanyTitle(simpleProject.getSubCompanyName());
                applyVO.setProjectTitle(simpleProject.getProjectTitle());
            }

            if (ObjectUtil.isNotNull(materialDto)) {
                applyVO.setMaterial(StrUtil.format("{}/{}", materialDto.getMaterialName(), materialDto.getMaterialSpec()));
                ingredientListVO.setCategoryId(materialDto.getMaterialCategoryId());
            }

            applyVO.setPosition(bom.getPosition());

            ingredientListVO.setOrderNo(bom.getOrderNo());
            ingredientListVO.setMaterialId(bom.getMaterialId());
            ingredientListVO.setUnit(bom.getUnit());
            ingredientListVO.setPosition(applyVO.getPosition());

            if (bom.getCategoryId() != null) {
                infoItems = parameterRequirements(bom.getCategoryId());
                resultItems.addAll(infoItems);
            }

            if (StrUtil.isNotBlank(bom.getParameterRequirements())) {
                List<InfoItemVO> itemVOList = JSONObject.parseArray(bom.getParameterRequirements(), InfoItemVO.class);

                Map<String, InfoItem> map = ingredientListServiceImpl.dictionary(bom.getCategoryId());

                Map<String, InfoItem> finalMap = map;
                itemVOList.stream().forEach(e -> {
                    if (e.getParamType().equals("2") && CollUtil.isNotEmpty(finalMap)) {
                        e.setSelectItem(finalMap.get(e.getParamCode()).getSelectItem());
                    }
                    e.setParamName(finalMap.get(e.getParamCode()).getParamName());
                    e.setKind((byte)2);
                });

                if (CollUtil.isNotEmpty(infoItems)) {
                    resultItems.addAll(itemVOList);
                }
            }

        }

        applyVO.setInfoItems(resultItems);
        ingredientListVO.setApplyVO(applyVO);

        return ingredientListVO;
    }

    private LoadingCache<Integer, List<InfoItemVO>> cache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .refreshAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<Integer, List<InfoItemVO>>() {
                public List<InfoItemVO> load(Integer categoryId) throws Exception {
                    List<InfoItemVO> infoItems = new ArrayList<>();
                    Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
                    Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();

                    MaterialFinishedBom bom = materialFinishedBomMapper.selectMaterialList(categoryId, companyId, projectId);
                    if (ObjectUtil.isNotNull(bom) && StrUtil.isNotBlank(bom.getOtherParameter())) {
                        List<String> bomRequirement = StrUtil.split(bom.getOtherParameter(), ",");

                        bomRequirement.stream().forEach(e -> {
                            InfoItemVO infoItemVO = new InfoItemVO();

                            infoItemVO.setParamType("1");
                            infoItemVO.setParamName(e);
                            infoItemVO.setKind((byte) 1);

                            infoItems.add(infoItemVO);
                        });
                    }

                    return infoItems;
                }
            });

    public List<InfoItemVO> parameterRequirements(Integer categoryId) {
        try {
            return cache.get(categoryId);
        } catch (Exception e) {
            log.error("parameterRequirements cache load error.", e);
        }
        return null;
    }

    /**
     * 当前项目下的bom列表
     *
     * @return 列表
     */
    private List<MaterialFinishedBom> listCurrentProjectMaterialFinishedBom() {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer projectId = currentUser.getCurrentProjectId();
        List<MaterialFinishedBom> finishedBomList = this.lambdaQuery().eq(MaterialFinishedBom::getProjectId, projectId).list();
        return CollUtil.isNotEmpty(finishedBomList) ? finishedBomList : Lists.newArrayList();
    }

    /**
     * 分类是否关联配料申请单
     *
     * @param categoryIds 成品分类id集合
     * @return 分类id-->是否关联
     */
    private Map<Integer, Boolean> relationIngredientApplyBillMap(List<Integer> categoryIds) {
        Map<Integer, Boolean> resMap = Maps.newHashMap();
        if (CollUtil.isEmpty(categoryIds)) {
            return resMap;
        }
        AuthUser currentUser = authUserHolder.getCurrentUser();
        List<IngredientList> ingredientList = ingredientListService.lambdaQuery().eq(IngredientList::getProjectId, currentUser.getCurrentProjectId()).list();
        if (CollUtil.isNotEmpty(ingredientList)) {
            List<Integer> alreadyUseCategoryIdList = ingredientList.stream().map(IngredientList::getCategoryId).filter(Objects::nonNull).collect(Collectors.toList());
            resMap = categoryIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(e -> e, alreadyUseCategoryIdList::contains));
        }
        return resMap;
    }

}
