package cn.pinming.microservice.material.management.biz.timetask;

import cn.pinming.microservice.material.management.biz.service.ISupplierService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 定时任务 更新供应商信息.
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2022/12/15 13:25
 */
@Component
@Slf4j
public class SupplierTimedTask {

    @Resource
    private ISupplierService supplierService;

    @Scheduled(cron = "0 0 0 * * ? ")
    public void sync() {
        log.info("供应商每日更新定时任务启动");
        supplierService.updateSupplierList();
        log.info("供应商每日更新定时任务结束");
    }
}
