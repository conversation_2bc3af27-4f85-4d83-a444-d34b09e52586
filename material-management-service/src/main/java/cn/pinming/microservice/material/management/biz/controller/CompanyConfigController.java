package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.form.CompanyConfigForm;
import cn.pinming.microservice.material.management.biz.service.ICompanyConfigService;
import cn.pinming.microservice.material.management.biz.vo.CompanyConfigVO;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 项目配置表 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-13 10:28:23
 */
@Api(tags = "企业配置-controller",value = "zh")
@RestController
@RequestMapping("/api/biz/company-config")
public class CompanyConfigController {

    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private ICompanyConfigService companyConfigService;

    @ApiOperation("新增、编辑配置")
    @PostMapping("/saveOrUpdate")
    @Log(title = "新增、编辑配置", businessType = BusinessType.INSERTORUPDATE)
    public ResponseEntity<Response> saveOrUpdate(@Validated @Valid @RequestBody CompanyConfigForm form){
        AuthUser user = authUserHolder.getCurrentUser();
        form.setCompanyId(user.getCurrentCompanyId());
        companyConfigService.saveOrUpdateConfig(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "回显",response = CompanyConfigVO.class)
    @GetMapping("/show/{type}")
    @Log(title = "回显", businessType = BusinessType.QUERY)
    public ResponseEntity<Response> show(@PathVariable("type")Byte type){
        AuthUser user = authUserHolder.getCurrentUser();
        CompanyConfigVO vo = companyConfigService.show(type,user);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }
}
