package cn.pinming.microservice.material.management.biz.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.dto.QrcodeRedisDTO;
import cn.pinming.microservice.material.management.biz.enums.PurchaseOrderStatusEnum;
import cn.pinming.microservice.material.management.biz.form.PurchaseOrderForm;
import cn.pinming.microservice.material.management.biz.mapper.PurchaseOrderDetailMapper;
import cn.pinming.microservice.material.management.biz.query.PurchaseOrderQuery;
import cn.pinming.microservice.material.management.biz.service.IPurchaseContractService;
import cn.pinming.microservice.material.management.biz.service.IPurchaseOrderDetailService;
import cn.pinming.microservice.material.management.biz.service.IPurchaseOrderService;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.AppConstant;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import cn.pinming.microservice.material.management.common.util.ExcelUtils;
import cn.pinming.microservice.material.management.common.util.RedisUtil;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material_unit.api.material.dto.InfoItem;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.PageList;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepoove.poi.data.PictureRenderData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <p>
 * 采购单 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Api(tags = "采购单", value = "zh")
@RestController
@RequestMapping("/api/purchase/order")
@Slf4j
@AllArgsConstructor
public class PurchaseOrderController {
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;

    public final IPurchaseOrderService orderService;

    public final IPurchaseOrderDetailService orderDetailService;

    public final IPurchaseContractService contractService;

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ProjectServiceProxy projectServiceProxy;

    @ApiOperation(value = "列表", response = PurchaseOrderVO.class)
    @Log(title = "列表", businessType = BusinessType.QUERY)
    @PostMapping("/list")
    public ResponseEntity<Response> list(@RequestBody PurchaseOrderQuery query) {
        AuthUser user = authUserHolder.getCurrentUser();
        if (user.getCurrentDepartmentId() != null) {
            query.setProjectIds(projectServiceProxy.getAllProjectIdByCompanyDeptId(user.getCurrentCompanyId(), user.getCurrentDepartmentId()));
            if (CollUtil.isEmpty(query.getProjectIds())) {
                return ResponseEntity.ok(new SuccessResponse(new Page<>()));
            }
        }
        IPage<?> page = orderService.pageListByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation("保存")
    @Log(title = "保存", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseEntity<Response> save(@RequestBody @Validated @Valid PurchaseOrderForm form) {
        orderService.savePurchaseOrder(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation("删除")
    @Log(title = "删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public ResponseEntity<Response> delete(@PathVariable String id) {
        orderService.deletePurchaseOrder(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "修改-详情", response = PurchaseOrderVO.class)
    @Log(title = "修改-详情", businessType = BusinessType.QUERY)
    @GetMapping("/{id}/update")
    public ResponseEntity<Response> updateDetail(@PathVariable String id) {
        AuthUser user = authUserHolder.getCurrentUser();
        PurchaseOrderVO resultVO = orderService.queryUpdateDetailById(id, user.getCurrentCompanyId());
        return ResponseEntity.ok(new SuccessResponse(resultVO));
    }

    @ApiOperation(value = "使用部位", response = PurchaseOrderVO.class)
    @Log(title = "使用部位", businessType = BusinessType.QUERY)
    @GetMapping("/use/part")
    public ResponseEntity<Response> usePart(@RequestParam(value = "remark", required = false) String remark) {
        AuthUser user = authUserHolder.getCurrentUser();
        List<String> remakes = orderDetailService.queryHistoryUsePartByProjectId(user.getCurrentProjectId(), remark);
        return ResponseEntity.ok(new SuccessResponse(remakes));
    }

    @ApiOperation(value = "参数要求", response = PurchaseOrderVO.class)
    @Log(title = "参数要求", businessType = BusinessType.QUERY)
    @GetMapping("/param")
    public ResponseEntity<Response> parameterRequirements(@RequestParam(value = "categoryId") Integer categoryId) {
        List<InfoItem> infoItems = orderService.parameterRequirements(categoryId);
        return ResponseEntity.ok(new SuccessResponse(infoItems));
    }

    @ApiOperation(value = "上次创建采购单的收货信息", response = PurchaseOrderVO.class)
    @Log(title = "上次创建采购单的收货信息", businessType = BusinessType.QUERY)
    @GetMapping("/last/receive/info")
    public ResponseEntity<Response> lastReceiveInfo() {
        PurchaseOrderReceiveInfoVO purchaseOrderReceiveInfoVO = orderService.lastReceiveInfo();
        return ResponseEntity.ok(new SuccessResponse(purchaseOrderReceiveInfoVO));
    }

    @ApiOperation("修改")
    @Log(title = "修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public ResponseEntity<Response> update(@RequestBody @Validated @Valid PurchaseOrderForm form) {
        orderService.updatePurchaseOrder(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "详情", response = PurchaseOrderVO.class)
    @Log(title = "详情", businessType = BusinessType.QUERY)
    @PostMapping("/{id}/detail")
    public ResponseEntity<Response> detail(@PathVariable String id) {
        AuthUser user = authUserHolder.getCurrentUser();
        PurchaseOrderVO resultVO = orderService.queryDetailById(id, user.getCurrentCompanyId());
        return ResponseEntity.ok(new SuccessResponse(resultVO));
    }

    @ApiOperation("取消")
    @Log(title = "取消", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/cancel")
    public ResponseEntity<Response> cancel(@PathVariable String id) {
        //待复核阶段才可以取消
        orderService.cancelById(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "所属合同列表", response = PurchaseContractVO.class)
    @Log(title = "所属合同列表", businessType = BusinessType.QUERY)
    @GetMapping("/contract")
    public ResponseEntity<Response> contract(@RequestParam(required = false) String contractName) {
        List<PurchaseContractVO> list = contractService.listContract(contractName);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "采购单类型统计", response = PurchaseOrderStatusVO.class)
    @Log(title = "采购单类型统计", businessType = BusinessType.QUERY)
    @GetMapping("/status")
    public ResponseEntity<Response> status() {
        List<PurchaseOrderStatusVO> list = contractService.listOrderStatus();
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "采购单PDF")
    @GetMapping("/pdf/{id}")
    public ResponseEntity<Response> pdf(@PathVariable String id) {
        String url = orderService.getPurchaseOrderUrl(id);
        return ResponseEntity.ok(new SuccessResponse(url));
    }

    @ApiOperation(value = "打印此页面")
    @Log(title = "导出收料明细", businessType = BusinessType.QUERY)
    @GetMapping("/print/{id}")
    public ResponseEntity<SuccessResponse> print(@PathVariable String id, HttpServletResponse response) {
        String url = orderService.getPrintPurchaseOrderUrl(id);
        return ResponseEntity.ok(new SuccessResponse(url));
    }

    @ApiOperation(value = "采购单详情终极版~~",response = PurchaseOrderVO.class)
    @GetMapping("/finalDetail/{id}")
    public ResponseEntity<Response> finalDetail(@PathVariable String id) {
        AuthUser user = authUserHolder.getCurrentUser();
        PurchaseOrderVO resultVO = orderService.finalDetailById(id, user.getCurrentCompanyId());
        return ResponseEntity.ok(new SuccessResponse(resultVO));
    }

    @ApiOperation(value = "根据采购单id和材料id获取信息", response = GoodsSimpleVO.class)
    @GetMapping("/seek/{purchaseId}/{materialId}")
    public ResponseEntity<Response> seek(@PathVariable("purchaseId") String purchaseId, @PathVariable("materialId") Integer materialId) {
        GoodsSimpleVO vo = purchaseOrderDetailMapper.selectInfo(purchaseId, materialId);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "查找采购单", response = PurchaseSimpleVO.class)
    @Log(title = "查找采购单", businessType = BusinessType.QUERY)
    @GetMapping("/find")
    public ResponseEntity<Response> find(@RequestParam String pruNo, @RequestParam Boolean scan) {
        PurchaseSimpleVO vo = orderService.findByPurNo(pruNo, scan);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "收货完毕归档")
    @Log(title = "收货完毕归档", businessType = BusinessType.UPDATE)
    @GetMapping("/complete/{id}")
    public ResponseEntity<Response> complete(@PathVariable String id) {
        orderService.updateReceiveStatus(id, PurchaseOrderStatusEnum.FINISH);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "导出收料明细")
    @Log(title = "导出收料明细", businessType = BusinessType.QUERY)
    @GetMapping("/export/{id}")
    public void export(@PathVariable String id, HttpServletResponse response) {
        String purchaseName = orderService.getPurchaseNameById(id);
        ExcelUtils.export(response, orderService.getMaterialDetailByOrderId(id), MaterialDetailVO.class, purchaseName + "收料明细");
    }

    @ApiOperation(value = "App-采购单详情-创建分享二维码")
    @GetMapping("/qrCode/{purchaseId}")
    public ResponseEntity<Response> qrCode(@PathVariable String purchaseId) throws IOException {
        String uuid = orderService.qrCode(purchaseId);
        return ResponseEntity.ok(new SuccessResponse(uuid));
    }

    @ApiOperation(value = "App-二维码分享页-获取时间")
    @GetMapping("/time/{code}")
    public ResponseEntity<Response> getTime(@PathVariable String code) {
        long expire = redisUtil.getExpire(StrUtil.format(AppConstant.QRCODE_VIEW_PREFIX,code));
        LocalDateTime localDateTime = LocalDateTime.now().plusSeconds(expire);
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String result = dtf.format(localDateTime);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "App-二维码分享页-获取采购单code")
    @GetMapping("/code/{purchaseId}")
    public ResponseEntity<Response> getCode(@PathVariable String purchaseId) {
        String code = IdUtil.fastSimpleUUID();
        String createId = authUserHolder.getCurrentUser().getId();
        QrcodeRedisDTO dto = new QrcodeRedisDTO();
        dto.setCode(code);
        dto.setPurchaseId(purchaseId);
        dto.setCreateId(createId);
        redisUtil.set(StrUtil.format(AppConstant.QRCODE_VIEW_PREFIX,code), JSON.toJSONString(dto),72 * 3600);
        return ResponseEntity.ok(new SuccessResponse(code));
    }

//    @ApiOperation(value = "App-采购单详情-二维码pdf")
//    @GetMapping("/qrcode/pdf/{purchaseId}")
//    public ResponseEntity<Response> qrCodePdf(@PathVariable String purchaseId) throws IOException {
//        String url = orderService.qrCodePdf(purchaseId);
//        return ResponseEntity.ok(new SuccessResponse(url));
//    }

}
