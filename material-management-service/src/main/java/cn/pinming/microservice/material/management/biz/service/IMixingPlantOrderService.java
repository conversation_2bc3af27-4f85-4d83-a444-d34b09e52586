package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.dto.MixingPlantOrderDTO;
import cn.pinming.microservice.material.management.biz.entity.MixingPlantOrder;
import cn.pinming.microservice.material.management.biz.form.PlantOrderForm;
import cn.pinming.microservice.material.management.biz.vo.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 拌合站订单 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-28 15:26:17
 */
public interface IMixingPlantOrderService extends IService<MixingPlantOrder> {

    IPage<MixingPlantOrderVO> plantOrderPage(Byte status, int pageNum, int pageSize);

    void updateStatus(String id, Byte status);

    void addPlantOrder(PlantOrderForm plantOrderForm);

    void updatePlantOrder(PlantOrderForm plantOrderForm);

    MixingPlantOrderUpdateVO queryUpdateVO(String orderId);

    MixingPlantOrderDetailVO queryOrderDetailById(String orderDetailId);

    String getPrintPlantOrderUrl(String orderDetailId);

    List<MixingPlantOrderSendDetailVO> queryPlantOrderSendDetailsById(String orderDetailId);

    String queryOrderIdByOrderDetailId(String orderDetailId);

    List<MixingPlantOrderStatusCountVO> orderStatusCount();

    List<MixingPlantOrderDetailVO> choose(String name,String no);

    List<String> customer();

    ExistTicketDetailVO orderDetail();

    MixingPlantOrderDTO getSimpleMixingPlantOrder(String cargoId);
}
