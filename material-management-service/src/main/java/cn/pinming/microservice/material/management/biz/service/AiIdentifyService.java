package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.algModelApi.api.dto.RebarResultDto;
import cn.pinming.microservice.material.management.biz.enums.AiIdentifyEnum;

/**
 * <AUTHOR>
 * @date 2022/2/9
 * @description
 */
public interface AiIdentifyService {

    /**
     * 获取业务token
     * @param bizId         业务ID
     * @param userId        用户ID
     * @param projectId     项目ID
     * @param companyId     企业ID
     * @return
     */
    String getToken(AiIdentifyEnum bizId, String userId, Integer projectId, Integer companyId);

    /**
     * AI智能识别
     * @param bizId     业务ID
     * @param token     业务token
     * @return
     */
    RebarResultDto getRebarCalculateResult(AiIdentifyEnum bizId, String token);



}
