package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.web.response.Response;
import cn.pinming.microservice.material.management.biz.form.LeaveTemplateContentForm;
import cn.pinming.microservice.material.management.biz.form.LeaveTemplateImageForm;
import cn.pinming.microservice.material.management.biz.form.LeaveTemplateNameForm;
import cn.pinming.microservice.material.management.biz.service.ILeaveTemplateService;
import cn.pinming.microservice.material.management.biz.vo.LeaveTemplateNameVO;
import cn.pinming.microservice.material.management.biz.vo.LeaveTemplateParamVO;
import cn.pinming.microservice.material.management.common.SuccessResponse;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 出场单模板 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-28 13:58:49
 */
@Api(tags = "出场单模板-controller", value = "ly")
@RestController
@RequestMapping("/api/leave/")
public class LeaveTemplateController {

    @Resource
    private ILeaveTemplateService leaveTemplateService;

    @ApiOperation(value = "保存模板内容")
    @Log(title = "保存模板", businessType = BusinessType.UPDATE)
    @PostMapping("/template/content")
    public ResponseEntity<Response> saveLeaveTemplate(@RequestBody @Validated LeaveTemplateContentForm leaveTemplateForm) {
        leaveTemplateService.saveLeaveTemplate(leaveTemplateForm);
        return ResponseEntity.ok(new SuccessResponse(true));
    }

    @ApiOperation(value = "保存模板图片", response = List.class)
    @Log(title = "保存模板", businessType = BusinessType.UPDATE)
    @PostMapping("/template/image")
    public ResponseEntity<Response> saveLeaveTemplateImage(@RequestBody @Validated LeaveTemplateImageForm leaveTemplateForm) {
        leaveTemplateService.saveLeaveTemplateImage(leaveTemplateForm);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "添加模板名")
    @Log(title = "添加模板名", businessType = BusinessType.INSERT)
    @PostMapping("/template/name")
    public ResponseEntity<Response> saveLeaveTemplateName(@RequestBody @Validated LeaveTemplateNameForm leaveTemplateNameForm) {
        leaveTemplateService.saveLeaveTemplateName(leaveTemplateNameForm);
        return ResponseEntity.ok(new SuccessResponse(true));
    }

    @ApiOperation(value = "模板内容")
    @Log(title = "模板内容", businessType = BusinessType.QUERY)
    @GetMapping("/template/{name}/{type}")
    public ResponseEntity<Response> findLeaveTemplate(@PathVariable String name, @PathVariable Byte type) {
        return ResponseEntity.ok(new SuccessResponse(leaveTemplateService.findLeaveTemplate(name, type)));
    }

    @ApiOperation(value = "模板图片信息")
    @Log(title = "模板信息", businessType = BusinessType.QUERY)
    @GetMapping("/template/image/{name}/{type}")
    public ResponseEntity<Response> findLeaveTemplateImage(@PathVariable String name, @PathVariable Byte type) {
        return ResponseEntity.ok(new SuccessResponse(leaveTemplateService.findLeaveTemplateImage(name, type)));
    }

    @ApiOperation(value = "删除模板图片")
    @Log(title = "删除模板图片", businessType = BusinessType.DELETE)
    @DeleteMapping("/template/image")
    public ResponseEntity<Response> deleteLeaveTemplateImage(@RequestBody @Validated LeaveTemplateImageForm leaveTemplateImageForm) {
        leaveTemplateService.deleteLeaveTemplateImage(leaveTemplateImageForm);
        return ResponseEntity.ok(new SuccessResponse(true));
    }

    @ApiOperation(value = "删除模板")
    @Log(title = "删除模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/template/{name}/{type}")
    public ResponseEntity<Response> deleteLeaveTemplate(@PathVariable String name, @PathVariable Byte type) {
        leaveTemplateService.deleteLeaveTemplate(name, type);
        return ResponseEntity.ok(new SuccessResponse(true));
    }

    @ApiOperation(value = "模板列表")
    @Log(title = "模板列表", businessType = BusinessType.QUERY)
    @GetMapping("/template/{type}")
    public ResponseEntity<Response> list(@PathVariable Byte type) {
        List<LeaveTemplateNameVO> templateNameList = leaveTemplateService.listLeaveTemplate(type);
        return ResponseEntity.ok(new SuccessResponse(templateNameList));
    }

    @ApiOperation(value = "模板参数列表")
    @Log(title = "模板名称列表", businessType = BusinessType.QUERY)
    @GetMapping("/template/param/{type}")
    public ResponseEntity<Response> paramList(@PathVariable Byte type) {
        List<LeaveTemplateParamVO> leaveTemplateParamVOList = leaveTemplateService.paramList(type);
        return ResponseEntity.ok(new SuccessResponse(leaveTemplateParamVOList));
    }
}
