package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.core.cookie.AuthUser;
import cn.pinming.microservice.material.management.biz.dto.PreReportDTO;
import cn.pinming.microservice.material.management.biz.dto.PreTruckReportTimeDTO;
import cn.pinming.microservice.material.management.biz.dto.VehicleInfoDTO;
import cn.pinming.microservice.material.management.biz.entity.PreWeighReport;
import cn.pinming.microservice.material.management.biz.query.PreReportQuery;
import cn.pinming.microservice.material.management.biz.vo.PreReportDetailVO;
import cn.pinming.microservice.material.management.biz.vo.PreReportVO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进出场预报备 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-11-16 15:07:43
 */
public interface PreWeighReportMapper extends BaseMapper<PreWeighReport> {

    @InterceptorIgnore(tenantLine = "true")
    IPage<PreReportDTO> selectPreReport(PreReportQuery query, AuthUser user);

    PreReportDetailVO selectDetail(@Param("id")String id);

    List<PreReportVO> selectPreReportByPurchaseId(@Param("purchaseId")String  purchaseId,@Param("memberId")String memberId);

    List<VehicleInfoDTO> selectVehicleList(@Param("companyId") Integer companyId, @Param("projectId") Integer projectId);

    String selectExitTicketId(@Param("exitTicketId") String id);
}
