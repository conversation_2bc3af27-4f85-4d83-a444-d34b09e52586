package cn.pinming.microservice.material.management.common.exception;

public enum BOExceptionEnum {

    //--------------------------------------------------  基础 -----------------------------------------------------//
    NO_PERMISSION("-1", "无权限"),
    PARENT_HIDDEN_DANGER_NOT_EXISTS("-1", "父级隐患类别不存在"),
    COMPANY_HIDDEN_DANGER_NOT_EXISTS("-2", "企业隐患类别不存在"),
    NO_PARAM("-20001", "必填参数未填写"),
    ILLEGAL_PARAM("-20002", "参数无效"),
    SERVER_ERROR("-20003", "系统错误"),
    LOGIC_ERROR("-20004", "逻辑错误"),
    PROJECT_ERROR("-20005","项目不存在"),
    //--------------------------------------------------  业务 -----------------------------------------------------//

    PURCHASE_CONTRACT_NOT_EXISTS("-1001", "采购合同不存在"),
    PURCHASE_ORDER_NOT_EXISTS("-1002", "采购单不存在"),
    MATERIAL_ID_REPEAT("-1003", "物料规格型号重复"),
    PURCHASE_CONTRACT_SELECT("-1004", "请选择采购合同"),
    PURCHASE_CONTRACT_CAN_NOT_REMOVE("-1005", "该合同已发生采购记录，不可删除。"),
    PURCHASE_CONTRACT_CAN_NOT_EDIT("-1006", "该合同已发生采购记录，不可编辑。"),
    MATERIAL_NOT_EXISTS("-1007", "物资材料不存在"),
    COMPANYID_CAN_NOT_NULL("-3","企业ID不能为空"),
    PROJECTID_CAN_NOT_NULL("-4","项目ID不能为空"),
    WEIGHSYSTEMNAME_IS_REPEAT("-5","名称重复，请修改"),
    WEIGHBRIDGE_HAS_EXIST_IN_COMPANY("-6","该地磅终端已存在于本企业中"),
    DATAID_CAN_NOT_NULL("-7","收货明细id不能为空"),
    ORDER_IS_NOT_EXIST("-8","该采购单不存在"),
    RECEIVE_IS_NOT_EXIST("-9","该收货单不存在"),
    WEIGHBRIDGE_IS_NOT_EXIST("-10","该地磅不存在"),
    TRUCK_HAS_EXISTS("-11","此车辆已报备，无需重复报备"),
    WEIGH_CAN_NOT_DELETE("-12","此地磅已产生收货记录，不可删除"),
    WEIGHSYSTEMNO_IS_REPEAT("-13","编码重复，请修改"),
    WEIGHPOINTNAME_IS_REPEAT("-14","名称重复，请修改"),
    TRUCK_NOT_EXISTS("-12","此车辆未报备"),
    WEIGH_IS_NOT_EXIST("-15","该地磅不存在"),
    TRUCK_IS_NOT_EXIST("-16","该车辆不存在"),
    WEIGHPOINT_CAN_NOT_REPEAT("-17","磅点名称不可重复"),
    TRUCKPIC_MUST_TWO("-18","车头车尾必须为两张"),
    SENDPIC_TOO_MANY("-20","送货单图片照片过多"),
    PURCHASE_IS_NOT_EXIST("-21","非本项目采购单，无法进行收料操作"),
    RECEIVELIST_CAN_NOT_EMPTY("-22","收货记录不能为空"),
    PIC_SIZE_ERROR("-23","图片上传异常,请删除后重新上传"),
    PARAM_CAN_NOT_EMPTY("-24","参数不能为空"),
    PARAM_EXPLAIN_FAIL("-25","参数解析失败"),
    COMPANY_GET_FAIL("-26","获取企业信息失败"),
    TRUCK_NO_REPEAT("-27","车牌号重复"),
    GOOD_TOTAL_GREATER_TOTAL_CAPACITY("-28","总装货量大于发货总量!"),
    INTERVAL_TIME_LESS_30("-29","后续到场间隔时长小于30分钟!"),
    PREREPORT_CAN_NOT_EDIT("-30","报备已提交，无法进行修改"),
    PREREPORT_CAN_NOT_DELETE("-31","报备已提交，无法进行删除"),
    PRETRUCK_CAN_NOT_SUBMIT_IN_COMMIT("-31","报备车辆列表为空，无法正式提交"),
    TRUCK_NO_ILLEGAL("-34","车牌号不合法!"),
    EXT_PAGE_EXPIRE("-35","页面已失效"),
    EXT_PAGE_VIEW("-36","本次发货车辆报备信息您已填写，感谢您对本项目物资采购工作的支持！如需重新填写，请联系项目对接人为您生成新的页面进行填写"),
    PRE_TRUCK_INFO_NOT_EXISTS("-37","车辆预报备信息不存在"),
    PRE_TRUCK_INFO_STATUS_ERROR("-38","改状态无法取消"),
    SUPPLIER_CAN_NOT_COMMIT("-39","供应商不能进行正式提交"),
    EXT_PAGE_DELETED("-40","页面数据已被创建者删除"),
    PURCHASEID_CAN_NOT_EMPTY("-41","采购单收料时，采购单id不能为空"),
    CONTRACTID_CAN_NOT_EMPTY("-42","合同收料时，合同id不能为空"),
    PURCHASEID_MUST_BE_EMPTY("-43","合同收料时，别传采购单id"),
    RATIO_IS_ERROR("-44","请填写正确的转换系数"),
    UNIT_CAN_NOT_EMPTY("-45","结算单位不能为空"),
    WARNING_TYPE_NOT_EMPTY("-46", "预警类型不能为空"),
    WARNING_SOURCE_NOT_EMPTY("-47", "预警来源不能为空"),
    WARNING_SOURCE_ID_NOT_EMPTY("-48", "预警来源id不能为空"),
    WARNING_SOURCE_PROJECT_NOT_EMPTY("-49", "预警来源项目不能为空"),
    WARNING_SOURCE_NO_NOT_EMPTY("50", "预警来源no不能为空"),
    WARNING_ID_NOT_EMPTY("51", "预警id不能为空"),
    WARNING_STATUS_NOT_EMPTY("52", "预警状态不能为空"),
    SEND_RECEIVE_IS_NOT_EXIST("-53","该weighId对应的收货记录不存在"),
    DOCUMENTPIC_IS_EXIST("-54","该weighId对应的收货明细记录已有单据照片"),
    SENDID_CAN_NOT_NULL("-53", "该发货单id不能为空"),
    MATERIAL_DATA_IS_NOT_EXIST("-54","该收货明细不存在"),
    TARE_OR_GROSS_IS_ERROR("-55","毛皮重有误"),
    WEIGHT_DEDUCT_IS_ERROR("-56","扣重不得大于等于净重（毛重-皮重）"),
    SUBMIT_IS_EMPTY("-57","本次提交并未修改数据"),
    CHOOSE_CURRENT_MATERIAL("-58","请重新选择物料"),
    CONTRACT_DETAIL_IS_NOT_EXIST("-59","该合同明细不存在，请重新选择材料"),
    VERIFY_ID_NOT_NULL("-60", "对账id不能为空"),
    DATAID_NOT_NULL("-61", "明细id不能为空"),
    REPETITION_NAME("-62", "重复名称"),
    IS_NOT_POUND_BILL("-62", "此单据非地磅称重小票"),
    NOT_IN_PROJECT("-62", "非本项目地磅称重小票"),
    DATA_IS_VERIFIED("-63","该收料明细已归档"),
    GOODSPIC_TOO_MANY("-64","货/铭牌图片照片过多"),
    PREREPORT_IS_NOT_EXIST("-65","该报备记录已删除"),
    INTERVAL_TIME_LESS_10("-66","计划到场时间距提交时间小于10分钟!"),
    SUPPLIER_NAME_EXIST("-63", "供应商名称已存在"),
    SUPPLIER_EXCEL_ILLEGAL("-64","请使用正确的供应商模板"),
    SUPPLIER_EXCEL_REPEAT("-65","文件内供应商名称重复,请检查"),
    CREDIT_CODE_ILLEGAL("-62", "组织机构代码不合法"),
    CREDIT_CODE_REPEAT("-68", "组织机构代码重复"),
    CHOOSE_CURRENT_PURCHASE("-70","该采购单处于待复核，请选择其他采购单！"),
    CHOOSE_CURRENT_PURCHASE_AGAIN("-71","该采购单处于待审批，请选择其他采购单！"),
    PURCHASE_IS_FINISH("-69","该采购单已收货完毕"),
    NET_IS_ERROR("-72","过磅数据尚未上传，请检查项目地磅网络是否已连接"),
    DATA_IS_NOT_UPLOAD("-73","过磅数据尚未上传，请稍后再添加"),
    ADD_SCAN_COMPANY_CONFIG("-74","请先配置企业级扫码过磅权限"),
    TEMPLATE_NAME_NOT_EXIST("-75", "模板名不存在"),
    TEMPLATE_NAME_REPEAT("-76", "模板名重复"),
    EXIT_TICKET_IS_EMPTY("-77","请选择正确的出场单"),
    TEMPLATE_NAME_NOT_DELETE("-78", "默认模板不可删除"),
    ORDERDETAIL_STATUS_IS_FIVE("-79","关联订单已发货完毕，不可操作"),
    ORDERDETAIL_STATUS_IS_SIX("-80","关联订单已归档，不可操作"),
    MACHINE_NOT_EXIST("-81", "该机组不存在"),
    TEMPLATE_CONTENT_EMPTY("-82", "模板内容为空，不可打印"),
    INGREDIENT_IS_DELETED("-83","该配料单不存在"),
    INGREDIENT_IS_Filed("-84","该配料单已归档"),
    APPLY_IS_FIRST("-85","创建通知单前先请创建申请单"),
    NOTICE_IS_FIRST("-86","创建确认单前先请创建通知单"),
    NOTICE_HAS_CREATED("-87","通知单已创建"),
    CONFIRM_HAS_CREATED("-88","确认单已创建"),
    APPLY_HAS_NOT_CREATED("-89","申请单未创建"),
    NOTICE_HAS_NOT_CREATED("-89","通知单未创建"),
    CONFIRM_HAS_NOT_CREATED("-89","确认单未创建"),
    FINISHED_CATEGORY_ID_REPEAT("-90", "成品类别重复"),
    PARAMETER_REPEAT("-91", "其他参数与固定参数重复"),
    FINISHED_CATEGORY_MATERIAL_REPEAT("-92", "其他材料品种组合重复"),
    FINISHED_CATEGORY_ALREADY_RELATION("-93", "该品种已经关联配料单"),
    UPDATE_RELATION_INGREDIENT("-94", "关联了配料单只能新增"),
    FINISHED_CATEGORY_WATER_NO_UPDATE("-95", "关联了配料单含水不能修改"),
    APPLYNO_IS_REPEATED("-96","该申请单编号已存在"),
    BOM_HAS_NOT_CREATED("-97","该订单明细材料还未创建BOM包,请先创建BOM包"),
    INGREDIENT_HAS_CREATED("-98","该订单明细已创建配料单"),
    APPLYDATE_CAN_NOT_AFTER_USEDATE("-99","申请日期不得晚于使用日期"),
    QRCODE_EXPIRE("-100","该二维码已失效"),
    PURCHASE_DETAIL_IS_DELETED("-101","该采购明细已删除"),
    RELATION_CONTRACT("-102", "该供应商关联合同不可删除"),
    MACHINE_IS_DELETED("-103","该机组已被删除"),
    MACHINE_CAPACITY_IS_NULL("-104","请为选择机组设置正确最大容量"),
    PERUSAGERESULT_CAN_NOT_BE_NULL("-105","标准盘数>=1时,各材料每盘用量不能为空"),
    IS_PROJECT_CONFIG("-106","此为项目层配置"),
    PROJECT_ID_NULL("-107","请切换到项目层"),
    IMPORT_EXIT_TIME_ILLEGAL("-123", "日期格式有误，日期格式示例(20221101 12:00:00或2022-11-01 12:00:00)"),
    API_PUSH_LIMIT("-133", "当前已有接口推送，不可以再次创建接口推送规则"),
    MATERIAL_MUST_HAVE("-134","非水材料必须选择一个"),
    OUTSIZE("-143","列表不能超过规定大小"),
    MOBILE_RECEIVE_IS_DELETED("-138","该移动收料已被删除"),
    MOBILE_CAN_NOT_UPDATE("-139","暂只支持移动端无合同收料修订"),
    DOCUMENT_ERROR("-147","非本项目单据。"),
    SUPPLIER_NONE("-147","供应商为空"),
    MODULE_ID_NONE("-149","请检查模版是否存在或者启用"),
    MATERIALS_REFUSED("-148","暂不支持一车多料业务"),
    WORDS_MATCH_ERROR("-148","语义相似度匹配失败"),
    MUST_IN_SCOPE("-127","无权限设置含水率时,含水率必须在可选范围内"),
    RECORD_HAS_USED("-153","该称重小票已被使用"),



    ;

    /**
     * 错误码
     */
    private final String errorCode;

    /**
     * 错误信息
     */
    private final String errorMsg;

    BOExceptionEnum(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public String errorCode() {
        return errorCode;
    }

    public String errorMsg() {
        return errorMsg;
    }
}
