package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.entity.MaterialFinishedBom;
import cn.pinming.microservice.material.management.biz.form.MaterialBomForm;
import cn.pinming.microservice.material.management.biz.query.BaseQuery;
import cn.pinming.microservice.material.management.biz.vo.IngredientListVO;
import cn.pinming.microservice.material.management.biz.vo.MaterialBomVO;
import cn.pinming.microservice.material_unit.api.material.dto.InfoItem;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 成品BOM包设置 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 14:31:34
 */
public interface IMaterialFinishedBomService extends IService<MaterialFinishedBom> {

    /**
     * 保存或更新Bom
     * @param materialBomForm BomForm
     */
    void saveOrUpdateBom(MaterialBomForm materialBomForm);

    /**
     * bom包列表
     * @param baseQuery query
     * @return 列表集合
     */
    IPage<MaterialBomVO> listMaterialFinishBom(BaseQuery baseQuery);

    /**
     * 本拌合站下已存在的成品类别
     * @return 分类集合
     */
    List<Integer> listExistCategories();


    /**
     * 本拌合站下其他参数要求列表
     * @return 参数列表
     */
    List<String> listOtherParameter();

    /**
     * 删除bom
     * @param id bomID
     */
    void deleteBom(String id);

    IngredientListVO seek(String mixingPlantOrderDetailId);
}
