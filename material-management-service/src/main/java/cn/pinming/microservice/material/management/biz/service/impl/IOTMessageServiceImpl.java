package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.management.biz.dto.MaterialDataDTO;
import cn.pinming.microservice.material.management.biz.dto.RemarkDTO;
import cn.pinming.microservice.material.management.biz.dto.WeighbridgeAcceptDataDTO;
import cn.pinming.microservice.material.management.biz.enums.WeighTypeEnum;
import cn.pinming.microservice.material.management.biz.form.StandardMaterialDataForm;
import cn.pinming.microservice.material.management.biz.form.StandardMaterialDataItemFrom;
import cn.pinming.microservice.material.management.biz.iot.service.IWeighbridgeService;
import cn.pinming.microservice.material.management.biz.service.IOTMessageService;
import cn.pinming.microservice.material.management.common.annotation.IOTLog;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2021/9/15 10:29 上午
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class IOTMessageServiceImpl implements IOTMessageService {

    @Autowired
    private IWeighbridgeService weighbridgeService;

    @IOTLog
    @Override
    public void processPushData(WeighbridgeAcceptDataDTO acceptDataDTO) {
        log.error("processPushData pushDataDTO:{}", JSONUtil.toJsonStr(acceptDataDTO));
        if (acceptDataDTO == null) {
            throw new BOException(BOExceptionEnum.PARAM_CAN_NOT_EMPTY);
        }
        // 过磅类型判断
        Boolean flag = weighTypeJudge(acceptDataDTO);
        if (flag) {
            weighbridgeService.processWeighbridgeDelivery(acceptDataDTO);
        } else {
            weighbridgeService.processWeighbridgeReceive(acceptDataDTO);
        }
    }

    @Override
    public void processPushData(StandardMaterialDataForm form) {
        log.error("StandardMaterialDataForm{}", JSONUtil.toJsonStr(form));
        if (form == null) { return; }
        //对象转换
        WeighbridgeAcceptDataDTO acceptDataDTO = this.transform(form);
        // 过磅类型判断
        boolean flag = weighTypeJudge(acceptDataDTO);
        if (flag) {
            weighbridgeService.processWeighbridgeDelivery(acceptDataDTO);
        } else {
            weighbridgeService.processWeighbridgeReceive(acceptDataDTO);
        }
    }

    /**
     * 数据转换 StandardMaterialDataForm -》 WeighbridgeAcceptDataDTO
     *
     * @param form
     * @return
     */
    private WeighbridgeAcceptDataDTO transform(StandardMaterialDataForm form) {
        WeighbridgeAcceptDataDTO dto = new WeighbridgeAcceptDataDTO();
        StandardMaterialDataItemFrom materialDataItemFrom = form.getMaterialDataList().get(0);
        Integer supplierId = null;
        if(StrUtil.isNotBlank(form.getSupplierId())){supplierId = Integer.valueOf(form.getSupplierId());}
        dto.setDataSourceType(supplierId);
        dto.setEnterTime(Convert.toLocalDateTime(materialDataItemFrom.getGrossTime()));
        dto.setLeaveTime(Convert.toLocalDateTime(materialDataItemFrom.getTareTime()));
        List<MaterialDataDTO> list = form.getMaterialDataList().stream().map(e -> {
            MaterialDataDTO dataDTO = new MaterialDataDTO();
            BeanUtils.copyProperties(e, dataDTO);
            dataDTO.setMaterial(e.getMaterialName());
            return dataDTO;
        }).collect(Collectors.toList());
        dto.setMaterialDataList(list);
        // dto.setMaterialWeightList();
        dto.setEnterPicList(materialDataItemFrom.getGrossPicList());
        dto.setLeavePicList(materialDataItemFrom.getTarePicList());
        dto.setDocumentPicList(form.getPicList());
        dto.setPurchaseId(form.getPurchaseId());
        dto.setReceiveNo(form.getReceiveNo());
        dto.setReceiveTime(Convert.toLocalDateTime(materialDataItemFrom.getGrossTime()));
        dto.setReceiver(form.getReceiver());
        dto.setTruckNo(form.getTruckNo());
        RemarkDTO remarkDTO = RemarkDTO.builder()
                .companyId(form.getCompanyId()).projectId(form.getProjectId()).weighId(materialDataItemFrom.getWeighId())
                .supplierId(supplierId).supplierName(form.getSupplierName())
                .actualReceive(materialDataItemFrom.getActualReceiveCount())
                .preReportId(form.getPreReportId()).build();
        dto.setRemark(JSONUtil.toJsonStr(remarkDTO));
        dto.setType(form.getType());
        return dto;
    }

    private Boolean weighTypeJudge(WeighbridgeAcceptDataDTO acceptDataDTO) {
        if (CollUtil.isEmpty(acceptDataDTO.getMaterialDataList())) { return false; }

        BigDecimal weightGross = acceptDataDTO.getMaterialDataList().get(0).getWeightGross();
        BigDecimal weightTare = acceptDataDTO.getMaterialDataList().get(0).getWeightTare();
        // 终端为发料,根据毛皮重判断实际收发料类型
        if (acceptDataDTO.getType() == WeighTypeEnum.DELIVERY.value()) {
            if (weightGross.compareTo(BigDecimal.ZERO) != 0 && weightTare.compareTo(weightGross) > 0) {
                BigDecimal cup = weightTare;
                acceptDataDTO.getMaterialDataList().get(0).setWeightTare(weightGross);
                acceptDataDTO.getMaterialDataList().get(0).setWeightGross(cup);
                acceptDataDTO.setType(WeighTypeEnum.RECEIVE.value());
                return false;
            }
            return true;
        // 终端为收料,根据毛皮重判断实际收发料类型
        } else if (acceptDataDTO.getType() == WeighTypeEnum.RECEIVE.value()) {
            if (weightTare.compareTo(weightGross) > 0) {
                BigDecimal cup = weightTare;
                acceptDataDTO.getMaterialDataList().get(0).setWeightTare(weightGross);
                acceptDataDTO.getMaterialDataList().get(0).setWeightGross(cup);
                acceptDataDTO.setType(WeighTypeEnum.DELIVERY.value());
                return true;
            }
            return false;
        // 终端为其他,根据毛皮时间判断实际收发料类型
        } else if (acceptDataDTO.getType() == WeighTypeEnum.OTHER.value()) {
            if (ObjectUtil.isNotNull(acceptDataDTO.getEnterTime()) && ObjectUtil.isNotNull(acceptDataDTO.getLeaveTime())) {
                if (acceptDataDTO.getEnterTime().isBefore(acceptDataDTO.getLeaveTime())) {
                    acceptDataDTO.setType(WeighTypeEnum.RECEIVE.value());
                    return false;
                } else {
                    acceptDataDTO.setType(WeighTypeEnum.DELIVERY.value());
                    return true;
                }
            }else {
                return false;
            }
        }
        return false;
    }
}
