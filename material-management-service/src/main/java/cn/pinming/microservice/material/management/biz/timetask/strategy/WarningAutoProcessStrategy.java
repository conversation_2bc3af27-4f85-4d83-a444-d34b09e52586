package cn.pinming.microservice.material.management.biz.timetask.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.pinming.microservice.material.management.biz.entity.MaterialWarning;
import cn.pinming.microservice.material.management.biz.entity.WarningHandlerAdvice;
import cn.pinming.microservice.material.management.biz.enums.WarningStatusEnum;
import cn.pinming.microservice.material.management.biz.mapper.MaterialWarningMapper;
import cn.pinming.microservice.material.management.biz.mapper.WarningHandlerAdviceMapper;
import cn.pinming.microservice.material.management.biz.service.IMaterialWarningService;
import cn.pinming.microservice.material.management.biz.service.IWarningHandlerAdviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 预警自动处理策略抽象类
 * <AUTHOR>
 * @date 2022/6/14
 */
@Slf4j
@Component
public abstract class WarningAutoProcessStrategy {

    private String autoProcessName = "预警机器人";
    private String autoProcessAdvice = "已处理";

    @Resource
    private IMaterialWarningService warningService;

    @Resource
    private IWarningHandlerAdviceService handlerAdviceService;

    public static Map<Byte, WarningAutoProcessStrategy> ALL_STRATEGY = new HashMap<>();

    void addChildClass(Byte type, WarningAutoProcessStrategy strategy) {
        ALL_STRATEGY.put(type, strategy);
    }

    /**
     * 预警自动关闭方式
     * @param warnings
     * @return
     */
    public abstract void autoProcessWay(List<MaterialWarning> warnings);

    @Transactional(rollbackFor = Exception.class)
    public void autoCloseWarning(List<String> warningIds) {
        log.info("执行预警自动关闭 start...");
        if (CollectionUtil.isEmpty(warningIds)) {
            log.info("没有需要执行的预警");
            log.info("执行预警自动关闭 end...");
            return;
        }
        // 自动处理
        List<MaterialWarning> updateWarnings = warningIds.stream().map(item -> {
            MaterialWarning warning = new MaterialWarning();
            warning.setId(item);
            warning.setHandlerName(autoProcessName);
            warning.setHandlerTime(LocalDateTime.now());
            warning.setWarningStatus(WarningStatusEnum.HANDLED.value());
            return warning;
        }).collect(Collectors.toList());
        warningService.updateBatchById(updateWarnings, 200);

        List<WarningHandlerAdvice> updateWarningHandlers = warningIds.stream().map(item -> {
            WarningHandlerAdvice warning = new WarningHandlerAdvice();
            warning.setWarningId(item);
            warning.setHandlerName(autoProcessName);
            warning.setHandlerTime(LocalDateTime.now());
            warning.setWarningStatus(WarningStatusEnum.HANDLED.value());
            warning.setHandlerAdvice(autoProcessAdvice);
            return warning;
        }).collect(Collectors.toList());
        handlerAdviceService.saveBatch(updateWarningHandlers, 200);

        log.info("执行预警自动关闭 end...");
    }

}
