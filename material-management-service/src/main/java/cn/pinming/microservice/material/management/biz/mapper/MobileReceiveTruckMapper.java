package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.entity.MobileReceiveTruck;
import cn.pinming.microservice.material.management.biz.vo.MobileReceiveVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 移动收料货车表 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:31:56
 */
public interface MobileReceiveTruckMapper extends BaseMapper<MobileReceiveTruck> {

    List<MobileReceiveVO> listByPurchaseId(@Param("purchaseId")String purchaseId);

}
