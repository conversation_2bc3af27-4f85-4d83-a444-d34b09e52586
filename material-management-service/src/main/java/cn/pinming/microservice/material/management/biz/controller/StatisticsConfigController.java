package cn.pinming.microservice.material.management.biz.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.entity.StatisticsMaterialConfig;
import cn.pinming.microservice.material.management.biz.entity.StatisticsProjectConfig;
import cn.pinming.microservice.material.management.biz.entity.StatisticsUseConfig;
import cn.pinming.microservice.material.management.biz.form.MaterialConfigForm;
import cn.pinming.microservice.material.management.biz.form.ProjectConfigForm;
import cn.pinming.microservice.material.management.biz.form.UseConfigForm;
import cn.pinming.microservice.material.management.biz.service.IStatisticsMaterialConfigService;
import cn.pinming.microservice.material.management.biz.service.IStatisticsProjectConfigService;
import cn.pinming.microservice.material.management.biz.service.IStatisticsUseConfigService;
import cn.pinming.microservice.material.management.biz.service.MaterialGroupConfigService;
import cn.pinming.microservice.material.management.biz.vo.StatisticsConfigVO;
import cn.pinming.microservice.material.management.biz.vo.StatisticsProjectVO;
import cn.pinming.microservice.material.management.biz.vo.StatisticsUseVO;
import cn.pinming.microservice.material.management.common.SuccessResponse;
import cn.pinming.microservice.material.management.common.util.IfBranchUtil;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <p>
 * 统计配置表-项目&物料 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-04-11 13:32:11
 */
@Api(tags = "总览-统计设置")
@RestController
@RequestMapping("/api/statistics/config")
public class StatisticsConfigController {

    @Resource
    private AuthUserHolder authUserHolder;

    @Resource
    private ProjectServiceProxy projectServiceProxy;

    @Resource
    private IStatisticsMaterialConfigService materialConfigService;

    @Resource
    private IStatisticsProjectConfigService projectConfigService;

    @Resource
    private IStatisticsUseConfigService useConfigService;

    @Resource
    private MaterialGroupConfigService materialGroupConfigService;

    @ApiOperation(value = "总览统计设置修改-物料")
    @PutMapping("/material")
    public ResponseEntity<SuccessResponse> updateMaterialConfig(@RequestBody MaterialConfigForm form) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (ObjectUtil.isNotNull(currentUser)) {
            String name = form.getName();
            String userid = currentUser.getId();
            QueryWrapper<StatisticsMaterialConfig> oneWrapper = new QueryWrapper<>();
            oneWrapper.lambda().eq(StatisticsMaterialConfig::getCreateId, userid);
            IfBranchUtil.isTureOrFalse(ObjectUtil.isNull(name)).trueOrFalseHandle(
                    () -> {
                        oneWrapper.lambda().last(" and (name is null or name = '')");
                    },
                    () -> {
                        oneWrapper.lambda().eq(StatisticsMaterialConfig::getName, name);
                    }
            );
            StatisticsMaterialConfig one = materialConfigService.getOne(oneWrapper);
            StatisticsMaterialConfig entity = new StatisticsMaterialConfig();
            entity.setName(form.getName());
            entity.setCategoryId(form.getCategoryIds());
            IfBranchUtil.isTureOrFalse(ObjectUtil.isNull(one)).trueOrFalseHandle(
                    () -> {
                        // 新增
                        materialConfigService.save(entity);
                    },
                    () -> {
                        // 修改
                        entity.setId(one.getId());
                        materialConfigService.updateById(entity);
                    }
            );
        }
        return ResponseEntity.ok(new SuccessResponse(true));
    }

    @ApiOperation(value = "总览统计设置查询-物料", response = StatisticsConfigVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tag", value = "是否有自定义分类名称", required = true, dataType = "Boolean", paramType = "query"),
            @ApiImplicitParam(name = "unit", value = "是否需要单位", required = true, dataType = "Boolean", paramType = "query")
    })
    @GetMapping("/material")
    public ResponseEntity<SuccessResponse> queryMaterialConfig(@RequestParam Boolean tag, @RequestParam Boolean unit) {
        List<StatisticsConfigVO> list = materialGroupConfigService.materialGroupConfig(tag, unit);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "总览统计设置修改-项目")
    @PutMapping("/project")
    public ResponseEntity<SuccessResponse> updateProjectConfig(@RequestBody ProjectConfigForm form) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (ObjectUtil.isNotNull(currentUser)) {
            String userid = currentUser.getId();
            QueryWrapper<StatisticsProjectConfig> oneWrapper = new QueryWrapper<>();
            oneWrapper.lambda().eq(StatisticsProjectConfig::getCreateId, userid);
            StatisticsProjectConfig one = projectConfigService.getOne(oneWrapper);
            StatisticsProjectConfig entity = new StatisticsProjectConfig();
            entity.setBelongDepartmentId(form.getDepartmentIds());
            entity.setBelongProjectId(form.getProjectIds());
            entity.setStatus(form.getStatus());
            IfBranchUtil.isTureOrFalse(ObjectUtil.isNull(one)).trueOrFalseHandle(
                    () -> {
                        // 新增
                        projectConfigService.save(entity);
                    },
                    () -> {
                        // 修改
                        entity.setId(one.getId());
                        projectConfigService.updateById(entity);
                    }
            );
        }
        return ResponseEntity.ok(new SuccessResponse(true));
    }

    @ApiOperation(value = "总览统计设置查询-项目", response = StatisticsProjectVO.class)
    @GetMapping("/project")
    public ResponseEntity<SuccessResponse> queryProjectConfig() {
        StatisticsProjectVO projectVO = new StatisticsProjectVO();
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (ObjectUtil.isNotNull(currentUser)) {
            String userid = currentUser.getId();
            QueryWrapper<StatisticsProjectConfig> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StatisticsProjectConfig::getCreateId, userid);
            StatisticsProjectConfig config = projectConfigService.getOne(queryWrapper);
            if (ObjectUtil.isNotNull(config)) {
                projectVO.setId(config.getId());
                projectVO.setDepartmentIds(Optional.ofNullable(config.getBelongDepartmentId()).orElse("").split(","));
                projectVO.setProjectIds(Optional.ofNullable(config.getBelongProjectId()).orElse("").split(","));
                projectVO.setStatus(Optional.ofNullable(config.getStatus()).orElse("").split(","));
            }
        }
        return ResponseEntity.ok(new SuccessResponse(projectVO));
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "总览统计设置修改-使用率")
    @PutMapping("/use")
    public ResponseEntity<SuccessResponse> updateUseConfig(@RequestBody UseConfigForm form) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (ObjectUtil.isNotNull(currentUser)) {
            String userid = currentUser.getId();
            QueryWrapper<StatisticsUseConfig> oneWrapper = new QueryWrapper<>();
            oneWrapper.lambda().eq(StatisticsUseConfig::getCreateId, userid);
            StatisticsUseConfig one = useConfigService.getOne(oneWrapper);
            StatisticsUseConfig entity = new StatisticsUseConfig();
            entity.setDay(form.getDay());
            IfBranchUtil.isTureOrFalse(ObjectUtil.isNull(one)).trueOrFalseHandle(
                    () -> {
                        // 新增
                        useConfigService.save(entity);
                    },
                    () -> {
                        // 修改
                        entity.setId(one.getId());
                        useConfigService.updateById(entity);
                    }
            );
        }
        return ResponseEntity.ok(new SuccessResponse(true));
    }

    @ApiOperation(value = "总览统计设置查询-使用率")
    @GetMapping("/use")
    public ResponseEntity<SuccessResponse> queryUseConfig() {
        StatisticsUseVO useVO = new StatisticsUseVO();
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (ObjectUtil.isNotNull(currentUser)) {
            String userid = currentUser.getId();
            QueryWrapper<StatisticsUseConfig> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StatisticsUseConfig::getCreateId, userid);
            StatisticsUseConfig config = useConfigService.getOne(queryWrapper);
            if (ObjectUtil.isNotNull(config)) {
                useVO.setId(config.getId());
                useVO.setDay(config.getDay());
            }
        }
        return ResponseEntity.ok(new SuccessResponse(useVO));
    }


    @GetMapping("/test")
    public ResponseEntity<SuccessResponse> test() {
        List<Integer> integers = projectServiceProxy.statisticsProjectIds(11346, null);
        Map<String, List<Integer>> maps = projectServiceProxy.directlyUnderDeptOrProject(11346, null);
        return ResponseEntity.ok(new SuccessResponse(maps));
    }

}
