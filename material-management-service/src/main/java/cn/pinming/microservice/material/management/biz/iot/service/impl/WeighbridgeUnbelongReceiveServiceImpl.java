package cn.pinming.microservice.material.management.biz.iot.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.management.biz.dto.WeighbridgeAcceptDataDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialData;
import cn.pinming.microservice.material.management.biz.entity.MaterialWeighbridge;
import cn.pinming.microservice.material.management.biz.enums.*;
import cn.pinming.microservice.material.management.biz.iot.service.IWeighbridgeDecisionService;
import cn.pinming.microservice.material.management.biz.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.biz.service.IMaterialWeighbridgeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;

import static cn.pinming.microservice.material.management.biz.enums.WarningSubTypeEnum.*;

@Slf4j
@Service(value = "unbelongReceive")
public class WeighbridgeUnbelongReceiveServiceImpl extends AbstractWeighbridgeDecisionService implements IWeighbridgeDecisionService {

    private BigDecimal zero = BigDecimal.ZERO;

    @Override
    public WeighbridgeAcceptDataDTO getAcceptDataDTO() {
        return super.getAcceptDataDTO();
    }

    @Override
    public void setAcceptDataDTO(WeighbridgeAcceptDataDTO acceptDataDTO) {
        super.setAcceptDataDTO(acceptDataDTO);
    }

    @Resource
    private MaterialDataMapper materialDataMapper;

    @Resource
    private IMaterialWeighbridgeService materialWeighbridgeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WeighbridgeAcceptDataDTO decisionWeighbridgeReceive(WeighbridgeAcceptDataDTO acceptDataDTO) {

        setAcceptDataDTO(acceptDataDTO);

        handleValidity();

        saveWeighbridgeReceive();
        saveWeighbridgeMaterialData();
        return null;
    }


    /**
     * 收料物料数据转换
     * 无归属收料特殊处理
     *
     * @param
     * @return
     */
    @Override
    protected void handleReceiveMaterialData(MaterialData materialData) {
        deviationRateAndSettlementUnitDecision(materialData);
        materialData.setIsContractUnitUsed(IsContractUnitUsedEnum.NO.value());
        materialData.setIsContractRateUsed(IsContractRateUsedEnum.NO.value());
        boolean flag = materialData.getWeightGross() != null && materialData.getWeightTare() != null && materialData.getWeightGross().compareTo(zero) > 0 && materialData.getWeightTare().compareTo(zero) > 0;
        // 毛皮重完整性判断
        if (flag) {
            materialData.setIsWeightIntegrality(IsWeightIntegralityEnum.COMPLETE.value());
        } else {
            materialData.setIsWeightIntegrality(IsWeightIntegralityEnum.INCOMPLETE.value());
        }
        // 转换系数判断分支
        if (materialData.getRatio() != null && materialData.getRatio().compareTo(zero) > 0) {
            boolean isIntegrality = (materialData.getWeightGross().compareTo(zero) > 0) && (materialData.getWeightTare().compareTo(zero) > 0);
            // 完整性判断
            if (isIntegrality) {
                // 应收数量判断
                weightSendDecision(materialData);
            } else {
                if (materialData.getWeightGross().compareTo(zero) <= 0) {
                    triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.TARE_GROSS_WEIGH.value(),
                            TARE_GROSS_WEIGH_ONE.subType(), TARE_GROSS_WEIGH_ONE.desc());
                } else if (materialData.getWeightTare().compareTo(zero) <= 0) {
                    triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.TARE_GROSS_WEIGH.value(),
                            TARE_GROSS_WEIGH_TWO.subType(), TARE_GROSS_WEIGH_TWO.desc());
                }
                // 不完整时，实重，净重，实收数量，偏差率全部置为null
                normalDeductSet(materialData);
                setNull(materialData);
            }
        } else {
            // 净重
            materialData.setWeightNet(NumberUtil.sub(materialData.getWeightGross(), materialData.getWeightTare()));
            // 实重
            materialData.setWeightActual(NumberUtil.sub(materialData.getWeightNet(), materialData.getWeightDeduct()));
            this.deductSet(materialData);
            materialData.setActualCount(null);
            materialData.setDeviationRate(null);
        }
    }

    private void setNull(MaterialData materialData) {
        materialData.setActualCount(null);
        materialData.setWeightActual(null);
        materialData.setWeightNet(null);
        materialData.setDeviationRate(null);
    }

    /**
     * 转换系数 + 结算单位判断
     *
     * @param materialData
     * @return
     */
    private void deviationRateAndSettlementUnitDecision(MaterialData materialData) {
        if (materialData.getRatio() == null || zero.compareTo(materialData.getRatio()) >= 0) {
            String str = StrUtil.format(WEIGH_CONVERT_FOUR.desc(), materialData.getRatio());
            triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.WEIGH_CONVERT.value(),
                    WEIGH_CONVERT_FOUR.subType(), str);
        }
        if (StrUtil.isBlank(materialData.getWeightUnit())) {
            triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.CHARGE_UNIT.value(),
                    CHARGE_UNIT_FOUR.subType(), CHARGE_UNIT_FOUR.desc());
        }
    }

    /**
     * 应收数量判断
     *
     * @param materialData
     * @return
     */
    private void weightSendDecision(MaterialData materialData) {
        if (materialData.getWeightSend() == null || materialData.getWeightSend().compareTo(zero) <= 0) {
            triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.RECEIVE_NUMBER.value(),
                    RECEIVE_NUMBER_ONE.subType(), RECEIVE_NUMBER_ONE.desc());
        }
        dateDecision(materialData);
    }

    /**
     * 计算净重、实重、实收数量、偏差状态
     *
     * @param materialData
     * @return
     */
    private void dateDecision(MaterialData materialData) {
//      净重
        materialData.setWeightNet(NumberUtil.sub(materialData.getWeightGross(), materialData.getWeightTare()));
//      实重
        materialData.setWeightActual(NumberUtil.sub(materialData.getWeightNet(), materialData.getWeightDeduct()));
//      杂扣重设置
        this.deductSet(materialData);
//      实收数量
        materialData.setActualCount(NumberUtil.mul(materialData.getWeightActual(), materialData.getRatio()));
//      偏差率
        if (materialData.getActualCount() != null && materialData.getWeightSend() != null && NumberUtil.isGreater(materialData.getWeightSend(), zero)) {
            BigDecimal deviation = NumberUtil.mul(NumberUtil.div(NumberUtil.sub(materialData.getActualCount(), materialData.getWeightSend()), materialData.getWeightSend(), 4), 100);
            materialData.setDeviationRate(deviation);
        }
    }

    private void deductSet(MaterialData materialData) {
        WeighbridgeAcceptDataDTO acceptDataDTO = this.getAcceptDataDTO();
        Byte isWebPushed = acceptDataDTO.getIsWebPushed();
        Integer companyId = acceptDataDTO.getCompanyId();
        Integer projectId = acceptDataDTO.getProjectId();
        String deviceSn = acceptDataDTO.getDeviceSn();
        MaterialData data = materialDataMapper.selectGunByWeighId(acceptDataDTO.getWeighId());
        boolean b = ObjectUtil.isNull(isWebPushed) && (ObjectUtil.isNull(data) || (ObjectUtil.isNotNull(data) && ObjectUtil.isNull(data.getWeightGross())));
        if (b){
            // 只有终端第一次上传才会执行如下
            if (acceptDataDTO.getType() == WeighTypeEnum.RECEIVE.value()) {
                // 只有收料有
                MaterialWeighbridge weighbridge = materialWeighbridgeService.lambdaQuery()
                        .eq(MaterialWeighbridge::getCompanyId, companyId)
                        .eq(MaterialWeighbridge::getProjectId, projectId)
                        .eq(MaterialWeighbridge::getWeighSystemNo, deviceSn)
                        .one();
                if (weighbridge.getDeductSet() == 2) {
                    // 含水率取扣重值
                    // 扣重值取0
                    // 重新计算实重
                    // 如果web端已上传，含水率取web端
                    materialData.setMoistureContent(materialData.getWeightDeduct());
                    BigDecimal mul = NumberUtil.mul(materialData.getWeightNet(), NumberUtil.div(NumberUtil.sub(BigDecimal.valueOf(100), materialData.getWeightDeduct()), BigDecimal.valueOf(100)));
                    materialData.setWeightDeduct(BigDecimal.ZERO);
                    materialData.setWeightActual(mul);
                }
            }
        }
    }
}
