package cn.pinming.microservice.material.management.biz.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CompanyConfigForm extends BaseForm{
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "类型 1 报备收料是否自动生成对账入库单 2.是否开启gboss角色权限  3.理重取值项目 4.移动收料是否允许相册上传 5.jar包设置 6.手工补单")
    @NotNull(message = "类型不能为空")
    private Byte type;

    @ApiModelProperty(value = "是否启用 1 是 2 否")
    private Byte isEnable;

    @ApiModelProperty(value = "部门配置合集")
    private String departmentIds;

    @ApiModelProperty(value = "项目配置合集")
    private String projectIds;

    @ApiModelProperty(value = "jar包配置id")
    private String jarConfigId;
}
