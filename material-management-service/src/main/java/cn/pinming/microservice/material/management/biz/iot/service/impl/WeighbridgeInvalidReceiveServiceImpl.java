package cn.pinming.microservice.material.management.biz.iot.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.management.biz.dto.ContractDecisionDTO;
import cn.pinming.microservice.material.management.biz.dto.MaterialDataDTO;
import cn.pinming.microservice.material.management.biz.dto.PushDataDTO;
import cn.pinming.microservice.material.management.biz.dto.WeighbridgeAcceptDataDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialData;
import cn.pinming.microservice.material.management.biz.enums.IsContractRateUsedEnum;
import cn.pinming.microservice.material.management.biz.enums.IsContractUnitUsedEnum;
import cn.pinming.microservice.material.management.biz.enums.IsWeightIntegralityEnum;
import cn.pinming.microservice.material.management.biz.iot.service.IWeighbridgeDecisionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 */
@Slf4j
@Transactional
@Service(value = "invalidReceive")
public class WeighbridgeInvalidReceiveServiceImpl extends AbstractWeighbridgeDecisionService implements IWeighbridgeDecisionService {

    BigDecimal zero = BigDecimal.ZERO;

    @Override
    public WeighbridgeAcceptDataDTO getAcceptDataDTO() {
        return super.getAcceptDataDTO();
    }

    @Override
    public void setAcceptDataDTO(WeighbridgeAcceptDataDTO acceptDataDTO) {
        super.setAcceptDataDTO(acceptDataDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WeighbridgeAcceptDataDTO decisionWeighbridgeReceive(WeighbridgeAcceptDataDTO acceptDataDTO) {
        setAcceptDataDTO(acceptDataDTO);
        handleValidity();
        saveWeighbridgeReceive();
        saveWeighbridgeMaterialData();
        return null;
    }

    /**
     * 收料物料数据转换
     * 无效收料不处理
     *
     * @param
     * @return
     */
    @Override
    protected void handleReceiveMaterialData(MaterialData materialData) {
        normalDeductSet(materialData);
        return;
    }
}
