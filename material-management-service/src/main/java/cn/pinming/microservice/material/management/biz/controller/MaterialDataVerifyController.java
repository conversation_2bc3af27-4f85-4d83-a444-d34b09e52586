package cn.pinming.microservice.material.management.biz.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.form.RelationVerifyReceiveForm;
import cn.pinming.microservice.material.management.biz.query.MaterialVerifyQuery;
import cn.pinming.microservice.material.management.biz.service.VerifyService;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.SuccessResponse;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <p>
 * 物料对账表 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-18 10:22:28
 */
@Slf4j
@Api(tags = "数据对账")
@RestController
@RequestMapping("/api/biz/material")
public class MaterialDataVerifyController {

    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private VerifyService verifyService;
    @Resource
    private ProjectServiceProxy projectServiceProxy;

    @ApiOperation(value = "对账列表", response = MaterialVerifyVO.class)
    @PostMapping("/verify")
    public ResponseEntity<SuccessResponse> list(@RequestBody MaterialVerifyQuery query) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Optional.ofNullable(currentUser).ifPresent(curUser -> query.setCompanyId(currentUser.getCurrentCompanyId()));
        AuthUser user = authUserHolder.getCurrentUser();
        if (user.getCurrentDepartmentId() != null) {
            query.setProjectIds(projectServiceProxy.getAllProjectIdByCompanyDeptId(user.getCurrentCompanyId(), user.getCurrentDepartmentId()));
            if (CollUtil.isEmpty(query.getProjectIds())) {
                return ResponseEntity.ok(new SuccessResponse(new Page<>()));
            }
        }
        IPage<MaterialVerifyVO> materialVerifyVOIPage = verifyService.listMaterialVerify(query);
        return ResponseEntity.ok(new SuccessResponse(materialVerifyVOIPage));
    }

    @ApiOperation(value = "删除对账")
    @DeleteMapping("/verify/{id}")
    public ResponseEntity delete(@PathVariable String id) {
        verifyService.removeMaterialVerifyById(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "导出对账单")
    @GetMapping("/verify/export/{id}")
    public void export(@PathVariable String id, HttpServletResponse response) {
        InputStream is = null;
        try {
            is = this.getClass().getClassLoader().getResourceAsStream("templates/verify-export.xlsx");
            if (is != null) {
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("utf-8");
                String fileName = URLEncoder.encode("对账单", "UTF-8");
                response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes("UTF-8"),"ISO-8859-1") + ".xlsx");
                ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build();
                WriteSheet verifySheet = EasyExcel.writerSheet("对账明细表").build();
                WriteSheet totalSheet = EasyExcel.writerSheet("对账数据透视表").build();
                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                // 基础信息
                VerifyExcelVO verifyExcelVO = new VerifyExcelVO();
                // 对账数据
                List<VerifyExcelDataVO> verifyExcelDataVOList = verifyService.getVerifyExcelVO(id, verifyExcelVO);
                // 透视数据
                MaterialVerifyPageVO materialVerifyPageVO = verifyService.verifyReceiveRecords(id, null);
                List<MaterialVerifyMaterialVO> materialList = materialVerifyPageVO.getMaterialList();
                excelWriter.fill(verifyExcelVO, fillConfig, verifySheet);
                excelWriter.fill(verifyExcelDataVOList, fillConfig, verifySheet);
                excelWriter.fill(materialList, fillConfig, totalSheet);
                excelWriter.finish();
            }
        } catch (IOException e) {
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Map<String, String> map = new HashMap<>(4);
            map.put("status", "failure");
            map.put("message", "导出文件失败" + e.getMessage());
            try {
                response.getWriter().println(JSONUtil.toJsonStr(map));
            } catch (IOException ioException) {
                log.error("response write io error.", ioException.getMessage());
            }
            if (is != null) {
                try {
                    is.close();
                } catch (Exception ioCloseException) {
                    log.error("file read io close error.", ioCloseException.getMessage());
                }
            }
        }
    }

    @ApiOperation(value = "归档")
    @GetMapping("/verify/file/{id}")
    public ResponseEntity file(@PathVariable String id) {
        verifyService.materialFile(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "对账详情", response = MaterialVerifyPageVO.class)
    @ApiImplicitParam(name = "id", value = "对账ID", required = true, dataType = "String", paramType = "path")
    @GetMapping("/verify/{id}")
    public ResponseEntity<SuccessResponse> verifyDetail(@PathVariable String id,
                                                        @RequestParam(required = false) String receiveId) {
        MaterialVerifyPageVO vo = verifyService.verifyReceiveRecords(id, receiveId);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "新增对账记录", response = MaterialVerifyAddVO.class)
    @ApiImplicitParam(name = "id", value = "项目ID", required = true, dataType = "Integer", paramType = "path")
    @PostMapping("/verify/{id}")
    public ResponseEntity<SuccessResponse> addVerifyRecord(@PathVariable Integer id) {
        MaterialVerifyAddVO verify = verifyService.addVerifyRecord(id);
        return ResponseEntity.ok(new SuccessResponse(verify));
    }

    @ApiOperation(value = "对账明细", response = MaterialVerifyDetailVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "start", value = "开始时间(yyyy-MM-dd)", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "end", value = "结束时间(yyyy-MM-dd)", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "supplierId", value = "供应商ID", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "contractId", value = "合同ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "verifyId", value = "对账ID", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/verify/detail")
    public ResponseEntity<SuccessResponse> verifyDetail(@RequestParam(required = false) String start,
                                                               @RequestParam(required = false) String end,
                                                               @RequestParam Integer supplierId,
                                                               @RequestParam String contractId,
                                                               @RequestParam String verifyId) {
        MaterialVerifyDetailVO materialVerifyDetailVO = verifyService.verifyDetail(start, end, supplierId, contractId, verifyId);
        return ResponseEntity.ok(new SuccessResponse(materialVerifyDetailVO));
    }

    @ApiOperation(value = "对账关联收料记录")
    @PostMapping("/verify/detail")
    public ResponseEntity<SuccessResponse> verifyDetail(@RequestBody RelationVerifyReceiveForm verifyReceiveForm) {
        Boolean relationVerifyDetail = verifyService.relationVerifyDetail(verifyReceiveForm.getSupplierId(), verifyReceiveForm.getContractId(),
                verifyReceiveForm.getVerifyId(), verifyReceiveForm.getReviseIds());
        return ResponseEntity.ok(new SuccessResponse(relationVerifyDetail));
    }

    @ApiOperation(value = "移除对账收料记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "verifyId", value = "对账ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "receiveId", value = "收料ID", required = true, dataType = "String", paramType = "query")
    })
    @DeleteMapping("/verify/detail")
    public ResponseEntity<SuccessResponse> removeVerifyReceiveRecord(@RequestParam String verifyId,
                                                                     @RequestParam String receiveId ) {
        Boolean removeVerifyReceiveRecord = verifyService.removeVerifyReceiveRecord(verifyId, receiveId);
        return ResponseEntity.ok(new SuccessResponse(removeVerifyReceiveRecord));
    }

    @ApiOperation(value = "扫码枪添加对账明细", response = MaterialVerifyScanVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "verifyId", value = "对账ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "contractId", value = "合同ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "inputVal", value = "输入框内容", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "scan", value = "是否为扫码枪内容", required = true, dataType = "Boolean", paramType = "query")
    })
    @GetMapping("/verify/scan")
    public ResponseEntity<SuccessResponse> verifyDetail(@RequestParam String verifyId,
                                                        @RequestParam String contractId,
                                                        @RequestParam String inputVal,
                                                        @RequestParam Boolean scan) {
        List<MaterialVerifyScanVO> materialVerifyScanVOS = verifyService.scanRangeCheck(verifyId, contractId, inputVal, scan);
        return ResponseEntity.ok(new SuccessResponse(materialVerifyScanVOS));
    }

    @ApiOperation(value = "扫码枪添加对账明细-确定添加")
    @PostMapping("/verify/scan")
    public ResponseEntity<SuccessResponse> scanSubmit(@RequestBody RelationVerifyReceiveForm verifyReceiveForm) {
        List<MaterialVerifyScanSubmitVO> list = verifyService.scanSubmit(verifyReceiveForm.getSupplierId(), verifyReceiveForm.getContractId(),
                verifyReceiveForm.getVerifyId(), verifyReceiveForm.getReviseIds());
        return ResponseEntity.ok(new SuccessResponse(list));
    }



    @ApiOperation(value = "对账人列表")
    @GetMapping("/verify/person")
    public ResponseEntity<SuccessResponse> verifyPerson() {
        List<VerifyPersonVO> verifyPersonVOList = verifyService.listVerifyPerson();
        return ResponseEntity.ok(new SuccessResponse(verifyPersonVOList));
    }

}
