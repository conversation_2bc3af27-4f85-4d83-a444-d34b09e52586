package cn.pinming.microservice.material.management.biz.service.impl;

import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.entity.MaterialBrand;
import cn.pinming.microservice.material.management.biz.form.MaterialBrandForm;
import cn.pinming.microservice.material.management.biz.mapper.MaterialBrandMapper;
import cn.pinming.microservice.material.management.biz.query.PurchaseBrandQuery;
import cn.pinming.microservice.material.management.biz.service.IMaterialBrandService;
import cn.pinming.microservice.material.management.biz.vo.MaterialBrandVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 材料品牌 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Service
public class MaterialBrandServiceImpl extends ServiceImpl<MaterialBrandMapper, MaterialBrand>
    implements IMaterialBrandService {

    @Resource
    private AuthUserHolder authUserHolder;

    private AuthUser getAuthUser() {
        return authUserHolder.getCurrentUser();
    }

    @Override
    public List<MaterialBrandVO> queryListByQuery(PurchaseBrandQuery query) {
        query.setProjectId(getAuthUser().getCurrentProjectId());
        return this.getBaseMapper().selectNameByQuery(query);
    }

    @Override
    public String saveMaterialBrand(MaterialBrandForm form) {
        Integer categoryId = form.getCategoryId();
        String brand = form.getBrand();
        MaterialBrand materialBrand = this.getOne(
            new LambdaQueryWrapper<MaterialBrand>().eq(MaterialBrand::getCategoryId, categoryId)
                .eq(MaterialBrand::getBrand, brand));
        if (Objects.isNull(materialBrand)) {
            materialBrand = new MaterialBrand();
            BeanUtils.copyProperties(form, materialBrand);
            this.save(materialBrand);
        }
        return brand;
    }


}
