package cn.pinming.microservice.material.management.proxy;

import cn.pinming.microservice.material.management.biz.dto.EmployeeSimpleDTO;
import cn.pinming.v2.company.api.dto.EmployeeDetailDto;
import cn.pinming.v2.company.api.dto.EmployeeDetailQueryDto;
import cn.pinming.v2.company.api.dto.EmployeeDto;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface EmployeeServiceProxy {


    /**
     * 查询在职员工信息
     *
     * @param companyId 企业id
     * @param memberId  用户ID
     * @return 员工信息
     */
    EmployeeDto findEmployee(Integer companyId, String memberId);

    /**
     * 按条件查询企业人员列表
     * @param employeeDetailQueryDto
     * @return
     */
    List<EmployeeDetailDto> employeeList(@NotNull EmployeeDetailQueryDto employeeDetailQueryDto);

    List<EmployeeSimpleDTO> employeeList(Integer companyId, List<String> memberIds);

}
