package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.entity.MaterialWarningConfig;
import cn.pinming.microservice.material.management.biz.form.WarningTypeConfigForm;
import cn.pinming.microservice.material.management.biz.mapper.MaterialWarningConfigMapper;
import cn.pinming.microservice.material.management.biz.service.IMaterialWarningConfigService;
import cn.pinming.microservice.material.management.biz.service.IMaterialWarningService;
import cn.pinming.microservice.material.management.biz.vo.WarningTypeVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 预警统计配置 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-08-24 10:27:10
 */
@Service
public class MaterialWarningConfigServiceImpl extends ServiceImpl<MaterialWarningConfigMapper, MaterialWarningConfig> implements IMaterialWarningConfigService {

    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private MaterialWarningConfigMapper warningConfigMapper;
    @Resource
    private IMaterialWarningService warningService;

    @Override
    public List<String> listWarningConfig() {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer companyId = currentUser.getCurrentCompanyId();
        Integer projectId = currentUser.getCurrentProjectId();
        MaterialWarningConfig warningTypeConfig = warningConfigMapper.getWarningTypeConfig(companyId, projectId);
        return getFinalConfigType(warningTypeConfig);
    }

    @Override
    public void saveWarningTypeConfig(WarningTypeConfigForm warningTypeConfigForm) {
        String warningType = warningTypeConfigForm.getWarningType();
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer companyId = currentUser.getCurrentCompanyId();
        Integer projectId = currentUser.getCurrentProjectId();
        MaterialWarningConfig warningTypeConfig = warningConfigMapper.getWarningTypeConfig(companyId, projectId);
        if (warningTypeConfig != null) {
            warningTypeConfig.setWarningType(warningType);
            updateById(warningTypeConfig);
        } else {
            warningTypeConfig = new MaterialWarningConfig();
            warningTypeConfig.setWarningType(warningType);
            save(warningTypeConfig);
        }
    }

    private List<String> getFinalConfigType(MaterialWarningConfig warningTypeConfig) {
        List<WarningTypeVO> warningTypeVOList = warningService.listWarningType();
        List<String> warningTypeList = warningTypeVOList.stream().map(WarningTypeVO::getWarningType).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList());
        if (warningTypeConfig == null || StrUtil.isEmpty(warningTypeConfig.getWarningType())) {
            return warningTypeList;
        }
        Set<String> configTypeSet = Arrays.stream(warningTypeConfig.getWarningType().split(StrUtil.COMMA)).collect(Collectors.toSet());
        return warningTypeList.stream().filter(type -> !configTypeSet.contains(type)).collect(Collectors.toList());
    }

}
