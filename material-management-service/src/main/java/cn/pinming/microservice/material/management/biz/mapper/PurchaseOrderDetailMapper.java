package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.PreGoodsDTO;
import cn.pinming.microservice.material.management.biz.dto.PurchaseOrderDetailDTO;
import cn.pinming.microservice.material.management.biz.dto.PurchaseOrderDetailSimpleDTO;
import cn.pinming.microservice.material.management.biz.entity.PurchaseOrderDetail;
import cn.pinming.microservice.material.management.biz.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.biz.vo.*;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface PurchaseOrderDetailMapper extends BaseMapper<PurchaseOrderDetail> {

    @InterceptorIgnore(tenantLine = "true")
    List<PurchaseOrderDetailDTO> selectOrderDetailById(@Param("id") String id);


    @InterceptorIgnore(tenantLine = "true")
    List<PurchaseOrderDetailDTO> selectOrderDetailByIds(@Param("orderIds") List<String> orderIds);

    /**
     * 地磅收料
     * @param query
     * @return
     */
    BigDecimal selectPurchaseAmountByQuery(SupplierAnalysisQuery query);

    List<SupplierAnalysisDetailVO> selectSupplierAnalysisPageVO(SupplierAnalysisQuery query);

    IPage<SupplierAnalysisDetailVO> selectSupplierPageVO(SupplierAnalysisQuery query);

    IPage<MaterialReceiveVO> selectSupplierAnalysisDetailPageVO(SupplierAnalysisQuery query);

    /**
     * 获取上下限和结算单位
     *
     * @param purchaseId
     * @param materialId
     * @return
     */
    ThresholdVO selectThreshold(@Param("purchaseId")String purchaseId,@Param("materialId")Integer materialId);

    /**
     * 各收货材料信息
     *
     * @param orderId
     * @return
     */
    List<GoodsSimpleVO> selectMaterialInfo(@Param("orderId")String orderId);

    List<PreGoodsDTO> listGoods(@Param("orderId") String orderId);

    GoodsSimpleVO selectInfo(@Param("purchaseId")String purchaseId,@Param("materialId")Integer materialId);

    IPage<MaterialReceiveVO> selectSupplierAnalysisDetailMobilePageVO(SupplierAnalysisQuery query);

    IPage<MaterialReceiveVO> selectSupplierAnalysisDetailUnionPageVO(SupplierAnalysisQuery query);

    @InterceptorIgnore(tenantLine = "true")
    List<PurchaseOrderDetailDTO> purchaseDetail(@Param("purchaseId")String purchaseId);

    String selectContractDetailId(@Param("purchaseOrderDetailId") String purchaseOrderDetailId);

    List<GoodsForReviseVO> listForRevise(String purchaseOrderId);

    List<String> queryHistoryUsePartByProjectId(@Param("projectId") Integer projectId, @Param("remark") String remark);

    PurchaseOrderDetailSimpleDTO selectMaterialDetailByPurchaseOrderIdAndMaterialId(String purchaseOrderId, Integer materialId);
}
