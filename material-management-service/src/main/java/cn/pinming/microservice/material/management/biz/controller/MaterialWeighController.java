package cn.pinming.microservice.material.management.biz.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.material.v2.Material;
import cn.pinming.material.v2.MaterialClientBuilder;
import cn.pinming.material.v2.model.query.OcrTemplateQuery;
import cn.pinming.material.v2.model.query.ReceiptRecycleQuery;
import cn.pinming.material.v2.model.vo.OcrTemplateVO;
import cn.pinming.material.v2.model.vo.ReceiptRecycleDataVO;
import cn.pinming.material.v2.model.vo.ReceiptRecycleVO;
import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.MaterialData;
import cn.pinming.microservice.material.management.biz.entity.MaterialSendReceive;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContract;
import cn.pinming.microservice.material.management.biz.entity.Supplier;
import cn.pinming.microservice.material.management.biz.enums.DeviationStatusEnum;
import cn.pinming.microservice.material.management.biz.enums.HandleTypeEnum;
import cn.pinming.microservice.material.management.biz.form.BatchSaveAndFixForm;
import cn.pinming.microservice.material.management.biz.form.RecycleFixForm;
import cn.pinming.microservice.material.management.biz.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.biz.query.MaterialWeighQuery;
import cn.pinming.microservice.material.management.biz.query.WeighbridgeSendQuery;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.biz.vo.MaterialWeighInfoVO;
import cn.pinming.microservice.material.management.biz.vo.WeighReceiveVO;
import cn.pinming.microservice.material.management.biz.vo.WeighbridgeSendDetailVO;
import cn.pinming.microservice.material.management.biz.vo.WeighbridgeSendVO;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.UserUtil;
import cn.pinming.microservice.material.management.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.v2.company.api.dto.EmployeeDto;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 过磅数据 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Api(tags = "地磅收货-controller", value = "zh")
@RestController
@RequestMapping("/api/weight")
public class MaterialWeighController {

    @Resource
    private IMaterialSendReceiveService sendReceiveService;
    @Resource
    private IMaterialDataService dataService;
    @Resource
    private MaterialDataMapper materialDataMapper;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private IMaterialHandlerService handlerService;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private UserUtil userUtil;
    @Resource
    private ISdkConfigService sdkConfService;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;
    @Resource
    private IPurchaseContractDetailService contractDetailService;
    @Resource
    private IPurchaseContractService contractService;
    @Resource
    private ISupplierService supplierService;

    @ApiOperation(value = "地磅收货列表", response = MaterialWeighInfoVO.class)
    @Log(title = "地磅收货列表", businessType = BusinessType.QUERY)
    @PostMapping("/showInfo")
    public ResponseEntity<Response> showWeightInfo(@RequestBody MaterialWeighQuery query) {
        AuthUser user = authUserHolder.getCurrentUser();
        query.setCompanyId(user.getCurrentCompanyId());
        if (user.getCurrentDepartmentId() != null) {
            query.setProjectIds(projectServiceProxy.getAllProjectIdByCompanyDeptId(user.getCurrentCompanyId(), user.getCurrentDepartmentId()));
            if (CollUtil.isEmpty(query.getProjectIds())) {
                return ResponseEntity.ok(new SuccessResponse(new Page<>()));
            }
        }
        query.setProjectId(user.getCurrentProjectId() == null ? query.getProjectId() : user.getCurrentProjectId());
        IPage<MaterialWeighInfoVO> materialWeighInfoVOList = sendReceiveService.showWeightInfo(query);
        return ResponseEntity.ok(new SuccessResponse(materialWeighInfoVOList));
    }

    @ApiOperation(value = "地磅收货明细", response = WeighReceiveVO.class)
    @Log(title = "地磅收货明细", businessType = BusinessType.QUERY)
    @PostMapping("/showDetail")
    public ResponseEntity<Response> showWeightDetail(@RequestBody MaterialWeighQuery query) {
        AuthUser user = authUserHolder.getCurrentUser();
        query.setCompanyId(user.getCurrentCompanyId());
        WeighReceiveVO weighReceiveVO = dataService.showWeightDetail(query);
        return ResponseEntity.ok(new SuccessResponse(weighReceiveVO));
    }

    @ApiOperation(value = "关联采购单")
    @Log(title = "关联采购单", businessType = BusinessType.OTHER)
    @GetMapping("/relate/{receiveId}/{orderNo}")
    public ResponseEntity<Response> relatePurchaseOrder(@PathVariable("receiveId") String receiveId, @PathVariable("orderNo") String orderNo) {
        AuthUser user = authUserHolder.getCurrentUser();
        sendReceiveService.relatePurchaseOrder(receiveId, orderNo, user);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "地磅发货列表", response = WeighbridgeSendVO.class)
    @Log(title = "地磅发货列表", businessType = BusinessType.QUERY)
    @PostMapping("/send/showInfo")
    public ResponseEntity<Response> sendShowInfo(@RequestBody WeighbridgeSendQuery query) {
        AuthUser user = authUserHolder.getCurrentUser();
        query.setCompanyId(user.getCurrentCompanyId());
        if (user.getCurrentDepartmentId() != null) {
            query.setProjectIds(projectServiceProxy.getAllProjectIdByCompanyDeptId(user.getCurrentCompanyId(), user.getCurrentDepartmentId()));
            if (CollUtil.isEmpty(query.getProjectIds())) {
                return ResponseEntity.ok(new SuccessResponse(new Page<>()));
            }
        }
        query.setProjectId(user.getCurrentProjectId() == null ? query.getProjectId() : user.getCurrentProjectId());
        IPage<WeighbridgeSendVO> weighbridgeSendVOIPage = sendReceiveService.selectWeighbridgeSendInfo(query);
        return ResponseEntity.ok(new SuccessResponse(weighbridgeSendVOIPage));
    }

    @ApiOperation(value = "地磅发货详情", response = WeighbridgeSendDetailVO.class)
    @Log(title = "地方发货详情", businessType = BusinessType.QUERY)
    @PostMapping("/send/showDetail")
    public ResponseEntity<Response> sendShowDetail(@RequestBody WeighbridgeSendQuery weighbridgeSendQuery) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        weighbridgeSendQuery.setCompanyId(currentUser.getCurrentCompanyId());
        WeighbridgeSendDetailVO weighbridgeSendDetailVO = dataService.showWeighBridgeSendDetail(weighbridgeSendQuery);
        return ResponseEntity.ok(new SuccessResponse(weighbridgeSendDetailVO));
    }

    @ApiOperation(value = "扫码添加记录权限")
    @Log(title = "扫码添加记录权限", businessType = BusinessType.QUERY)
    @GetMapping("/recordPrivilege/{type}")
    public ResponseEntity<Response> recordPrivilege(@PathVariable("type") Byte type) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        String userId = currentUser.getId();
        Boolean enable = false;
        if (currentUser.getCurrentProjectId() != null) {
            HandleTypeEnum[] values = HandleTypeEnum.values();
            for (HandleTypeEnum handleTypeEnum : values) {
                if (handleTypeEnum.value() == type) {
                    enable = handlerService.enableHandle(userId, handleTypeEnum.value());
                }
            }
        }
        return ResponseEntity.ok(new SuccessResponse(enable));
    }

    @ApiOperation(value = "收货单位枚举")
    @Log(title = "收货单位枚举", businessType = BusinessType.QUERY)
    @PostMapping("/send/company")
    public ResponseEntity<Response> sendCompany() {
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
        Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();
        List<String> list = materialDataMapper.sendCompany(companyId,projectId);
        return ResponseEntity.ok(new SuccessResponse(list));
    }


    @ApiOperation(value = "收料详情打印")
    @Log(title = "收料详情打印", businessType = BusinessType.QUERY)
    @PostMapping("/print/{templateId}")
    public ResponseEntity<Response> detailPrint(@RequestBody MaterialWeighQuery query, @PathVariable String templateId) {
        AuthUser user = authUserHolder.getCurrentUser();
        query.setCompanyId(user.getCurrentCompanyId());
        String templateContent = dataService.print(query, templateId);
        return ResponseEntity.ok(new SuccessResponse(templateContent));
    }

    @ApiOperation(value = "称重数据删除")
    @Log(title = "称重数据删除", businessType = BusinessType.DELETE)
    @GetMapping("/delete/{id}")
    public ResponseEntity<Response> delete(@PathVariable("id") String id) {
        dataService.delete(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "根据合同id查询回收单模板列表", response = OcrTemplateVO.class)
    @PostMapping("/recycle/template/list")
    public ResponseEntity<Response> recycleTemplateList(@RequestBody OcrTemplateQuery query) {
        Integer companyId = userUtil.getCompanyId();
        Integer projectId = userUtil.getProjectId();
        SdkConfigDTO sdkConfig = sdkConfService.getSdkConfig(companyId, projectId);
        Material materialClient = new MaterialClientBuilder().build(sdkConfig.getHost(), sdkConfig.getAppKey(), sdkConfig.getAppSecretKey());
        List<OcrTemplateVO> result = materialClient.ocrTemplateList(query);
        if (CollUtil.isEmpty(result)) {
            throw new BOException(BOExceptionEnum.LOGIC_ERROR.errorCode(), "未找到所选合同的单据模板");
        }
        return ResponseEntity.ok(new SuccessResponse(result));
    }


    @ApiOperation(value = "根据模板id查询回收单列表", response = ReceiptRecycleDataVO.class)
    @PostMapping("/recycle/info/list")
    public ResponseEntity<Response> recycleInfoList(@RequestBody ReceiptRecycleQuery query) {
        Integer companyId = userUtil.getCompanyId();
        Integer projectId = userUtil.getProjectId();
        SdkConfigDTO sdkConfig = sdkConfService.getSdkConfig(companyId, projectId);
//        sdkConfig.setHost("localhost:8080");
        Material materialClient = new MaterialClientBuilder().build(sdkConfig.getHost(), sdkConfig.getAppKey(), sdkConfig.getAppSecretKey());
        ReceiptRecycleDataVO result = materialClient.receiptRecycleList(query);
        if (result != null && CollUtil.isNotEmpty(result.getList())) {
            List<Long> recycleIds = result.getList().stream().map(ReceiptRecycleVO::getId).collect(Collectors.toList());
            List<RecycleDataIdDTO> dataIds = materialDataMapper.selectDataIdsByRecycleId(recycleIds, projectId);
            Map<String, RecycleDataIdDTO> recycleDataIdMap = new HashMap<>();
            if (CollUtil.isNotEmpty(dataIds)) {
                recycleDataIdMap = dataIds.stream().collect(Collectors.toMap(RecycleDataIdDTO::getRecycleId, t -> t));
            }
            Map<String, RecycleDataIdDTO> finalRecycleDataIdMap = recycleDataIdMap;
            result.getList().forEach(t -> {
                Long id = t.getId();
                if (finalRecycleDataIdMap.containsKey(id.toString())) {
                    RecycleDataIdDTO dataIdDTO = finalRecycleDataIdMap.get(id.toString());
                    t.setDataId(dataIdDTO.getDataId());
                    t.setReceiveId(dataIdDTO.getReceiveId());
                }

                String contractId = t.getContractId();
                if (StrUtil.isNotBlank(contractId)) {
                    PurchaseContract purchaseContract = contractService.getById(contractId);
                    if (purchaseContract != null) {
                        t.setContractName(purchaseContract.getName());
                        Integer supplierId = purchaseContract.getSupplierId();
                        if (supplierId != null) {
                            Supplier supplier = supplierService.getById(supplierId);
                            if (supplier != null) {
                                t.setSupplierId(supplier.getId());
                                t.setSupplierName(supplier.getName());
                            }
                        }
                    }
                }
            });
        }
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "批量保存单据回收修订数据")
    @PostMapping("/recycle/fix")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<Response> batchSaveAndFix(@Validated @RequestBody BatchSaveAndFixForm form) {
        List<RecycleFixForm> list = form.getList();
        if (CollUtil.isNotEmpty(list)) {
//            String receiver;
//            EmployeeDto employee = employeeServiceProxy.findEmployee(userUtil.getCompanyId(), userUtil.getMid());
//            if (ObjectUtil.isNotNull(employee)) {
//                receiver = employee.getMemberName();
//            } else {
//                receiver = null;
//            }
            List<MaterialSendReceive> sendReceiveList = list.stream().map(e -> {
                MaterialSendReceive sendReceive = new MaterialSendReceive();
                sendReceive.setExtNo(e.getExtNo());
                sendReceive.setId(e.getReceiveId());
                sendReceive.setCreateId(userUtil.getMid());
//                sendReceive.setReceiver(receiver);
                return sendReceive;
            }).collect(Collectors.toList());
            sendReceiveService.updateBatchById(sendReceiveList);

            List<String> contractDetailIdList = list.stream().map(RecycleFixForm::getContractDetailId)
                    .distinct().collect(Collectors.toList());

            List<SimpleContractDetailDTO> contractDetailList = contractDetailService.querySimpleContractDetails(contractDetailIdList);
            Map<String, SimpleContractDetailDTO> contractDetailMap = new HashMap<>();
            if (CollUtil.isNotEmpty(contractDetailList)) {
                contractDetailMap = contractDetailList.stream().collect(Collectors.toMap(SimpleContractDetailDTO::getContractDetailId, t -> t));
            }

            Map<String, SimpleContractDetailDTO> finalContractDetailMap = contractDetailMap;
            List<MaterialData> dataList = list.stream().map(e -> {
                MaterialData data = new MaterialData();
                BeanUtils.copyProperties(e, data);
                data.setId(e.getDataId());
                if (finalContractDetailMap.containsKey(e.getContractDetailId())) {
                    SimpleContractDetailDTO simpleContractDetailDTO = finalContractDetailMap.get(e.getContractDetailId());
                    data.setMaterialId(simpleContractDetailDTO.getMaterialId());
                    data.setMaterialName(simpleContractDetailDTO.getMaterialName());
                    data.setCategoryId(simpleContractDetailDTO.getCategoryId());
                    data.setCategoryName(simpleContractDetailDTO.getCategoryName());
                    data.setWeightUnit(simpleContractDetailDTO.getUnit());
                    data.setRatio(simpleContractDetailDTO.getConversionRate());
                    data.setReceiveMode((byte)1);
                    data.setCreateId(userUtil.getMid());
                    BigDecimal deviation = NumberUtil.mul(NumberUtil.div(NumberUtil.sub(data.getActualCount(), data.getWeightSend()), data.getWeightSend(), 4), 100);
                    data.setDeviationRate(deviation);
                    // 偏差状态
                    byte deviationStatus = DeviationStatusEnum.NORMAL.value();
                    if (NumberUtil.isLess(data.getDeviationRate(), simpleContractDetailDTO.getDeviationFloor())) {
                        deviationStatus = DeviationStatusEnum.NEGATIVEDIFFERENCE.value();
                    } else if (NumberUtil.isGreater(data.getDeviationRate(), simpleContractDetailDTO.getDeviationCeiling())) {
                        deviationStatus = DeviationStatusEnum.POSITIVEDIFFERENCE.value();
                    }

                    data.setDeviationStatus(deviationStatus);
                    data.setActualReceive(data.getActualCount());
                    boolean flag2 = data.getDeviationStatus() == DeviationStatusEnum.POSITIVEDIFFERENCE.value() || data.getDeviationStatus() == DeviationStatusEnum.NORMAL.value();
                    if (flag2) {
                        data.setActualReceive(data.getWeightSend());
                    }
                    data.setPushState((byte) 0);
                }
                return data;
            }).collect(Collectors.toList());
            dataService.updateBatchById(dataList);
        }
        return ResponseEntity.ok(new SuccessResponse());
    }
}
