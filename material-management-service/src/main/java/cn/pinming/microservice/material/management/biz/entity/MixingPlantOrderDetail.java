package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 拌合站订单明细
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-28 15:25:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_mixing_plant_order_detail")
@ApiModel(value = "MixingPlantOrderDetail对象", description = "拌合站订单明细")
public class MixingPlantOrderDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单明细id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "拌合站订单id")
    @TableField("mixing_plant_order_id")
    private String mixingPlantOrderId;

    @ApiModelProperty(value = "采购单明细id")
    @TableField("purchase_order_detail_id")
    private String purchaseOrderDetailId;

    @ApiModelProperty(value = "材料ID（拌合站）")
    @TableField("material_id")
    private Integer materialId;

    @ApiModelProperty(value = "二级分类ID（拌合站）")
    @TableField("category_id")
    private Integer categoryId;

    @ApiModelProperty(value = "参数要求（拌合站）")
    @TableField("parameter_requirements")
    private String parameterRequirements;

    @ApiModelProperty(value = "下单数量（拌合站）")
    private BigDecimal count;

    @ApiModelProperty(value = "下单单位（拌合站）")
    private String unit;

    @ApiModelProperty(value = "计划使用部位（拌合站）")
    private String position;

    @ApiModelProperty(value = "订单明细状态，1：待配料；2：配料中；3：生产中；4：发货中；5：发货完毕 7：已作废")
    private Byte status;

    @ApiModelProperty(value = "完成关闭时间")
    @TableField(value = "finish_time")
    private LocalDateTime finishTime;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
