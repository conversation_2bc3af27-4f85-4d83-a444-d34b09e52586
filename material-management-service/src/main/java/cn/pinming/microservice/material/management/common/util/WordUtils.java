package cn.pinming.microservice.material.management.common.util;

import cn.hutool.core.util.StrUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import lombok.SneakyThrows;
import org.springframework.core.io.ClassPathResource;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2021/10/26 11:17 上午
 */
public class WordUtils {

    /**
     * 获取指定的模板地址
     *
     * @param fileName 文件名称
     * @return
     */
    public static InputStream getTemplatePath(String fileName) {
        String tempPath = StrUtil.format("templates/{}.docx", fileName);
        ClassPathResource classPathResource = new ClassPathResource(tempPath);
        InputStream inputStream = null;
        try {
            inputStream = classPathResource.getInputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return inputStream;
    }

    /**
     * 渲染word文件到指定文件地址
     *
     * @param inputStream 模板文件地址
     * @param destFile    目标文件地址
     * @param data        数据
     * @return
     */
    @SneakyThrows
    public static String render(InputStream inputStream, String destFile, Object data) {
        XWPFTemplate template = XWPFTemplate.compile(inputStream).render(data);
        template.writeAndClose(new FileOutputStream(destFile));
        return destFile;
    }

    @SneakyThrows
    public static String render(InputStream inputStream, String destFile, Object data, Configure config) {
        XWPFTemplate template = XWPFTemplate.compile(inputStream, config).render(data);
        template.writeAndClose(new FileOutputStream(destFile));
        return destFile;
    }


}
