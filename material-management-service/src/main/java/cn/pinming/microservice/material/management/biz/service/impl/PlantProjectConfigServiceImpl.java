package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.entity.PlantProjectConfig;
import cn.pinming.microservice.material.management.biz.mapper.PlantProjectConfigMapper;
import cn.pinming.microservice.material.management.biz.service.IPlantProjectConfigService;
import cn.pinming.microservice.material.management.biz.vo.ProjectVO;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.v2.project.api.dto.ConstructionProjectDto;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 统计配置表-拌合站项目 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-21 10:08:52
 */
@Service
public class PlantProjectConfigServiceImpl extends ServiceImpl<PlantProjectConfigMapper, PlantProjectConfig> implements IPlantProjectConfigService {

    @Resource
    private AuthUserHolder siteContextHolder;

    @Resource
    private ProjectServiceProxy projectServiceProxy;

    @Override
    public PlantProjectConfig queryOneConfig() {
        AuthUser currentUser = siteContextHolder.getCurrentUser();
        String id = currentUser.getId();
        Integer currentCompanyId = currentUser.getCurrentCompanyId();

        QueryWrapper<PlantProjectConfig> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(PlantProjectConfig::getCompanyId, currentCompanyId)
                .eq(PlantProjectConfig::getCreateId, id)
                .eq(PlantProjectConfig::getIsDeleted, 0);
        PlantProjectConfig one = this.getOne(wrapper);
        return one;
    }

    @Override
    public List<ProjectVO> queryPlantProjectConfig() {
        AuthUser currentUser = siteContextHolder.getCurrentUser();
        Integer currentCompanyId = currentUser.getCurrentCompanyId();
        Integer currentDepartmentId = currentUser.getCurrentDepartmentId();
        // 当前企业下所有项目
        List<ProjectVO> allProjectsByCompanyId = projectServiceProxy.getAllProjectsByCompanyDeptId(currentCompanyId, currentDepartmentId);
        // 开通拌合站的项目
        List<Integer> plateIds = projectServiceProxy.getPluginProjectIds(ProjectServiceProxy.PLATE_PLUGIN_NO);
        // 过滤出拌合站项目
        allProjectsByCompanyId = allProjectsByCompanyId.stream().filter(item -> plateIds.contains(item.getProjectId())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(allProjectsByCompanyId)) {
            PlantProjectConfig one = queryOneConfig();
            if (ObjectUtil.isNotNull(one)) {
                String belongProjectId = one.getBelongProjectId();
                if (StringUtils.isNotBlank(belongProjectId)) {
                    List<Integer> checkedProjectIds = Arrays.stream(belongProjectId.split(",")).filter(ObjectUtil::isNotEmpty).map(Integer::parseInt).collect(Collectors.toList());
                    allProjectsByCompanyId.forEach(project -> {
                        Integer projectId = project.getProjectId();
                        ConstructionProjectDto constructionProjectDto = projectServiceProxy.findProject(projectId);
                        project.setDepartmentId(constructionProjectDto.getDepartmentId());
                        if (checkedProjectIds.contains(projectId)) {
                            project.setChecked(true);
                        }
                    });
                }
            }
        }
        return allProjectsByCompanyId;
    }
}
