package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.entity.ProjectExtCode;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 外部编码 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-05-06 15:08:46
 */
public interface IProjectExtCodeService extends IService<ProjectExtCode> {



    /**
     * 编辑地磅外部系统编码时信息回显
     * @param companyId 企业id
     * @param projectId 项目id
     * @return 外部系统编码
     */
    String getExtCode(Integer companyId, Integer projectId);

    /**
     * 编辑外部系统编码
     * @param companyId 企业id
     * @param projectId 项目id
     * @param extCode 外部系统编码
     */
    void editExtCode(Integer projectId, Integer companyId,String extCode);

}
