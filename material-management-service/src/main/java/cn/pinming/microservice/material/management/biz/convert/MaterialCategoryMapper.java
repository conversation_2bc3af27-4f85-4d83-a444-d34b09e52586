package cn.pinming.microservice.material.management.biz.convert;

import cn.pinming.microservice.material.management.biz.vo.MaterialCategoryVO;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialCategoryDto;

import java.util.List;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 对象转化
 *
 * <AUTHOR>
 * @version 2021/9/3 4:21 下午
 */
@Mapper
public interface MaterialCategoryMapper {

    MaterialCategoryMapper INSTANCE = Mappers.getMapper(MaterialCategoryMapper.class);

    @Mappings({
        @Mapping(source = "materialCategoryId",target = "id"),
        @Mapping(source = "materialCategoryName",target = "name")
    })
    MaterialCategoryVO DTOToVO(MaterialCategoryDto dto, @Context CycleAvoidingMappingContext context);

    List<MaterialCategoryVO> DTOToVOs(List<MaterialCategoryDto> dtos,@Context CycleAvoidingMappingContext context);

}
