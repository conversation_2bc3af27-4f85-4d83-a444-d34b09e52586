package cn.pinming.microservice.material.management.biz.listener;

import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.management.biz.form.SupplierForm;
import cn.pinming.microservice.material.management.biz.service.ISupplierService;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2022/4/13 17:34
 */
public class SupplierDataListener extends AnalysisEventListener<SupplierForm> {

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 500;

    /**
     * 缓存的数据
     */
    private List<SupplierForm> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    /**
     * 表头标题
     */
    private static final String[] HEADERS = {"供应商名称(必填)", "组织机构代码", "外部系统代码"};

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        for (String head : HEADERS) {
            boolean contains = headMap.containsValue(head);
            if (!contains) {
                throw new BOException(BOExceptionEnum.SUPPLIER_EXCEL_ILLEGAL);
            }
        }
    }

    @Override
    public void invoke(SupplierForm data, AnalysisContext context) {
//        if (StrUtil.isBlank(data.getName())) {
//            throw new BOException(BOExceptionEnum.LOGIC_ERROR.errorCode(), "请填写供应商名称");
//        }
        if (StrUtil.isNotBlank(data.getName())) {
            cachedDataList.add(data);
        }
        //校验数据供应商名称重复
        long count = cachedDataList.stream().map(e -> StrUtil.trim(e.getName())).distinct().count();
        if (count < cachedDataList.size()) {
            throw new BOException(BOExceptionEnum.SUPPLIER_EXCEL_REPEAT);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        //后置处理
        if (cachedDataList.size() == 0) {
            throw new BOException(BOExceptionEnum.LOGIC_ERROR.errorCode(), "模板内容为空");
        }
    }

}
