package cn.pinming.microservice.material.management.biz.timetask;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpUtil;
import cn.pinming.microservice.material.management.biz.entity.*;
import cn.pinming.microservice.material.management.biz.form.YongYouForm;
import cn.pinming.microservice.material.management.biz.form.YongYouFormBodys;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/6/8 19:36
 */
@Component
@Slf4j
public class YongYouPushTimedTask {

    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private IProjectExtCodeService projectExtCodeService;
    @Resource
    private ISupplierService supplierService;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private IMaterialPushLogService materialPushLogService;
    @Resource
    private IMaterialSendReceiveService sendReceiveService;

    @Value("${yongyou.push.address}")
    private String url;

    @Value("${push.switch}")
    private Boolean enable;

//    @Scheduled(cron = "0 0/5 * * * ? ")
    public void yongYouPushData() {
        if (!enable) {
            return;
        }
        // 需推送的数据
        List<String> pushIds = materialDataService.needPushData();
        if (CollUtil.isEmpty(pushIds)) {
            log.info("无需要推送的数据......");
            return;
        }
        List<MaterialData> materialDataList = materialDataService.lambdaQuery().in(MaterialData::getId, pushIds).list();
        List<MaterialPushLog> errorLogList = Lists.newArrayList();
        for (MaterialData materialData : materialDataList) {
            MaterialSendReceive materialSendReceive = sendReceiveService.getById(materialData.getReceiveId());

            // 通过orderId和contractDetail判断完整性
//            if (StrUtil.isEmpty(materialData.getContractDetailId()) && StrUtil.isEmpty(materialData.getPurchaseOrderId())) {
//                errorLogList.add(buildMaterialPushLog(materialSendReceive.getReceiveNo(), materialData.getId(), "orderId或者contractDetail不完整性"));
//                materialDataService.updatePushDataState(Collections.singletonList(materialData.getId()), (byte) 2);
//                log.info("失败推送,明细ID【{}】",  materialData.getId());
//                continue;
//            }

            // 封装数据
            Integer projectId = materialData.getProjectId();
            List<ProjectExtCode> projectExtCodeList = projectExtCodeService.lambdaQuery().eq(ProjectExtCode::getProjectId, projectId).list();
            ProjectExtCode projectExtCode = projectExtCodeList.get(0);
            YongYouForm yongYouForm = new YongYouForm();
            yongYouForm.setVbillno(materialSendReceive.getReceiveNo());
            yongYouForm.setProjectcode(projectExtCode.getExtCode());

            YongYouFormBodys yongYouFormBodys = new YongYouFormBodys();
            // 供应商判断
            if (materialData.getSupplierId() != null) {
                Supplier supplier = supplierService.getById(materialData.getSupplierId());
                if (supplier != null && StrUtil.isNotEmpty(supplier.getExtCode())) {
                    yongYouForm.setSuppliercode(supplier.getExtCode());
                } else {
                    log.info("该供应商不存在或者没有系统外部代码【supplierId】:{}", materialData.getSupplierId());
                }
            }

            // 物料判断
            if (materialData.getMaterialId() != null) {
                MaterialDto materialDto = materialServiceProxy.materialById(materialData.getMaterialId());
                if (materialDto != null && StrUtil.isNotEmpty(materialDto.getExtCode())) {
                    yongYouFormBodys.setMetailcode(materialDto.getExtCode());
                } else {
                    log.info("该材料不存在或者没有系统外部代码【materialId】:{}", materialData.getMaterialId());
                }
            }

            yongYouFormBodys.setInnum(String.valueOf(materialData.getWeightActual()));
            yongYouForm.setBodys(Collections.singletonList(yongYouFormBodys));

            String pushData = JSONObject.toJSONString(Collections.singletonList(yongYouForm));
            log.info("push data:{}", pushData);
            try {
                String result = HttpUtil.post(url, pushData, 10000);
                log.info("push result:{}", result);
                JSONObject jsonObject = JSONObject.parseObject(result);
                Integer retstatus = (Integer) jsonObject.get("retstatus");
                // 更改数据的推送状态
                if (retstatus == 200) {
                    materialDataService.updatePushDataState(Collections.singletonList(materialData.getId()), (byte) 1);
                    log.info("成功推送,明细ID【{}】", materialData.getId());
                } else {
                    String errorMsg = (String) jsonObject.get("msg");
                    materialDataService.updatePushDataState(Collections.singletonList(materialData.getId()), (byte) 2);
                    log.info("失败推送,明细ID【{}】",  materialData.getId());
                    errorLogList.add(buildMaterialPushLog(materialSendReceive.getReceiveNo(), materialData.getId(), errorMsg));
                }
            } catch (HttpException e) {
                log.error("接口请求超时:{}", e.getMessage());
                materialDataService.updatePushDataState(Collections.singletonList(materialData.getId()), (byte) 2);
                log.info("失败推送,明细ID【{}】",  materialData.getId());
                errorLogList.add(buildMaterialPushLog(materialSendReceive.getReceiveNo(),materialData.getId(), "接口请求超时"));
            }
        }
        materialPushLogService.saveBatch(errorLogList);
    }


    private MaterialPushLog buildMaterialPushLog(String receiveNo, String dataId, String reason) {
        return MaterialPushLog.builder().receiveNo(receiveNo).materialDataId(dataId).reason(reason).build();
    }
}
