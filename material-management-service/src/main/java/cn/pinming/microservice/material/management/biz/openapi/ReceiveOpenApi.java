package cn.pinming.microservice.material.management.biz.openapi;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.upload.OssFile;
import cn.pinming.core.upload.UploadComponent;
import cn.pinming.core.upload.UploadConfig;
import cn.pinming.core.upload.domain.enums.FileTypeEnums;
import cn.pinming.microservice.material.management.biz.controller.CommonController;
import cn.pinming.microservice.material.management.biz.form.StandardMaterialDataForm;
import cn.pinming.microservice.material.management.biz.query.MaterialWeighQuery;
import cn.pinming.microservice.material.management.biz.service.IMaterialDataService;
import cn.pinming.microservice.material.management.biz.service.IMaterialSendReceiveService;
import cn.pinming.microservice.material.management.biz.vo.MaterialWeighInfoVO;
import cn.pinming.microservice.material.management.biz.vo.WeighReceiveVO;
import cn.pinming.microservice.material.management.common.response.Result;
import cn.pinming.microservice.material.management.common.util.OpenApiUtil;
import cn.pinming.microservice.material.management.proxy.FileServiceProxy;
import cn.pinming.openapi.client.OpenIStrategy;
import cn.pinming.openapi.client.annotation.OpenApi;
import cn.pinming.openapi.client.annotation.OpenApiEndpoint;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@OpenApiEndpoint
public class ReceiveOpenApi extends OpenIStrategy {

    @Resource
    private IMaterialSendReceiveService sendReceiveService;
    @Resource
    private IMaterialDataService dataService;
    @Resource
    private CommonController commonController;

    /**
     * 查看收料列表
     */
    @OpenApi(itype = "/material/listReceive")
    public String listReceive(Map<String, String[]> map) {
        MaterialWeighQuery params = OpenApiUtil.parseObject(map,"data",MaterialWeighQuery.class);
        log.info("----根据条件params查看收料列表 ------>{}" + params);
        Integer companyId = OpenApiUtil.getCompanyId(map);
        params.setCompanyId(companyId);
        IPage<MaterialWeighInfoVO> result = sendReceiveService.showWeightInfo(params);
        return OpenApiUtil.toJSONString(Result.success(result));
    }

    /**
     * 查看收料详情
     */
    @OpenApi(itype = "/material/detailReceive")
    public String detailReceive(Map<String, String[]> map) {
        String data = getMapValue(map,"data");
        log.info("----根据条件data查看收料详情 ------>{}" + data);
        Integer companyId = OpenApiUtil.getCompanyId(map);
        MaterialWeighQuery query = JSONObject.parseObject(data, MaterialWeighQuery.class);
        query.setCompanyId(companyId);
        WeighReceiveVO vo = dataService.showWeightDetail(query);
        return OpenApiUtil.toJSONString(Result.success(vo));
    }

    /**
     * 接收数据
     * @param map
     * @return
     */
    @OpenApi(itype = "/material/receive")
    public String receive(Map<String, String[]> map) {
        String data = getMapValue(map,"data");
        List<StandardMaterialDataForm> list= JSONArray.parseArray(data, StandardMaterialDataForm.class);
        if (CollUtil.isEmpty(list)) {return OpenApiUtil.toJSONString(Result.build("数据列表为空"));}
        Integer companyId = list.get(0).getCompanyId();
        log.info("----openApi接收来自{}" + companyId + "企业的数据:{data} ------>{}" + data);
        picTranslate(list);
        commonController.sync(list);
        return OpenApiUtil.toJSONString(Result.success());
    }

    @Value("${temporary.path}")
    private String temporaryPath;
    @Resource
    private FileServiceProxy fileServiceProxy;
    private void picTranslate(List<StandardMaterialDataForm> list) {
        list.stream().forEach(e -> {
            Map<String,String> pathToUuid = new HashMap<>();
            Map<String,String> urlToPath = new HashMap<>();
            List<String> pics = new ArrayList<>();
            List<String> documentPics = e.getPicList();
            pics.addAll(documentPics);
            e.getMaterialDataList().stream().forEach(obj -> {
                pics.addAll(obj.getGrossPicList());
                pics.addAll(obj.getTarePicList());
            });


            if (CollUtil.isNotEmpty(pics)) {
                // 下载
                List<String> paths = new ArrayList<>();
                for (int i = 0; i < pics.size(); i++) {
                    String filePath = temporaryPath + StrUtil.format("{}.jpg", System.currentTimeMillis(),i);
                    downloadImage(pics.get(i),filePath);
                    paths.add(filePath);
                    urlToPath.put(pics.get(i),filePath);
                }

                // 上传
                UploadComponent uploadComponent = fileServiceProxy.getDynamicUploadComponent();
                UploadConfig uploadConfig = new UploadConfig("ff8080815afee284015afee408680001");
                if (CollUtil.isNotEmpty(paths)) {
                    paths.stream().forEach(item -> {
                        File tmp = new File(item);
                        if (tmp.exists()) {
                            try {
                                OssFile ossFile = new OssFile(tmp, "jpg", FileTypeEnums.IMAGE);
                                String uuid = uploadComponent.uploadFile(ossFile, uploadConfig);
                                fileServiceProxy.confirmFile(uuid, "material-management");
                                pathToUuid.put(item,uuid);
                                uploadComponent.close();
                            } catch (Exception exception) {
                                log.error("openApi-图片上传oss出错:{}",item);
                            }
                        }
                    });
                }

                // 替换
                if (CollUtil.isNotEmpty(urlToPath) && CollUtil.isNotEmpty(pathToUuid)) {
                    Map<String,String> urlToUuid = new HashMap<>();
                    urlToPath.forEach((k,v) -> {
                        String uuid = pathToUuid.get(v);
                        urlToUuid.put(k,uuid);
                    });

                    List<String> docCol = e.getPicList().stream().map(m -> urlToUuid.get(m)).collect(Collectors.toList());
                    e.setPicList(docCol);
                    e.getMaterialDataList().stream().forEach(material -> {
                        List<String> grossCol = material.getGrossPicList().stream().map(m -> urlToUuid.get(m)).collect(Collectors.toList());
                        List<String> tareCol = material.getTarePicList().stream().map(m -> urlToUuid.get(m)).collect(Collectors.toList());
                        material.setGrossPicList(grossCol);
                        material.setTarePicList(tareCol);
                    });
                }
            }
        });
    }

    private void downloadImage(String imageUrl, String savePath) {
        try {
            URL url = new URL(imageUrl);
            URLConnection connection = url.openConnection();
            InputStream inputStream = connection.getInputStream();

            byte[] buffer = new byte[1024];
            int bytesRead;
            FileOutputStream outputStream = new FileOutputStream(savePath);

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            outputStream.close();
            inputStream.close();
        } catch (IOException e) {
            log.error("openApi-图片下载本地出错:{}",imageUrl);
        }
    }


    @Override
    protected String toJson(Object o) {
        return JSONObject.toJSONString(o);
    }
}
