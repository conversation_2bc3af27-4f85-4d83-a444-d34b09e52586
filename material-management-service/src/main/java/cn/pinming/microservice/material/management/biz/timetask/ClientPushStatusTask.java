package cn.pinming.microservice.material.management.biz.timetask;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.management.biz.entity.MaterialWeighbridge;
import cn.pinming.microservice.material.management.biz.enums.MaterialWeighbridgeStatusEnum;
import cn.pinming.microservice.material.management.biz.service.IMaterialWeighbridgeService;
import cn.pinming.microservice.material.management.common.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 地磅客户端状态更新定时任务
 */
@Component
@Slf4j
public class ClientPushStatusTask {

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private IMaterialWeighbridgeService weighbridgeService;

//    @Scheduled(cron = "0 * * * * ? ")
//    public void process() {
//        Set<String> keys = redisUtil.keys("material:cli:*");
//        if (CollUtil.isNotEmpty(keys)) {
//            log.error("keys:{}", JSONUtil.toJsonStr(keys));
//            List<String> weighSystemNoList = keys.stream().map(obj -> obj.replace("material:cli:", "")).collect(Collectors.toList());
//            weighbridgeService.lambdaUpdate().set(MaterialWeighbridge::getStatus, MaterialWeighbridgeStatusEnum.ON.value()).in(MaterialWeighbridge::getWeighSystemNo, weighSystemNoList).update();
//            weighbridgeService.lambdaUpdate().set(MaterialWeighbridge::getStatus, MaterialWeighbridgeStatusEnum.OFF.value()).notIn(MaterialWeighbridge::getWeighSystemNo, weighSystemNoList).update();
//        }
//    }

}
