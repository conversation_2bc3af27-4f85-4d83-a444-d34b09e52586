package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.*;
import cn.pinming.microservice.material.management.biz.enums.*;
import cn.pinming.microservice.material.management.biz.form.*;
import cn.pinming.microservice.material.management.biz.iot.service.IWeighbridgeService;
import cn.pinming.microservice.material.management.biz.iot.service.impl.WeighbridgeDeliveryServiceImpl;
import cn.pinming.microservice.material.management.biz.mapper.*;
import cn.pinming.microservice.material.management.biz.query.DeviationInfoQuery;
import cn.pinming.microservice.material.management.biz.query.MobileWeighInfoQuery;
import cn.pinming.microservice.material.management.biz.query.SummaryDeliveryQuery;
import cn.pinming.microservice.material.management.biz.query.WeighInfoQuery;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.NoUtil;
import cn.pinming.microservice.material.management.common.util.PicEchoUtil;
import cn.pinming.microservice.material.management.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.material.management.proxy.FileServiceProxy;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material.management.wrapper.SupplierWrapper;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.v2.company.api.dto.EmployeeDto;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import cn.pinming.weaponx.wrapper.wrapper.ProjectTitleWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 移动收料表 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:30:10
 */
@Service
@Slf4j
public class MobileReceiveServiceImpl extends ServiceImpl<MobileReceiveMapper, MobileReceive> implements IMobileReceiveService {

    @Resource
    private IMobileReceiveTruckService truckService;
    @Resource
    private NoUtil noUtil;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;
    @Resource
    private IMobileReceiveDetailService detailService;
    @Resource
    private MobileReceiveMapper mobileReceiveMapper;
    @Resource
    private SupplierWrapper supplierWrapper;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private ProjectServiceProxy serviceProxy;
    @Resource
    private MobileReceiveDetailMapper receiveDetailMapper;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private IPurchaseOrderService orderService;
    @Resource
    private PurchaseOrderMapper orderMapper;
    @Resource
    private PurchaseContractMapper contractMapper;
    @Resource
    private ProjectTitleWrapper projectTitleWrapper;
    @Resource
    private MaterialSendReceiveMapper materialSendReceiveMapper;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private MaterialDataMapper materialDataMapper;
    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;
    @Resource
    private IMaterialReviseService materialReviseService;
    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private IPurchaseOrderService purchaseOrderService;
    @Resource
    private IWeighbridgeService weighbridgeService;
    @Resource
    private WeighbridgeDeliveryServiceImpl deliveryService;
    @Resource
    private ISupplierService supplierService;
    @Resource
    private IMaterialWeighbridgeService materialWeighbridgeService;
    @Resource
    private ICompanyConfigService companyConfigService;
    @Resource
    private MobileReceiveTotalServiceImpl mobileReceiveTotalService;
    @Resource
    private PicEchoUtil picEchoUtil;

    protected AuthUser getAuthUser() {
        return authUserHolder.getCurrentUser();
    }

    /**
     * 新增收料记录
     *
     * @param form
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(MobileReceiveForm form) {
//      只有项目层的人才可以新增收料记录，所以拦截到的user一定有projectId和companyId
        if (form.getReceiveType() == ReceiveTypeEnum.PURCHASE.value()) {
            if (StrUtil.isBlank(form.getPurchaseId())) {
                throw new BOException(BOExceptionEnum.PURCHASEID_CAN_NOT_EMPTY);
            }
        }
        if (form.getReceiveType() == ReceiveTypeEnum.CONTRACT.value()) {
            if (StrUtil.isBlank(form.getContractId())) {
                throw new BOException(BOExceptionEnum.CONTRACTID_CAN_NOT_EMPTY);
            }
            if (StrUtil.isNotBlank(form.getPurchaseId())) {
                throw new BOException(BOExceptionEnum.PURCHASEID_MUST_BE_EMPTY);
            }
        }
        if (CollectionUtil.isEmpty(form.getList())) {
            throw new BOException(BOExceptionEnum.RECEIVELIST_CAN_NOT_EMPTY);
        }

//      移动收料信息保存
        MobileReceive receive = new MobileReceive();
        String receiveId = IdUtil.simpleUUID();
//      这里的收货项目,实际收货人来源于创建记录的人
        String receiveNo = noUtil.getReceiveNo(form.getProjectId());
        BeanUtils.copyProperties(form, receive);
        receive.setReceiveId(receiveId);
        receive.setReceiveNo(receiveNo);

        if (StrUtil.isNotBlank(form.getPurchaseId()) || StrUtil.isNotBlank(form.getContractId())) {
//          存在一条超负差
            boolean flag1 = form.getList().stream().anyMatch(e -> e.getDeviationStatus() == DeviationStatusEnum.NEGATIVEDIFFERENCE.value());
//          不存在超负差，有一个超正差
            boolean flag2 = !flag1 && form.getList().stream().anyMatch(e -> e.getDeviationStatus() == DeviationStatusEnum.POSITIVEDIFFERENCE.value());
            if (flag1) {
                receive.setDeviationStatus(DeviationStatusEnum.NEGATIVEDIFFERENCE.value());
            } else if (flag2) {
                receive.setDeviationStatus(DeviationStatusEnum.POSITIVEDIFFERENCE.value());
            } else {
                receive.setDeviationStatus(DeviationStatusEnum.NORMAL.value());
            }
        }

        super.save(receive);

//      车辆到场信息保存
//      可能存在没报备车辆送货情况，所以不做判断
        List<String> totalList = new ArrayList<>();
        totalList.addAll(form.getGoodsPic());
        totalList.addAll(form.getSendPic());
        totalList.addAll(form.getTruckPic());
        checkActualSize(totalList);
        fileServiceProxy.confirmFiles(totalList, "material-management");

        MobileReceiveTruck receiveTruck = new MobileReceiveTruck();
        BeanUtils.copyProperties(form, receiveTruck);
        receiveTruck.setReceiveId(receiveId);
        receiveTruck.setSendPic(String.join(",", form.getSendPic()));
        receiveTruck.setGoodsPic(String.join(",", form.getGoodsPic()));
        receiveTruck.setTruckPic(String.join(",", form.getTruckPic()));
        truckService.save(receiveTruck);

//      各材料收料信息录入
        SimpleTransformDTO simpleTransformDTO = new SimpleTransformDTO();
        simpleTransformDTO.setType(form.getType());
        simpleTransformDTO.setReceiveType(form.getReceiveType());
        detailService.add(form.getList(), receiveId, simpleTransformDTO);
        String purchaseId = form.getPurchaseId();
        if (StrUtil.isNotBlank(purchaseId)) {
            PurchaseOrder order = orderService.getById(purchaseId);
            //更新采购单状态为收货中
            if (ObjectUtil.isNotEmpty(order)) {
                orderService.updatePurchaseOrderReceiving(order.getId(), form.getCompanyId(), form.getProjectId());
            }
        }
    }

    private void checkActualSize(List<String> list) {
        long count = list.parallelStream().filter(StrUtil::isNotBlank).count();
        if (list.size() != count) {
            throw new BOException(BOExceptionEnum.PIC_SIZE_ERROR);
        }
    }

    /**
     * 收料单详情
     *
     * @param receiveId
     * @return
     */
    @Override
    public ReceiveCardDetailVO detail(String receiveId) {
        AuthUser user = authUserHolder.getCurrentUser();
        ReceiveCardDetailVO vo = mobileReceiveMapper.detail(receiveId);
        List<String> pointsPicList = receiveDetailMapper.getPointsPic(receiveId);
        List<String> pointsPicAll = new ArrayList<>();
        BigDecimal m = new BigDecimal("100");

        if (ObjectUtil.isNotEmpty(vo)) {
            List<String> sendPicList = StrUtil.split(vo.getSendPic(), ",", true, true);
            List<String> goodsPicList = StrUtil.split(vo.getGoodsPic(), ",", true, true);
            List<String> truckPicList = StrUtil.split(vo.getTruckPic(), ",", true, true);
            if (CollUtil.isNotEmpty(pointsPicList)) {
                pointsPicList.stream().forEach(e -> {
                    List<String> pointsPic = StrUtil.split(e, ",", true, true);
                    pointsPicAll.add(pointsPic.get(0));
                });
                vo.setPointsPic(String.join(",", fileServiceProxy.fileDownloadUrlByUUIDs(pointsPicAll)));
            }
            if (CollectionUtil.isNotEmpty(sendPicList)) {
                vo.setSendPic(String.join(",", fileServiceProxy.fileDownloadUrlByUUIDs(sendPicList)));
            }
            if (CollectionUtil.isNotEmpty(goodsPicList)) {
                vo.setGoodsPic(String.join(",", fileServiceProxy.fileDownloadUrlByUUIDs(goodsPicList)));
            }
            if (CollectionUtil.isNotEmpty(truckPicList)) {
                vo.setTruckPic(String.join(",", fileServiceProxy.fileDownloadUrlByUUIDs(truckPicList)));
            }

            if (vo.getSupplierId() != null) {
                supplierWrapper.wrap(vo, user.getCurrentCompanyId());
            }
            if (vo.getReceiverProject() != null) {
                ProjectVO projectVO = serviceProxy.getProjectById(vo.getReceiverProject());
                vo.setReceiverProjectTitle(projectVO.getProjectTitle());
            }
            if (vo.getCreateId() != null) {
                EmployeeDto dto = employeeServiceProxy.findEmployee(user.getCurrentCompanyId(), vo.getCreateId());
                vo.setCreateName(dto.getMemberName());
            }

            List<ReceiveCardMaterialDetailDTO> detailDTOs = receiveDetailMapper.detail(receiveId);
            List<ReceiveCardMaterialDetailVO> vos = new ArrayList<>();
            detailDTOs.forEach(e -> {
                ReceiveCardMaterialDetailVO detailVO = new ReceiveCardMaterialDetailVO();

                BeanUtils.copyProperties(e, detailVO);
                if (StrUtil.isNotBlank(e.getPurchaseId())) {
                    MaterialSimpleDTO dto = orderMapper.selectMaterial(e.getPurchaseId(), e.getMaterialId());
                    if (ObjectUtil.isNotEmpty(dto)) {
                        detailVO.setCount(dto.getCount());
                        detailVO.setDeviationCeiling(dto.getDeviationCeiling());
                        detailVO.setDeviationFloor(dto.getDeviationFloor());
                    }
                }
                if (StrUtil.isNotBlank(e.getContractId())) {
                    MaterialSimpleDTO dto = contractMapper.selectMaterial(e.getContractId(), e.getMaterialId());
                    if (ObjectUtil.isNotEmpty(dto)) {
                        detailVO.setDeviationFloor(dto.getDeviationFloor());
                        detailVO.setDeviationCeiling(dto.getDeviationCeiling());
                    }
                }

                detailVO.setRemark(e.getPurchaseRemark());
//              材料全称
                MaterialDto dto = materialServiceProxy.materialById(e.getMaterialId());
                if (ObjectUtil.isNotEmpty(dto)) {
                    String str = StrUtil.format("{}/{}/{}", dto.getMaterialCategoryName(), dto.getMaterialName(), dto.getMaterialSpec());
                    detailVO.setMaterialName(str);
                    detailVO.setMaterialCategoryName(dto.getMaterialCategoryName());
                    detailVO.setMaterialType(dto.getMaterialName());
                    detailVO.setMaterialSpec(dto.getMaterialSpec());
                }

                vos.add(detailVO);

            });

            vo.setList(vos);
        }

        return vo;
    }

    @Override
    public SummaryAnalysisVO countMobileReceiveSummary(SummaryDeliveryQuery query) {
        return mobileReceiveMapper.countSummary(query);
    }

    @Override
    public WeighInfoVO select(MobileWeighInfoQuery query) {
        AuthUser user = authUserHolder.getCurrentUser();
        List<Integer> projectIdList = this.getProjectIdList(query.getPoint());
        query.setProjectIdList(projectIdList);
        query.setCompanyId(user.getCurrentCompanyId());
        if (CollUtil.isEmpty(query.getProjectIdList())) {
            return null;
        }

        WeighInfoVO vo = new WeighInfoVO();
        Map<String, WeighTruckChangeDTO> map = new HashMap<>();
        Integer sendingCarNumber = 0;

        WeighInfoDTO dto = getBaseMapper().select(query);
        if (ObjectUtil.isNotEmpty(dto)) {
            BeanUtil.copyProperties(dto, vo);
            vo.setSendingCarNumber(sendingCarNumber);
            vo.setWeighingCarCount(sendingCarNumber + vo.getWeighingCarNumber());

            List<WeighTruckChangeDTO> list = getBaseMapper().selectMobileList(query);
            if (CollUtil.isNotEmpty(list)) {
                map = list.parallelStream().collect(Collectors.toMap(WeighTruckChangeDTO::getWeighTime, e -> e));
            }

            List<WeighTruckChangeDTO> result = getList(query, map);

            vo.setList(result);
        } else {
            List<WeighTruckChangeDTO> result = getList(query, map);

            vo.setList(result);
        }
        return vo;
    }


    private List<WeighTruckChangeDTO> getList(MobileWeighInfoQuery query, Map<String, WeighTruckChangeDTO> map) {
        Date start = Date.from(query.getStartDate().atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
        Date end = Date.from(query.getEndDate().atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
        List<DateTime> dateTimes = DateUtil.rangeToList(start, end, DateField.MONTH);
        DateFormat df = new SimpleDateFormat("yyyy-MM");
        List<WeighTruckChangeDTO> result = new ArrayList<>();
        for (DateTime dateTime : dateTimes) {
            WeighTruckChangeDTO dto = new WeighTruckChangeDTO();
            WeighTruckChangeDTO data = map.get(df.format(dateTime));
            if (data != null) {
                BeanUtils.copyProperties(data, dto);
            } else {
                dto.setReceiveNum(0);
                dto.setSendNum(0);
            }
            dto.setWeighTime(df.format(dateTime));
            result.add(dto);
        }
        return result;
    }


    @Override
    public IPage<WeighDeviationDetailVO> selectDeviationList(DeviationInfoQuery query) {
        IPage<WeighDeviationDetailVO> page = new Page<>();

        BigDecimal m = new BigDecimal("100");
        BigDecimal zero = BigDecimal.ZERO;
        List<Integer> projectIdList = this.getProjectIdList(query.getPoint());
        if (CollUtil.isEmpty(projectIdList)) {
            return null;
        }
        AuthUser user = authUserHolder.getCurrentUser();
        query.setCompanyId(user.getCurrentCompanyId());
        query.setProjectIdList(projectIdList);


        IPage<WeighDeviationDetailVO> result = materialSendReceiveMapper.selectDeviationPage(query);
        List<WeighDeviationDetailVO> list = result.getRecords();
        if (CollUtil.isNotEmpty(list)) {
            List<WeighDeviationDetailVO> listWithSupplier = list.stream().filter(n -> n.getSupplierId() != null).collect(Collectors.toList());
            supplierWrapper.wrap(listWithSupplier, query.getCompanyId());
            projectTitleWrapper.wrap(list, query.getCompanyId());
            list.stream().forEach(e -> {
                MaterialDto dto = materialServiceProxy.materialById(e.getMaterialId());
                e.setMaterialName(StrUtil.format("{}/{}/{}", dto.getMaterialCategoryName(), dto.getMaterialName(), dto.getMaterialSpec()));

                BigDecimal deviationSum = NumberUtil.sub(e.getActualSettlementTotal(), e.getSendSettlementTotal());
                e.setDeviationCount(deviationSum);
                if (e.getSendSettlementTotal() != null && e.getSendSettlementTotal().compareTo(zero) != 0) {
                    BigDecimal deviationRateSum = NumberUtil.mul(NumberUtil.div(deviationSum, e.getSendSettlementTotal(), 4), m);
                    e.setDeviation(deviationRateSum);
                }

                if (e.getReceiveType() == ReceiveTypeEnum.WEIGHWITHPURCHASE.value()) {
                    if (StrUtil.isBlank(e.getPurchaseId())) {
                        e.setReceiveType(ReceiveTypeEnum.WEIGHWITHOUTPURCHASE.value());
                    }
                }
                if (e.getReceiveType() == ReceiveTypeEnum.CONTRACT.value() || e.getReceiveType() == ReceiveTypeEnum.PURCHASE.value()) {
                    e.setReceiveType(ReceiveTypeEnum.CONTRACTANDPURCHASE.value());
                }
            });
        }

        BeanUtil.copyProperties(result, page);
        page.setRecords(list);
        return page;
    }

    @Override
    public Byte getReceiveTypeByReceiveId(String receiveId) {
        return mobileReceiveMapper.selectReceiveTypeByReceiveId(receiveId);
    }

    private List<Integer> getProjectIdList(Integer point) {
        AuthUser user = authUserHolder.getCurrentUser();
        List<Integer> result = new ArrayList<>();
//      用户在项目层，顶点也只能是项目（组织树选分公司，最外面就是项目）
        if (user.getCurrentProjectId() != null) {
            result = Collections.singletonList(user.getCurrentProjectId());
        } else {
//          直属节点 -> 项目列表 （已包含用户设置项目范围）
            List<Integer> list = projectServiceProxy.statisticsProjectIds(user.getCurrentCompanyId(), point);
            result.addAll(list);
        }
        return result;
    }

    @Override
    public QrcodeInfoDTO processQrcodeInfo(String input) {
        String s = "[{}\"]";
        String s1 = input.replaceAll(s, "");
        String s2 = s1.replace("input", "");
        List<String> params = StrUtil.split(s2, CharPool.COMMA);
        Map<String, Object> resultMap = new HashMap(16);
        for (String param : params) {
            String[] kv;
            if (param.contains("gTime") || param.contains("tTime")) {
                kv = param.split(":", 2);
            } else {
                kv = param.split(":");
            }
            if (kv.length == 2) {
                resultMap.put(kv[0], kv[1]);
            }

        }
        return BeanUtil.fillBeanWithMap(resultMap, new QrcodeInfoDTO(), true);
    }

    @Override
    public void checkBillQrcode(QrcodeInfoDTO bill) {
        Integer companyId = getAuthUser().getCurrentCompanyId();
        Integer projectId = getAuthUser().getCurrentProjectId();
        //校验
        if (!QrcodeTypeEnum.POUND_BILL.key().equals(bill.getType())) {
            throw new BOException(BOExceptionEnum.LOGIC_ERROR.errorCode(), "此单据非地磅称重小票");
        }
        //校验
        if (!Objects.equals(companyId, bill.getCoId()) || !Objects.equals(projectId, bill.getPjId())) {
            throw new BOException(BOExceptionEnum.LOGIC_ERROR.errorCode(), "非本项目地磅称重小票");
        }
        //是否有权限在终端上传之前扫码收料
        CompanyConfig companyConfig = companyConfigService.lambdaQuery()
                .eq(CompanyConfig::getCompanyId, companyId)
                .eq(CompanyConfig::getType, CompanyConfigTypeEnum.SCAN.value())
                .one();
        if (ObjectUtil.isEmpty(companyConfig)) {
            throw new BOException(BOExceptionEnum.ADD_SCAN_COMPANY_CONFIG);
        }
        //校验
        int count = materialDataMapper.selectVerifyByWeighId(bill.getTUuid(), companyId);
        if (count > 0) {
            throw new BOException(BOExceptionEnum.LOGIC_ERROR.errorCode(), "此过磅单据已完成对账,不可继续操作");
        }
        FeedBackDTO dto = materialDataMapper.selectFeedBack(bill.getTUuid());
        if (companyConfig.getIsEnable() == 1) {
            // 无权限
            if (ObjectUtil.isEmpty(dto)) {
                // 没上传
                Integer count1 = materialWeighbridgeService.lambdaQuery()
                        .eq(MaterialWeighbridge::getCompanyId, companyId)
                        .eq(MaterialWeighbridge::getProjectId, projectId)
                        //.eq(MaterialWeighbridge::getStatus, MaterialWeighbridgeStatusEnum.OFF.value())
                        .count();
                if (count1 > 0) {
                    // 存在离线状态的地磅
                    throw new BOException(BOExceptionEnum.NET_IS_ERROR);
                } else {
                    // 不存在离线状态的地磅
                    throw new BOException(BOExceptionEnum.DATA_IS_NOT_UPLOAD);
                }
            }
        }
        // TODO: 2022/5/7 还有进出场时间为空的情况
        if (bill.getGTime().isBefore(bill.getTTime())) {
            bill.setWeighType(WeighTypeEnum.RECEIVE.value());
        } else {
            bill.setWeighType(WeighTypeEnum.DELIVERY.value());
        }

        //校验purNo
        String purNo = bill.getPurNo();
        PurchaseOrder purchaseOrder = purchaseOrderMapper.selectOrderByPruNo(purNo, projectId);
        if (StrUtil.isBlank(purNo) || ObjectUtil.isEmpty(purchaseOrder)) {
            //1.purNo为空 或 在本项目内不存在
            bill.setPurNo(null);
        } else {
            //1.purNo在本项目存在，且只有一个物料
            //2.purNo在本项目存在，且有多个物料
            //小程序根据orderId和PurchaseSimpleVO.list进行判断
            bill.setOrderId(purchaseOrder.getId());
            //查询物料
            bill.setPurchaseSimpleVO(orderService.selectOrderInfo(purchaseOrder.getId(), null));
        }
        //修订提示
        if (ObjectUtil.isNotEmpty(dto)) {
            bill.setExist(true);
            bill.setNWeight(dto.getWeightNet());
            if (StrUtil.isNotBlank(dto.getOrderNo())) {
                bill.setPurNo(dto.getOrderNo());
            }
            FeedBackVO vo = new FeedBackVO();
            BeanUtil.copyProperties(dto, vo);
            if (dto.getMaterialId() != null) {
                MaterialDto materialDto = materialServiceProxy.materialById(dto.getMaterialId());
                if (materialDto != null) {
                    vo.setMaterialName(StrUtil.format("{}/{}/{}", materialDto.getMaterialCategoryName(), materialDto.getMaterialName(), materialDto.getMaterialSpec()));
                    vo.setCategoryId(materialDto.getMaterialCategoryId());
                }
            }
            if (dto.getSupplierId() != null) {
                Supplier supplier = supplierService.lambdaQuery()
                        .eq(Supplier::getCompanyId, companyId)
                        .eq(Supplier::getProjectId, projectId)
                        .eq(Supplier::getId, dto.getSupplierId())
                        .one();
                if (ObjectUtil.isNotEmpty(supplier)) {
                    vo.setSupplierName(supplier.getName());
                }
            }
            if (dto.getSupplierId() == null && dto.getSupplierName() != null) {
                vo.setSupplierName(dto.getSupplierName());
            }

            vo.setDocumentPic(picEchoUtil.echo(dto.getDocumentPic(),-1));
            bill.setFeedBackVO(vo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WeighReceiveResultVO weighReceiveCommit(WeighReceiveCommitForm form) {
        AuthUser user = authUserHolder.getCurrentUser();
        WeighReceiveResultVO resultVO = new WeighReceiveResultVO();
        String weighId = form.getTUuid();
        if (form.getMoistureContent() == null) {
            form.setMoistureContent(BigDecimal.ZERO);
        }

        int count1 = materialDataMapper.selectCountByWeighId(weighId, user.getCurrentCompanyId(), true);
        int count2 = materialDataMapper.selectCountByWeighId(weighId, user.getCurrentCompanyId(), false);
        MaterialData data = materialDataService.lambdaQuery()
                .eq(MaterialData::getWeighId, weighId)
                .one();

        if (ObjectUtil.isNull(data) || count1 == 1) {
            // 称重数据第一次上传
            commit(user, form, resultVO);
        } else if (count2 == 1) {
            // 已上传,未归档,跳转修订页面
            resultVO.setResult("跳转修订页面");
            resultVO.setType((byte) 0);
            BeanUtil.copyProperties(data, resultVO);
            resultVO.setDataId(data.getId());
        } else {
            //已上传且对账、归档。
            resultVO.setResult("修订失败");
            resultVO.setType((byte) 3);
        }

        return resultVO;
    }

    /**
     * 称重数据第一次上传
     */
    public void commit(AuthUser user, WeighReceiveCommitForm form, WeighReceiveResultVO resultVO) {
        if (form.getWeighType() == WeighTypeEnum.RECEIVE.value() && form.getReviceType() != null && form.getReviceType() == 3) {
            //无归属收料
            form.setOrderNo(null);
            form.setContractDetailId(null);
        }
//        // 含水率校验
//        ProjectConfig projectConfig = projectConfigService.lambdaQuery()
//                .eq(ProjectConfig::getCompanyId, user.getCurrentCompanyId())
//                .eq(ProjectConfig::getProjectId, user.getCurrentProjectId())
//                .eq(ProjectConfig::getType, ProjectConfigEnum.TWO.value())
//                .one();
//        if (ObjectUtil.isNotNull(projectConfig)) {
//            if (projectConfig.getIsEnable() == 2) {
//                List<String> split = StrUtil.split(projectConfig.getScope(), ",");
//                List<BigDecimal> collect = split.stream().map(BigDecimal::new).collect(Collectors.toList());
//                boolean flag = collect.contains(form.getMoistureContent().setScale(2, RoundingMode.HALF_UP));
//                if (!flag) {
//                    log.warn("{}该数据的含水率不在该项目设置含水率范围内", form.getSNo());
//                    return;
//                }
//            }
//        }

        //未上传 走称重数据上传落库规则、按临时收料落库 || 已上传 无重量
        WeighbridgeAcceptDataDTO dto = new WeighbridgeAcceptDataDTO();
        dto.setDataSourceType(form.getSupplierId());
        dto.setPurchaseId(form.getOrderNo());
        dto.setTruckNo(form.getTNo());
        dto.setReceiveNo(form.getSNo());
        dto.setIsAutoVerify(form.getIsAutoVerify());
        dto.setIsWebPushed(form.getIsWebPushed());
        dto.setTypeDetail(form.getTypeDetail());
        dto.setEnterPicList(form.getEnterPics());
        dto.setLeavePicList(form.getLeavePics());
        dto.setDocumentPicList(form.getDocumentPics());
        dto.setExtNo(form.getExtNo());
        dto.setRecordId1(form.getRecordId1());
        dto.setRecordId2(form.getRecordId2());
        dto.setPosition(form.getPosition());
        dto.setReceiveModeType((byte)1);
        // 进出场时间判断调整
        if (form.getEnterTime() != null && form.getLeaveTime() != null) {
            dto.setEnterTime(form.getEnterTime().isBefore(form.getLeaveTime()) ? form.getEnterTime() : form.getLeaveTime());
            dto.setLeaveTime(form.getEnterTime().isAfter(form.getLeaveTime()) ? form.getEnterTime() : form.getLeaveTime());
            dto.setReceiveTime(form.getEnterTime().isBefore(form.getLeaveTime()) ? form.getEnterTime() : form.getLeaveTime());
        }
        RemarkDTO remarkDTO = RemarkDTO.builder()
                .companyId(form.getCoId()).projectId(form.getPjId()).weighId(form.getTUuid())
                .supplierId(form.getSupplierId()).supplierName(form.getSupplierName())
                .actualReceive(form.getActualReceive())
                .receiverName(form.getReceiverName())
                .build();
        dto.setRemark(JSONUtil.toJsonStr(remarkDTO));
        //组装材料数据
        MaterialDataDTO materialDataDTO = new MaterialDataDTO();
        BeanUtils.copyProperties(form, materialDataDTO);
        materialDataDTO.setWeightGross(form.getGWeight());
        materialDataDTO.setWeightTare(form.getTWeight());
        materialDataDTO.setWeightDeduct(form.getBWeight());
        materialDataDTO.setMoistureContent(form.getMoistureContent());
        dto.setMaterialDataList(Collections.singletonList(materialDataDTO));
        if (form.getWeighType() == WeighTypeEnum.RECEIVE.value()) {
            //收料
            dto.setType(WeighTypeEnum.RECEIVE.value());
            weighbridgeService.processWeighbridgeReceive(dto);
        }
        else if (form.getWeighType() == WeighTypeEnum.DELIVERY.value()) {
            //发料
            if (StrUtil.isNotEmpty(form.getOrderNo())) {
                dto.setPurchaseOrder(getReceiveIdByPurchaseNo(form.getOrderNo()));
            }
            dto.setType(WeighTypeEnum.DELIVERY.value());
            deliveryService.decisionWeighbridgeReceive(dto);
        }
        resultVO.setResult("提交成功");
        resultVO.setType((byte) 1);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void billFix(WeighReceiveFixForm form) {
        MaterialData materialData = materialDataService.getOne(new LambdaQueryWrapper<MaterialData>().eq(MaterialData::getWeighId, form.getTUuid()), false);
        //参数组装
        form.setId(materialData.getId());
        form.setReceiveId(materialData.getReceiveId());
        form.setWeightGross(materialData.getWeightGross());
        form.setWeightTare(materialData.getWeightTare());
        if (form.getRatio() != null && form.getRatio().compareTo(form.getConversionRate()) == 0) {
            form.setIsContractRateUsed(IsContractRateUsedEnum.YES.value());
        } else {
            form.setIsContractRateUsed(IsContractRateUsedEnum.NO.value());
        }
        // 调用修正接口
        materialReviseService.add(form);
        //更新扫码收料次数
//        if (form.getIsScanReceive()) {
//            MaterialSendReceive materialSendReceive = materialSendReceiveMapper.selectById(materialData.getReceiveId());
//            materialSendReceiveService.lambdaUpdate().set(MaterialSendReceive::getScanReceiveCount, materialSendReceive.getScanReceiveCount() + 1)
//                    .eq(MaterialSendReceive::getId, materialSendReceive.getId()).update();
//        }
    }

    @Override
    public Map<String, WeighCarVO> showWeighCar(WeighInfoQuery query) {
        Map<String, WeighCarVO> voMap = new HashMap<>();
        Map<String, List<Integer>> map = new HashMap<>();

        AuthUser user = authUserHolder.getCurrentUser();
        query.setCompanyId(user.getCurrentCompanyId());
//      用户在项目层，顶点也只能是项目（组织树选分公司，最外面就是项目）
        if (user.getCurrentProjectId() != null) {
            query.setProjectIdList(Collections.singletonList(user.getCurrentProjectId()));
            SimpleConstructionProjectDto simpleProject = projectServiceProxy.findSimpleProject(user.getCurrentProjectId());
            if (ObjectUtil.isNotEmpty(simpleProject)) {
                map.put(user.getCurrentProjectId() + "-" + simpleProject.getProjectTitle(), Collections.singletonList(user.getCurrentProjectId()));
            }
        } else {
//          直属节点 -> 项目列表 （已包含用户设置项目范围）
            map = projectServiceProxy.directlyUnderDeptOrProject(user.getCurrentCompanyId(), query.getPoint());
            if (CollUtil.isEmpty(map)) {
                return null;
            }
            List<Integer> projectIdList = map.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
            query.setProjectIdList(projectIdList);
        }
        if (CollUtil.isEmpty(map)) {
            return null;
        }

        List<WeighCarDTO> weighCarDTOList = this.getBaseMapper().showWeighCar(query);
        List<WeighCarDetailDTO> weighCarDetailDTOList = this.getBaseMapper().showWeighCarDetail(query);

        Map<String, List<Integer>> finalMap1 = map;
        map.keySet().forEach(e -> {
            WeighCarVO vo = new WeighCarVO();
            List<WeighCarDTO> list = new ArrayList<>();
            List<WeighCarDetailDTO> detailList = new ArrayList<>();

            if (CollUtil.isNotEmpty(weighCarDTOList)) {
                weighCarDTOList.stream().forEach(m -> {
                    if (finalMap1.get(e).contains(m.getProjectId())) {
                        list.add(m);
                    }
                });
                if (CollUtil.isNotEmpty(list)) {
                    Integer carTotal = list.stream().map(WeighCarDTO::getCarTotal).reduce(Integer::sum).orElse(0);
                    Integer carMonthly = list.stream().map(WeighCarDTO::getCarMonthly).reduce(Integer::sum).orElse(0);
                    vo.setCarTotal(carTotal);
                    vo.setCarMonthly(carMonthly);
                } else {
                    vo.setCarTotal(0);
                    vo.setCarMonthly(0);
                }
            }

            if (CollUtil.isNotEmpty(weighCarDetailDTOList)) {
                weighCarDetailDTOList.stream().forEach(m -> {
                    if (finalMap1.get(e).contains(m.getProjectId())) {
                        detailList.add(m);
                    }
                });
                if (CollUtil.isNotEmpty(detailList)) {
                    Map<String, List<WeighCarDetailDTO>> dateListMap = detailList.stream().collect(Collectors.groupingBy(WeighCarDetailDTO::getDate));
                    Map<String, WeighCarDetailDTO> result = new HashMap<>();

                    dateListMap.keySet().forEach(m -> {
                        WeighCarDetailDTO weighCarDetailDTO = new WeighCarDetailDTO();
                        int sendingCarNum = dateListMap.get(m).stream().mapToInt(WeighCarDetailDTO::getSendingCarNumber).sum();
                        int weighingCarNum = dateListMap.get(m).stream().mapToInt(WeighCarDetailDTO::getWeighingCarNumber).sum();
                        weighCarDetailDTO.setSendingCarNumber(sendingCarNum);
                        weighCarDetailDTO.setWeighingCarNumber(weighingCarNum);
                        result.put(m, weighCarDetailDTO);
                    });

                    List<WeighCarDetailVO> detailVOS = this.getList(query, result);
                    vo.setList(detailVOS);
                } else {
                    List<WeighCarDetailVO> detailVOS = this.getList(query, null);
                    vo.setList(detailVOS);
                }
            }

            voMap.put(e, vo);
        });

        return voMap;
    }

    @Resource
    private ContractReviseImpl contractRevise;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fresh(MobileReceiveUpdateForm form) {
        mobileReceiveCheck(form);
        // 无合同收料合同校验（暂时）
        contractCheck(form.getReceiveId());
        // 修订记录
        contractRevise.revise(form);
        // 偏差状态变更
        List<MobileReceiveTotal> collect = form.getList().stream().map(e -> {
            MobileReceiveTotal total = new MobileReceiveTotal();
            total.setId(e.getTotalId());
            total.setDeviationStatus(e.getDeviationStatus());
            return total;
        }).collect(Collectors.toList());
        mobileReceiveTotalService.updateBatchById(collect);
    }

    @Override
    public SuccessOrFailVO batch(List<MobileMaterialBatchForm> list) {
        AuthUser user = authUserHolder.getCurrentUser();
        list.stream().forEach(e -> e.setPjId(user.getCurrentProjectId()));
        list.stream().forEach(e -> e.setCoId(user.getCurrentCompanyId()));
        // 收料
        List<MobileMaterialBatchForm> receive = list.stream().filter(e -> e.getKind() == 1).collect(Collectors.toList());
        // 发料
        List<MobileMaterialBatchForm> send = list.stream().filter(e -> e.getKind() == 2).collect(Collectors.toList());
        log.info("本次批量上传{}条发料数据", send.size());
        log.info("本次批量上传{}条收料数据", receive.size());

        SuccessOrFailVO successOrFailVO = new SuccessOrFailVO();
        Map<String,String> errorMap = new HashMap<>();
        // 处理收料
        if (CollUtil.isNotEmpty(receive)) {
            receive.stream().forEach(e -> e.setWeighType(WeighTypeEnum.RECEIVE.value()));
            // 更新
            List<MobileMaterialBatchForm> update = receive.stream().filter(e -> e.getExist() == true).collect(Collectors.toList());
            // 新增
            List<MobileMaterialBatchForm> add = receive.stream().filter(e -> e.getExist() == false).collect(Collectors.toList());

            // 批量更新
            if (CollUtil.isNotEmpty(update)) {
                WeighReceiveFixForm form = new WeighReceiveFixForm();
                update.stream().forEach(e -> {
                    weighReceiveFixFormTrans(e, form);
                    try {
                        this.billFix(form);
                        successOrFailVO.receiveUpdateSuccess();
                    } catch (Exception exception) {
                        log.warn(exception.getMessage());
                        errorMap.put(e.getSNo(), exception.getMessage());
                        successOrFailVO.receiveUpdateFail();
                    }
                });
            }
            // 批量新增
            if (CollUtil.isNotEmpty(add)) {
                WeighReceiveCommitForm form = new WeighReceiveCommitForm();
                add.stream().forEach(e -> {
                    e.setSNo(noUtil.getReceiveNo(String.valueOf(e.getPjId())));
                    weighReceiveCommitFormTrans(e, form);
                    try {
                        this.weighReceiveCommit(form);
                        successOrFailVO.receiveUpdateSuccess();
                    } catch (Exception exception) {
                        log.warn(exception.getMessage());
                        errorMap.put(e.getSNo(), exception.getMessage());
                        successOrFailVO.receiveUpdateFail();
                    }
                });
            }
        }

        // 处理发料
        if (CollUtil.isNotEmpty(send)) {
            // 更新
            List<MobileMaterialBatchForm> update = send.stream().filter(e -> e.getExist() == true).collect(Collectors.toList());
            // 新增
            List<MobileMaterialBatchForm> add = send.stream().filter(e -> e.getExist() == false).collect(Collectors.toList());

            // 批量更新
            if (CollUtil.isNotEmpty(update)) {
                WeighSendFixForm form = new WeighSendFixForm();
                update.stream().forEach(e -> {
                    weighSendFixFormTrans(e, form);
                    try {
                        materialReviseService.sendFix(form);
                        successOrFailVO.receiveUpdateSuccess();
                    } catch (Exception exception) {
                        log.warn(exception.getMessage());
                        errorMap.put(e.getSNo(), exception.getMessage());
                        successOrFailVO.receiveUpdateFail();
                    }
                });
            }
            // 批量新增
            if (CollUtil.isNotEmpty(add)) {
                WeighReceiveCommitForm form = new WeighReceiveCommitForm();
                add.stream().forEach(e -> {
                    e.setSNo(noUtil.getSendNo(e.getPjId()));
                    weighReceiveCommitFormTrans(e, form);
                    try {
                        this.weighReceiveCommit(form);
                        successOrFailVO.receiveUpdateSuccess();
                    } catch (Exception exception) {
                        log.warn(exception.getMessage());
                        errorMap.put(e.getSNo(), exception.getMessage());
                        successOrFailVO.receiveUpdateFail();
                    }
                });
            }
        }
        successOrFailVO.setErrorMap(errorMap);

        return successOrFailVO;
    }

    /**
     * 对象转换
     */
    private void weighReceiveFixFormTrans(MobileMaterialBatchForm e, WeighReceiveFixForm form) {
        BigDecimal weightActual = NumberUtil.sub(NumberUtil.sub(e.getGWeight(), e.getTWeight()), e.getBWeight());
        BeanUtils.copyProperties(e, form);
        form.setActualCount(NumberUtil.mul(e.getRatio(), weightActual));
        form.setTruckNo(e.getTNo());
        form.setWeightDeduct(e.getBWeight());
        form.setWeightGross(e.getGWeight());
        form.setWeightTare(e.getTWeight());
        form.setTUuid(e.getTUuid());
        form.setIsAutoVerify((byte) 2);
        form.setIsScanReceive(true);
    }

    /**
     * 对象转换
     */
    private void weighReceiveCommitFormTrans(MobileMaterialBatchForm e, WeighReceiveCommitForm form) {
        BeanUtils.copyProperties(e, form);
        if (StrUtil.isBlank(form.getTUuid())) {
            form.setTUuid(IdUtil.fastSimpleUUID());
        }
        form.setLeaveTime(e.getTTime());
        form.setEnterTime(e.getGTime());
        form.setIsAutoVerify((byte) 2);
        form.setIsWebPushed((byte) 1);
    }

    /**
     * 对象转换
     */
    private void weighSendFixFormTrans(MobileMaterialBatchForm e, WeighSendFixForm form) {
        BigDecimal weightActual = NumberUtil.sub(NumberUtil.sub(e.getGWeight(), e.getTWeight()), e.getBWeight());
        BeanUtils.copyProperties(e, form);
        form.setSendId(e.getReceiveId());
        form.setActualCount(NumberUtil.mul(e.getRatio(), weightActual));
        form.setIsAutoVerify((byte) 2);
    }

    /**
     * 移动收料修订校验
     *
     * @param form
     */
    public void mobileReceiveCheck(MobileReceiveUpdateForm form) {
        // 有合同收料-按合同：合同校验
        if (form.getReceiveType() == 1 && StrUtil.isBlank(form.getContractId())) {
            throw new BOException(BOExceptionEnum.CONTRACTID_CAN_NOT_EMPTY);
        }
    }

    /**
     * 只有无合同收料才能变更合同,其他收料类型暂不支持修订（暂定）
     *
     * @param receiveId
     */
    public void contractCheck(String receiveId) {
        MobileReceive one = this.lambdaQuery()
                .eq(MobileReceive::getReceiveId, receiveId)
                .one();
        if (ObjectUtil.isNull(one)) {
            throw new BOException(BOExceptionEnum.MOBILE_RECEIVE_IS_DELETED);
        }
        if (StrUtil.isNotBlank(one.getContractId())) {
            throw new BOException(BOExceptionEnum.MOBILE_CAN_NOT_UPDATE);
        }
    }

    private List<WeighCarDetailVO> getList(WeighInfoQuery query, Map<String, WeighCarDetailDTO> map) {
        Date start = Date.from(query.getStartDate().atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
        Date end = Date.from(query.getEndDate().atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
        List<DateTime> dateTimes = DateUtil.rangeToList(start, end, DateField.MONTH);
        DateFormat df = new SimpleDateFormat("yyyy-MM");

        List<WeighCarDetailVO> result = new ArrayList<>();
        for (DateTime dateTime : dateTimes) {
            WeighCarDetailVO vo = new WeighCarDetailVO();
            if(CollUtil.isNotEmpty(map)){
                WeighCarDetailDTO dto = map.get(df.format(dateTime));
                if(ObjectUtil.isNotEmpty(dto)){
                    vo.setSendingCarNumber(dto.getSendingCarNumber());
                    vo.setWeighingCarNumber(dto.getWeighingCarNumber());
                }else {
                    vo.setWeighingCarNumber(0);
                    vo.setSendingCarNumber(0);
                }
            }else {
                vo.setWeighingCarNumber(0);
                vo.setSendingCarNumber(0);
            }
            vo.setDate(df.format(dateTime));
            result.add(vo);
        }
        return result;
    }

    private PurchaseOrder getReceiveIdByPurchaseNo(String purchaseNo) {
        return purchaseOrderService.selectIdByPurchaseNo(purchaseNo);
    }
}
