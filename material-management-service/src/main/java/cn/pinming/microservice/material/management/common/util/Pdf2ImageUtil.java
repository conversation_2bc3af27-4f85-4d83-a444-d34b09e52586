//package cn.pinming.microservice.material.management.common.util;
//
//import org.apache.pdfbox.pdmodel.PDDocument;
//import org.apache.pdfbox.rendering.ImageType;
//import org.apache.pdfbox.rendering.PDFRenderer;
//import org.springframework.stereotype.Component;
//
//
//import javax.imageio.ImageIO;
//import java.awt.image.BufferedImage;
//import java.io.ByteArrayOutputStream;
//import java.io.File;
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.List;
//
//@Component
//public class Pdf2ImageUtil {
//
//    public static void pdf2Image(File file,String imgFile){
//        try {
//            PDDocument document = PDDocument.load(file);
//            // 名为PDFRenderer的类将PDF文档呈现为AWT BufferedImage 。
//            PDFRenderer renderer = new PDFRenderer(document);
//            BufferedImage image = renderer.renderImage(0);
//            ImageIO.write(image, "JPEG",new File(imgFile));
//            System.out.println("Image created");
//            document.close();
//
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }
//
//
////    /**
////     * dpi越大转换后越清晰，相对转换速度越慢
////     */
////    private static final Integer DPI = 100;
////
////    /**
////     * 转换后的图片类型
////     */
////    private static final String IMG_TYPE = "png";
////
////    /**
////     * PDF转图片
////     *
////     * @param fileContent PDF文件的二进制流
////     * @return 图片文件的二进制流
////     */
////    public static List<byte[]> pdfToImage(byte[] fileContent) throws IOException {
////        List<byte[]> result = new ArrayList<>();
////        try (PDDocument document = PDDocument.load(fileContent)) {
////            PDFRenderer renderer = new PDFRenderer(document);
////            for (int i = 0; i < document.getNumberOfPages(); ++i) {
////                BufferedImage bufferedImage = renderer.renderImageWithDPI(i, DPI);
////                ByteArrayOutputStream out = new ByteArrayOutputStream();
////                ImageIO.write(bufferedImage, IMG_TYPE, out);
////                result.add(out.toByteArray());
////            }
////        }
////        return result;
////    }
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
////    public static final float DEFAULT_DPI = 105;
////    public static final String DEFAULT_FORMAT = "jpg";
////
/////** pdf转图片
//// * @param pdfPath PDF路径
//// * @imgPath img路径
//// * @page_end 要转换的页码，也可以定义开始页码和结束页码，我这里只需要一页，根据需求自行添加
//// */
////    public static void pdfToImage(String pdfPath, String imgPath,int page_end) {
////        try {
////            //图像合并使用参数
////            // 总宽度
////            int width = 0;
////            // 保存一张图片中的RGB数据
////            int[] singleImgRGB;
////            int shiftHeight = 0;
////            //保存每张图片的像素值
////            BufferedImage imageResult = null;
////            //利用PdfBox生成图像
////            PDDocument pdDocument = PDDocument.load(new File(pdfPath));
////            PDFRenderer renderer = new PDFRenderer(pdDocument);
////            //循环每个页码
////            for (int i = 0, len = pdDocument.getNumberOfPages(); i < len; i++) {
////                if (i==page_end) {
////                    BufferedImage image = renderer.renderImageWithDPI(i, DEFAULT_DPI, ImageType.RGB);
////                    int imageHeight = image.getHeight();
////                    int imageWidth = image.getWidth();
////                    //计算高度和偏移量
////                    //使用第一张图片宽度;
////                    width = imageWidth;
////                    //保存每页图片的像素值
////                    imageResult = new BufferedImage(width, imageHeight, BufferedImage.TYPE_INT_RGB);
////                    //这里有高度，可以将imageHeight*len，我这里值提取一页所以不需要
////                    singleImgRGB = image.getRGB(0, 0, width, imageHeight, null, 0, width);
////                    // 写入流中
////                    imageResult.setRGB(0, shiftHeight, width, imageHeight, singleImgRGB, 0, width);
////                }else if(i>page_end) {
////                    continue;
////                }
////
////
////            }
////
////            pdDocument.close();
////            // 写图片
////            ImageIO.write(imageResult, DEFAULT_FORMAT, new File(imgPath));
////
////        } catch (Exception e) {
////
////            e.printStackTrace();
////        }
////    }
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
////    /***
////     * PDF文件转PNG图片，全部页数
////     *
////     * @param PdfFilePath pdf完整路径
////     * @param imgFilePath 图片存放的文件夹
////     * @param dpi dpi越大转换后越清晰，相对转换速度越慢
////     * @return
////     */
////    public static void pdf2Image(String PdfFilePath, String dstImgFolder, int dpi) {
////        File file = new File(PdfFilePath);
////        PDDocument pdDocument;
////        try {
////            String imgPDFPath = file.getParent();
////            int dot = file.getName().lastIndexOf('.');
////            String imagePDFName = file.getName().substring(0, dot); // 获取图片文件名
////            String imgFolderPath = null;
////            if (dstImgFolder.equals("")) {
////                imgFolderPath = imgPDFPath + File.separator + imagePDFName;// 获取图片存放的文件夹路径
////            } else {
////                imgFolderPath = dstImgFolder + File.separator + imagePDFName;
////            }
////
////            if (createDirectory(imgFolderPath)) {
////
////                pdDocument = PDDocument.load(file);
////                PDFRenderer renderer = new PDFRenderer(pdDocument);
////                /* dpi越大转换后越清晰，相对转换速度越慢 */
////
////                Pdfread reader = new PdfReader(PdfFilePath);
////                int pages = reader.getNumberOfPages();
////                StringBuffer imgFilePath = null;
////                for (int i = 0; i < pages; i++) {
////                    String imgFilePathPrefix = imgFolderPath + File.separator + imagePDFName;
////                    imgFilePath = new StringBuffer();
////                    imgFilePath.append(imgFilePathPrefix);
////                    imgFilePath.append("_");
////                    imgFilePath.append(String.valueOf(i + 1));
////                    imgFilePath.append(".png");
////                    File dstFile = new File(imgFilePath.toString());
////                    BufferedImage image = renderer.renderImageWithDPI(i, dpi);
////                    ImageIO.write(image, "png", dstFile);
////                }
////                System.out.println("PDF文档转PNG图片成功！");
////
////            } else {
////                System.out.println("PDF文档转PNG图片失败：" + "创建" + imgFolderPath + "失败");
////            }
////
////        } catch (IOException e) {
////            e.printStackTrace();
////        }
////    }
////
////    private static boolean createDirectory(String folder) {
////        File dir = new File(folder);
////        if (dir.exists()) {
////            return true;
////        } else {
////            return dir.mkdirs();
////        }
////    }
//
//}
