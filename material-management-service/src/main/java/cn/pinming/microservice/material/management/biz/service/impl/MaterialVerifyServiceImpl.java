package cn.pinming.microservice.material.management.biz.service.impl;

import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.MaterialDataVerifyDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialVerify;
import cn.pinming.microservice.material.management.biz.enums.HandleTypeEnum;
import cn.pinming.microservice.material.management.biz.mapper.MaterialVerifyMapper;
import cn.pinming.microservice.material.management.biz.query.MaterialVerifyQuery;
import cn.pinming.microservice.material.management.biz.service.IMaterialHandlerService;
import cn.pinming.microservice.material.management.biz.service.IMaterialVerifyService;
import cn.pinming.microservice.material.management.biz.vo.CooperateVO;
import cn.pinming.microservice.material.management.biz.vo.MaterialVerifyVO;
import cn.pinming.microservice.material.management.biz.vo.ProjectVO;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.proxy.CooperateServiceProxy;
import cn.pinming.microservice.material.management.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.v2.company.api.dto.EmployeeDto;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 物料对账表 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-18 10:22:28
 */
@Service
public class MaterialVerifyServiceImpl extends ServiceImpl<MaterialVerifyMapper, MaterialVerify> implements IMaterialVerifyService {


}
