package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.entity.PreTruckReport;
import cn.pinming.microservice.material.management.biz.form.PreReportForm;
import cn.pinming.microservice.material.management.biz.form.PreTruckReportForm;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 送货车辆预报备 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-11-16 15:07:43
 */
public interface IPreTruckReportService extends IService<PreTruckReport> {

    /**
     * 预报备 - 新增或编辑送货车辆
     *
     * @param preWeighReportId 预进场报备id
     * @param list             送货车辆列表
     * @param goodTotal        批次计划发货总量
     * @param isCommit         true 提交 false 暂存
     * @param processType     1.作废现存报备记录，使用本次报备 2.保留现存报备记录，迫加本次报备
     *
     */
    void saveOrUpdateTruck(String preWeighReportId, List<PreTruckReportForm> list, BigDecimal goodTotal, boolean isCommit, Byte processType,Integer companyId,Integer projectId);

    /**
     * 删除车辆预报备
     *
     * @param id 车辆预报备id
     */
    void deletePreTruckReport(String id);

    String checkTruckNo(PreReportForm form);
}
