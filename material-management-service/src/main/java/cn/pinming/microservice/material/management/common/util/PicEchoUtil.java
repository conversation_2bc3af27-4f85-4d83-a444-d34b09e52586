package cn.pinming.microservice.material.management.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.management.proxy.FileServiceProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PicEchoUtil {
    @Resource
    private FileServiceProxy fileServiceProxy;

    public List<String> echo(String pic, int limit) {
        if (StrUtil.isBlank(pic)) {
            return null;
        }

        List<String> result = new ArrayList<>();
        List<String> collect = StrUtil.split(pic, ",").stream().filter(e -> StrUtil.isNotBlank(e) & !e.startsWith("D:")).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            collect.stream().forEach(e -> {
                if (limit != -1 && result.size() >= limit ) {
                    return;
                }
                if (e.startsWith("http")) {
                    result.add(e);
                } else {
                    try {
                        result.add(fileServiceProxy.fileDownloadUrlByUUID(e));
                    } catch (Exception ex) {
                        log.info("获取图片出错");
                    }
                }
            });
        }
        return result;
    }
}
