package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.math.MathUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.FourCircleDTO;
import cn.pinming.microservice.material.management.biz.dto.FourCircleUsageDTO;
import cn.pinming.microservice.material.management.biz.entity.StatisticsUseConfig;
import cn.pinming.microservice.material.management.biz.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.biz.mapper.MaterialWeighbridgeMapper;
import cn.pinming.microservice.material.management.biz.mapper.MobileReceiveMapper;
import cn.pinming.microservice.material.management.biz.query.WeighInfoQuery;
import cn.pinming.microservice.material.management.biz.service.IFourCircleService;
import cn.pinming.microservice.material.management.biz.service.IStatisticsUseConfigService;
import cn.pinming.microservice.material.management.biz.vo.FourCircleVO;
import cn.pinming.microservice.material.management.biz.vo.StatisticsUseVO;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class FourCircleImpl implements IFourCircleService {

    @Resource
    private MaterialWeighbridgeMapper materialWeighbridgeMapper;
    @Resource
    private MobileReceiveMapper mobileReceiveMapper;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private IStatisticsUseConfigService useConfigService;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private MaterialDataMapper materialDataMapper;


    @Override
    public FourCircleVO selectRate(Integer point) {
        Integer day = null;

        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (ObjectUtil.isNotNull(currentUser)) {
            String userid = currentUser.getId();
            QueryWrapper<StatisticsUseConfig> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StatisticsUseConfig::getCreateId, userid);
            StatisticsUseConfig config = useConfigService.getOne(queryWrapper);
            if (ObjectUtil.isNotNull(config)) {
                day = config.getDay();
            }
        }
        List<Integer> projectIdList = this.getProjectIdList(point);
        if (CollUtil.isEmpty(projectIdList)) { return null; }

        FourCircleVO vo = new FourCircleVO();
//      条件项目总数
        long projectNum = projectIdList.stream().count();
//      地磅安装数
        BigDecimal installationNum = materialWeighbridgeMapper.selectInstallationNum(projectIdList);
//      地磅安装率
        BigDecimal installationRate = NumberUtil.round(NumberUtil.mul(NumberUtil.div(installationNum, projectNum, 2), 100),0);
        vo.setInstallationRate(installationRate);

//      地磅在线率
        BigDecimal onlineRate = materialWeighbridgeMapper.selectOnlineRate(projectIdList);
        vo.setOnlineRate(onlineRate);

//      地磅使用率
        List<Integer> pjList = materialWeighbridgeMapper.selectInstallationPJList(projectIdList);
        BigDecimal usageRate = materialDataMapper.selectUsageRate(pjList, day);
        vo.setUsageRate(usageRate);

//      移动收料使用率
        Integer mobileUsageNum = mobileReceiveMapper.selectUsageNum(projectIdList, day);
        BigDecimal mobileUsageRate = NumberUtil.round(NumberUtil.mul(NumberUtil.div(mobileUsageNum, BigDecimal.valueOf(projectNum)), 100),0);
        vo.setMobileUsageRate(mobileUsageRate);
        return vo;
    }

    @Override
    public Map<String, FourCircleVO> selectList(Integer point) {
        Integer defaultDay = 7;
        List<Integer> projectIdList = new ArrayList<>();
        Map<String, List<Integer>> map = new HashMap<>();
        Integer day = null;

        AuthUser user = authUserHolder.getCurrentUser();
        String userid = user.getId();
//      用户在项目层，顶点也只能是项目（组织树选分公司，最外面就是项目）
        if (user.getCurrentProjectId() != null) {
            projectIdList.add(user.getCurrentProjectId());
            SimpleConstructionProjectDto simpleProject = projectServiceProxy.findSimpleProject(user.getCurrentProjectId());
            if(ObjectUtil.isNotEmpty(simpleProject)){
                map.put(user.getCurrentProjectId() + "-" + simpleProject.getProjectTitle(), Arrays.asList(user.getCurrentProjectId()));
            }
        } else {
//          直属节点 -> 项目列表 （已包含用户设置项目范围）
            map = projectServiceProxy.directlyUnderDeptOrProject(user.getCurrentCompanyId(), point);
            if (CollUtil.isNotEmpty(map)) {
                projectIdList = map.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
            }
        }
        if (CollUtil.isEmpty(map)) {
            return null;
        }

        QueryWrapper<StatisticsUseConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StatisticsUseConfig::getCreateId, userid);
        StatisticsUseConfig config = useConfigService.getOne(queryWrapper);
        if (ObjectUtil.isNotNull(config)) {
            day = config.getDay();
        }else {
            day = defaultDay;
        }

        List<Integer> installationProjectList = materialWeighbridgeMapper.selectInstallationList(projectIdList);
        List<Integer> onlineProjectList = materialWeighbridgeMapper.selectOnlineList(projectIdList);
        List<Integer> usageProjectList = materialDataMapper.selectUsageList(installationProjectList,day);
        List<Integer> mobileUsageProjectList = mobileReceiveMapper.selectMobileUsageList(projectIdList,day);

        Map<String, List<Integer>> finalMap1 = map;
        Map<String, FourCircleVO> result = new HashMap<>();
        finalMap1.keySet().forEach(e -> {
            FourCircleVO vo = new FourCircleVO();
            List<Integer> installList = new ArrayList<>();
            List<Integer> onlineList = new ArrayList<>();
            List<Integer> usageList = new ArrayList<>();
            List<Integer> mobileUsageList = new ArrayList<>();
            long installCount = 0;
            long count = finalMap1.get(e).stream().count();

            if(count != 0) {
                if(CollUtil.isNotEmpty(installationProjectList)){
                    installationProjectList.stream().forEach(m -> {
                        if (finalMap1.get(e).contains(m)) {
                            installList.add(m);
                        }
                    });
                    if(CollUtil.isNotEmpty(installList)){
                        installCount = installList.stream().count();
                        double installationRate = NumberUtil.mul(NumberUtil.div(installCount, count, 4), 100);
                        vo.setInstallationRate(BigDecimal.valueOf(installationRate));
                    }
                }

                if(CollUtil.isNotEmpty(onlineProjectList)){
                    onlineProjectList.stream().forEach(m -> {
                        if (finalMap1.get(e).contains(m)) {
                            onlineList.add(m);
                        }
                    });
                    if(CollUtil.isNotEmpty(onlineList)){
                        long onlineCount = onlineList.stream().count();
                        if(installCount != 0){
                            double onlineRate = NumberUtil.mul(NumberUtil.div(onlineCount, installCount, 4), 100);
                            vo.setOnlineRate(BigDecimal.valueOf(onlineRate));
                        }
                    }
                }

                if(CollUtil.isNotEmpty(usageProjectList)){
                    usageProjectList.stream().forEach(m -> {
                        if (finalMap1.get(e).contains(m)) {
                            usageList.add(m);
                        }
                    });
                    if(CollUtil.isNotEmpty(usageList)){
                        long usageCount = usageList.stream().count();
                        if(installCount != 0){
                            double usageRate = NumberUtil.mul(NumberUtil.div(usageCount, installCount, 4), 100);
                            vo.setUsageRate(BigDecimal.valueOf(usageRate));
                        }
                    }
                }

                if(CollUtil.isNotEmpty(mobileUsageProjectList)){
                    mobileUsageProjectList.stream().forEach(m -> {
                        if (finalMap1.get(e).contains(m)) {
                            mobileUsageList.add(m);
                        }
                    });
                    if(CollUtil.isNotEmpty(mobileUsageList)){
                        long mobileUsageCount = mobileUsageList.stream().count();
                        double mobileUsageRate = NumberUtil.mul(NumberUtil.div(mobileUsageCount, count, 4), 100);
                        vo.setMobileUsageRate(BigDecimal.valueOf(mobileUsageRate));
                    }
                }
            }

            result.put(e, vo);
        });

        return result;
    }

    private List<Integer> getProjectIdList(Integer point) {
        List<Integer> projectIdList = new ArrayList<>();
        AuthUser user = authUserHolder.getCurrentUser();
//      用户在项目层，顶点也只能是项目（组织树选分公司，最外面就是项目）
        if (user.getCurrentProjectId() != null) {
            projectIdList.add(user.getCurrentProjectId());
        } else {
//          直属节点 -> 项目列表 （已包含用户设置项目范围）
            List<Integer> list = projectServiceProxy.statisticsProjectIds(user.getCurrentCompanyId(), point);
            projectIdList.addAll(list);
        }
        return projectIdList;
    }
}
