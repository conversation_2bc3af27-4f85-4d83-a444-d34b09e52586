//package cn.pinming.microservice.material.management.common.aspectj;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.json.JSONUtil;
//import cn.pinming.core.cookie.AuthUser;
//import cn.pinming.core.cookie.support.AuthUserHolder;
//import cn.pinming.core.web.response.ErrorResponse;
//import cn.pinming.microservice.material.management.biz.entity.MaterialApiLog;
//import cn.pinming.microservice.material.management.common.annotation.Log;
//import cn.pinming.microservice.material.management.common.exception.BOException;
//import cn.pinming.microservice.material.management.common.manager.AsyncFactory;
//import cn.pinming.microservice.material.management.common.manager.AsyncManager;
//import cn.pinming.microservice.material.management.common.util.ServletUtils;
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.base.Stopwatch;
//
//import java.util.*;
//import java.util.concurrent.TimeUnit;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.aspectj.lang.JoinPoint;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.Signature;
//import org.aspectj.lang.annotation.AfterReturning;
//import org.aspectj.lang.annotation.AfterThrowing;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Pointcut;
//import org.aspectj.lang.reflect.MethodSignature;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.stereotype.Component;
//import org.springframework.validation.BindingResult;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import javax.validation.ConstraintViolation;
//import javax.validation.ConstraintViolationException;
//import java.lang.reflect.Method;
//import java.time.LocalDateTime;
//
//@Slf4j
//@Aspect
//@Component
//public class LogAspect {
//
//    @Resource
//    private AuthUserHolder authUserHolder;
//
//    @Pointcut("@annotation(cn.pinming.microservice.material.management.common.annotation.Log)")
////    这是Pointcut的签名，这样在之后使用定义好的Pointcut的时候，可以这样： @Before("logPointCut()")
//    public void logPointCut() {
//    }
//
//
//    /**
//     * 处理完请求后执行
//     *
//     * @param joinPoint 切点
//     */
////    @AfterReturning(pointcut = "logPointCut()", returning = "jsonResult")
////    public void doAfterReturning(JoinPoint joinPoint, Object jsonResult) {
////        handleLog(joinPoint, null, jsonResult);
////    }
//    @Around("logPointCut()")
//    public Object timing(ProceedingJoinPoint joinPoint) throws Throwable {
//        Object object = null;
//        Stopwatch stopwatch = Stopwatch.createStarted();
//        try {
//            object = joinPoint.proceed();
//        } catch (Exception e) {
//            handleLog(joinPoint, e, null, stopwatch.elapsed(TimeUnit.MILLISECONDS));
//
//            if (e instanceof BOException) {
//                return new ResponseEntity<>(new ErrorResponse(((BOException) e).getErrorCode(), ((BOException) e).getErrorMsg()), HttpStatus.OK);
//            }else {
//                Set<ConstraintViolation<?>> constraintViolations = ((ConstraintViolationException) e).getConstraintViolations();
//                for (ConstraintViolation<?> violation : constraintViolations) {
//                    return new ResponseEntity<>(new ErrorResponse("-200", violation.getMessage()), HttpStatus.OK);
//                }
//            }
//        }
//        //log.info("API request completed, takes " + stopwatch.elapsed(TimeUnit.MILLISECONDS) + " ms.");
//        handleLog(joinPoint, null, object, stopwatch.elapsed(TimeUnit.MILLISECONDS));
//        return object;
//    }
//
//    /**
//     * 拦截异常操作
//     *
//     * @param joinPoint 切点
//     * @param e         异常
//     */
////    @AfterThrowing(value = "logPointCut()", throwing = "e")
////    public void doAfterThrowing(JoinPoint joinPoint, Exception e) {
////        handleLog(joinPoint, e, null);
////    }
//
//    protected void handleLog(final JoinPoint joinPoint, final Throwable e, Object jsonResult,
//                             long elapsed) {
//        try {
//            // 获得注解
//            Log controllerLog = getAnnotationLog(joinPoint);
//            if (controllerLog == null) {
//                return;
//            }
//
//            AuthUser currentUser = authUserHolder.getCurrentUser();
//            MaterialApiLog apiLog = new MaterialApiLog();
//            if (currentUser != null) {
//                apiLog.setCompanyId(currentUser.getCurrentCompanyId());
//                apiLog.setProjectId(currentUser.getCurrentProjectId());
//                apiLog.setCreateId(currentUser.getId());
//            }
//            apiLog.setCreateTime(LocalDateTime.now());
//            apiLog.setElapsed(elapsed);
//            if (Objects.nonNull(jsonResult)) {
//                apiLog.setRequestResult(
//                    StringUtils.substring(JSONUtil.toJsonStr(jsonResult), 0, 2000));
//            }
//            apiLog.setRequestUri(ServletUtils.getRequest().getRequestURI());
//            if (e != null) {
//                apiLog.setErrorInfo(StringUtils.substring(e.getMessage(), 0, 2000));
//            }
//            // 处理设置注解上的参数
//            getControllerMethodDescription(joinPoint, controllerLog, apiLog);
//            // 保存数据库
//            AsyncManager.me().execute(AsyncFactory.recordOper(apiLog));
//        } catch (Exception exp) {
//            log.error("异常信息:{}", exp.getMessage());
//            exp.printStackTrace();
//        }
//    }
//
//    /**
//     * 获取注解中对方法的描述信息 用于Controller层注解
//     *
//     * @param log    日志
//     * @param apiLog 操作日志
//     * @throws Exception
//     */
//    public void getControllerMethodDescription(JoinPoint joinPoint, Log log, MaterialApiLog apiLog)
//        throws Exception {
//        // 是否需要保存request，参数和值
//        if (log.isSaveRequestData()) {
//            // 获取参数的信息，传入到数据库中。
//            setRequestValue(joinPoint, apiLog);
//        }
//    }
//
//    /**
//     * 获取请求的参数，放到log中
//     *
//     * @param apiLog 操作日志
//     */
//    private void setRequestValue(JoinPoint joinPoint, MaterialApiLog apiLog) {
//        Map<String, String[]> map = ServletUtils.getRequest().getParameterMap();
//        if (CollUtil.isNotEmpty(map)) {
//            String params = JSONObject.toJSONString(map);
//            apiLog.setRequestParam(params);
//        } else {
//            Object args = joinPoint.getArgs();
//            if (Objects.nonNull(args)) {
//                String params = argsArrayToString(joinPoint.getArgs());
//                apiLog.setRequestParam(params);
//            }
//        }
//    }
//
//
//    /**
//     * 参数拼装
//     */
//    private String argsArrayToString(Object[] paramsArray) {
//        StringBuilder params = new StringBuilder();
//        if (paramsArray != null && paramsArray.length > 0) {
//            for (int i = 0; i < paramsArray.length; i++) {
//                if (Objects.nonNull(paramsArray[i]) && !isFilterObject(paramsArray[i])) {
//                    Object jsonObj = JSONObject.toJSONString(paramsArray[i]);
//                    params.append(jsonObj).append(" ");
//                }
//            }
//        }
//        return params.toString().trim();
//    }
//
//
//    /**
//     * 判断是否需要过滤的对象。
//     *
//     * @param o 对象信息。
//     * @return 如果是需要过滤的对象，则返回true；否则返回false。
//     */
//    @SuppressWarnings("rawtypes")
//    public boolean isFilterObject(final Object o) {
//        Class<?> clazz = o.getClass();
//        if (clazz.isArray()) {
//            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
//        } else if (Collection.class.isAssignableFrom(clazz)) {
//            Collection collection = (Collection) o;
//            for (Iterator iter = collection.iterator(); iter.hasNext(); ) {
//                return iter.next() instanceof MultipartFile;
//            }
//        } else if (Map.class.isAssignableFrom(clazz)) {
//            Map map = (Map) o;
//            for (Iterator iter = map.entrySet().iterator(); iter.hasNext(); ) {
//                Map.Entry entry = (Map.Entry) iter.next();
//                return entry.getValue() instanceof MultipartFile;
//            }
//        }
//        return o instanceof MultipartFile || o instanceof HttpServletRequest ||
//            o instanceof HttpServletResponse
//            || o instanceof BindingResult;
//    }
//
//    /**
//     * 是否存在注解，如果存在就获取
//     */
//    private Log getAnnotationLog(JoinPoint joinPoint) {
//        Signature signature = joinPoint.getSignature();
//        MethodSignature methodSignature = (MethodSignature) signature;
//        Method method = methodSignature.getMethod();
//        if (method != null) {
//            return method.getAnnotation(Log.class);
//        }
//        return null;
//    }
//}
