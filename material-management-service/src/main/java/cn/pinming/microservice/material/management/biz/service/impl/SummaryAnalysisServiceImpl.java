package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.enums.DeviationStatusEnum;
import cn.pinming.microservice.material.management.biz.enums.ReceiveQueryTypeEnum;
import cn.pinming.microservice.material.management.biz.enums.WarningTypeEnum;
import cn.pinming.microservice.material.management.biz.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.biz.mapper.MaterialSendReceiveMapper;
import cn.pinming.microservice.material.management.biz.mapper.MaterialWarningMapper;
import cn.pinming.microservice.material.management.biz.query.*;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.util.IfBranchUtil;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialCategoryDto;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2021/9/6 11:02 上午
 */
@Transactional(rollbackFor = Exception.class)
@Service
@Slf4j
public class SummaryAnalysisServiceImpl implements ISummaryAnalysisService {

    @Autowired
    private IMobileReceiveService mobileReceiveService;
    @Autowired
    private IMobileReceiveTotalService mobileReceiveTotalService;
    @Resource
    private MaterialSendReceiveMapper sendReceiveMapper;
    @Resource
    private MaterialDataMapper materialDataMapper;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private MaterialWarningMapper materialWarningMapper;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private MaterialGroupConfigService materialGroupConfigService;
    @Resource
    private IMaterialWarningConfigService warningConfigService;

    private AuthUser getAuthUser() {
        return authUserHolder.getCurrentUser();
    }

    @Override
    public SummaryAnalysisVO querySummary(SummaryDeliveryQuery query) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (currentUser.getCurrentProjectId() != null) {
            query.setProjectIdList(Collections.singletonList(currentUser.getCurrentProjectId()));
        } else {
            List<Integer> projectIds = projectServiceProxy.statisticsDeptProjectIds(currentUser.getCurrentCompanyId(), query.getDepartmentId());
            query.setProjectIdList(projectIds);
        }

        if (CollUtil.isEmpty(query.getProjectIdList())) {
            return null;
        }
        SummaryAnalysisVO receiveSummary = sendReceiveMapper.querySummary(query);
        SummaryAnalysisVO mobileReceiveSummary = mobileReceiveService.countMobileReceiveSummary(query);
        WarningSummaryAnalysisVO warningSummaryAnalysisVO = materialWarningMapper.queryWarningSummary(query);

        SummaryAnalysisVO countSummary = new SummaryAnalysisVO();
        if (receiveSummary != null) {
            BeanUtil.copyProperties(receiveSummary, countSummary);
        }
        if (warningSummaryAnalysisVO != null) {
            BeanUtil.copyProperties(warningSummaryAnalysisVO, countSummary);
        }
        if (mobileReceiveSummary != null) {
            // 本月收料车次
            BigDecimal monthReceiveCarsNum = NumberUtil.add(countSummary.getMonthReceiveCarsNum(), mobileReceiveSummary.getMonthReceiveCarsNum());
            countSummary.setMonthReceiveCarsNum(monthReceiveCarsNum.intValue());
            // 累计收料车次
            BigDecimal countReceiveCarsNum = NumberUtil.add(countSummary.getCountReceiveCarsNum(), mobileReceiveSummary.getCountReceiveCarsNum());
            countSummary.setCountReceiveCarsNum(countReceiveCarsNum.intValue());
            //本月超负差-车次
            BigDecimal monthMinusCarsNum = NumberUtil.add(countSummary.getMonthMinusCarsNum(), mobileReceiveSummary.getMonthMinusCarsNum());
            countSummary.setMonthMinusCarsNum(monthMinusCarsNum.intValue());
            //累计超负差-车次
            BigDecimal countMinusCarsNum = NumberUtil.add(countSummary.getCountMinusCarsNum(), mobileReceiveSummary.getCountMinusCarsNum());
            countSummary.setCountMinusCarsNum(countMinusCarsNum.intValue());
        }
        return countSummary;
    }

    @Override
    public List<CategoryReceiveVO> listReceiveByQuery(SupplierAnalysisQuery query) {
        query.setSize(-1);
        if (getAuthUser().getCurrentCompanyId() != null) {
            query.setCompanyId(getAuthUser().getCurrentCompanyId());
        }
        if (getAuthUser().getCurrentProjectId() != null) {
            query.setProjectIds(Collections.singletonList(getAuthUser().getCurrentProjectId()));
        }
        List<CategoryReceiveVO> categoryReceiveVOS = materialDataMapper.selectReceiveListByQuery(query);

        return categoryReceiveVOS;
    }

    @Override
    public List<ReceiveOverviewCardVO> receiveOverviewCard(ReceiveOverviewCardQuery query) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer companyId = currentUser.getCurrentCompanyId();
        Integer projectId = currentUser.getCurrentProjectId();
        // 二级分类
        List<String> categoryIds = materialGroupConfigService.categoryIds(false);
        if (CollectionUtil.isEmpty(categoryIds) || CollectionUtil.isEmpty(categoryIds.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()))) {
            return Lists.newArrayList();
        }
        Map<String, String> categoryNameMap = categoryNameMap(companyId, categoryIds);
        List<ReceiveOverviewCardVO> result = new ArrayList<>(categoryIds.size());
        // 收料方式
        String receiveWay = query.getReceiveWay();
        Pair<List<Byte>, List<Byte>> listListPair = StatisticsReceiveWayQuery.formatReceiveWay(receiveWay);
        // 地磅收料模式
        List<Byte> wagon = listListPair.getKey();
        // 移动收料模式
        List<Byte> mobile = listListPair.getValue();
        // 统计项目
        List<Integer> projectIds;
        // 判断是分公司还是项目,deptId为空表示项目
        Integer deptId = query.getDeptId();
        // 分公司层
        if (ObjectUtil.isNull(projectId)) {
            // 项目范围
            projectIds = projectServiceProxy.statisticsProjectIds(companyId, deptId);
        } else {
            // 项目层
            projectIds = Arrays.asList(projectId);
        }
        // 查询
        List<ReceiveOverviewCardDTO> list = Lists.newArrayList();
        // 地磅收料
        if (CollectionUtil.isNotEmpty(wagon)) {
            List<ReceiveOverviewCardDTO> wagonList = materialDataMapper.wagonReceiveOverviewCard(query.getStart(), query.getEnd(), wagon, categoryIds, projectIds);
            list.addAll(Optional.ofNullable(wagonList).orElse(Lists.newArrayList()));
        }
        // 移动收料
        if (CollectionUtil.isNotEmpty(mobile)) {
            List<ReceiveOverviewCardDTO> mobileList = materialDataMapper.mobileReceiveOverviewCard(query.getStart(), query.getEnd(), mobile, categoryIds, projectIds);
            list.addAll(Optional.ofNullable(mobileList).orElse(Lists.newArrayList()));
        }
        // 统计数据
        for (String categoryId : categoryIds) {
            ReceiveOverviewCardVO cardVO = new ReceiveOverviewCardVO();
            cardVO.setCategoryId(categoryId);
            cardVO.setCategoryName(categoryNameMap.get(categoryId));
            if (CollectionUtil.isNotEmpty(list)) {
                // 材料品种
                Set<Integer> materialIdSet = list.stream().map(ReceiveOverviewCardDTO::getMaterialId).collect(Collectors.toSet());
                List<MaterialDto> materialDtos = materialServiceProxy.listMaterialByIds(materialIdSet);
                // 材料ID-材料名称
                Map<Integer, String> materialNameMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, MaterialDto::getMaterialName));
                list.forEach(item -> item.setMaterialName(materialNameMap.get(item.getMaterialId())));
                // 按categoryId、unit分组
                Map<String, Map<String, List<ReceiveOverviewCardDTO>>> categoryUnitMap = list.stream().filter(item -> ObjectUtil.isNotNull(item.getMaterialName())).collect(
                        Collectors.groupingBy(ReceiveOverviewCardDTO::getCategoryId,
                                Collectors.collectingAndThen(
                                        Collectors.toList(), categoryList -> categoryList.stream()
                                                .collect(Collectors.groupingBy(ReceiveOverviewCardDTO::getUnit)))));
                // 单位map
                Map<String, List<ReceiveOverviewCardDTO>> unitListMap = categoryUnitMap.get(categoryId);
                if (ObjectUtil.isNotNull(unitListMap)) {
                    Map<String, List<ReceiveOverviewHistogram>> map = new HashMap<>();
                    unitListMap.forEach((unit, values) -> {
                        // 品种集合
                        List<ReceiveOverviewHistogram> varietyList = Lists.newArrayList();
                        // 按品种分组
                        Map<String, List<ReceiveOverviewCardDTO>> varietyMap = values.stream().collect(Collectors.groupingBy(ReceiveOverviewCardDTO::getMaterialName));
                        varietyMap.forEach((varietyName, datas) -> {
                            // 材料ID
                            Set<Integer> materialIds = datas.stream().map(ReceiveOverviewCardDTO::getMaterialId).collect(Collectors.toSet());
                            // 面单量之和
                            Double weightSendTotal = datas.stream().collect(Collectors.summingDouble(ReceiveOverviewCardDTO::getWeightSend));
                            // 实际量之和
                            Double actualCountTotal = datas.stream().collect(Collectors.summingDouble(ReceiveOverviewCardDTO::getActualCount));
                            // 实收量之和
                            Double actualReceiveTotal = datas.stream().collect(Collectors.summingDouble(ReceiveOverviewCardDTO::getActualReceive));
                            ReceiveOverviewHistogram histogram = ReceiveOverviewHistogram.builder()
                                    .x(varietyName)
                                    .y1(weightSendTotal)
                                    .y2(actualCountTotal)
                                    .y3(actualReceiveTotal)
                                    .materialIds(materialIds)
                                    .build();
                            // 品种
                            varietyList.add(histogram);
                        });
                        // 根据面单应收量排序
                        List<ReceiveOverviewHistogram> resultList = varietyList.stream().sorted((v1, v2) -> (int) (v2.getY1() - v1.getY1())).collect(Collectors.toList());
                        // 单位
                        map.put(unit, resultList);
                    });
                    cardVO.setGraph(map);
                }
            }
            result.add(cardVO);
        }
        return result;
    }

    private Map<String, String> categoryNameMap(Integer companyId, List<String> categoryIds) {
        // 二级分类名称
        List<MaterialCategoryDto> materialCategoryDtos = materialServiceProxy.listCategoryByIds(companyId, categoryIds.stream().map(Integer::parseInt).collect(Collectors.toList()));
        // 二级分类ID-二级分类名称
        Map<String, String> categoryNameMap = materialCategoryDtos.stream().collect(Collectors.toMap(item -> String.valueOf(item.getMaterialCategoryId()), MaterialCategoryDto::getMaterialCategoryName, (k1, k2) -> k1));
        return categoryNameMap;
    }

    @Override
    public Map<String, List<ReceiveOverviewHistogram>> receiveOverviewOther(ReceiveOverviewCardQuery query) {
        Map<String, List<ReceiveOverviewHistogram>> resultMap = new HashMap<>();
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer companyId = currentUser.getCurrentCompanyId();
        Integer projectId = currentUser.getCurrentProjectId();
        // 二级分类
        List<String> categoryIds = materialGroupConfigService.categoryIds(false);
        if (CollectionUtil.isEmpty(categoryIds) || CollectionUtil.isEmpty(categoryIds.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()))) {
            return resultMap;
        }
        // 收料方式
        String receiveWay = query.getReceiveWay();
        Pair<List<Byte>, List<Byte>> listListPair = StatisticsReceiveWayQuery.formatReceiveWay(receiveWay);
        // 地磅收料模式
        List<Byte> wagon = listListPair.getKey();
        // 移动收料模式
        List<Byte> mobile = listListPair.getValue();
        // 统计项目
        List<Integer> projectIds;
        // 判断是分公司还是项目,deptId为空表示项目
        Integer deptId = query.getDeptId();
        // 分公司层
        if (ObjectUtil.isNull(projectId)) {
            // 项目范围
            projectIds = projectServiceProxy.statisticsProjectIds(companyId, deptId);
        } else {
            // 项目层
            projectIds = Arrays.asList(projectId);
        }
        // 查询
        List<ReceiveOverviewCardDTO> list = Lists.newArrayList();
        // 地磅收料
        if (CollectionUtil.isNotEmpty(wagon)) {
            List<ReceiveOverviewCardDTO> wagonList = materialDataMapper.wagonReceiveOverviewOther(query.getStart(), query.getEnd(), wagon, categoryIds, projectIds);
            list.addAll(Optional.ofNullable(wagonList).orElse(Lists.newArrayList()));
        }
        // 移动收料
        if (CollectionUtil.isNotEmpty(mobile)) {
            List<ReceiveOverviewCardDTO> mobileList = materialDataMapper.mobileReceiveOverviewOther(query.getStart(), query.getEnd(), mobile, categoryIds, projectIds);
            list.addAll(Optional.ofNullable(mobileList).orElse(Lists.newArrayList()));
        }
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> categoryIdList = list.stream().map(ReceiveOverviewCardDTO::getCategoryId).collect(Collectors.toList());
            Map<String, Map<String, List<ReceiveOverviewCardDTO>>> unitCategoryMap = list.stream().collect(
                    Collectors.groupingBy(ReceiveOverviewCardDTO::getUnit,
                            Collectors.collectingAndThen(
                                    Collectors.toList(), unitList -> unitList.stream()
                                            .collect(Collectors.groupingBy(ReceiveOverviewCardDTO::getCategoryId)))));
            Map<String, String> categoryNameMap = categoryNameMap(companyId, categoryIdList);
            unitCategoryMap.forEach((unit, categoryMap) -> {
                List<ReceiveOverviewHistogram> graphList = Lists.newArrayList();
                categoryMap.forEach((categoryId, values) -> {
                    // 面单量之和
                    Double weightSendTotal = values.stream().collect(Collectors.summingDouble(ReceiveOverviewCardDTO::getWeightSend));
                    // 实际量之和
                    Double actualCountTotal = values.stream().collect(Collectors.summingDouble(ReceiveOverviewCardDTO::getActualCount));
                    // 实收量之和
                    Double actualReceiveTotal = values.stream().collect(Collectors.summingDouble(ReceiveOverviewCardDTO::getActualReceive));
                    String categoryName = StringUtils.isBlank(categoryId) ? "" : categoryNameMap.get(categoryId);
                    ReceiveOverviewHistogram graph = ReceiveOverviewHistogram.builder()
                            .x(categoryName)
                            .y1(weightSendTotal)
                            .y2(actualCountTotal)
                            .y3(actualReceiveTotal)
                            .categoryId(StringUtils.isBlank(categoryId) ? -1 : Integer.parseInt(categoryId))
                            .build();
                    graphList.add(graph);
                });
                List<ReceiveOverviewHistogram> resultList = graphList.stream().sorted((v1, v2) -> (int) (v2.getY1() - v1.getY1())).collect(Collectors.toList());
                resultMap.put(unit, resultList);
            });
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> receiveOverviewModal(ReceiveOverviewModalQuery query) {
        Map<String, Object> resultMap = new HashMap<>();
        // 横向柱状图
        ReceiveOverviewModalVO modalVO = ReceiveOverviewModalVO.builder().build();
        // 表格
        ReceiveOverviewTableVO tableVO = ReceiveOverviewTableVO.builder().build();

        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer companyId = currentUser.getCurrentCompanyId();
        // 部门ID
        Integer deptId = query.getDeptId();
        // 直属下级单位(分公司or项目)
        Map<String, List<Integer>> stringListMap = projectServiceProxy.directlyUnderDeptOrProject(companyId, deptId);
        // 不为空表示大宗材柱状图下钻
        Set<Integer> materialIds = query.getMaterialIds();
        // 不为空表示其他物料材料下钻，-1表示材料ID为空的
        Integer categoryId = query.getCategoryId();
        // 单位
        String unit = query.getUnit();
        // 开始时间
        String start = query.getStart();
        // 结束时间
        String end = query.getEnd();
        if (StringUtils.isNotBlank(start) && StringUtils.isNotBlank(end)) {
            modalVO.setTime(start + "~" + end);
            tableVO.setTime(start + "~" + end);
        }
        // 收料方式
        String receiveWay = query.getReceiveWay();
        Pair<List<Byte>, List<Byte>> listListPair = StatisticsReceiveWayQuery.formatReceiveWay(receiveWay);
        // 地磅收料模式
        List<Byte> wagon = listListPair.getKey();
        // 移动收料模式
        List<Byte> mobile = listListPair.getValue();

        // 开始时间
        List<String> starts = Lists.newArrayList();
        // 结束时间
        List<String> ends = Lists.newArrayList();
        // 横向柱状图数据
        List<ReceiveOverviewModal> modals = Lists.newArrayList();
        // 表格数据
        Map<String, List<ReceiveOverviewTable>> map = new LinkedHashMap<>();

        String finalStart = start;
        String finalEnd = end;
        Map<String, List<ReceiveOverviewCardSecondDTO>> dataMap = new HashMap<>();
        stringListMap.forEach((name, pjIds) -> {
            // 查询数据
            List<ReceiveOverviewCardSecondDTO> list = Lists.newArrayList();
            // 大宗材下钻
            if (CollectionUtil.isNotEmpty(materialIds)) {
                if (CollectionUtil.isNotEmpty(wagon)) {
                    // 地磅收料
                    List<ReceiveOverviewCardSecondDTO> wagonReceiveOverviewCardSecond = materialDataMapper.wagonReceiveOverviewCardSecond(finalStart, finalEnd, unit, wagon, materialIds, pjIds);
                    list.addAll(Optional.ofNullable(wagonReceiveOverviewCardSecond).orElse(Lists.newArrayList()));
                }
                if (CollectionUtil.isNotEmpty(mobile)) {
                    // 移动收料
                    List<ReceiveOverviewCardSecondDTO> mobileReceiveOverviewCardSecond = materialDataMapper.mobileReceiveOverviewCardSecond(finalStart, finalEnd, unit, mobile, materialIds, pjIds);
                    list.addAll(Optional.ofNullable(mobileReceiveOverviewCardSecond).orElse(Lists.newArrayList()));
                }
            }
            // 其他材料下钻
            if (ObjectUtil.isNotNull(categoryId)) {
                if (CollectionUtil.isNotEmpty(wagon)) {
                    // 地磅收料
                    List<ReceiveOverviewCardSecondDTO> wagonReceiveOverviewOtherSecond = materialDataMapper.wagonReceiveOverviewOtherSecond(finalStart, finalEnd, unit, wagon, categoryId, pjIds);
                    list.addAll(Optional.ofNullable(wagonReceiveOverviewOtherSecond).orElse(Lists.newArrayList()));
                }
                if (CollectionUtil.isNotEmpty(mobile)) {
                    // 移动收料
                    List<ReceiveOverviewCardSecondDTO> mobileReceiveOverviewOtherSecond = materialDataMapper.mobileReceiveOverviewOtherSecond(finalStart, finalEnd, unit, mobile, categoryId, pjIds);
                    list.addAll(Optional.ofNullable(mobileReceiveOverviewOtherSecond).orElse(Lists.newArrayList()));
                }
            }
            // 时间
            if (CollectionUtil.isNotEmpty(list)) {
                Optional<String> min = list.stream().map(ReceiveOverviewCardSecondDTO::getTime).min(String::compareTo);
                Optional<String> max = list.stream().map(ReceiveOverviewCardSecondDTO::getTime).max(String::compareTo);
                IfBranchUtil.isTrue(min.isPresent()).trueHandle(() -> starts.add(min.get()));
                IfBranchUtil.isTrue(max.isPresent()).trueHandle(() -> ends.add(max.get()));
            }
            dataMap.put(name, list);
        });
        // 时间范围
        if (CollectionUtil.isNotEmpty(starts) && CollectionUtil.isNotEmpty(ends) && StringUtils.isBlank(start) && StringUtils.isBlank(end)) {
            StringBuilder timeRangeBuilder = new StringBuilder();
            starts.stream().min(String::compareTo).ifPresent(minStart -> timeRangeBuilder.append(minStart));
            timeRangeBuilder.append("~");
            ends.stream().max(String::compareTo).ifPresent(maxEnd -> timeRangeBuilder.append(maxEnd));
            modalVO.setTime(timeRangeBuilder.toString());
            tableVO.setTime(timeRangeBuilder.toString());
        }
        if (CollectionUtil.isNotEmpty(starts) && CollectionUtil.isNotEmpty(ends)) {
            String startMonth = starts.stream().min(String::compareTo).get();
            String endMonth = ends.stream().max(String::compareTo).get();
            List<String> timeRange = buildTimeRange(startMonth, endMonth);
            dataMap.forEach((name, list) -> {
                // 横向柱状图数据
                ReceiveOverviewModal modal = ReceiveOverviewModal.builder().name(name.replaceFirst("^\\d+-", "")).build();
                // 表格数据
                List<ReceiveOverviewTable> tables = Lists.newArrayList();
                // 横向柱状图数据
                Double weightSend = list.stream().collect(Collectors.summingDouble(ReceiveOverviewCardSecondDTO::getWeightSend));
                Double actualCount = list.stream().collect(Collectors.summingDouble(ReceiveOverviewCardSecondDTO::getActualCount));
                Double actualReceive = list.stream().collect(Collectors.summingDouble(ReceiveOverviewCardSecondDTO::getActualReceive));
                modal.setY1(weightSend);
                modal.setY2(actualCount);
                modal.setY3(actualReceive);
                // 表格数据
                Map<String, List<ReceiveOverviewCardSecondDTO>> listMap = list.stream().sorted(Comparator.comparing(ReceiveOverviewCardSecondDTO::getMonth))
                        .collect(Collectors.groupingBy(ReceiveOverviewCardSecondDTO::getMonth));
                // 月份序列
                for (String monthTime : timeRange) {
                    ReceiveOverviewTable table = ReceiveOverviewTable.builder().build();
                    List<ReceiveOverviewCardSecondDTO> monthList = listMap.get(monthTime);
                    table.setTime(monthTime);
                    if (ObjectUtil.isNotNull(monthList)) {
                        Double weightSendTotal = monthList.stream().collect(Collectors.summingDouble(ReceiveOverviewCardSecondDTO::getWeightSend));
                        Double actualCountTotal = monthList.stream().collect(Collectors.summingDouble(ReceiveOverviewCardSecondDTO::getActualCount));
                        Double diffCountTotal = monthList.stream().collect(Collectors.summingDouble(item -> item.getActualCount() - item.getWeightSend()));
                        Double actualReceiveTotal = monthList.stream().collect(Collectors.summingDouble(ReceiveOverviewCardSecondDTO::getActualReceive));
                        table.setY1(weightSendTotal);
                        table.setY2(actualCountTotal);
                        table.setY3(diffCountTotal);
                        table.setY4(actualReceiveTotal);
                    }
                    tables.add(table);
                }
                modals.add(modal);
                map.put(name.replaceFirst("^\\d+-", ""), tables);
            });
        }
        modalVO.setList(modals);
        tableVO.setMap(map);
        resultMap.put("modalVO", modalVO);
        resultMap.put("tableVO", tableVO);
        return resultMap;
    }

    private List<String> buildTimeRange(String start, String end) {
        List<String> list = Lists.newArrayList();
        DateTimeFormatter dayFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("yyyy/MM");
        LocalDate startMonth = LocalDate.parse(start.replaceFirst("\\d{2}$", "01"), dayFormatter);
        LocalDate endMonth = LocalDate.parse(end.replaceFirst("\\d{2}$", "01"), dayFormatter);
        int months = Period.between(startMonth, endMonth).getMonths() + 1;
        for (int i = 0; i < months; i++) {
            LocalDate date = startMonth.plus(i, ChronoUnit.MONTHS);
            list.add(date.format(monthFormatter));
        }
        return list;
    }

    @Override
    public List<CategoryReceiveVO> countMobileReceiveNum(SupplierAnalysisQuery query) {
        query.setSize(-1);
        if (getAuthUser().getCurrentCompanyId() != null) {
            query.setCompanyId(getAuthUser().getCurrentCompanyId());
        }
        if (getAuthUser().getCurrentProjectId() != null) {
            query.setProjectIds(Collections.singletonList(getAuthUser().getCurrentProjectId()));
        }
        if (query.getType() != null) {
            ReceiveQueryTypeEnum queryTypeEnum = ReceiveQueryTypeEnum.getReceiveQueryTypeByValue(query.getType());
            query.setReceiveType(queryTypeEnum.getReceiveType());
        }
        List<CategoryReceiveVO> mobileReceiveVOS = mobileReceiveTotalService.countMobileReceiveNum(query);

        if (mobileReceiveVOS != null) {
            List<Integer> categoryIds = mobileReceiveVOS.stream().filter(obj -> obj.getCategoryId() != null)
                    .map(CategoryReceiveVO::getCategoryId).collect(Collectors.toList());
            List<MaterialCategoryDto> categoryList = materialServiceProxy.listCategoryByIds(getAuthUser().getCurrentCompanyId(), categoryIds);
            if (CollUtil.isNotEmpty(categoryList)) {
                Map<Integer, String> materialNameMap = categoryList.stream()
                        .filter(obj -> (obj.getMaterialCategoryId() != null && obj.getMaterialCategoryCode() != null))
                        .collect(Collectors.toMap(MaterialCategoryDto::getMaterialCategoryId, MaterialCategoryDto::getMaterialCategoryName));
                mobileReceiveVOS = mobileReceiveVOS.stream().filter(obj -> obj.getCategoryId() != null)
                        .map(obj -> {
                            String name = materialNameMap.get(obj.getCategoryId());
                            obj.setCategoryName(name);
                            return obj;
                        }).collect(Collectors.toList());
            }

        }
        return mobileReceiveVOS;
    }

    @Override
    public List<ReceiveDeviationVO> listDeviationByQuery(SupplierAnalysisQuery query) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        query.setCompanyId(currentUser.getCurrentCompanyId());

        if (currentUser.getCurrentProjectId() != null) {
            query.setProjectIds(Collections.singletonList(currentUser.getCurrentProjectId()));
        } else {
            List<Integer> projectIds = projectServiceProxy.statisticsProjectIds(currentUser.getCurrentCompanyId(), query.getDepartmentId());
            query.setProjectIds(projectIds);
        }

        if (CollUtil.isEmpty(query.getProjectIds())) {
            return new ArrayList<>();
        }
        Pair<List<Byte>, List<Byte>> formatReceiveWay = StatisticsReceiveWayQuery.formatReceiveWay(query.getReceiveWay());
        // 地磅收料模式
        List<Byte> wagon = formatReceiveWay.getKey();
        // 移动收料模式
        List<Byte> mobile = formatReceiveWay.getValue();

        List<Integer> materialIds = null;
        if (query.getCategoryId() != null) {
            materialIds = materialServiceProxy.getMaterialIdsByCategoryId(getAuthUser().getCurrentCompanyId(), query.getCategoryId());
        } else if (query.getCategoryIds() != null) {
            materialIds = materialServiceProxy.getMaterialIdsByCategoryIds(getAuthUser().getCurrentCompanyId(), query.getCategoryIds());
        }
        if (CollUtil.isEmpty(materialIds)) {
            return new ArrayList<>();
        }

        query.setMaterialIds(materialIds);

        boolean queryWeighbridge = CollUtil.isNotEmpty(wagon);
        boolean queryMobile = CollUtil.isNotEmpty(mobile);

        List<ReceiveDeviationVO> weighbridgeList = null;
        if (queryWeighbridge) {
            weighbridgeList = materialDataMapper.selectDeviationByQuery(query, wagon);
        }

        List<ReceiveDeviationVO> mobileList = null;
        if (queryMobile) {
            mobileList = mobileReceiveTotalService.countDeviation(query, mobile);
        }

        List<ReceiveDeviationVO> list = new ArrayList<>(64);
        if (CollUtil.isNotEmpty(weighbridgeList)) {
            if (CollUtil.isNotEmpty(mobileList)) {
                Map<String, ReceiveDeviationVO> weighbridgeMap = weighbridgeList.stream().filter(obj -> obj.getDate() != null)
                        .collect(Collectors.toMap(ReceiveDeviationVO::getDate, obj -> obj));
                Map<String, ReceiveDeviationVO> mobileMap = mobileList.stream().filter(obj -> obj.getDate() != null)
                        .collect(Collectors.toMap(ReceiveDeviationVO::getDate, obj -> obj));

                Collection<String> allKey = CollUtil.addAll(CollUtil.newArrayList(weighbridgeMap.keySet())
                        , CollUtil.newArrayList(mobileMap.keySet()));
                Set<String> keySet = new HashSet<>(allKey);

                List<ReceiveDeviationVO> finalList = list;
                keySet.stream().forEach(key -> {
                    ReceiveDeviationVO vo = weighbridgeMap.get(key);

                    ReceiveDeviationVO mobileVo = mobileMap.get(key);
                    if (vo == null) {
                        finalList.add(mobileVo);
                        return;
                    }
                    if (mobileVo == null) {
                        finalList.add(vo);
                        return;
                    }
                    double negative = NumberUtil.add(vo.getNegative(), mobileVo.getNegative());
                    vo.setNegative(new Double(negative).intValue());

                    double positive = NumberUtil.add(vo.getPositive(), mobileVo.getPositive());
                    vo.setPositive(new Double(positive).intValue());

                    double normal = NumberUtil.add(vo.getNormal(), mobileVo.getNormal());
                    vo.setNormal(new Double(normal).intValue());

                    double unidentified = NumberUtil.add(vo.getUnidentified(), mobileVo.getUnidentified());
                    vo.setUnidentified(new Double(unidentified).intValue());
                    finalList.add(vo);
                });
                list = finalList;
            } else {
                list = weighbridgeList;
            }
        } else {
            list = mobileList;
        }


        Map<String, ReceiveDeviationVO> deviationVOMap = new HashMap<>();
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        deviationVOMap = list.parallelStream().collect(Collectors.toMap(ReceiveDeviationVO::getDate, e -> e));
        String startTime = null;
        String endTime = null;
        if (query.getStartDate() == null || query.getEndDate() == null) {
            if (CollUtil.isNotEmpty(list)) {
                startTime = list.get(0).getDate();
                endTime = list.get(list.size() > 0 ? list.size() - 1 : 0).getDate();
            }
        } else {
            LocalDate startDate = query.getStartDate();
            LocalDate endDate = query.getEndDate();
            startTime = startDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            endTime = endDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        }
        List<String> dateTimes = rangeDateToList(startTime, endTime, "yyyy-MM");
        List<ReceiveDeviationVO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(dateTimes)) {
            for (String dateTime : dateTimes) {
                ReceiveDeviationVO vo = new ReceiveDeviationVO();
                ReceiveDeviationVO deviationVO = deviationVOMap.get(dateTime);
                if (deviationVO != null) {
                    BeanUtils.copyProperties(deviationVO, vo);
                }
                vo.setDate(dateTime);
                result.add(vo);
            }
        }
        return result;
    }

    @Override
    public List<ReceiveDeviationSummaryVO> listDeviationSummaryByQuery(SupplierAnalysisQuery query) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        query.setCompanyId(currentUser.getCurrentCompanyId());

        if (currentUser.getCurrentProjectId() != null) {
            query.setProjectIds(Collections.singletonList(currentUser.getCurrentProjectId()));
        } else {
            List<Integer> projectIds = projectServiceProxy.statisticsProjectIds(currentUser.getCurrentCompanyId(), query.getDepartmentId());
            query.setProjectIds(projectIds);
        }

        if (CollUtil.isEmpty(query.getProjectIds())) {
            return new ArrayList<>();
        }
        List<Integer> materialIds = null;
        if (query.getCategoryId() != null) {
            materialIds = materialServiceProxy.getMaterialIdsByCategoryId(getAuthUser().getCurrentCompanyId(), query.getCategoryId());
        } else if (query.getCategoryIds() != null) {
            materialIds = materialServiceProxy.getMaterialIdsByCategoryIds(getAuthUser().getCurrentCompanyId(), query.getCategoryIds());
        }
        if (CollUtil.isEmpty(materialIds)) {
            return new ArrayList<>();
        }
        query.setMaterialIds(materialIds);

        Pair<List<Byte>, List<Byte>> formatReceiveWay = StatisticsReceiveWayQuery.formatReceiveWay(query.getReceiveWay());
        // 地磅收料模式
        List<Byte> wagon = formatReceiveWay.getKey();
        // 移动收料模式
        List<Byte> mobile = formatReceiveWay.getValue();

        boolean queryWeighbridge = CollUtil.isNotEmpty(wagon);
        boolean queryMobile = CollUtil.isNotEmpty(mobile);

        List<ReceiveDeviationSummaryVO> list = new ArrayList<>(4);

        List<ReceiveDeviationSummaryVO> weighbridgeList = null;
        if (queryWeighbridge) {
            weighbridgeList = materialDataMapper.selectDeviationSummaryByQuery(query, wagon);
        }

        List<ReceiveDeviationSummaryVO> mobileList = null;
        if (queryMobile) {
            mobileList = mobileReceiveTotalService.countDeviationStatus(query, mobile);
        }

        Map<Byte, ReceiveDeviationSummaryVO> weighbridgeMap = new HashMap<>(0);
        if (CollUtil.isNotEmpty(weighbridgeList)) {
            weighbridgeMap = weighbridgeList.stream().map(obj -> {
                if (obj.getDeviationStatus() == null) {
                    obj.setDeviationStatus(DeviationStatusEnum.UNIDENTIFIED.value());
                }
                return obj;
            }).collect(Collectors.toMap(ReceiveDeviationSummaryVO::getDeviationStatus, obj -> obj));
        }
        Map<Byte, ReceiveDeviationSummaryVO> mobileMap = new HashMap<>(0);
        if (CollUtil.isNotEmpty(mobileList)) {
            mobileMap = mobileList.stream().map(obj -> {
                if (obj.getDeviationStatus() == null) {
                    obj.setDeviationStatus(DeviationStatusEnum.UNIDENTIFIED.value());
                }
                return obj;
            }).collect(Collectors.toMap(ReceiveDeviationSummaryVO::getDeviationStatus, obj -> obj));
        }

        AtomicReference<Integer> totalAtomic = new AtomicReference<>(1);

        Map<Byte, ReceiveDeviationSummaryVO> finalWeighbridgeMap = weighbridgeMap;
        Map<Byte, ReceiveDeviationSummaryVO> finalMobileMap = mobileMap;
        Arrays.stream(DeviationStatusEnum.values()).forEach(obj -> {
            ReceiveDeviationSummaryVO weighbridgeVO = finalWeighbridgeMap.get(obj.value());
            ReceiveDeviationSummaryVO mobileVO = finalMobileMap.get(obj.value());
            Integer num = getDeviationSummaryNum(weighbridgeVO, mobileVO);
            totalAtomic.updateAndGet(v -> v + num);

            ReceiveDeviationSummaryVO vo = new ReceiveDeviationSummaryVO();
            vo.setName(obj.description());
            vo.setDeviationStatus(obj.value());
            vo.setCount(num);
            list.add(vo);
        });

        if (CollUtil.isNotEmpty(list)) {
            Integer total = totalAtomic.get();
            list.forEach(obj -> {
                BigDecimal rate = NumberUtil.div(obj.getCount(), total, 2);
                obj.setRate(rate);
            });
        }
        return list;
    }

    private Integer getDeviationSummaryNum(ReceiveDeviationSummaryVO weighbridgeVO, ReceiveDeviationSummaryVO mobileVO) {
        Integer count = 0;
        if (ObjectUtil.isNotEmpty(weighbridgeVO)) {
            count = weighbridgeVO.getCount();
        }
        Integer count1 = 0;
        if (ObjectUtil.isNotEmpty(mobileVO)) {
            count1 = mobileVO.getCount();
        }
        return NumberUtil.add(count, count1).intValue();
    }

    @Override
    public List<SummaryDeliveryVO> listSummaryDeliveryByQuery(SummaryDeliveryQuery query) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (currentUser.getCurrentProjectId() != null) {
            query.setProjectIdList(Collections.singletonList(currentUser.getCurrentProjectId()));
        } else {
            List<Integer> projectIds = projectServiceProxy.statisticsProjectIds(currentUser.getCurrentCompanyId(), query.getDepartmentId());
            query.setProjectIdList(projectIds);
        }

        if (CollUtil.isEmpty(query.getProjectIdList())) {
            return null;
        }
        List<SummaryDeliveryVO> list = materialDataMapper.listSummaryDeliveryByQuery(query);

        BigDecimal num = list.stream().map(SummaryDeliveryVO::getAccumulationCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        list.stream().forEach(e -> {
            BigDecimal percentage = NumberUtil.mul(NumberUtil.div(e.getAccumulationCount(), num, 4), 100);
            e.setPercentage(percentage);
//          写死 写得死死的
            e.setUnit("吨");
        });

        return list;
    }

    @Override
    public List<SummaryDeliverySecondVO> listSummaryDeliverySecondByQuery(SummaryDeliveryQuery query) {
        // 直属下级单位 项目层为当前项目名-项目id
        Map<String, List<Integer>> underDeptMap = null;

        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (currentUser.getCurrentProjectId() != null) {
            Integer currentProjectId = currentUser.getCurrentProjectId();
            ProjectVO projectVO = projectServiceProxy.getProjectById(currentProjectId);
            underDeptMap = new HashMap<>(1);
            underDeptMap.put(projectVO.getProjectId() + "-" + projectVO.getProjectTitle(), Collections.singletonList(projectVO.getProjectId()));
        } else {
            underDeptMap = projectServiceProxy.directlyUnderDeptOrProject(currentUser.getCurrentCompanyId(), query.getDepartmentId());
        }

        List<Integer> allProject = underDeptMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        query.setProjectIdList(allProject);
        List<Integer> categoryIdList = materialDataMapper.listCategoryIdList(query);

        List<SummaryDeliverySecondVO> summaryDeliverySecondVOS = new ArrayList<>();
        underDeptMap.forEach((k, v) -> {
            query.setProjectIdList(v);
            SummaryDeliverySecondVO summaryDeliverySecondVO = new SummaryDeliverySecondVO();
            int index = k.indexOf("-");
            summaryDeliverySecondVO.setProjectId(Integer.valueOf(k.substring(0, index)));
            summaryDeliverySecondVO.setProjectName(k.substring(index + 1));

            // 取条件内的收料记录
            List<SummaryDeliverySecondDetailDTO> detailVOList = materialDataMapper.listDetailList(query, categoryIdList);
            Map<Integer, SummaryDeliverySecondDetailDTO> deliverySecondDetailDTOMap = detailVOList.stream().collect(Collectors.toMap(SummaryDeliverySecondDetailDTO::getCategoryId, e -> e));
            List<SummaryDeliverySecondDetailVO> summaryDeliverySecondDetailVOS = new ArrayList<>();
            categoryIdList.forEach(categoryId -> {
                // 每种材料的收料记录
                SummaryDeliverySecondDetailVO summaryDeliverySecondDetailVO = new SummaryDeliverySecondDetailVO();
                // 获取该分类信息
                SummaryDeliverySecondDetailDTO summaryDeliverySecondDetailDTO = deliverySecondDetailDTOMap.get(categoryId);
                if (summaryDeliverySecondDetailDTO != null) {
                    BeanUtils.copyProperties(summaryDeliverySecondDetailDTO, summaryDeliverySecondDetailVO);
                } else {
                    summaryDeliverySecondDetailVO.setCategoryId(categoryId);
                    List<MaterialCategoryDto> categoryDtos = materialServiceProxy.listCategoryByIds(currentUser.getCurrentCompanyId(), Collections.singleton(categoryId));
                    summaryDeliverySecondDetailVO.setMonthlyCount(BigDecimal.ZERO);
                    summaryDeliverySecondDetailVO.setAccumulationCount(BigDecimal.ZERO);
                    if (CollUtil.isNotEmpty(categoryDtos)) {
                        summaryDeliverySecondDetailVO.setCategoryName(categoryDtos.get(0).getMaterialCategoryName());
                    }
                }
                summaryDeliverySecondDetailVOS.add(summaryDeliverySecondDetailVO);
            });
            summaryDeliverySecondVO.setList(summaryDeliverySecondDetailVOS);
            ;
            summaryDeliverySecondVOS.add(summaryDeliverySecondVO);
        });
        return summaryDeliverySecondVOS;
    }


    @Override
    public List<SummaryWarningVO> listSummaryWarningByQuery(SummaryDeliveryQuery query) {

        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (currentUser.getCurrentProjectId() != null) {
            query.setProjectIdList(Collections.singletonList(currentUser.getCurrentProjectId()));
        } else {
            List<Integer> projectIds = projectServiceProxy.statisticsProjectIds(currentUser.getCurrentCompanyId(), query.getDepartmentId());
            query.setProjectIdList(projectIds);
        }

        if (CollUtil.isEmpty(query.getProjectIdList())) {
            return null;
        }
        List<SummaryWarningVO> list = materialWarningMapper.listSummaryWarningByQuery(query);
        Map<Byte, String> warningTypeEnumMap = Arrays.stream(WarningTypeEnum.values()).collect(Collectors.toMap(WarningTypeEnum::value, WarningTypeEnum::description));
        list.forEach(summaryWarningVO -> summaryWarningVO.setWarningType(warningTypeEnumMap.get(summaryWarningVO.getType())));
        Integer total = list.stream().map(SummaryWarningVO::getTotal).reduce(Integer::sum).orElse(0);
        list.forEach(e -> e.setPercentage(NumberUtil.mul(NumberUtil.div(String.valueOf(NumberUtil.add(e.getUnHandleCount().toString(), e.getHandleCount().toString())), total.toString(), 4), 100)));
        return list;
    }

    @Override
    public List<WarningSecondVO> listWarningSecondByQuery(SummaryDeliveryQuery query) {
        // 直属下级单位 项目层为当前项目名-项目id
        Map<String, List<Integer>> underDeptMap = null;

        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (currentUser.getCurrentProjectId() != null) {
            Integer currentProjectId = currentUser.getCurrentProjectId();
            ProjectVO projectVO = projectServiceProxy.getProjectById(currentProjectId);
            underDeptMap = new HashMap<>(1);
            underDeptMap.put(projectVO.getProjectId() + "-" + projectVO.getProjectTitle(), Collections.singletonList(projectVO.getProjectId()));
        } else {
            underDeptMap = projectServiceProxy.directlyUnderDeptOrProject(currentUser.getCurrentCompanyId(), query.getDepartmentId());
        }

        List<WarningSecondVO> warningSecondVOS = new ArrayList<>();
        underDeptMap.forEach((k, v) -> {
            query.setProjectIdList(v);
            WarningSecondVO warningSecondVO = new WarningSecondVO();
            int index = k.indexOf("-");
            warningSecondVO.setProjectId(Integer.valueOf(k.substring(0, index)));
            warningSecondVO.setProjectName(k.substring(index + 1));
            // 该单位数据
            List<SummaryWarningSecondVO> warningSecondDetailVOS = materialWarningMapper.listSummaryWarningSecondByQuery(query);
            Map<Byte, SummaryWarningSecondVO> warningSecondVOMap = warningSecondDetailVOS.stream().collect(Collectors.toMap(SummaryWarningSecondVO::getType, e -> e));
            // 封装数据
            List<String> noFilterWarningType = warningConfigService.listWarningConfig();
            List<WarningSecondDetailVO> warningSecondDetailVOList = new ArrayList<>();
            Arrays.stream(WarningTypeEnum.values()).filter(warningTypeEnum ->
                    noFilterWarningType.contains(String.valueOf(warningTypeEnum.value()))
            ).forEach(warningType -> {
                WarningSecondDetailVO warningSecondDetailVO = new WarningSecondDetailVO();
                warningSecondDetailVO.setWarningType(warningType.description());
                SummaryWarningSecondVO summaryWarningSecondVO = warningSecondVOMap.get(warningType.value());
                warningSecondDetailVO.setHandleCount(summaryWarningSecondVO == null ? 0 : summaryWarningSecondVO.getHandleCount());
                warningSecondDetailVO.setUnHandleCount(summaryWarningSecondVO == null ? 0 : summaryWarningSecondVO.getUnHandleCount());
                warningSecondDetailVO.setCount(warningSecondDetailVO.getHandleCount() + warningSecondDetailVO.getUnHandleCount());
                warningSecondDetailVOList.add(warningSecondDetailVO);
            });
            warningSecondVO.setWarningSecondDetailVOList(warningSecondDetailVOList);
            warningSecondVOS.add(warningSecondVO);
        });
        return warningSecondVOS;
    }


    @Override
    public List<ReceiveOverviewSecondVO> listReceiveOverviewSecondByQuery(SummaryDeliveryQuery query, byte type) {

        // 直属下级单位 项目层为当前项目名-项目id
        Map<String, List<Integer>> underDeptMap = null;

        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (currentUser.getCurrentProjectId() != null) {
            Integer currentProjectId = currentUser.getCurrentProjectId();
            ProjectVO projectVO = projectServiceProxy.getProjectById(currentProjectId);
            underDeptMap = new HashMap<>(1);
            underDeptMap.put(projectVO.getProjectId() + "-" + projectVO.getProjectTitle(), Collections.singletonList(projectVO.getProjectId()));
        } else {
            underDeptMap = projectServiceProxy.allDirectlyUnderDeptOrProject(currentUser.getCurrentCompanyId(), query.getDepartmentId());
            //log.error("underDeptMap:{}", JSONUtil.toJsonStr(underDeptMap));
        }

        // 所有projectId
        List<Integer> allPj = underDeptMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        query.setProjectIdList(allPj);
        List<ReceiveOverviewSecondDTO> allReceiveOverviewSecondDTOS = materialDataMapper.selectReceiveOverviewSecondByQuery(query, type);
        if (CollUtil.isEmpty(allReceiveOverviewSecondDTOS)) {
            return new ArrayList<>();
        }
        String start = allReceiveOverviewSecondDTOS.get(0).getTime();
        String end = allReceiveOverviewSecondDTOS.get(allReceiveOverviewSecondDTOS.size() > 0 ? allReceiveOverviewSecondDTOS.size() - 1 : 0).getTime();
        // 根据月份进行序列
        List<String> dateList = rangeDateToList(start, end, "yyyy/MM");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/MM");
        String curMonth = dateTimeFormatter.format(LocalDateTime.now());
        List<ReceiveOverviewSecondVO> receiveOverviewSecondVOS = new ArrayList<>();
        underDeptMap.forEach((k, v) -> {
            query.setProjectIdList(v);
            ReceiveOverviewSecondVO receiveOverviewSecondVO = new ReceiveOverviewSecondVO();
            int index = k.indexOf("-");
            String projectId = k.substring(0, index);
            Integer pjId = null;
            if (NumberUtil.isNumber(projectId)) {
                 pjId = Integer.valueOf(projectId);
            }
            receiveOverviewSecondVO.setProjectId(pjId);
            receiveOverviewSecondVO.setProjectName(k.substring(index + 1));
            // 该单位数据
            List<ReceiveOverviewSecondDTO> receiveOverviewSecondDTOS = materialDataMapper.selectReceiveOverviewSecondByQuery(query, type);
            receiveOverviewSecondVO.setTotal(receiveOverviewSecondDTOS.stream().map(ReceiveOverviewSecondDTO::getTotal).reduce(Integer::sum).orElse(0));
            Map<String, ReceiveOverviewSecondDTO> overviewSecondDTOMap = receiveOverviewSecondDTOS.stream().collect(Collectors.toMap(ReceiveOverviewSecondDTO::getTime, e -> e));
            List<ReceiveOverviewCarCount> receiveOverviewCarCounts = new ArrayList<>();
            if (CollUtil.isNotEmpty(dateList)) {
                for (String s : dateList) {
                    ReceiveOverviewCarCount receiveOverviewCarCount = new ReceiveOverviewCarCount();
                    receiveOverviewCarCount.setTime(s);
                    ReceiveOverviewSecondDTO receiveOverviewSecondDTO = overviewSecondDTOMap.get(s);
                    if (receiveOverviewSecondDTO == null) {
                        // 此月数据不存在
                        receiveOverviewCarCount.setCarCount(0);
                    } else {
                        // 此月存在数据
                        receiveOverviewCarCount.setCarCount(receiveOverviewSecondDTO.getTotal());
                    }
                    if (overviewSecondDTOMap.get(curMonth) != null) {
                        receiveOverviewSecondVO.setMonthRise(overviewSecondDTOMap.get(curMonth).getMonthRise());
                    } else {
                        receiveOverviewSecondVO.setMonthRise(0);
                    }
                    receiveOverviewCarCounts.add(receiveOverviewCarCount);
                }
                receiveOverviewSecondVO.setReceiveOverviewCarCountList(receiveOverviewCarCounts);
                receiveOverviewSecondVOS.add(receiveOverviewSecondVO);
            }
        });

        return receiveOverviewSecondVOS;
    }


    /**
     * 根据开始时间和结束时间返回月份序列
     * 格式要求 yyyy/MM
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return 开始到结束时间根据月份进行序列
     */
    private List<String> rangeDateToList(String start, String end, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            Date startTime = sdf.parse(start);
            Date endTime = sdf.parse(end);
            List<DateTime> dateTimes = DateUtil.rangeToList(startTime, endTime, DateField.MONTH);
            return dateTimes.stream().map(dateTime -> DateUtil.format(dateTime, pattern)).collect(Collectors.toList());
        } catch (ParseException e) {
            log.error("date parse error");
        }
        return null;
    }

    @Override
    public List<WarningOverviewSecondVO> listWarningOverviewSecondByQuery(SummaryDeliveryQuery query) {

        // 直属下级单位 项目层为当前项目名-项目id
        Map<String, List<Integer>> underDeptMap = null;

        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (currentUser.getCurrentProjectId() != null) {
            Integer currentProjectId = currentUser.getCurrentProjectId();
            ProjectVO projectVO = projectServiceProxy.getProjectById(currentProjectId);
            underDeptMap = new HashMap<>(1);
            underDeptMap.put(projectVO.getProjectId() + "-" + projectVO.getProjectTitle(), Collections.singletonList(projectVO.getProjectId()));
        } else {
            underDeptMap = projectServiceProxy.allDirectlyUnderDeptOrProject(currentUser.getCurrentCompanyId(), query.getDepartmentId());
        }
        // 所有projectId
        List<Integer> allPj = underDeptMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        query.setProjectIdList(allPj);
        List<WarningOverviewSecondDTO> allWarningOverviewSecondDTOS = materialWarningMapper.queryOverviewWarningSummary(query);
        if (CollUtil.isEmpty(allWarningOverviewSecondDTOS)) {
            return null;
        }
        String start = allWarningOverviewSecondDTOS.get(0).getTime();
        String end = allWarningOverviewSecondDTOS.get(allWarningOverviewSecondDTOS.size() > 0 ? allWarningOverviewSecondDTOS.size() - 1 : 0).getTime();
        // 根据月份进行序列
        List<String> dateList = rangeDateToList(start, end, "yyyy/MM");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/MM");
        String curMonth = dateTimeFormatter.format(LocalDateTime.now());
        List<WarningOverviewSecondVO> warningOverviewSecondVOS = new ArrayList<>();
        underDeptMap.forEach((k, v) -> {
            query.setProjectIdList(v);
            WarningOverviewSecondVO warningOverviewSecondVO = new WarningOverviewSecondVO();
            int index = k.indexOf("-");
            warningOverviewSecondVO.setProjectId(Integer.valueOf(k.substring(0, index)));
            warningOverviewSecondVO.setProjectName(k.substring(index + 1));
            // 该单位数据
            List<WarningOverviewSecondDTO> warningOverviewSecondDTOS = materialWarningMapper.queryOverviewWarningSummary(query);
            warningOverviewSecondVO.setTotal(warningOverviewSecondDTOS.stream().map(WarningOverviewSecondDTO::getTotal).reduce(Integer::sum).orElse(0));
            warningOverviewSecondVO.setUnHandle(warningOverviewSecondDTOS.stream().map(WarningOverviewSecondDTO::getUnHandle).reduce(Integer::sum).orElse(0));
            Map<String, WarningOverviewSecondDTO> overviewSecondDTOMap = warningOverviewSecondDTOS.stream().collect(Collectors.toMap(WarningOverviewSecondDTO::getTime, e -> e));
            List<WarningOverviewSecondDetailVO> warningOverviewSecondDetailVOS = new ArrayList<>();
            if (dateList != null) {
                for (int i = 0; i < dateList.size(); i++) {
                    WarningOverviewSecondDetailVO warningOverviewSecondDetailVO = new WarningOverviewSecondDetailVO();
                    warningOverviewSecondDetailVO.setTime(dateList.get(i));
                    WarningOverviewSecondDTO warningOverviewSecondDTO = overviewSecondDTOMap.get(dateList.get(i));
                    if (warningOverviewSecondDTO == null) {
                        // 此月数据不存在
                        warningOverviewSecondDetailVO.setCount(0);
                    } else {
                        // 此月存在数据
                        warningOverviewSecondDetailVO.setCount(warningOverviewSecondDTO.getTotal());
                    }
                    WarningOverviewSecondDTO dto = overviewSecondDTOMap.get(curMonth);
                    if (dto == null) {
                        warningOverviewSecondVO.setMonthRise(0);
                    } else {
                        warningOverviewSecondVO.setMonthRise(dto.getTotal());
                    }
                    warningOverviewSecondDetailVOS.add(warningOverviewSecondDetailVO);
                }
                warningOverviewSecondVO.setWarningOverviewSecondDetailVOS(warningOverviewSecondDetailVOS);
                warningOverviewSecondVOS.add(warningOverviewSecondVO);
            }
        });
        return warningOverviewSecondVOS;
    }

    @Override
    public List<DeviationOverviewSecondVO> listDeviationOverviewSecondByQuery(SummaryDeliveryQuery query) {
        // 直属下级单位 项目层为当前项目名-项目id
        Map<String, List<Integer>> underDeptMap = null;

        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (currentUser.getCurrentProjectId() != null) {
            Integer currentProjectId = currentUser.getCurrentProjectId();
            ProjectVO projectVO = projectServiceProxy.getProjectById(currentProjectId);
            underDeptMap = new HashMap<>(1);
            underDeptMap.put(projectVO.getProjectId() + "-" + projectVO.getProjectTitle(), Collections.singletonList(projectVO.getProjectId()));
        } else {
            underDeptMap = projectServiceProxy.allDirectlyUnderDeptOrProject(currentUser.getCurrentCompanyId(), query.getDepartmentId());
        }
        // 所有projectId
        List<Integer> allPj = underDeptMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        query.setProjectIdList(allPj);
        // 所有单位数据
        List<DeviationOverviewSecondDTO> allDeviationOverviewSecondDTOS = materialDataMapper.selectDeviationSecondByQuery(query);
        if (CollUtil.isEmpty(allDeviationOverviewSecondDTOS)) {
            return new ArrayList<>();
        }

        String start = allDeviationOverviewSecondDTOS.get(0).getTime();
        String end = allDeviationOverviewSecondDTOS.get(allDeviationOverviewSecondDTOS.size() > 0 ? allDeviationOverviewSecondDTOS.size() - 1 : 0).getTime();
        // 根据月份进行序列
        List<String> dateList = rangeDateToList(start, end, "yyyy/MM");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/MM");
        String curMonth = dateTimeFormatter.format(LocalDateTime.now());
        List<DeviationOverviewSecondVO> deviationOverviewSecondVOS = new ArrayList<>();
        underDeptMap.forEach((k, v) -> {
            query.setProjectIdList(v);
            DeviationOverviewSecondVO deviationOverviewSecondVO = new DeviationOverviewSecondVO();
            int index = k.indexOf("-");
            deviationOverviewSecondVO.setProjectId(Integer.valueOf(k.substring(0, index)));
            deviationOverviewSecondVO.setProjectName(k.substring(index + 1));
            // 该单位数据
            List<DeviationOverviewSecondDTO> deviationOverviewSecondDTOS = materialDataMapper.selectDeviationSecondByQuery(query);
            deviationOverviewSecondVO.setTotal(deviationOverviewSecondDTOS.stream().map(DeviationOverviewSecondDTO::getCountMinusCarsNum).reduce(Integer::sum).orElse(0));
            Map<String, DeviationOverviewSecondDTO> overviewSecondDTOMap = deviationOverviewSecondDTOS.stream().collect(Collectors.toMap(DeviationOverviewSecondDTO::getTime, e -> e));
            List<DeviationOverviewSecondDetailVO> deviationOverviewSecondDetailVOS = new ArrayList<>();
            if (dateList != null) {
                for (String s : dateList) {
                    DeviationOverviewSecondDetailVO deviationOverviewSecondDetailVO = new DeviationOverviewSecondDetailVO();
                    deviationOverviewSecondDetailVO.setTime(s);
                    DeviationOverviewSecondDTO deviationOverviewSecondDTO = overviewSecondDTOMap.get(s);
                    if (deviationOverviewSecondDTO == null) {
                        // 此月数据不存在
                        deviationOverviewSecondDetailVO.setCount(0);
                    } else {
                        // 此月存在数据
                        deviationOverviewSecondDetailVO.setCount(deviationOverviewSecondDTO.getCountMinusCarsNum());
                    }
                    DeviationOverviewSecondDTO secondDTO = overviewSecondDTOMap.get(curMonth);
                    if (secondDTO == null) {
                        deviationOverviewSecondVO.setMonthRise(0);
                    } else {
                        deviationOverviewSecondVO.setMonthRise(secondDTO.getCountMinusCarsNum());
                    }
                    deviationOverviewSecondDetailVOS.add(deviationOverviewSecondDetailVO);
                }
                deviationOverviewSecondVO.setDeviationOverviewSecondDetailVOS(deviationOverviewSecondDetailVOS);
                deviationOverviewSecondVOS.add(deviationOverviewSecondVO);
            }
        });
        return deviationOverviewSecondVOS;
    }

    @Override
    public List<DeviationSecondVO> listDeviationSecondByQuery(SupplierAnalysisQuery query) {
        // 直属下级单位 项目层为当前项目名-项目id
        Map<String, List<Integer>> underDeptMap = null;

        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (currentUser.getCurrentProjectId() != null) {
            Integer currentProjectId = currentUser.getCurrentProjectId();
            ProjectVO projectVO = projectServiceProxy.getProjectById(currentProjectId);
            underDeptMap = new HashMap<>(1);
            underDeptMap.put(projectVO.getProjectId() + "-" + projectVO.getProjectTitle(), Collections.singletonList(projectVO.getProjectId()));
        } else {
            underDeptMap = projectServiceProxy.directlyUnderDeptOrProject(currentUser.getCurrentCompanyId(), query.getDepartmentId());
        }
        // 所有projectId
        List<Integer> allPj = underDeptMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        query.setMaterialIds(allPj);
        // 所有数据 取最早时间和最晚时 生成时间序列
        List<ReceiveDeviationVO> receiveDeviationVOS = this.listDeviationByQuery(query);
        if (CollUtil.isEmpty(receiveDeviationVOS)) {
            return null;
        }
        String start = receiveDeviationVOS.get(0).getDate();
        String end = receiveDeviationVOS.get(receiveDeviationVOS.size() - 1).getDate();
        List<String> dateToList = rangeDateToList(start, end, "yyyy-MM");
        List<Integer> materialIds = null;
        if (query.getCategoryId() != null) {
            materialIds = materialServiceProxy.getMaterialIdsByCategoryId(getAuthUser().getCurrentCompanyId(), query.getCategoryId());
        } else if (query.getCategoryIds() != null) {
            materialIds = materialServiceProxy.getMaterialIdsByCategoryIds(getAuthUser().getCurrentCompanyId(), query.getCategoryIds());
        }
        query.setMaterialIds(materialIds);

        List<DeviationSecondVO> deviationSecondVOS = new ArrayList<>();
        underDeptMap.forEach((k, v) -> {
            query.setProjectIds(v);
            DeviationSecondVO deviationSecondVO = new DeviationSecondVO();
            int index = k.indexOf("-");
            deviationSecondVO.setProjectId(Integer.valueOf(k.substring(0, index)));
            deviationSecondVO.setProjectName(k.substring(index + 1));
            // 根据query获取该单位下的receiveDeviationVOMap
            Map<String, ReceiveDeviationVO> receiveDeviationVOMap = getReceiveDeviationVOMapByQuery(query);
            List<DeviationSecondDetailVO> deviationSecondDetailVOList = new ArrayList<>();
            for (String time : dateToList) {
                DeviationSecondDetailVO deviationSecondDetailVO = new DeviationSecondDetailVO();
                deviationSecondDetailVO.setDate(time);
                ReceiveDeviationVO receiveDeviationVO = receiveDeviationVOMap.get(time);
                if (receiveDeviationVO != null) {
                    BeanUtils.copyProperties(receiveDeviationVO, deviationSecondDetailVO);
                }
                deviationSecondDetailVOList.add(deviationSecondDetailVO);
            }
            deviationSecondVO.setDeviationSecondDetailVOList(deviationSecondDetailVOList);
            deviationSecondVOS.add(deviationSecondVO);
        });
        return deviationSecondVOS;
    }


    private Map<String, ReceiveDeviationVO> getReceiveDeviationVOMapByQuery(SupplierAnalysisQuery query) {
        if (CollUtil.isEmpty(query.getProjectIds())) {
            return null;
        }
        Pair<List<Byte>, List<Byte>> formatReceiveWay = StatisticsReceiveWayQuery.formatReceiveWay(query.getReceiveWay());
        // 地磅收料模式
        List<Byte> wagon = formatReceiveWay.getKey();
        // 移动收料模式
        List<Byte> mobile = formatReceiveWay.getValue();

        boolean queryWeighbridge = CollUtil.isNotEmpty(wagon);
        boolean queryMobile = CollUtil.isNotEmpty(mobile);

        List<ReceiveDeviationVO> weighbridgeList = null;
        if (queryWeighbridge) {
            weighbridgeList = materialDataMapper.selectDeviationByQuery(query, wagon);
        }

        List<ReceiveDeviationVO> mobileList = null;
        if (queryMobile) {
            mobileList = mobileReceiveTotalService.countDeviation(query, mobile);
        }

        List<ReceiveDeviationVO> list = new ArrayList<>(64);
        if (CollUtil.isNotEmpty(weighbridgeList)) {
            if (CollUtil.isNotEmpty(mobileList)) {
                Map<String, ReceiveDeviationVO> weighbridgeMap = weighbridgeList.stream().filter(obj -> obj.getDate() != null)
                        .collect(Collectors.toMap(ReceiveDeviationVO::getDate, obj -> obj));
                Map<String, ReceiveDeviationVO> mobileMap = mobileList.stream().filter(obj -> obj.getDate() != null)
                        .collect(Collectors.toMap(ReceiveDeviationVO::getDate, obj -> obj));

                Collection<String> allKey = CollUtil.addAll(CollUtil.newArrayList(weighbridgeMap.keySet())
                        , CollUtil.newArrayList(mobileMap.keySet()));
                Set<String> keySet = new HashSet<>(allKey);

                List<ReceiveDeviationVO> finalList = list;
                keySet.stream().forEach(key -> {
                    ReceiveDeviationVO vo = weighbridgeMap.get(key);

                    ReceiveDeviationVO mobileVo = mobileMap.get(key);
                    if (vo == null) {
                        finalList.add(mobileVo);
                        return;
                    }
                    if (mobileVo == null) {
                        finalList.add(vo);
                        return;
                    }
                    double negative = NumberUtil.add(vo.getNegative(), mobileVo.getNegative());
                    vo.setNegative(new Double(negative).intValue());

                    double positive = NumberUtil.add(vo.getPositive(), mobileVo.getPositive());
                    vo.setPositive(new Double(positive).intValue());

                    double normal = NumberUtil.add(vo.getNormal(), mobileVo.getNormal());
                    vo.setNormal(new Double(normal).intValue());
                    finalList.add(vo);
                });
                list = finalList;
            } else {
                list = weighbridgeList;
            }
        } else {
            list = mobileList;
        }

        Map<String, ReceiveDeviationVO> deviationVOMap = new HashMap<>();
        if (CollUtil.isNotEmpty(list)) {
            deviationVOMap = list.parallelStream().collect(Collectors.toMap(ReceiveDeviationVO::getDate, e -> e));
        }
        return deviationVOMap;
    }

    @Override
    public List<DeviationSecondSummaryVO> listDeviationSecondSummaryByQuery(SupplierAnalysisQuery query) {
        // 偏差数据
        List<DeviationSecondVO> deviationSecondVOS = listDeviationSecondByQuery(query);
        if (CollUtil.isEmpty(deviationSecondVOS)) {
            return null;
        }

        List<DeviationSecondSummaryVO> deviationSecondSummaryVOS = deviationSecondVOS.stream().map(deviationSecondVO -> {
            DeviationSecondSummaryVO deviationSecondSummaryVO = new DeviationSecondSummaryVO();
            deviationSecondSummaryVO.setProjectId(deviationSecondVO.getProjectId());
            deviationSecondSummaryVO.setProjectName(deviationSecondVO.getProjectName());
            List<DeviationSecondSummaryDetailVO> detailVOS = new ArrayList<>();
            // 正常
            DeviationSecondSummaryDetailVO normal = new DeviationSecondSummaryDetailVO();
            normal.setDeviationType(DeviationStatusEnum.NORMAL.description());
            normal.setCount(deviationSecondVO.getDeviationSecondDetailVOList().stream().map(DeviationSecondDetailVO::getNormal).reduce(Integer::sum).orElse(0));
            // 超负差
            DeviationSecondSummaryDetailVO negative = new DeviationSecondSummaryDetailVO();
            negative.setDeviationType(DeviationStatusEnum.NEGATIVEDIFFERENCE.description());
            negative.setCount(deviationSecondVO.getDeviationSecondDetailVOList().stream().map(DeviationSecondDetailVO::getNegative).reduce(Integer::sum).orElse(0));
            // 超正差
            DeviationSecondSummaryDetailVO positive = new DeviationSecondSummaryDetailVO();
            positive.setDeviationType(DeviationStatusEnum.POSITIVEDIFFERENCE.description());
            positive.setCount(deviationSecondVO.getDeviationSecondDetailVOList().stream().map(DeviationSecondDetailVO::getPositive).reduce(Integer::sum).orElse(0));
            // 无法确定
            DeviationSecondSummaryDetailVO unidentified = new DeviationSecondSummaryDetailVO();
            unidentified.setCount(deviationSecondVO.getDeviationSecondDetailVOList().stream().map(DeviationSecondDetailVO::getUnidentified).reduce(Integer::sum).orElse(0));
            unidentified.setDeviationType(DeviationStatusEnum.UNIDENTIFIED.description());

            detailVOS.add(negative);
            detailVOS.add(normal);
            detailVOS.add(unidentified);
            detailVOS.add(positive);
            deviationSecondSummaryVO.setDeviationSecondSummaryDetailVOList(detailVOS);
            return deviationSecondSummaryVO;
        }).collect(Collectors.toList());

        return deviationSecondSummaryVOS;
    }

    @Override
    public WarningOverviewTipsVO warningOverviewTip(Integer deptId) {

        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer companyId = currentUser.getCurrentCompanyId();
        List<Integer> projectIds = new ArrayList<>();
        if (currentUser.getCurrentProjectId() != null) {
            projectIds = Collections.singletonList(currentUser.getCurrentProjectId());
        } else {
            projectIds = projectServiceProxy.statisticsProjectIds(companyId, deptId);
        }
        List<Byte> warningTypeList = new ArrayList<>(Arrays.asList(new Byte[]{1, 2, 3, 4, 6, 7}));
        if (CollUtil.isEmpty(projectIds)) {
            return new WarningOverviewTipsVO(0, warningTypeList, projectIds);
        }
        Integer count = materialWarningMapper.queryOverviewWarningTips(companyId, projectIds, warningTypeList);
        return new WarningOverviewTipsVO(count, warningTypeList, projectIds);
    }
}
