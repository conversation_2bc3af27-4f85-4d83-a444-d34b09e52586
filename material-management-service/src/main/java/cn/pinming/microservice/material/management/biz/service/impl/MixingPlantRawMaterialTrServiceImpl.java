package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.entity.IngredientApplyDetail;
import cn.pinming.microservice.material.management.biz.entity.MixingPlantRawMaterialTr;
import cn.pinming.microservice.material.management.biz.form.RawMaterialLabReportForm;
import cn.pinming.microservice.material.management.biz.mapper.IngredientApplyDetailMapper;
import cn.pinming.microservice.material.management.biz.mapper.MixingPlantRawMaterialTrMapper;
import cn.pinming.microservice.material.management.biz.service.IMixingPlantRawMaterialTrService;
import cn.pinming.microservice.material.management.biz.vo.MixingPlantRawMaterialTrVO;
import cn.pinming.microservice.material.management.biz.vo.MixingPlantRawMaterialVO;
import cn.pinming.microservice.material.management.common.AppConstant;
import cn.pinming.microservice.material.management.common.util.IfBranchUtil;
import cn.pinming.microservice.material.management.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.material.management.proxy.FileServiceProxy;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.model.dto.FilePreviewDto;
import cn.pinming.v2.company.api.dto.EmployeeDetailDto;
import cn.pinming.v2.company.api.dto.EmployeeDetailQueryDto;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 拌合站—原料实验报告 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 16:30:16
 */
@Service
public class MixingPlantRawMaterialTrServiceImpl extends ServiceImpl<MixingPlantRawMaterialTrMapper, MixingPlantRawMaterialTr> implements IMixingPlantRawMaterialTrService {

    @Resource
    private FileServiceProxy fileServiceProxy;

    @Resource
    private EmployeeServiceProxy employeeServiceProxy;

    @Resource
    private MaterialServiceProxy materialServiceProxy;

    @Resource
    private AuthUserHolder authUserHolder;

    @Resource
    private IngredientApplyDetailMapper ingredientApplyDetailMapper;

    @Override
    public Page<MixingPlantRawMaterialTrVO> trPageList(Integer current, Integer size) {
        AuthUser currentUser = authUserHolder.getCurrentUser();

        QueryWrapper<MixingPlantRawMaterialTr> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(MixingPlantRawMaterialTr::getProjectId, currentUser.getCurrentProjectId()).orderByDesc(MixingPlantRawMaterialTr::getCreateTime);
        Page<MixingPlantRawMaterialTr> page = this.page(new Page<>(current, size), wrapper);
        Page<MixingPlantRawMaterialTrVO> voPage = new Page<>();
        BeanUtils.copyProperties(page, voPage);
        // 附件预览地址
        List<MixingPlantRawMaterialTr> records = page.getRecords();
        if (ObjectUtil.isNotNull(records)) {
            // 分页数据
            List<MixingPlantRawMaterialTrVO> voRecords = records.stream().map(item -> {
                MixingPlantRawMaterialTrVO vo = new MixingPlantRawMaterialTrVO();
                BeanUtils.copyProperties(item, vo);
                return vo;
            }).collect(Collectors.toList());
            voPage.setRecords(voRecords);

            // 配合比申请单关联数据
            List<String> labReportIds = voRecords.stream().map(MixingPlantRawMaterialTrVO::getId).collect(Collectors.toList());
            List<IngredientApplyDetail> ingredientApplyDetails = ingredientApplyDetailMapper.queryIngredientApplyDetailByReportIds(labReportIds);

            // 人员
            List<String> memberIds = voRecords.stream().map(MixingPlantRawMaterialTrVO::getCreateId).distinct().collect(Collectors.toList());
            EmployeeDetailQueryDto queryDto = new EmployeeDetailQueryDto();
            queryDto.setCompanyId(currentUser.getCurrentCompanyId());
            queryDto.setMemberIdList(memberIds);
            List<EmployeeDetailDto> employeeList = Optional.ofNullable(employeeServiceProxy.employeeList(queryDto)).orElse(Lists.newArrayList());
            Map<String, String> memberMap = employeeList.stream().collect(Collectors.toMap(EmployeeDetailDto::getMemberId, EmployeeDetailDto::getMemberName));

            // 二级分类/品种/规格
            Set<Integer> materialIds = voRecords.stream().flatMap(item -> Arrays.stream(item.getLabMaterialId().split(",")))
                    .map(Integer::parseInt).collect(Collectors.toSet());
            Map<Integer, String> categorySpecMap = materialServiceProxy.materialCategorySpec(materialIds);
            for (MixingPlantRawMaterialTrVO record : voRecords) {
                // 是否关联配合比申请单
                if (CollectionUtil.isNotEmpty(ingredientApplyDetails)) {
                    record.setIngredientRel(ingredientApplyDetails.stream().anyMatch(item ->
                            StringUtils.isNotBlank(item.getReportId()) && item.getReportId().equals(record.getId())));
                }
                // 人员名称
                record.setCreateName(memberMap.get(record.getCreateId()));
                // 附件预览地址
                String accessoryUuid = record.getAccessoryUuid();
                if (StringUtils.isNotBlank(accessoryUuid)) {
                    FilePreviewDto filePreview = fileServiceProxy.findFilePreview(accessoryUuid);
                    record.setAccessoryUrl(ObjectUtil.isNotNull(filePreview) ? filePreview.getFileUrl() : "");
                }
                // 二级分类/品种/规格
                List<MixingPlantRawMaterialVO> rawMaterialVOS = Lists.newArrayList();
                String labMaterialId = record.getLabMaterialId();
                List<Integer> materialIdList = Arrays.stream(labMaterialId.split(",")).map(Integer::parseInt).collect(Collectors.toList());
                for (Integer materialId : materialIdList) {
                    MixingPlantRawMaterialVO rawMaterialVO = new MixingPlantRawMaterialVO();
                    rawMaterialVO.setMaterialId(materialId);
                    rawMaterialVO.setMaterialName(categorySpecMap.get(materialId));
                    rawMaterialVOS.add(rawMaterialVO);
                }
                record.setRawMaterialVOS(rawMaterialVOS);
            }
        }
        return voPage;
    }

    @Override
    public MixingPlantRawMaterialTrVO getLabReportById(String id) {
        MixingPlantRawMaterialTrVO vo = new MixingPlantRawMaterialTrVO();
        MixingPlantRawMaterialTr rawMaterialTr = this.getById(id);
        BeanUtils.copyProperties(rawMaterialTr, vo);
        // 附件预览地址
        String accessoryUuid = rawMaterialTr.getAccessoryUuid();
        if (StringUtils.isNotBlank(accessoryUuid)) {
            FilePreviewDto filePreview = fileServiceProxy.findFilePreview(accessoryUuid);
            vo.setAccessoryUrl(ObjectUtil.isNotNull(filePreview) ? filePreview.getFileUrl() : "");
        }
        // 二级分类/品种/规格
        Set<Integer> materialIds = Arrays.stream(vo.getLabMaterialId().split(","))
                .map(Integer::parseInt).collect(Collectors.toSet());
        Map<Integer, String> categorySpecMap = materialServiceProxy.materialCategorySpec(materialIds);
        List<MixingPlantRawMaterialVO> rawMaterialVOS = Lists.newArrayList();
        String labMaterialId = vo.getLabMaterialId();
        List<Integer> materialIdList = Arrays.stream(labMaterialId.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        for (Integer materialId : materialIdList) {
            MixingPlantRawMaterialVO rawMaterialVO = new MixingPlantRawMaterialVO();
            rawMaterialVO.setMaterialId(materialId);
            rawMaterialVO.setMaterialName(categorySpecMap.get(materialId));
            rawMaterialVOS.add(rawMaterialVO);
        }
        vo.setRawMaterialVOS(rawMaterialVOS);
        return vo;
    }

    @Override
    public void addLabReport(RawMaterialLabReportForm reportForm) {
        MixingPlantRawMaterialTr entity = new MixingPlantRawMaterialTr();
        BeanUtils.copyProperties(reportForm, entity);
        this.save(entity);
        // 附件
        String accessoryUuid = entity.getAccessoryUuid();
        if (StringUtils.isNotBlank(accessoryUuid)) {
            // 上传附件
            fileServiceProxy.confirmFiles(Arrays.asList(accessoryUuid), AppConstant.FILE_TYPE_CODE);
        }
    }

    @Override
    public void updateLabReport(RawMaterialLabReportForm reportForm) {
        MixingPlantRawMaterialTr entity = new MixingPlantRawMaterialTr();
        BeanUtils.copyProperties(reportForm, entity);
        IfBranchUtil.isTrue(StringUtils.isBlank(entity.getAccessoryUuid())).trueHandle(() -> entity.setAccessoryUuid(""));
        IfBranchUtil.isTrue(StringUtils.isBlank(entity.getAccessoryFileName())).trueHandle(() -> entity.setAccessoryFileName(""));
        this.updateById(entity);
        // 附件
        String accessoryUuid = entity.getAccessoryUuid();
        if (StringUtils.isNotBlank(accessoryUuid)) {
            // 上传附件
            fileServiceProxy.confirmFiles(Arrays.asList(accessoryUuid), AppConstant.FILE_TYPE_CODE);
        }
    }

    @Override
    public List<String> queryHistoryTrNoOrUnit(Integer type, String keyword) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer currentProjectId = currentUser.getCurrentProjectId();
        List<String> result = Lists.newArrayList();
        QueryWrapper<MixingPlantRawMaterialTr> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(MixingPlantRawMaterialTr::getProjectId, currentProjectId);
        if (StringUtils.isNotBlank(keyword)) {
            if (type == 1) {
                wrapper.lambda().like(MixingPlantRawMaterialTr::getLabReportNo, keyword);
            } else if (type == 2) {
                wrapper.lambda().like(MixingPlantRawMaterialTr::getLabUnit, keyword);
            }
        }
        List<MixingPlantRawMaterialTr> list = this.list(wrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            result = list.stream().map(type == 1 ? MixingPlantRawMaterialTr::getLabReportNo :
                    MixingPlantRawMaterialTr::getLabUnit).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public Boolean trNoValidate(String trNo) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer currentProjectId = currentUser.getCurrentProjectId();
        QueryWrapper<MixingPlantRawMaterialTr> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(MixingPlantRawMaterialTr::getProjectId, currentProjectId).eq(MixingPlantRawMaterialTr::getLabReportNo, trNo);
        MixingPlantRawMaterialTr one = this.getOne(wrapper);
        if (ObjectUtil.isNotNull(one)) {
            return true;
        }
        return false;
    }

    @Override
    public Map<Integer,List<MixingPlantRawMaterialTrVO>> seek(List<Integer> materialIdList) {
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
        Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();

        Map<Integer,List<MixingPlantRawMaterialTrVO>> map = new HashMap<>();
        materialIdList.stream().forEach(e -> {
            List<MixingPlantRawMaterialTrVO> list = this.getBaseMapper().seek(companyId,projectId,e);
            map.put(e,list);
        });

        return map;
    }
}
