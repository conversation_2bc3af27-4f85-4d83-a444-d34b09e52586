package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.CreditCodeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import cn.pinming.core.common.model.PageList;
import cn.pinming.core.common.model.Pagination;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.CCRCResponseDTO;
import cn.pinming.microservice.material.management.biz.dto.CCRCSupplierDTO;
import cn.pinming.microservice.material.management.biz.dto.SupplierDTO;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContract;
import cn.pinming.microservice.material.management.biz.entity.Supplier;
import cn.pinming.microservice.material.management.biz.enums.DeleteEnum;
import cn.pinming.microservice.material.management.biz.form.SupplierForm;
import cn.pinming.microservice.material.management.biz.mapper.SupplierMapper;
import cn.pinming.microservice.material.management.biz.query.SupplierQuery;
import cn.pinming.microservice.material.management.biz.service.IPurchaseContractService;
import cn.pinming.microservice.material.management.biz.service.ISupplierService;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.RedisUtil;
import cn.pinming.microservice.material.management.proxy.CompanyProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material.management.wrapper.CreateNameWrapper;
import cn.pinming.v2.common.QueryPagination;
import cn.pinming.v2.common.api.service.PlugService;
import cn.pinming.v2.company.api.dto.CompanyDto;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import cn.pinming.yfzx.cxptz.thirdbutt.api.service.TokenService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <p>
 * 供应商表 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-04-13 13:17:24
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class SupplierServiceImpl extends ServiceImpl<SupplierMapper, Supplier> implements ISupplierService {

    @Resource
    private CreateNameWrapper memberNameWrapper;
    @Resource
    private AuthUserHolder authUserHolder;
    @Reference
    private PlugService plugService;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private CompanyProxy companyProxy;
    @Resource
    private IPurchaseContractService purchaseContractService;
    @Reference
    private TokenService ztcjTokenService;
    @Value("${common.ctcj.token.clientId}")
    private String tokenClientId;
    @Value("${common.ctcj.token.clientSecret}")
    private String tokenClientSecret;
    @Value("${common.ctcj.token.apiUrl}")
    private String tokenApiUrl;

    private static final String URL = "https://wz.crucg.com:8880/api/thirdparty/supplier/getSupplierHistory";

    private AuthUser getAuthUser() {
        return authUserHolder.getCurrentUser();
    }

    @Override
    public Page<SupplierVO> listByQuery(SupplierQuery query) {
        Page<SupplierDTO> page = this.getBaseMapper().selectSupplierPageListByQuery(query);
        List<SupplierDTO> records = page.getRecords();
        Page<SupplierVO> result = new Page<>();
        if (CollUtil.isNotEmpty(records)) {
            BeanUtils.copyProperties(page, result);
            // 拌合站列表
            List<Integer> plantList = records.stream().map(SupplierDTO::getPlantId).filter(Objects::nonNull).collect(Collectors.toList());
            List<ProjectVO> simpleProjects = projectServiceProxy.getSimpleProjects(plantList);

            Set<Integer> supplierList = records.stream().map(SupplierDTO::getId).filter(Objects::nonNull).collect(Collectors.toSet());
            Set<Integer> usedSupplierList = purchaseContractService.listUsedSupplierId();
            Map<Integer, Byte> supplierUsedMap = Maps.newHashMap();
            supplierList.forEach(supplierId -> {
                Byte used = usedSupplierList.contains(supplierId) ? DeleteEnum.NORMAL.value() : DeleteEnum.DELETE.value();
                supplierUsedMap.put(supplierId, used);
            });

            Map<Integer, ProjectVO> projectInfoMap = Maps.newHashMap();
            if (CollUtil.isNotEmpty(simpleProjects)) {
                projectInfoMap = simpleProjects.stream().collect(Collectors.toMap(ProjectVO::getProjectId, e -> e));
            }
            Map<Integer, ProjectVO> finalProjectInfoMap = projectInfoMap;
            List<SupplierVO> supplierVOList = records.stream().map(supplierDTO -> {
                SupplierVO supplierVO = new SupplierVO();
                BeanUtils.copyProperties(supplierDTO, supplierVO);
                ProjectVO projectVO = finalProjectInfoMap.get(supplierDTO.getPlantId());
                supplierVO.setEnableDelete(supplierUsedMap.getOrDefault(supplierDTO.getId(), DeleteEnum.NORMAL.value()));
                Optional.ofNullable(projectVO).ifPresent(project -> supplierVO.setPlantName(projectVO.getProjectTitle()));
                return supplierVO;
            }).collect(Collectors.toList());
            result.setRecords(supplierVOList);
        }
        memberNameWrapper.wrap(result, getAuthUser().getCurrentCompanyId());
        return result;
    }

    @Override
    public void saveSupplier(SupplierForm form) {
        checkSupplierName(form.getId(), form.getName());
        //校验组织机构代码
        if (StrUtil.isNotBlank(form.getCreditCode()) && !CreditCodeUtil.isCreditCode(form.getCreditCode())) {
            throw new BOException(BOExceptionEnum.CREDIT_CODE_ILLEGAL);
        }
        if (StrUtil.isNotEmpty(form.getCreditCode())) {
            checkCreditCode(form.getId(), form.getCreditCode());
        }
        //外部系统代码去空格
        Optional.ofNullable(form.getExtCode()).ifPresent(ext -> form.setExtCode(StrUtil.cleanBlank(ext)));
        //保存
        Supplier supplier = new Supplier();
        BeanUtils.copyProperties(form, supplier);
        this.saveOrUpdate(supplier);
    }

    @Override
    public void updateSupplierStatus(String id) {
        this.getBaseMapper().updateStatusById(id, getAuthUser().getId());
    }

    @Override
    public void deleteSupplierById(Integer id) {
        Set<Integer> usedSupplierIdList = purchaseContractService.listUsedSupplierId();
        if (usedSupplierIdList.contains(id)) {
            throw new BOException(BOExceptionEnum.RELATION_CONTRACT);
        }
        this.lambdaUpdate().eq(Supplier::getId, id).set(Supplier::getIsDeleted, DeleteEnum.DELETE.value()).update(new Supplier());
    }

    @Override
    public ImportResultVO importSupplier(List<SupplierForm> list) {
        ImportResultVO resultVO = new ImportResultVO();
        //供应商查重
        List<String> supplierNames = list.stream().map(SupplierForm::getName).collect(Collectors.toList());
        List<Supplier> repeatNameSupplier = this.lambdaQuery().in(Supplier::getName, supplierNames).list();
        List<String> existSupplierNames = repeatNameSupplier.stream().map(Supplier::getName).collect(Collectors.toList());
        //组织机构代码查重

        List<Supplier> repeatCodeSupplier = Lists.newArrayList();
        List<String> supplierCodes = list.stream().map(SupplierForm::getCreditCode).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(supplierCodes)) {
            repeatCodeSupplier = this.lambdaQuery().in(Supplier::getCreditCode, supplierCodes).list();
        }
        List<String> existSupplierCodes = repeatCodeSupplier.stream().map(Supplier::getCreditCode).collect(Collectors.toList());

        List<String> exist = list.stream().filter(e -> existSupplierNames.contains(e.getName()) || existSupplierCodes.contains(e.getCreditCode()))
                .map(SupplierForm::getName).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(exist) && exist.size() > 0) {
            resultVO.setError(exist.size());
            resultVO.setReason("供应商或组织机构代码已存在,不需要导入");
            resultVO.setList(exist);
        }

        //供应商写入
        List<SupplierForm> newSupplierList = list.stream().filter(e -> StrUtil.isNotBlank(e.getName()))
                .filter(e -> !existSupplierNames.contains(e.getName()))
                .filter(e -> !existSupplierCodes.contains(e.getCreditCode())).collect(Collectors.toList());
        this.saveBatch(BeanUtil.copyToList(newSupplierList, Supplier.class));
        resultVO.setSuccess(newSupplierList.size());
        return resultVO;
    }

    @Override
    public List<MixPlantInfoVO> listMixPlantInfo() {
        List<Integer> plateIds = projectServiceProxy.getPluginProjectIds(ProjectServiceProxy.PLATE_PLUGIN_NO);
        List<MixPlantInfoVO> mixPlantInfoVOList = Lists.newArrayList();
        List<SimpleConstructionProjectDto> projectVOList;

        if (CollUtil.isNotEmpty(plateIds)) {
            projectVOList = projectServiceProxy.findProjectsByProjectIds(plateIds);
            if (CollUtil.isNotEmpty(projectVOList)) {
                // 公司列表
                List<Integer> companyList = projectVOList.stream().filter(Objects::nonNull).map(SimpleConstructionProjectDto::getCompanyId).collect(Collectors.toList());
                List<CompanyDto> companyDtos = companyProxy.findSimpleCompanyByCompanyList(companyList);
                Map<Integer, CompanyDto> companyInfoMap = Maps.newHashMap();
                if (CollUtil.isNotEmpty(companyDtos)) {
                    companyInfoMap = companyDtos.stream().collect(Collectors.toMap(CompanyDto::getCompanyId, e -> e));
                }

                final Map<Integer, CompanyDto> finalCompanyInfoMap = companyInfoMap;
                mixPlantInfoVOList = projectVOList.stream().map(projectDto -> {
                    MixPlantInfoVO mixPlantInfoVO = new MixPlantInfoVO();
                    mixPlantInfoVO.setProjectId(projectDto.getProjectId());
                    String companyName = finalCompanyInfoMap.get(projectDto.getCompanyId()).getCompanyName();
                    mixPlantInfoVO.setPlantName(companyName + StrUtil.SLASH + projectDto.getProjectTitle());
                    return mixPlantInfoVO;
                }).collect(Collectors.toList());
            }
        }
        return mixPlantInfoVOList;
    }

    @Override
    public void initDataList() {
        int count = this.count();
        if (count == 0) {
            String token = ztcjTokenService.getToken(tokenClientId, tokenClientSecret, tokenApiUrl);
            HttpResponse response = HttpRequest.get(URL).header("Authorization", "Bearer " + token).execute();
            String body = response.body();
            CCRCResponseDTO ccrcResponseDTO = JSONUtil.toBean(body, CCRCResponseDTO.class);
            if (CollUtil.isNotEmpty(ccrcResponseDTO.getData())) {
                List<CCRCSupplierDTO> list = ccrcResponseDTO.getData();
                List<Supplier> suppliers = list.stream().map(obj -> {
                    Supplier supplier = new Supplier();
                    supplier.setCompanyId(11541);
                    supplier.setCreditCode(obj.getTaxpayerCode());
                    supplier.setName(obj.getSupplierName());
                    return supplier;
                }).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(suppliers)) {
                    this.saveBatch(suppliers);
                }
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSupplierList() {
        //查询数据库
        List<Supplier> supplierList = this.lambdaQuery().select(Supplier::getId, Supplier::getCreditCode).list();
        String token = ztcjTokenService.getToken(tokenClientId, tokenClientSecret, tokenApiUrl);
        List<Supplier> totalList = new ArrayList<>();
            HttpResponse response = HttpRequest.get(URL).header("Authorization", "Bearer " + token).execute();
            String body = response.body();
            CCRCResponseDTO ccrcResponseDTO = JSONUtil.toBean(body, CCRCResponseDTO.class);
            if (CollUtil.isNotEmpty(ccrcResponseDTO.getData())) {
                List<CCRCSupplierDTO> list = ccrcResponseDTO.getData();
                List<Supplier> suppliers = list.stream().map(obj -> {
                    Supplier supplier = new Supplier();
                    supplier.setCompanyId(11541);
                    supplier.setCreditCode(obj.getTaxpayerCode());
                    supplier.setName(obj.getSupplierName());
                    return supplier;
                }).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(suppliers)) {
                    totalList.addAll(suppliers);
                }
        }

        List<Supplier> collect = totalList.stream().filter(obj -> {
            for (Supplier newObj : supplierList) {
                if (obj.getCreditCode().equals(newObj.getCreditCode())) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        this.saveBatch(collect);
    }

    private void checkSupplierName(Integer id, String name) {
        int count = this.lambdaQuery().ne(Objects.nonNull(id), Supplier::getId, id).eq(Supplier::getName, name).count();
        if (count != 0) {
            throw new BOException(BOExceptionEnum.SUPPLIER_NAME_EXIST);
        }
    }

    private void checkCreditCode(Integer id, String creditCode) {
        int count = this.lambdaQuery().ne(Objects.nonNull(id), Supplier::getId, id).eq(Supplier::getCreditCode, creditCode).count();
        if (count != 0) {
            throw new BOException(BOExceptionEnum.CREDIT_CODE_REPEAT);
        }
    }



    @Override
    public List<SupplierDTO> findListByCompanyIdAndSupplierIds(@NonNull Integer companyId, @NonNull List<Integer> supplierIds) {
        List<SupplierDTO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(supplierIds)) {
            List<Supplier> list = getBaseMapper().selectSupplierList(companyId, supplierIds);
            if (CollUtil.isNotEmpty(list)) {
                result = BeanUtil.copyToList(list, SupplierDTO.class);
            }
        }
        return result;
    }
}