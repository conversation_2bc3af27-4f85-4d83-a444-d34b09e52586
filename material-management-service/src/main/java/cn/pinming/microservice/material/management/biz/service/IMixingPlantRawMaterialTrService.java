package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.entity.MixingPlantRawMaterialTr;
import cn.pinming.microservice.material.management.biz.form.RawMaterialLabReportForm;
import cn.pinming.microservice.material.management.biz.vo.MixingPlantRawMaterialTrVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 拌合站—原料实验报告 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 16:30:16
 */
public interface IMixingPlantRawMaterialTrService extends IService<MixingPlantRawMaterialTr> {

    Page<MixingPlantRawMaterialTrVO> trPageList(Integer current, Integer size);

    MixingPlantRawMaterialTrVO getLabReportById(String id);

    void addLabReport(RawMaterialLabReportForm reportForm);

    void updateLabReport(RawMaterialLabReportForm reportForm);

    List<String> queryHistoryTrNoOrUnit(Integer type, String keyword);

    Boolean trNoValidate(String trNo);

    Map<Integer,List<MixingPlantRawMaterialTrVO>> seek(List<Integer> materialIdList);
}
