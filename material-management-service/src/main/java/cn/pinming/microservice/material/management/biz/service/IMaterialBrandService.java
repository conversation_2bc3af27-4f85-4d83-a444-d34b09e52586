package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.entity.MaterialBrand;
import cn.pinming.microservice.material.management.biz.form.MaterialBrandForm;
import cn.pinming.microservice.material.management.biz.query.PurchaseBrandQuery;
import cn.pinming.microservice.material.management.biz.vo.MaterialBrandVO;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * <p>
 * 材料品牌 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface IMaterialBrandService extends IService<MaterialBrand> {

    List<MaterialBrandVO> queryListByQuery(PurchaseBrandQuery query);

    String saveMaterialBrand(MaterialBrandForm form);
}
