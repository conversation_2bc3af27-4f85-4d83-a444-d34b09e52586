package cn.pinming.microservice.material.management.proxy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.management.biz.convert.CycleAvoidingMappingContext;
import cn.pinming.microservice.material.management.biz.convert.MaterialCategoryMapper;
import cn.pinming.microservice.material.management.biz.convert.MaterialUnitMapper;
import cn.pinming.microservice.material.management.biz.vo.MaterialCategoryVO;
import cn.pinming.microservice.material.management.biz.vo.MaterialUnitVO;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material_unit.api.beans.MyPage;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialCategoryDto;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialUnitConversionDto;
import cn.pinming.microservice.material_unit.api.material.service.MaterialService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2021/9/3 2:16 下午
 */
@Slf4j
@Component
public class MaterialServiceProxyImpl implements MaterialServiceProxy {

    @Reference
    private MaterialService materialService;

    @Override
    public List<MaterialUnitVO> unitListByCategoryId(@NonNull Integer companyId,
                                                     @NonNull Integer categoryId) {
        List<MaterialUnitConversionDto> list =
                materialService.listUnitConversionByCategoryId(companyId, categoryId);
        List<MaterialUnitVO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            result = MaterialUnitMapper.INSTANCE.unitToUnitVOs(list);
        }
        return result;
    }

    @Override
    public List<MaterialUnitVO> unitListByCategoryIds(@NonNull Integer companyId,
                                                      @NonNull List<Integer> categoryIds) {
        List<MaterialUnitVO> result = new ArrayList<>();
        List<MaterialUnitConversionDto> list = materialService.listUnitConversionByCategoryIds(companyId, categoryIds);
        if (CollUtil.isNotEmpty(list)) {
            result = MaterialUnitMapper.INSTANCE.unitToUnitVOs(list);
        }
        return result;
    }

    @Override
    public Map<Integer, List<String>> unitMapByCategoryIds(Integer companyId, List<Integer> categoryIds) {
        Map<Integer, List<String>> map = new HashMap<>();
        List<MaterialUnitConversionDto> list = materialService.listUnitConversionByCategoryIds(companyId, categoryIds);
        if (CollUtil.isNotEmpty(list)) {
            Map<Integer, List<MaterialUnitConversionDto>> listMap = list.stream().collect(Collectors.groupingBy(MaterialUnitConversionDto::getMaterialCategoryId));
            listMap.forEach((k, v) -> {
                if (CollectionUtil.isNotEmpty(v)) {
                    List<String> units = v.stream().filter(item -> ObjectUtil.isNotNull(item.getSourceUnit()) && StringUtils.isNotBlank(item.getSourceUnit().getUnitName()))
                            .map(item -> item.getSourceUnit().getUnitName()).distinct().collect(Collectors.toList());
                    map.put(k, units);
                }
            });
        }
        return map;
    }

    @Override
    public List<MaterialCategoryDto> listCategoryByIds(Integer companyId, Collection<Integer> categoryIds) {
        return materialService.listCategoryByIds(companyId, categoryIds);
    }

    @Override
    public List<MaterialDto> listMaterialByIds(@NonNull Collection<Integer> materialIds) {
        List<MaterialDto> list = materialService.listMaterialByIds(materialIds);
        if (list == null) {
            return new ArrayList<>(0);
        }
        return list;
    }

    @Override
    public MaterialDto materialById(@NonNull Integer materialId) {
        List<MaterialDto> materials = listMaterialByIds(Collections.singletonList(materialId));
        if (CollUtil.isNotEmpty(materials)) {
            return materials.get(0);
        }
        return null;
    }

    @Override
    public MaterialDto materialByExtCode(@NonNull String extCode) {
        List<MaterialDto> list = materialService.listMaterialByExtCodes(Collections.singletonList(extCode));
        if (CollUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public List<MaterialDto> materialByExtCodes(@NonNull Collection<String> extCodes) {
        return materialService.listMaterialByExtCodes(extCodes);
    }

    @Override
    public List<MaterialCategoryDto> materialCategoryByExtCodes(@NonNull Collection<String> extCodes) {
        return materialService.listMaterialCategoryByExtCodes(extCodes);
    }

    @Override
    public Map<Integer, String> materialSpec(Set<Integer> materialIds) {
        Map<Integer, String> map = new HashMap<>();

        List<MaterialDto> materialDtos = Lists.newArrayList();
        Set<Integer> materialSet = materialIds.stream().filter(ObjectUtil::isNotNull).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(materialSet)) {
            materialDtos = listMaterialByIds(materialSet);
        }

        for (Integer materialId : materialIds) {
            if (materialId != null) {
                Optional<MaterialDto> any = materialDtos.stream().filter(item -> materialId.intValue() == item.getMaterialId()).findAny();
                if (any.isPresent()) {
                    MaterialDto materialDto = any.get();
                    map.put(materialId, StrUtil.format("{}/{}", materialDto.getMaterialName(), materialDto.getMaterialSpec()));
                } else {
                    map.put(materialId, "--");
                }
            } else {
                map.put(materialId, "--");
            }
        }
        return map;
    }

    @Override
    public Map<Integer, String> materialCategorySpec(Set<Integer> materialIds) {
        Map<Integer, String> map = new HashMap<>();

        List<MaterialDto> materialDtos = Lists.newArrayList();
        Set<Integer> materialSet = materialIds.stream().filter(ObjectUtil::isNotNull).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(materialSet)) {
            materialDtos = listMaterialByIds(materialSet);
        }

        for (Integer materialId : materialIds) {
            if (materialId != null) {
                Optional<MaterialDto> any = materialDtos.stream().filter(item -> materialId.intValue() == item.getMaterialId()).findAny();
                if (any.isPresent()) {
                    MaterialDto materialDto = any.get();
                    map.put(materialId, StrUtil.format("{}/{}/{}", materialDto.getMaterialCategoryName(),
                            materialDto.getMaterialName(), materialDto.getMaterialSpec()));
                } else {
                    map.put(materialId, "--");
                }
            } else {
                map.put(materialId, "--");
            }
        }
        return map;
    }

    @Override
    public List<MaterialCategoryVO> listCategoryByCompanyId(Integer companyId) {
        List<MaterialCategoryDto> list = materialService.treeListAllCategory(companyId);
        return MaterialCategoryMapper.INSTANCE.DTOToVOs(list, new CycleAvoidingMappingContext());
    }

    @Override
    public List<Integer> getMaterialIdsByCategoryId(Integer companyId, Integer categoryId) {
        List<MaterialDto> materialDtos = materialService.listAllMaterialByCategoryId(companyId, categoryId);
        assert materialDtos != null;
        return materialDtos.stream().map(MaterialDto::getMaterialId).collect(Collectors.toList());
    }

    @Override
    public List<Integer> getMaterialIdsByCategoryIds(Integer companyId, List<Integer> categoryIds) {
        List<MaterialDto> materialDtos = materialService.listAllMaterialByCategoryIds(companyId, categoryIds);
        assert materialDtos != null;
        return  materialDtos.stream().map(MaterialDto::getMaterialId)
                .collect(Collectors.toList());
    }

    @Override
    public MyPage<MaterialDto> listMaterial(@NotNull Integer companyId, Long timestamp, @NotNull Integer pageNum, @NotNull Integer pageSize) {
        return materialService.listMaterial(companyId, timestamp, pageNum, pageSize);
    }
}
