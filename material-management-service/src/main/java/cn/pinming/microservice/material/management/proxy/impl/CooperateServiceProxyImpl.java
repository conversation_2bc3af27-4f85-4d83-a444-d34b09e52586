package cn.pinming.microservice.material.management.proxy.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.management.biz.convert.CooperateMapper;
import cn.pinming.microservice.material.management.biz.entity.Supplier;
import cn.pinming.microservice.material.management.biz.enums.EnableEnum;
import cn.pinming.microservice.material.management.biz.service.ISupplierService;
import cn.pinming.microservice.material.management.biz.vo.CooperateVO;
import cn.pinming.microservice.material.management.biz.vo.SupplierVO;
import cn.pinming.microservice.material.management.common.util.RedisUtil;
import cn.pinming.microservice.material.management.proxy.CooperateServiceProxy;
import cn.pinming.microservice.material_unit.api.enterprise.dto.CooperateEnterpriseDto;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2021/9/7 9:36 上午
 */
@Slf4j
@Component
public class CooperateServiceProxyImpl implements CooperateServiceProxy {

    @Resource
    private ISupplierService supplierService;
    @Resource
    private RedisUtil redisUtil;

    @Override
    public List<CooperateVO> findCooperateByCompanyId(@NonNull Integer companyId) {
        log.error("1");
        List<Supplier> list = supplierService.list(new LambdaQueryWrapper<Supplier>()
                .eq(Supplier::getCompanyId, companyId)
                .eq(Supplier::getIsEnable, EnableEnum.ON.value()).select(Supplier::getId, Supplier::getName));
        List<CooperateVO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            result = BeanUtil.copyToList(list, CooperateVO.class);
        }
        return result;
    }

    @Override
    public List<CooperateVO> findCooperateByIds(@NonNull Integer companyId,
                                                @NonNull String cooperateIds) {
        List<CooperateVO> result = new ArrayList<>();
        if (StrUtil.isEmpty(cooperateIds)) {
            return result;
        }
        List<Supplier> list = supplierService.list(new LambdaQueryWrapper<Supplier>()
                .eq(Supplier::getCompanyId, companyId).in(Supplier::getId, StrUtil.split(cooperateIds, CharPool.COMMA))
                .select(Supplier::getId, Supplier::getName));
        if (CollUtil.isNotEmpty(list)) {
            return BeanUtil.copyToList(list, CooperateVO.class);
        }
        return result;
    }

    @Override
    public List<CooperateEnterpriseDto> findByCompanyId(Integer companyId, Integer current, Integer size) {
        String supplierCacheKey = StrUtil.format("material:{}", companyId);
        if (redisUtil.hasKey(supplierCacheKey)) {
//            log.error("从缓存中获取:{}", redisUtil.get(supplierCacheKey, String.class));
            return JSONUtil.toList(redisUtil.get(supplierCacheKey, String.class), CooperateEnterpriseDto.class);
        }
        Page<Supplier> page = new Page<>();
        page.setCurrent(current);
        page.setSize(size);

        Page<Supplier> supplierPage = supplierService.page(page, new LambdaQueryWrapper<Supplier>()
                .eq(Supplier::getCompanyId, companyId).eq(Supplier::getIsEnable, EnableEnum.ON.value()));
        List<Supplier> records = supplierPage.getRecords();
        List<CooperateEnterpriseDto> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            result = records.stream().map(e -> {
                CooperateEnterpriseDto dto = new CooperateEnterpriseDto();
                dto.setId(e.getId());
                dto.setEnterpriseName(e.getName());
                return dto;
            }).collect(Collectors.toList());
        }
        redisUtil.set(supplierCacheKey, JSONUtil.toJsonStr(result), 60 * 5);
//        log.error("放置缓存中:{},{}", supplierCacheKey, JSONUtil.toJsonStr(result));
        return result;
    }
}
