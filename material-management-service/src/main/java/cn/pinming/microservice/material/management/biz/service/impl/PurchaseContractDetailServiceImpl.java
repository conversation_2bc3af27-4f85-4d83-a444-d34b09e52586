package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.management.biz.dto.ContractDetailDTO;
import cn.pinming.microservice.material.management.biz.dto.SimpleContractDetailDTO;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContractDetail;
import cn.pinming.microservice.material.management.biz.mapper.PurchaseContractDetailMapper;
import cn.pinming.microservice.material.management.biz.service.IPurchaseContractDetailService;
import cn.pinming.microservice.material.management.biz.vo.GoodsSimpleVO;
import cn.pinming.microservice.material.management.biz.vo.PurchaseContractDetailVO;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 采购合同物料明细 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Service
public class PurchaseContractDetailServiceImpl extends ServiceImpl<PurchaseContractDetailMapper, PurchaseContractDetail> implements IPurchaseContractDetailService {
    @Resource
    private PurchaseContractDetailMapper contractDetailMapper;

    @Override
    public List<String> listRemoveIdsById(String contractId, List<Integer> materialIdList) {
//        return this.lambdaQuery().eq(PurchaseContractDetail::getContractId, contractId)
//                .notIn(PurchaseContractDetail::getMaterialId, materialIdList)
//                .select(PurchaseContractDetail::getId).list().parallelStream()
//                .map(PurchaseContractDetail::getId).collect(
//                    Collectors.toList());
        return contractDetailMapper.listRemoveIdsById(contractId,materialIdList);
    }

    @Override
    public List<PurchaseContractDetailVO> listContractDetailById(String contractId) {
        if (StrUtil.isBlank(contractId)) {
            throw new BOException(BOExceptionEnum.PURCHASE_CONTRACT_SELECT);
        }

        List<PurchaseContractDetail> list = this.lambdaQuery().eq(PurchaseContractDetail::getContractId, contractId).list();
        List<PurchaseContractDetailVO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            result = list.parallelStream().map(obj -> {
                PurchaseContractDetailVO vo = new PurchaseContractDetailVO();
                BeanUtils.copyProperties(obj, vo);
                return vo;
            }).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public List<ContractDetailDTO> selectByContractIds(List<String> contractIds) {
        return this.getBaseMapper().selectByContractIds(contractIds);
    }

    @Override
    public List<ContractDetailDTO> selectByContractDetailIds(List<String> contractIds) {
        return this.getBaseMapper().selectByContractDetailIds(contractIds);
    }

    @Override
    public List<String> listUnitById(Integer companyId, Integer projectId) {
        return this.getBaseMapper().selectUnitById(companyId,projectId);
    }

    @Override
    public List<SimpleContractDetailDTO> querySimpleContractDetails(List<String> contractDetailId) {
        List<PurchaseContractDetail> list = lambdaQuery()
                .in(PurchaseContractDetail::getId, contractDetailId)
                .list();

        List<SimpleContractDetailDTO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            result = list.stream().map(e -> {
                SimpleContractDetailDTO simpleContractDetailDTO = new SimpleContractDetailDTO();
                BeanUtils.copyProperties(e, simpleContractDetailDTO);
                simpleContractDetailDTO.setContractDetailId(e.getId());
                return simpleContractDetailDTO;
            }).collect(Collectors.toList());
        }

        return result;
    }

}
