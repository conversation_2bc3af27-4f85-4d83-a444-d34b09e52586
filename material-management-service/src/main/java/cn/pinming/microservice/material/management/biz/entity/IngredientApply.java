package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 拌合站配料申请单
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 09:54:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_ingredient_apply")
@ApiModel(value = "IngredientApply对象", description = "拌合站配料申请单")
public class IngredientApply implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "配料单id")
    @TableField("ingredient_list_id")
    private String ingredientListId;

    @ApiModelProperty(value = "申请单编号")
    private String no;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "参数要求")
    @TableField("parameter_requirements")
    private String parameterRequirements;

    @ApiModelProperty(value = "申请日期")
    @TableField("apply_date")
    private LocalDate applyDate;

    @ApiModelProperty(value = "使用日期")
    @TableField("use_date")
    private LocalDate useDate;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
