package cn.pinming.microservice.material.management.config;

import cn.hutool.json.JSONUtil;
import cn.pinming.core.web.response.ErrorResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
public class ztcjWebSecurityConfig extends WebSecurityConfigurerAdapter {
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.csrf().disable()
                .authorizeRequests()
                .antMatchers(HttpMethod.POST, "/api/common/extWeightList").hasAnyAuthority("SCOPE_chengjian-crucgzhgd")
                .anyRequest().permitAll() // 允许所有其他请求，不进行身份验证
                .and()
                .addFilterBefore(new PermitAllFilter(), UsernamePasswordAuthenticationFilter.class) // 添加PermitAllFilter
                .exceptionHandling()
                .accessDeniedHandler((request, response, accessDeniedException) -> {
                    response.reset();
                    response.setContentType("application/json");
                    response.setCharacterEncoding("utf-8");
                    ErrorResponse errorResponse = new ErrorResponse("-200", "无权限访问,请联系中铁城建管理员开启相应权限(SCOPE_chengjian-crucgzhgd)");
                    try {
                        response.getWriter().println(JSONUtil.toJsonStr(errorResponse));
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                })
                .and()
                .oauth2ResourceServer().jwt().jwkSetUri("https://reg.crcc.cn/discovery/certs");
    }

    public static class PermitAllFilter extends OncePerRequestFilter {
        @Override
        protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
            String servletPath = request.getServletPath();
            if (!servletPath.startsWith("/api/common/extWeightList")) {
                request = new ModifyHttpServletRequest(request);
            }
            log.error("Authorization header is not allowed:{} ", request.getHeader("Authorization"));
            filterChain.doFilter(request, response); // 直接放行请求
        }
    }

    public static class ModifyHttpServletRequest extends HttpServletRequestWrapper {
        public ModifyHttpServletRequest(HttpServletRequest request) {
            super(request);
        }

        @Override
        public String getHeader(String name) {
            if ("Authorization".equalsIgnoreCase(name)) {
                return null; // 删除Authorization头部
            }
            return super.getHeader(name);
        }
    }
}
