package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * AI数钢筋结果表
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-02-08 17:10:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("s_material_ai_identify_rebar")
@ApiModel(value = "MaterialAiIdentifyRebar对象", description = "AI数钢筋结果表")
public class MaterialAiIdentifyRebar implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "业务唯一编号")
    @TableField("ticket_id")
    private String ticketId;

    @ApiModelProperty(value = "钢筋原图UUID")
    @TableField("origin_pic_id")
    private String originPicId;

    @ApiModelProperty(value = "原图的钢筋数量")
    @TableField("origin_rebar_count")
    private Integer originRebarCount;

    @ApiModelProperty(value = "用户调整后的钢筋图片UUID")
    @TableField("pic_id")
    private String picId;

    @ApiModelProperty(value = "调整后的钢筋数量")
    @TableField("rebar_count")
    private Integer rebarCount;


}
