package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.ExistTicketDetailDTO;
import cn.pinming.microservice.material.management.biz.dto.ExistTicketDetailStatisticsDTO;
import cn.pinming.microservice.material.management.biz.dto.ExitTicketPageDTO;
import cn.pinming.microservice.material.management.biz.dto.PreInfoDTO;
import cn.pinming.microservice.material.management.biz.entity.MixingPlantExitTicket;
import cn.pinming.microservice.material.management.biz.query.BaseQuery;
import cn.pinming.microservice.material.management.biz.vo.ExistTicketHistoryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 拌合站出场单 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-27 15:11:44
 */
public interface MixingPlantExitTicketMapper extends BaseMapper<MixingPlantExitTicket> {

    IPage<ExitTicketPageDTO> selectPages(BaseQuery query);

    ExistTicketDetailDTO selectDetail(@Param("id") String id);

    ExistTicketDetailStatisticsDTO statistics(@Param("id") String mixingPlantOrderDetailId);

    ExistTicketHistoryVO history(@Param("id")String id);

    List<String> selectTruckNoHistory(@Param("truckNo") String truckNo,@Param("companyId")Integer companyId,@Param("projectId")Integer projectId);

    List<MixingPlantExitTicket> selectIsCanceled(@Param("id") String mixingPlantOrderDetailId);

    /**
     * 获取指定订单下的发货数量
     * @param orderId 订单ID
     * @param projectId 项目ID
     * @return 共发货数量
     */
    String selectSendTotalCountByOrderId(@Param("orderId") String orderId, @Param("projectId") Integer projectId);

    List<ExistTicketDetailStatisticsDTO> statisticsByIdS(@Param("idList") List<String> detailIdS);

    List<ExistTicketHistoryVO> historys(@Param("list") List<String> orderDetailIdList);

    String getPreTruckNoByTicketNo(@Param("ticketNo") String ticketNo, @Param("orderId") String orderId);
}
