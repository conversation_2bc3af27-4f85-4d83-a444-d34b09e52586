package cn.pinming.microservice.material.management.biz.providers.event;

import cn.pinming.microservice.material.management.biz.dto.MaterialDataForFY;
import cn.pinming.microservice.material.management.biz.dto.WeighbridgeAcceptDataDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialData;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 */

public class PushEvent extends ApplicationEvent {
    private List<MaterialDataForFY> materialDataS;
    private String tuckNo;

    public PushEvent(Object source, List<MaterialDataForFY> materialDataS) {
        super(source);
        this.materialDataS = materialDataS;
        this.tuckNo = tuckNo;
    }

    public List<MaterialDataForFY> getMaterialDataS() {
        return materialDataS;
    }

    public void setMaterialDataS(List<MaterialDataForFY> materialDataS) {
        this.materialDataS = materialDataS;
    }
}
