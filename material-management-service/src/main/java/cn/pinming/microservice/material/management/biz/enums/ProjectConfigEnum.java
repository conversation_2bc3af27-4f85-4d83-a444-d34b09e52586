package cn.pinming.microservice.material.management.biz.enums;

public enum ProjectConfigEnum {
    ONE((byte) 1, "报备收料是否自动生成对账入库单"),
    TWO((byte) 2, "是否有权限设置含水率"),
    THREE((byte) 3, "允许修正后立即完成对账核对"),
    FOUR((byte) 4, "ocr相关配置"),
    FIVE((byte) 5, "同步仓库配置"),
    ;

    private final byte value;

    private final String description;

    ProjectConfigEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }

    public String description() {
        return description;
    }
}
