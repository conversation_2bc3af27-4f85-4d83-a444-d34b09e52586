package cn.pinming.microservice.material.management.biz.service.impl;

import cn.pinming.microservice.material.management.biz.dto.HandleAdviceDTO;
import cn.pinming.microservice.material.management.biz.entity.WarningHandlerAdvice;
import cn.pinming.microservice.material.management.biz.mapper.WarningHandlerAdviceMapper;
import cn.pinming.microservice.material.management.biz.service.IWarningHandlerAdviceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 预警处理人建议表 服务实现类
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 13:21:53
 */
@Service
public class WarningHandlerAdviceServiceImpl extends ServiceImpl<WarningHandlerAdviceMapper, WarningHandlerAdvice> implements IWarningHandlerAdviceService {

    @Resource
    private WarningHandlerAdviceMapper handlerAdviceMapper;

    @Override
    public List<HandleAdviceDTO> getLastHandlerAdvice(List<String> warningIds) {
        return handlerAdviceMapper.selectLastHandlerAdvice(warningIds);
    }
}
