package cn.pinming.microservice.material.management.common.annotation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 */
public class NotLessThanZeroValidator implements ConstraintValidator<NotLessThanZero,Object> {
    @Override
    public boolean isValid(Object o, ConstraintValidatorContext context) {
       if(null == o){
           return true;
       }
       if(o instanceof Number){
           if(((Number)o ).doubleValue() > 0){
               return true;
           }
       }
       return false;
    }
}
