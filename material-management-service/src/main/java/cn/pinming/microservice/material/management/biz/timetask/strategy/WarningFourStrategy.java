package cn.pinming.microservice.material.management.biz.timetask.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.management.biz.entity.MaterialWarning;
import cn.pinming.microservice.material.management.biz.enums.WarningSubTypeEnum;
import cn.pinming.microservice.material.management.biz.enums.WarningTypeEnum;
import cn.pinming.microservice.material.management.biz.mapper.MaterialWarningMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 结算单位异常预警自动处理方案
 * <AUTHOR>
 * @date 2022/6/14
 */
@Slf4j
@Component
public class WarningFourStrategy extends WarningAutoProcessStrategy implements CommandLineRunner {

    @Resource
    private MaterialWarningMapper warningMapper;

    /**
     * 4.1 检查相应data与对应合同明细的结算单位是否相同，相同则自动置为 “已处理”•。仍不相同，则不作处理
     * 4.2 检查相应data与对应合同明细的结算单位是否相同，相同则自动置为 〝已处理”，仍不相同，则不作处理
     * @param warnings
     * @return
     */
    @Override
    public void autoProcessWay(List<MaterialWarning> warnings) {
        List<String> warningIds = Lists.newArrayList();
        log.info("结算单位异常预警自动处理 start...");
        if (CollectionUtil.isEmpty(warnings)) {
            log.info("待处理的预警为空");
            log.info("结算单位异常预警自动处理 end...");
            return;
        }
        // 预警子分类分组
        Map<Byte, List<MaterialWarning>> subTypeMap = warnings.stream().filter(item -> ObjectUtil.isNotNull(item.getWarningSubType()))
                .collect(Collectors.groupingBy(MaterialWarning::getWarningSubType));
        List<MaterialWarning> materialWarningsOne = subTypeMap.get(WarningSubTypeEnum.CHARGE_UNIT_ONE.subType());
        if (CollectionUtil.isEmpty(materialWarningsOne)) {
            log.info("结算单位异常subType=1预警为空");
        } else {
            List<String> ids = materialWarningsOne.stream().map(MaterialWarning::getId).collect(Collectors.toList());
            warningIds.addAll(Optional.ofNullable(warningMapper.chargeUnitOne(ids)).orElse(Lists.newArrayList()));
        }
        List<MaterialWarning> materialWarningsTwo = subTypeMap.get(WarningSubTypeEnum.CHARGE_UNIT_TWO.subType());
        if (CollectionUtil.isEmpty(materialWarningsTwo)) {
            log.info("结算单位异常subType=2预警为空");
        } else {
            List<String> ids = materialWarningsTwo.stream().map(MaterialWarning::getId).collect(Collectors.toList());
            warningIds.addAll(Optional.ofNullable(warningMapper.chargeUnitOne(ids)).orElse(Lists.newArrayList()));
        }
        log.info("结算单位异常预警自动处理 end...");
        super.autoCloseWarning(warningIds);
    }

    @Override
    public void run(String... args) throws Exception {
        super.addChildClass(WarningTypeEnum.CHARGE_UNIT.value(), this);
    }
}
