package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.management.biz.entity.ClientLog;
import cn.pinming.microservice.material.management.biz.mapper.ClientLogMapper;
import cn.pinming.microservice.material.management.biz.service.IClientLogService;
import cn.pinming.microservice.material.management.biz.service.IMaterialWeighbridgeService;
import cn.pinming.microservice.material.management.common.util.RedisUtil;
import cn.pinming.microservice.material.management.config.websocket.HeartBeatMessage;
import cn.pinming.microservice.material.management.proxy.CompanyProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 地磅客户端记录信息 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-01 17:37:10
 */
@Service
public class ClientLogServiceImpl extends ServiceImpl<ClientLogMapper, ClientLog> implements IClientLogService {

    @Resource
    private IMaterialWeighbridgeService weighbridgeService;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private CompanyProxy companyProxy;
    @Resource
    private RedisUtil redisUtil;

    private static final TimedCache<Object, Object> TIMED_CACHE = CacheUtil.newTimedCache(60 * 1000);

    @Override
    public void saveClientStatus(HeartBeatMessage msg) {
        redisUtil.set(StrUtil.format("material:cli:{}", msg.getSn()), JSONUtil.toJsonStr(msg), 90);
    }


//    @Override
//    public void saveClientStatus(HeartBeatMessage msg) {
//        if (msg.getAction().equals(ActionType.heartBeat)) {
//            Integer companyId = msg.getCompanyId();
//            Integer projectId = msg.getProjectId();
//            //查询最近一条时间范围在1分钟的记录
//            String id = this.getBaseMapper().selectLastClientInfo(companyId, projectId);
//            if (StrUtil.isNotBlank(id)) {
//                //更新
//                this.lambdaUpdate().eq(ClientLog::getId, id).update(new ClientLog());
//            } else {
//                //新增
//                saveClientLog(ClientStatusEnum.UP, msg);
//            }
//            //更新地磅状态
//            weighbridgeService.updateBridgeStatus(msg.getCompanyId(), msg.getProjectId(), msg.getSn(), ClientStatusEnum.UP.value());
//            TIMED_CACHE.put(msg.getCompanyId() + ":" + msg.getProjectId(), msg);
//            TIMED_CACHE.schedulePrune(500);
//            TIMED_CACHE.setListener((key, val) -> {
//                HeartBeatMessage message = (HeartBeatMessage) val;
//                //新增离线数据
//                saveClientLog(ClientStatusEnum.DOWN, message);
//                //更新地磅状态
//                weighbridgeService.updateBridgeStatus(message.getCompanyId(), message.getProjectId(), message.getSn(), ClientStatusEnum.DOWN.value());
//            });
//        }
//    }
//
//    private void saveClientLog(ClientStatusEnum statusEnum, HeartBeatMessage msg) {
//        ClientLog clientLog = new ClientLog();
//        clientLog.setSessionId(msg.getCompanyId() + "-" + msg.getProjectId());
//        clientLog.setStatus(statusEnum.value());
//        clientLog.setCompanyId(msg.getCompanyId());
//        if (Objects.nonNull(msg.getProjectId())) {
//            ConstructionProjectDto project = projectServiceProxy.findProject(msg.getProjectId());
//            clientLog.setProject(project.getProjectTitle());
//        }
//        if (Objects.nonNull(msg.getCompanyId())) {
//            clientLog.setCompany(companyProxy.findSimpleCompanyName(msg.getCompanyId()));
//        }
//        clientLog.setProjectId(msg.getProjectId());
//        clientLog.setWeighSystemNo(msg.getSn());
//        clientLog.setVersion(msg.getVersion());
//        this.save(clientLog);
//    }

}
