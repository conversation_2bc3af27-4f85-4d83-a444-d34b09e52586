package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 移动收料总计表
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:31:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_mobile_receive_total")
@ApiModel(value = "MobileReceiveTotal对象", description = "移动收料总计表")
public class MobileReceiveTotal implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id")
    private String id;

    @ApiModelProperty(value = "采购单材料备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "采购单材料品牌")
    @TableField("purchase_brand")
    private String purchaseBrand;

    @ApiModelProperty(value = "收货单id")
    @TableField("receive_id")
    private String receiveId;

    @ApiModelProperty(value = "二级分类id")
    @TableField("category_id")
    private Integer categoryId;

    @ApiModelProperty(value = "材料id")
    @TableField("material_id")
    private Integer materialId;

    @ApiModelProperty(value = "到场品牌")
    @TableField("brand")
    private String brand;

    @ApiModelProperty(value = "偏差率")
    @TableField("deviation_rate")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "偏差状态 0 正常 1 负偏差 2 正偏差 ")
    @TableField("deviation_status")
    private Byte deviationStatus;

    @ApiModelProperty(value = "面单应收总件数")
    @TableField("send_number")
    private Integer sendNumber;

    @ApiModelProperty(value = "面单应收总含量")
    @TableField("send_content")
    private BigDecimal sendContent;

    @ApiModelProperty(value = "面单应收结算合计")
    @TableField("send_settlement_total")
    private BigDecimal sendSettlementTotal;

    @ApiModelProperty(value = "实际收料总件数")
    @TableField("actual_number")
    private Integer actualNumber;

    @ApiModelProperty(value = "实际收料总含量")
    @TableField("actual_content")
    private BigDecimal actualContent;

    @ApiModelProperty(value = "实际收料结算合计")
    @TableField("actual_settlement_total")
    private BigDecimal actualSettlementTotal;

    @ApiModelProperty(value = "结算单位")
    @TableField("settlement_unit")
    private String settlementUnit;

    @ApiModelProperty(value = "含量单位")
    private String unit;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
