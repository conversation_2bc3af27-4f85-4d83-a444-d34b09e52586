package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.PreTruckReport;
import cn.pinming.microservice.material.management.biz.entity.PreTruckReportDetail;
import cn.pinming.microservice.material.management.biz.enums.PreTruckStatusEnum;
import cn.pinming.microservice.material.management.biz.enums.WarningSourceEnum;
import cn.pinming.microservice.material.management.biz.enums.WarningTypeEnum;
import cn.pinming.microservice.material.management.biz.form.MaterialWarningForm;
import cn.pinming.microservice.material.management.biz.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.biz.mapper.PreTruckReportDetailMapper;
import cn.pinming.microservice.material.management.biz.mapper.PreWeighReportMapper;
import cn.pinming.microservice.material.management.biz.query.PreTruckDetailReportQuery;
import cn.pinming.microservice.material.management.biz.service.IMaterialWarningService;
import cn.pinming.microservice.material.management.biz.service.IPreTruckReportDetailService;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.NoUtil;
import cn.pinming.microservice.material.management.proxy.FileServiceProxy;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material.management.wrapper.CreateNameWrapper;
import cn.pinming.microservice.material.management.wrapper.SupplierWrapper;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.weaponx.wrapper.wrapper.MemberNameWrapper;
import cn.pinming.weaponx.wrapper.wrapper.ProjectTitleWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 送货车辆 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-11-16 15:07:43
 */
@Service
public class PreTruckReportDetailServiceImpl extends ServiceImpl<PreTruckReportDetailMapper, PreTruckReportDetail> implements IPreTruckReportDetailService {
    @Resource
    private NoUtil noUtil;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private SupplierWrapper supplierWrapper;
    @Resource
    private CreateNameWrapper memberNameWrapper;
    @Resource
    private ProjectTitleWrapper projectTitleWrapper;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private MaterialDataMapper materialDataMapper;
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private PreWeighReportMapper preWeighReportMapper;
    @Resource
    private IPreTruckReportDetailService preTruckReportDetailService;
    @Resource
    private IMaterialWarningService materialWarningService;

    private AuthUser getAuthUser() {
        return authUserHolder.getCurrentUser();
    }

    /**
     * 后续到场间隔时长默认为30，必填，可修改并校验不小于30的正整数
     */
    private static final int INTERVAL_TIME = 30;

    /**
     * 提交时，第一次计划到场时间距提交时间不能短于10分钟；
     */
    private static final int ARRIVE_INTERVAL_TIME = 10;

    @Override
    public IPage<?> pageListByQuery(PreTruckDetailReportQuery query) {
        if (query.getProjectId() == null || query.getProjectId() == 0) {
            query.setProjectId(getAuthUser().getCurrentProjectId());
        }

        Integer categoryId = query.getCategoryId();
        if (categoryId != null && categoryId != 0) {
            List<Integer> materialIdList = materialServiceProxy.getMaterialIdsByCategoryId(getAuthUser().getCurrentCompanyId(), categoryId);
            query.setMaterialIds(materialIdList);
        }

        IPage<PreTruckReportDetailDTO> page = this.getBaseMapper().selectPageDTO(query);
        List<PreTruckReportDetailDTO> records = page.getRecords();
        List<PreTruckReportDetailVO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            List<Integer> materialIds = records.parallelStream().map(PreTruckReportDetailDTO::getMaterialId).distinct().collect(Collectors.toList());
            List<MaterialDto> materialList = materialServiceProxy.listMaterialByIds(materialIds);
            Map<Integer, MaterialDto> materialDtoMap = materialList.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, e -> e));
            list = records.parallelStream().map(e -> {
                PreTruckReportDetailVO vo = new PreTruckReportDetailVO();
                BeanUtils.copyProperties(e, vo);
                MaterialDto materialDto = materialDtoMap.get(vo.getMaterialId());
                if (materialDto != null) {
                    vo.setName(StrUtil.format("{}/{}/{}", materialDto.getMaterialCategoryName(), materialDto.getMaterialName(), materialDto.getMaterialSpec()));
                }
                return vo;
            }).collect(Collectors.toList());
        }
        supplierWrapper.wrap(list, getAuthUser().getCurrentCompanyId());
        memberNameWrapper.wrap(list, getAuthUser().getCurrentCompanyId());
        IPage<PreTruckReportDetailVO> result = new Page<>();
        BeanUtils.copyProperties(page, result);
        result.setRecords(list);
        return result;
    }

    @Override
    public TruckReportInfoVO findPreTruckReportInfo(String id) {
        TruckReportInfoVO result = new TruckReportInfoVO();
        PreTruckReportInfoDTO dto = this.getBaseMapper().selectPreTruckInfoById(id);
        if(ObjectUtil.isNotEmpty(dto)){
            supplierWrapper.wrap(dto, getAuthUser().getCurrentCompanyId());
            memberNameWrapper.wrap(dto, getAuthUser().getCurrentCompanyId());
            projectTitleWrapper.wrap(dto, getAuthUser().getCurrentCompanyId());
            //生成的过磅信息
            PreTruckReportInfoVO preReportInfoVO = new PreTruckReportInfoVO();
            BeanUtils.copyProperties(dto, preReportInfoVO);
            preReportInfoVO.setName(StrUtil.format("{}/{}/{}", dto.getCategoryName(), dto.getMaterialName(), dto.getMaterialSpec()));
            result.setPreInfo(preReportInfoVO);
            //实际的过磅信息
            if (dto.getStatus().equals(PreTruckStatusEnum.COMMIT.value()) && StrUtil.isNotBlank(dto.getMaterialDataId())) {
                ActTruckReportInfoDTO actReportInfoDTO = materialDataMapper.selectWeightInfo(dto.getMaterialDataId());
                ActTruckReportInfoVO actReportInfoVO = new ActTruckReportInfoVO();
                BeanUtils.copyProperties(dto, actReportInfoVO);
                if (actReportInfoDTO != null) {
                    BeanUtils.copyProperties(actReportInfoDTO, actReportInfoVO);
                    actReportInfoVO.setTruckNo(dto.getTruckNo());
                    actReportInfoVO.setDeviationRate(NumberUtil.mul(NumberUtil.div(NumberUtil.sub(actReportInfoDTO.getActualCount(), actReportInfoDTO.getWeightSend()), actReportInfoDTO.getWeightSend(), 2),100));

                    //处理图片
                    actReportInfoVO.setEnterPics(getUUIDS(actReportInfoDTO.getEnterPic()));
                    actReportInfoVO.setLeavePics(getUUIDS(actReportInfoDTO.getLeavePic()));
                    actReportInfoVO.setUnit(dto.getUnit());
                    result.setActInfo(actReportInfoVO);
                }
            }
            BeanUtils.copyProperties(dto, result);
            result.setStatus(dto.getStatus());
        }
        return result;
    }

    private List<String> getUUIDS(String picStr) {
        List<String> list = new ArrayList<>();
        if (StrUtil.isNotBlank(picStr)) {
            List<String> uuids = StrUtil.split(picStr, ",");
            uuids = uuids.parallelStream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
            list = fileServiceProxy.fileDownloadUrlByUUIDs(uuids);
        }
        return list;
    }


    @Override
    public List<VehicleInfoVO> findVehicleList(Integer companyId, Integer projectId) {
        List<VehicleInfoDTO> list = preWeighReportMapper.selectVehicleList(companyId, projectId);
        List<VehicleInfoVO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            //排除存在2、3状态的车辆预报备id
//            List<String> preTruckList = list.parallelStream().map(VehicleInfoDTO::getTruckNo).collect(Collectors.toList());
//            List<String> preTruckIds = this.getBaseMapper().selectRemoveVehicleList(preTruckList);
//            if (CollUtil.isNotEmpty(preTruckIds)) {
//                list = list.stream().filter(e->!preTruckIds.contains(e.getTruckNo())).collect(Collectors.toList());
//            }
            supplierWrapper.wrap(list, companyId);
            projectTitleWrapper.wrap(list, companyId);
            result = list.parallelStream().map(obj -> {
                VehicleInfoVO vo = new VehicleInfoVO();
                BeanUtils.copyProperties(obj, vo);
                vo.setGoodsname(StrUtil.format("{}/{}/{}", obj.getMaterialId(), obj.getMaterialName(), obj.getMaterialSpec()));
                vo.setForwardingunit(obj.getSupplierTitle());
                vo.setReceivingunit(obj.getProjectTitle());
                return vo;
            }).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePreReportDetailList(String id, List<PreTruckReport> list) {
        List<PreTruckReportDetail> batchList = new ArrayList<>();
        list.forEach(obj -> {
            PreTruckReportDetail detail = new PreTruckReportDetail();
            String truckReportId = obj.getId();

            LocalDateTime arriveTime = obj.getArriveTime();
            this.checkArriveIntervalTime(arriveTime);
            int times = obj.getTimes();
            if (times == 1) {
                detail.setPreWeighReportId(id);
                detail.setArriveTime(arriveTime);
                detail.setNo(noUtil.getWeighNo(getAuthUser().getCurrentProjectId()));
                detail.setPreTruckReportId(obj.getId());
                batchList.add(detail);
            } else {
                int intervalTime = obj.getIntervalTime();
                if (intervalTime < INTERVAL_TIME) {
                    throw new BOException(BOExceptionEnum.INTERVAL_TIME_LESS_30);
                }
                if (StrUtil.isBlank(truckReportId)) {
                    throw new BOException("-20", "truckReportId为空");
                }
                batchList.addAll(this.generatePreTruckDetailList(id, truckReportId, times, intervalTime, arriveTime));
            }
        });
        this.saveBatch(batchList);
    }

    /**
     * 校验第一次计划到场时间距提交时间不能短于10分钟；
     *
     * @param arriveTime 计划到场时间
     */
    private void checkArriveIntervalTime(LocalDateTime arriveTime) {
        Duration between = LocalDateTimeUtil.between(LocalDateTime.now(), arriveTime);
        if (between.toMinutes() < ARRIVE_INTERVAL_TIME) {
            throw new BOException(BOExceptionEnum.INTERVAL_TIME_LESS_10);
        }
    }

    /**
     * 生成车辆预报列表
     *
     * @param id            预报备id
     * @param truckReportId 车辆预报备id
     * @param times         本车辆计划送货次数
     * @param intervalTime  后续到场间隔时长(预估)分钟
     * @param arriveTime    第一次计划到场时间
     * @return
     */
    private List<PreTruckReportDetail> generatePreTruckDetailList(String id, String truckReportId, int times, int intervalTime, LocalDateTime arriveTime) {
        List<PreTruckReportDetail> list = new ArrayList<>();
        for (int i = 0; i < times; i++) {
            PreTruckReportDetail detail = new PreTruckReportDetail();
            detail.setPreWeighReportId(id);
            detail.setPreTruckReportId(truckReportId);
            LocalDateTime offset = LocalDateTimeUtil.offset(arriveTime, (long) intervalTime * i, ChronoUnit.MINUTES);
            detail.setArriveTime(offset);
            detail.setNo(noUtil.getWeighNo(getAuthUser().getCurrentProjectId()));
            list.add(detail);
        }
        return list;
    }

    @Override
    public void cancelById(String id) {
        //判断状态 等待下发到终端  等待车辆过磅才能取消
        PreTruckReportDetail reportDetail = this.getById(id);
        Optional.ofNullable(reportDetail).orElseThrow(() -> new BOException(BOExceptionEnum.PRE_TRUCK_INFO_NOT_EXISTS));
        Byte status = reportDetail.getStatus();
        if (status.equals(PreTruckStatusEnum.WAIT_PUBLISH.value()) || status.equals(PreTruckStatusEnum.WAIT_WEIGH.value())) {
            this.removeById(id);
        } else {
            throw new BOException(BOExceptionEnum.PRE_TRUCK_INFO_STATUS_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTruckStatus(String id, Byte status) {
        String dataById = this.getBaseMapper().selectDataById(id);
        if(StrUtil.isBlank(dataById)){
            log.warn("本次id:" + id + "属于终端手动生成");
            return;
        }
        PreTruckReportDetail dto = new PreTruckReportDetail();
        dto.setId(id);
        dto.setStatus(status);
        this.updateById(dto);
        if (status.equals(PreTruckStatusEnum.OVERDUE_IN.value())) {

            List<String> list = this.getBaseMapper().selectPreTruckIds(id);
            this.lambdaUpdate().in(PreTruckReportDetail::getId, list)
                    .set(PreTruckReportDetail::getStatus, PreTruckStatusEnum.OVERDUE_IN.value()).update();

            triggerWarning(id);
        }
    }

    @Override
    public TruckReportDetailInfoForWarnDTO selectInfoForWarn(String preTruckReportId){
        TruckReportDetailInfoForWarnDTO dto = this.getBaseMapper().selectInfoForWarn(preTruckReportId);
        return dto;
    }

    private void triggerWarning(String id) {
        TruckReportDetailInfoForWarnDTO dto = preTruckReportDetailService.selectInfoForWarn(id);
        if(ObjectUtil.isNotEmpty(dto)){
            try {
                MaterialWarningForm materialWarningForm = new MaterialWarningForm();
                materialWarningForm.setSourceProjectId(dto.getProjectId());
                materialWarningForm.setWarningSourceId(id);
                materialWarningForm.setWarningSourceNo(dto.getNo());
                String time = LocalDateTimeUtil.format(dto.getArriveTime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN);
                supplierWrapper.wrap(dto,dto.getCompanyId());
                String str = StrUtil.format("{}送货车辆{}计划于{}进场",dto.getSupplierTitle(),dto.getTruckNo(),time);
                materialWarningForm.setWarningInfo(str);
                materialWarningForm.setWarningSource(WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value());
                materialWarningForm.setWarningType(WarningTypeEnum.TIMEOUT_NO_ENTER.value());
                materialWarningService.saveMaterialWarning(materialWarningForm);
            } catch (Exception e) {
                log.error("triggerWarning error", e);
            }
        }
    }

}
