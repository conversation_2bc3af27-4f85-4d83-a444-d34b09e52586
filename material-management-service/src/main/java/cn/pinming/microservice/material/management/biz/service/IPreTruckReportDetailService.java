package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.dto.TruckReportDetailInfoForWarnDTO;
import cn.pinming.microservice.material.management.biz.entity.PreTruckReport;
import cn.pinming.microservice.material.management.biz.entity.PreTruckReportDetail;
import cn.pinming.microservice.material.management.biz.query.PreTruckDetailReportQuery;
import cn.pinming.microservice.material.management.biz.vo.PreReportHistoryVO;
import cn.pinming.microservice.material.management.biz.vo.TruckReportInfoVO;
import cn.pinming.microservice.material.management.biz.vo.VehicleInfoVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 送货车辆 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-11-16 15:07:43
 */
public interface IPreTruckReportDetailService extends IService<PreTruckReportDetail> {

    /**
     * 保存车辆预报备信息
     * @param preWeighReportId  称重预报备id
     * @param list              车辆信息列表
     */
    void savePreReportDetailList(String preWeighReportId, List<PreTruckReport> list);

    /**
     * 车辆预报备列表
     * @param query  查询条件
     * @return
     */
    IPage<?> pageListByQuery(PreTruckDetailReportQuery query);

    /**
     * 车辆预报备详情
     * @param id   车辆预报备id
     * @return
     */
    TruckReportInfoVO findPreTruckReportInfo(String id);

    /**
     * 获取车辆预报备列表
     * @param companyId 企业id
     * @param projectId 项目id
     * @return
     */
    List<VehicleInfoVO> findVehicleList(Integer companyId, Integer projectId);

    void cancelById(String id);

    void updateTruckStatus(String id, Byte status);

    TruckReportDetailInfoForWarnDTO selectInfoForWarn(String preTruckReportId);

}
