package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.net.URLEncoder;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import cn.hutool.json.JSONUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.upload.OssFile;
import cn.pinming.core.upload.UploadComponent;
import cn.pinming.core.upload.UploadConfig;
import cn.pinming.core.upload.domain.enums.FileTypeEnums;
import cn.pinming.material.v2.Material;
import cn.pinming.material.v2.MaterialClientBuilder;
import cn.pinming.material.v2.model.form.PurchaseForm;
import cn.pinming.material.v2.model.form.PurchaseItemForm;
import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.*;
import cn.pinming.microservice.material.management.biz.enums.*;
import cn.pinming.microservice.material.management.biz.form.IngredientAutoAddForm;
import cn.pinming.microservice.material.management.biz.form.PurchaseOrderDetailForm;
import cn.pinming.microservice.material.management.biz.form.PurchaseOrderForm;
import cn.pinming.microservice.material.management.biz.mapper.*;
import cn.pinming.microservice.material.management.biz.query.ContractQuery;
import cn.pinming.microservice.material.management.biz.query.PurchaseOrderQuery;
import cn.pinming.microservice.material.management.biz.query.PurchaseQuery;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.AppConstant;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.*;
import cn.pinming.microservice.material.management.proxy.*;
import cn.pinming.microservice.material.management.wrapper.CreateNameWrapper;
import cn.pinming.microservice.material.management.wrapper.ProjectNameWrapper;
import cn.pinming.microservice.material.management.wrapper.SupplierWrapper;
import cn.pinming.microservice.material_unit.api.material.dto.InfoItem;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialCategoryDto;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.material_unit.api.material.dto.SelectItem;
import cn.pinming.microservice.material_unit.api.material.service.MaterialService;
import cn.pinming.model.FileInfos;
import cn.pinming.model.dto.FilePreviewDto;
import cn.pinming.v2.common.api.dto.FileDto;
import cn.pinming.v2.common.api.service.FileService;
import cn.pinming.v2.company.api.dto.CompanyDto;
import cn.pinming.v2.company.api.dto.EmployeeDto;
import cn.pinming.v2.company.api.service.CompanyService;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import cn.pinming.weaponx.wrapper.wrapper.ProjectTitleWrapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 采购单 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Slf4j
@Service
public class PurchaseOrderServiceImpl extends ServiceImpl<PurchaseOrderMapper, PurchaseOrder> implements IPurchaseOrderService {

    @Value("${temporary.path}")
    private String temporaryPath;
    @Value("${qrCodeUrl}")
    private String qrCodeUrl;
    @Value("${qrCodeuri}")
    private String qrCodeuri;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private IPurchaseOrderDetailService purchaseOrderDetailService;
    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;
    @Resource
    private IPurchaseContractDetailService purchaseContractDetailService;
    @Resource
    private NoUtil noUtil;
    @Resource
    private ProjectTitleWrapper projectTitleWrapper;
    @Resource
    private SupplierWrapper supplierWrapper;
    @Resource
    private CreateNameWrapper memberNameWrapper;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;
    @Resource
    private PurchaseOrderMapper orderMapper;
    @Resource
    private PurchaseOrderDetailMapper orderDetailMapper;
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private MaterialDataMapper materialDataMapper;
    @Resource
    private CompanyProxy companyProxy;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private IPurchaseContractService contractService;
    @Resource
    private PurchaseContractDetailMapper purchaseContractDetailMapper;
    @Resource
    private CreateNameWrapper createNameWrapper;
    @Resource
    private ProjectNameWrapper projectNameWrapper;
    @Resource
    private IMixingPlantOrderService plantOrderService;
    @Resource
    private IMixingPlantOrderDetailService plantOrderDetailService;
    @Reference
    private MaterialService materialService;
    @Reference
    private FileService fileService;
    @Reference
    private FileInfos fileInfos;
    @Resource
    private IngredientListServiceImpl ingredientListService;
    @Resource
    private MixingPlantOrderDetailMapper mixingPlantOrderDetailMapper;
    @Resource
    private PreTruckReportDetailMapper preTruckReportDetailMapper;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private IPreTruckReportService preTruckReportService;
    @Resource
    private MaterialToMapUtil materialToMapUtil;
    @Resource
    private Html2ImageUtil html2ImageUtil;
    @Resource
    private ParameterRequirementUtil parameterRequirementUtil;
    @Resource
    private ISdkConfigService sdkConfService;
    @Resource
    private ISupplierService supplierService;

    @DubboReference
    private CompanyService companyService;

    public List<CompanySimpleDTO> findSimpleCompanyByIds(List<Integer> companyList) {
        List<CompanyDto> list = companyService.findCompanyList(companyList);
        if (CollUtil.isNotEmpty(list)) {
            return BeanUtil.copyToList(list, CompanySimpleDTO.class);
        }
        return Collections.emptyList();
    }


    private AuthUser getAuthUser() {
        return authUserHolder.getCurrentUser();
    }

    @Override
    public IPage<?> pageListByQuery(PurchaseOrderQuery query) {
        //项目层数据
        if (ObjectUtil.isNotEmpty(authUserHolder.getCurrentUser())) {
            Integer projectId = getAuthUser().getCurrentProjectId();
            if (projectId != null) {
                query.setReceiverProject(getAuthUser().getCurrentProjectId());
            }
            query.setCompanyId(authUserHolder.getCurrentUser().getCurrentCompanyId());
        }
        IPage<PurchaseOrderDTO> page = this.getBaseMapper().selectPageDTO(query);
        List<PurchaseOrderDTO> records = page.getRecords();
        List<PurchaseOrderVO> voList = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            Integer companyId = Objects.nonNull(getAuthUser()) ? getAuthUser().getCurrentCompanyId() : query.getCompanyId();
            //供应商
            supplierWrapper.wrap(records, companyId);
            //创建人
            memberNameWrapper.wrap(records, companyId);
            //项目名称
            projectTitleWrapper.wrap(records, companyId);
            List<String> orderIds = records.stream().map(PurchaseOrderDTO::getId).collect(Collectors.toList());
            List<PurchaseOrderDetailDTO> list = orderDetailMapper.selectOrderDetailByIds(orderIds);
            Map<String, List<PurchaseOrderDetailDTO>> orderDetailMap = list.stream().collect(Collectors.groupingBy(PurchaseOrderDetailDTO::getOrderId));
            Map<String, Boolean> dataRelationMap = receiveDataRelation(orderIds, companyId);
            voList = records.stream().map(obj -> {
                String orderId = obj.getId();
                PurchaseOrderVO vo = new PurchaseOrderVO();
                BeanUtils.copyProperties(obj, vo);
                if (orderDetailMap.containsKey(orderId)) {
                    vo.setList(orderDetailMap.get(orderId));
                }
                // 是否关联收料记录
                vo.setReceiveDataRelation(dataRelationMap.getOrDefault(orderId, false));
                return vo;
            }).collect(Collectors.toList());
        }
        IPage<PurchaseOrderVO> result = new Page<>();
        BeanUtils.copyProperties(page, result);
        result.setRecords(voList);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String savePurchaseOrder(PurchaseOrderForm form) {
        List<PurchaseOrderDetailForm> list = form.getList();
        //校验采购单的材料是否在采购合同指定材料范围内
        this.checkMaterialRange(form.getContractId(), list);

        String position = list.stream().filter(e -> StrUtil.isNotBlank(e.getRemark())).map(PurchaseOrderDetailForm::getRemark).distinct().collect(Collectors.joining(";"));
        PurchaseOrder order = new PurchaseOrder();
        BeanUtils.copyProperties(form, order);
        order.setPosition(position);
        if (ObjectUtil.isNotEmpty(getAuthUser())) {
            order.setCreateId(authUserHolder.getCurrentUser().getId());
            order.setUpdateId(authUserHolder.getCurrentUser().getId());
        }
        //采购单号
        order.setOrderNo(noUtil.getPurchaseOrderNo(form.getReceiverProject()));
        //采购类目
        List<ContractDetailDTO> contractDetailList = purchaseContractDetailService
                .selectByContractDetailIds(list.parallelStream().map(PurchaseOrderDetailForm::getContractDetailId).collect(Collectors.toList()));
        if (CollUtil.isNotEmpty(contractDetailList)) {
            String category = contractDetailList.parallelStream().map(ContractDetailDTO::getType).distinct().collect(Collectors.joining("、"));
            order.setCategory(category);
        }
        this.save(order);

        //采购单物资明细
        List<PurchaseOrderDetail> orderDetailList = list.stream().map(obj -> {
            PurchaseOrderDetail detail = new PurchaseOrderDetail();
            BeanUtils.copyProperties(obj, detail);
            // 参数要求
            List<ParameterRequirementsDTO> parameterRequirementsModel = obj.getParameterRequirementsModel();
            IfBranchUtil.isTrue(CollectionUtil.isNotEmpty(parameterRequirementsModel)).trueHandle(() -> detail.setParameterRequirements(JSONObject.toJSONString(parameterRequirementsModel)));
            if (ObjectUtil.isNotEmpty(authUserHolder.getCurrentUser())) {
                detail.setCreateId(authUserHolder.getCurrentUser().getId());
                detail.setUpdateId(authUserHolder.getCurrentUser().getId());
            } else {
                detail.setCreateId(form.getCreateId());
                detail.setUpdateId(form.getUpdateId());
            }
            if (form.getCompanyId() != null && form.getCompanyId() != 0) {
                detail.setCompanyId(form.getCompanyId());
            }
            if (form.getProjectId() != null && form.getProjectId() != 0) {
                detail.setProjectId(form.getProjectId());
            }
            detail.setOrderId(order.getId());
            return detail;
        }).collect(Collectors.toList());
        purchaseOrderDetailService.saveBatch(orderDetailList);

        // 判断该采购单所属供应商是否关联平台拌合站，如果是则需按采购单明细生成或更新对应订单信息
        Integer plantId = this.getBaseMapper().queryPlantId(form.getSupplierId());
        if (ObjectUtil.isNotNull(plantId)) {
            // 新增拌合站订单
            String plantOrderId = UUIDUtil.randomUUIDWithoutConnector();
            savePlantOrderByPurchaseOrder(plantOrderId, order.getId(), order.getReceiverProject(), plantId);
            // 新增拌合站订单明细
            savePlantOrderDetailsByPurchaseOrder(plantOrderId, plantId, orderDetailList);
        }

        // 推送到基石订单发货平台
        pushMaterialClientPlatform(order, orderDetailList);
        return order.getId();
    }

    @SneakyThrows
    public void pushMaterialClientPlatform(PurchaseOrder order, List<PurchaseOrderDetail> orderDetailList) {
        // 判断是否推送
        Byte isPush = order.getIsPush();
        if (isPush != null && isPush == 1) {
            Integer companyId = getAuthUser().getCurrentCompanyId();
            Integer projectId = getAuthUser().getCurrentProjectId();

            // 推送到基石订单发货平台
            SdkConfigDTO sdkConfig = sdkConfService.getSdkConfig(companyId, projectId);
            Material materialClient = new MaterialClientBuilder().build(sdkConfig.getHost(), sdkConfig.getAppKey(), sdkConfig.getAppSecretKey());

            PurchaseForm purchaseForm = new PurchaseForm();
            purchaseForm.setAttributionCode(String.valueOf(projectId));
            purchaseForm.setOrderExtId(order.getId());
            purchaseForm.setSupplierExtId(String.valueOf(order.getSupplierId()));
            purchaseForm.setPosition(order.getPosition());
            ProjectVO project = projectServiceProxy.getProjectById(order.getReceiverProject());
            if (project != null) {
                purchaseForm.setProject(project.getProjectTitle());
            }
            purchaseForm.setAddress(order.getReceiverAddress());
            purchaseForm.setReceiver(order.getReceiver());
            purchaseForm.setMobile(order.getReceiverTel());
            purchaseForm.setReceiveDate(order.getReceiveTime().toLocalDate());
            purchaseForm.setRemark(order.getRemark());
            purchaseForm.setSource("品茗采购单");

            List<String> contractDetailIds = orderDetailList.stream().map(PurchaseOrderDetail::getContractDetailId).collect(Collectors.toList());
            List<SimpleContractDetailDTO> contractDetailList = purchaseContractDetailService.querySimpleContractDetails(contractDetailIds);

            Map<String, SimpleContractDetailDTO> contractDetailMap = new HashMap<>();
            if (CollUtil.isNotEmpty(contractDetailList)) {
                contractDetailMap = contractDetailList.stream().collect(Collectors.toMap(SimpleContractDetailDTO::getContractDetailId, Function.identity()));
            }

            // 处理明细
            Map<String, SimpleContractDetailDTO> finalContractDetailMap = contractDetailMap;
            List<PurchaseItemForm> list = orderDetailList.stream().map(obj -> {
                PurchaseItemForm item = new PurchaseItemForm();
                item.setExtId(obj.getId());
                item.setBrand(obj.getBrand());
                item.setAmount(obj.getCount());
                item.setDeductRatio(BigDecimal.ZERO);// 写死
                item.setScaleFactor(BigDecimal.ZERO);
                item.setPosition(obj.getRemark());
//                item.setRemark(obj.getRemark());
                if (finalContractDetailMap.containsKey(obj.getContractDetailId())) {
                    SimpleContractDetailDTO detail = finalContractDetailMap.get(obj.getContractDetailId());
                    item.setName(detail.getMaterialName());
                    item.setSpec(detail.getMaterialSpec());
                    item.setUnit(detail.getUnit());
                    item.setScaleFactor(detail.getConversionRate());
                    item.setUnitType(1);// 桩桩系统都是吨
                    item.setArgument(parameterRequirementUtil.parseParameterRequirements(detail.getCategoryId(), obj.getParameterRequirements()));
                }
                return item;
            }).collect(Collectors.toList());
            purchaseForm.setList(list);
            try {
                materialClient.purchaseOrderAdd(purchaseForm);
                lambdaUpdate().eq(PurchaseOrder::getId, order.getId()).set(PurchaseOrder::getPushStatus, 1).update(new PurchaseOrder());
            } catch (Exception e) {
                log.error("推送到基石订单发货平台失败", e);
            }

        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deletePurchaseOrder(String orderId) {
        // 删除采购单
        this.removeById(orderId);
        UpdateWrapper<PurchaseOrderDetail> detailDeleteWrapper = new UpdateWrapper<>();
        detailDeleteWrapper.lambda().eq(PurchaseOrderDetail::getOrderId, orderId);
        // 删除采购单明细
        purchaseOrderDetailService.remove(detailDeleteWrapper);
    }

    @Override
    public PurchaseOrderVO queryUpdateDetailById(String id, Integer companyId) {
        PurchaseOrderVO purchaseOrderVO = this.queryDetailById(id, companyId);
        // 采购单明细
        List<PurchaseOrderDetailDTO> list = Optional.ofNullable(purchaseOrderVO.getList()).orElse(Lists.newArrayList());
        // 拌合站订单明细
        List<MixingPlantOrderDetail> orderDetailList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> orderDetailIds = list.stream().map(PurchaseOrderDetailDTO::getOrderDetailId).collect(Collectors.toList());
            QueryWrapper<MixingPlantOrderDetail> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(MixingPlantOrderDetail::getIsDeleted, 0)
                    .in(MixingPlantOrderDetail::getPurchaseOrderDetailId, orderDetailIds);
            orderDetailList = Optional.ofNullable(plantOrderDetailService.list(wrapper)).orElse(Lists.newArrayList());
        }
        // 合同关联
        ContractQuery query = new ContractQuery();
        query.setSupplierId(purchaseOrderVO.getSupplierId());
        List<PurchaseContractVO> purchaseContractVOS = contractService.listBySupplierId(query);
        if (CollectionUtil.isNotEmpty(purchaseContractVOS)) {
            Optional<PurchaseContractVO> any = purchaseContractVOS.stream().filter(item -> item.getId().equals(purchaseOrderVO.getContractId())).findAny();
            if (any.isPresent()) {
                PurchaseContractVO purchaseContractVO = any.get();
                // 项目下拉数据
                purchaseOrderVO.setBelongProject(purchaseContractVO.getBelongProject());
                purchaseOrderVO.setBelongProjectId(purchaseContractVO.getBelongProjectId());
                List<PurchaseContractDetailVO> contractDetailVOS = purchaseContractVO.getList();
                if (CollUtil.isNotEmpty(contractDetailVOS)) {
                    List<PurchaseOrderDetailDTO> detailList = Lists.newArrayList();
                    for (PurchaseContractDetailVO item : contractDetailVOS) {
                        PurchaseOrderDetailDTO purchaseOrderDetailDTO = new PurchaseOrderDetailDTO();
                        Optional<PurchaseOrderDetailDTO> orderDetailAny = list.stream().filter(orderDetail -> orderDetail.getContractDetailId().equals(item.getContractDetailId())).findAny();
                        if (orderDetailAny.isPresent()) {
                            PurchaseOrderDetailDTO purchaseOrderDetail = orderDetailAny.get();
                            BeanUtils.copyProperties(purchaseOrderDetail, purchaseOrderDetailDTO);
                            // 本次是否下单
                            purchaseOrderDetailDTO.setIsOrder(true);
                            // 二级分类ID
                            purchaseOrderDetailDTO.setCategoryId(item.getCategoryId());
                            // 当对应生成的订单状态为“待配料”时，可修改，否则不可修改
                            Optional<MixingPlantOrderDetail> detailOptional = orderDetailList.stream().filter(orderDetail ->
                                    orderDetail.getPurchaseOrderDetailId().equals(purchaseOrderDetail.getOrderDetailId())).findAny();
                            IfBranchUtil.isTrue(detailOptional.isPresent()).trueHandle(() -> {
                                MixingPlantOrderDetail detail = detailOptional.get();
                                IfBranchUtil.isTrue(detail.getStatus() != PlantOrderDetailStatusEnum.ONE.value())
                                        .trueHandle(() -> purchaseOrderDetailDTO.setReceiveDataRelation(true));
                            });
                            // 参数要求解析
                            Integer categoryId = purchaseOrderDetailDTO.getCategoryId();
                            String parameterRequirements = purchaseOrderDetailDTO.getParameterRequirements();
                            purchaseOrderDetailDTO.setParameterRequirementsModel(JSONObject.parseArray(parameterRequirements, ParameterRequirementsDTO.class));
                            purchaseOrderDetailDTO.setParameterRequirementsDesc(parseParameterRequirements(categoryId, parameterRequirements));
                        } else {
                            BeanUtils.copyProperties(item, purchaseOrderDetailDTO);
                            // 本次是否下单
                            purchaseOrderDetailDTO.setIsOrder(false);
                            purchaseOrderDetailDTO.setReceiveDataRelation(false);
                        }
                        // 品牌下拉列表数据
                        purchaseOrderDetailDTO.setBrands(item.getBrand());
                        detailList.add(purchaseOrderDetailDTO);
                    }
                    purchaseOrderVO.setList(detailList);
                }
            }
        }
        return purchaseOrderVO;
    }

    @Override
    public String parseParameterRequirements(Integer categoryId, String parameterRequirements) {
        List<String> infoList = Lists.newArrayList();
        if (ObjectUtil.isNull(categoryId) || StringUtils.isBlank(parameterRequirements)) {
            return "";
        }
        List<InfoItem> infoItems = parameterRequirements(categoryId);
        if (CollectionUtil.isNotEmpty(infoItems)) {
            // 按paramCode转map
            Map<String, InfoItem> infoItemMap = infoItems.stream().collect(Collectors.toMap(InfoItem::getParamCode, Function.identity()));
            List<ParameterRequirementsDTO> parseArray = JSONObject.parseArray(parameterRequirements, ParameterRequirementsDTO.class);
            for (ParameterRequirementsDTO dto : parseArray) {
                String paramType = dto.getParamType();
                String paramCode = dto.getParamCode();
                String paramValue = dto.getParamValue();
                InfoItem infoItem = infoItemMap.getOrDefault(paramCode, new InfoItem());
                if (ParameterRequirementsTypeEnum.INPUT.code().equals(paramType) && dto.getKind() == null) {
                    infoList.add(infoItem.getParamName() + "：" + paramValue);
                } else if (ParameterRequirementsTypeEnum.SELECT.code().equals(paramType) && dto.getKind() == null) {
                    List<SelectItem> selectItem = Optional.ofNullable(infoItem.getSelectItem()).orElse(Lists.newArrayList());
                    Optional<SelectItem> any = selectItem.stream().filter(item -> item.getValue().equals(paramValue)).findAny();
                    infoList.add(infoItem.getParamName() + "：" + (any.isPresent() ? any.get().getText() : ""));
                } else if (ParameterRequirementsTypeEnum.INPUT.code().equals(paramType) && dto.getKind() == 1) {
                    // bom包的参数要求只有参数项，没有参数内容
                    infoList.add(dto.getParamName() + "：");
                }
            }
        }
        return infoList.stream().collect(Collectors.joining("，"));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updatePurchaseOrder(PurchaseOrderForm form) {
        List<PurchaseOrderDetailForm> list = form.getList();
        //校验采购单的材料是否在采购合同指定材料范围内
        this.checkMaterialRange(form.getContractId(), list);

        String position = list.stream().filter(e -> StrUtil.isNotBlank(e.getRemark())).map(PurchaseOrderDetailForm::getRemark).distinct().collect(Collectors.joining(";"));
        PurchaseOrder order = new PurchaseOrder();
        BeanUtils.copyProperties(form, order);
        order.setPosition(position);
        if (ObjectUtil.isNotEmpty(getAuthUser())) {
            order.setCreateId(authUserHolder.getCurrentUser().getId());
            order.setUpdateId(authUserHolder.getCurrentUser().getId());
        }
        //采购类目
        List<ContractDetailDTO> contractDetailList = purchaseContractDetailService
                .selectByContractDetailIds(list.parallelStream().map(PurchaseOrderDetailForm::getContractDetailId).collect(Collectors.toList()));
        if (CollUtil.isNotEmpty(contractDetailList)) {
            String category = contractDetailList.parallelStream().map(ContractDetailDTO::getType).distinct().collect(Collectors.joining("、"));
            order.setCategory(category);
        }
        // 修改采购单
        this.updateById(order);
        //采购单物资明细
        List<PurchaseOrderDetail> orderDetailList = list.stream().map(obj -> {
            PurchaseOrderDetail detail = new PurchaseOrderDetail();
            BeanUtils.copyProperties(obj, detail);
            // 参数要求
            List<ParameterRequirementsDTO> parameterRequirementsModel = obj.getParameterRequirementsModel();
            IfBranchUtil.isTrue(CollectionUtil.isNotEmpty(parameterRequirementsModel)).trueHandle(() -> detail.setParameterRequirements(JSONObject.toJSONString(parameterRequirementsModel)));
            if (ObjectUtil.isNotEmpty(authUserHolder.getCurrentUser())) {
                detail.setCreateId(authUserHolder.getCurrentUser().getId());
                detail.setUpdateId(authUserHolder.getCurrentUser().getId());
            } else {
                detail.setCreateId(form.getCreateId());
                detail.setUpdateId(form.getUpdateId());
            }
            if (form.getCompanyId() != null && form.getCompanyId() != 0) {
                detail.setCompanyId(form.getCompanyId());
            }
            if (form.getProjectId() != null && form.getProjectId() != 0) {
                detail.setProjectId(form.getProjectId());
            }
            detail.setOrderId(order.getId());
            return detail;
        }).collect(Collectors.toList());

        // 修改拌合站订单：校验是否存在拌合站及拌合站订单
        QueryWrapper<MixingPlantOrder> plantWrapper = new QueryWrapper<>();
        plantWrapper.lambda().eq(MixingPlantOrder::getIsDeleted, 0).eq(MixingPlantOrder::getPurchaseOrderId, order.getId());
        MixingPlantOrder mixingPlantOrder = plantOrderService.getOne(plantWrapper);
        Integer plantId = this.getBaseMapper().queryPlantId(order.getSupplierId());
        Boolean addPlantOrder = ObjectUtil.isNull(mixingPlantOrder) && ObjectUtil.isNotNull(plantId);
        // 修改采购单时，无拌合站订单数据，但当前供应商关联了拌合站(拌合站可能在中途入场的场景)，此时需要生成拌合站订单
        String plantOrderId = UUIDUtil.randomUUIDWithoutConnector();
        if (addPlantOrder) {
            // 新增拌合站订单
            savePlantOrderByPurchaseOrder(plantOrderId, order.getId(), order.getReceiverProject(), plantId);
            // 新增拌合站订单明细
            savePlantOrderDetailsByPurchaseOrder(plantOrderId, plantId, orderDetailList);
        }
        Boolean addPlantOrderDetail = ObjectUtil.isNotNull(mixingPlantOrder);
        // 采购单明细修改：添加新增的，修改存在的，减去删除的
        QueryWrapper<PurchaseOrderDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PurchaseOrderDetail::getOrderId, form.getId());
        // 已存在的
        List<PurchaseOrderDetail> purchaseOrderDetails = purchaseOrderDetailService.list(queryWrapper);
        List<String> updateDataIds = orderDetailList.stream().filter(item -> ObjectUtil.isNotNull(item.getId())).map(PurchaseOrderDetail::getId).collect(Collectors.toList());
        List<String> dataIds = purchaseOrderDetails.stream().map(PurchaseOrderDetail::getId).collect(Collectors.toList());
        // 1. 添加新增的
        List<PurchaseOrderDetail> addList = orderDetailList.stream().filter(item -> ObjectUtil.isNull(item.getId())).collect(Collectors.toList());
        IfBranchUtil.isTrue(CollectionUtil.isNotEmpty(addList)).trueHandle(() -> {
            // 新增采购单明细
            purchaseOrderDetailService.saveBatch(addList);
            if (addPlantOrderDetail) {
                // 新增拌合站订单明细
                savePlantOrderDetailsByPurchaseOrder(mixingPlantOrder.getId(), plantId, addList);
            }
        });
        // 2. 修改存在的
        List<String> updateIds = dataIds.stream().filter(dataId -> updateDataIds.contains(dataId)).collect(Collectors.toList());
        IfBranchUtil.isTrue(CollectionUtil.isNotEmpty(updateIds)).trueHandle(() -> {
            List<PurchaseOrderDetail> updateList = orderDetailList.stream().filter(detail -> updateIds.contains(detail.getId())).collect(Collectors.toList());
            purchaseOrderDetailService.updateBatchById(updateList);
        });
        // 3. 减去删除的
        List<String> deleteIds = dataIds.stream().filter(dataId -> !updateDataIds.contains(dataId)).collect(Collectors.toList());
        IfBranchUtil.isTrue(CollectionUtil.isNotEmpty(deleteIds)).trueHandle(() -> {
            // 删除采购单明细
            purchaseOrderDetailService.removeByIds(deleteIds);
            if (addPlantOrderDetail) {
                // 删除拌合站订单明细
                UpdateWrapper<MixingPlantOrderDetail> deleteWrapper = new UpdateWrapper<>();
                deleteWrapper.lambda().in(MixingPlantOrderDetail::getPurchaseOrderDetailId, deleteIds);
                plantOrderDetailService.remove(deleteWrapper);
            }
        });
    }

    private void savePlantOrderByPurchaseOrder(String plantOrderId, String purchaseOrderId, Integer receiverProject, Integer plantId) {
        MixingPlantOrder plantOrder = new MixingPlantOrder();
        plantOrder.setId(plantOrderId);
        plantOrder.setOrderNo(noUtil.getBizNo(NoUtil.PLANT_KEY_PREFIX, receiverProject));
        plantOrder.setPlantId(plantId);
        plantOrder.setPurchaseOrderId(purchaseOrderId);
        SimpleConstructionProjectDto simpleProject = projectServiceProxy.findSimpleProject(receiverProject);
        IfBranchUtil.isTrue(ObjectUtil.isNotNull(simpleProject)).trueHandle(() -> plantOrder.setReceiverProjectTitle(simpleProject.getProjectTitle()));
        plantOrderService.save(plantOrder);
    }

    private void savePlantOrderDetailsByPurchaseOrder(String plantOrderId, Integer plantId, List<PurchaseOrderDetail> orderDetailList) {
        List<MixingPlantOrderDetail> plantOrderDetailList = orderDetailList.stream().map(item -> {
            MixingPlantOrderDetail detail = new MixingPlantOrderDetail();
            detail.setId(UUIDUtil.randomUUIDWithoutConnector());
            detail.setPurchaseOrderDetailId(item.getId());
            detail.setMixingPlantOrderId(plantOrderId);
            detail.setProjectId(plantId);
            return detail;
        }).collect(Collectors.toList());
        plantOrderDetailService.saveBatch(plantOrderDetailList);

        List<String> ids = plantOrderDetailList.stream().map(MixingPlantOrderDetail::getId).collect(Collectors.toList());
        // 自动生成配料单
        List<IngredientAutoAddForm> autoAddFormList = mixingPlantOrderDetailMapper.selectAutoAddForm(ids);
        ingredientListService.autoAdd(autoAddFormList, plantId);
    }

    private void checkMaterialRange(String contractId, List<PurchaseOrderDetailForm> list) {
        List<String> contractDetailId = list.stream().map(PurchaseOrderDetailForm::getContractDetailId).distinct().collect(Collectors.toList());
        int count = purchaseContractDetailService.count(new LambdaQueryWrapper<PurchaseContractDetail>()
                .eq(PurchaseContractDetail::getContractId, contractId).in(PurchaseContractDetail::getId, contractDetailId));
        if (contractDetailId.size() > count) {
            //提示哪些物料不是本合同下的，由用户删除
            throw new BOException("-200", "此物料不在本合同采购范围内。");
        }
    }

    @Override
    public PurchaseOrderVO queryDetailById(String id, Integer companyId) {
        PurchaseOrderDTO orderDTO = this.getBaseMapper().selectPurchaseOrderById(id);
        Optional.ofNullable(orderDTO).orElseThrow(() -> new BOException(BOExceptionEnum.PURCHASE_ORDER_NOT_EXISTS));
        if (ObjectUtil.isNotEmpty(authUserHolder.getCurrentUser())) {
            supplierWrapper.wrap(orderDTO, getAuthUser().getCurrentCompanyId());
            memberNameWrapper.wrap(orderDTO, getAuthUser().getCurrentCompanyId());
//            projectTitleWrapper.wrap(orderDTO, getAuthUser().getCurrentCompanyId());
        } else {
            supplierWrapper.wrap(orderDTO, companyId);
            memberNameWrapper.wrap(orderDTO, companyId);
        }

        ProjectVO project = projectServiceProxy.getProjectById(orderDTO.getProjectId());
        PurchaseOrderVO resultVO = new PurchaseOrderVO();
        BeanUtils.copyProperties(orderDTO, resultVO);
        // 二维码
        byte[] bytes = QrCodeUtil.generatePng(resultVO.getOrderNo(), 120, 120);
        resultVO.setDetailQrcode(bytes);
        resultVO.setExportDetailQrcode(ZXingCode.getQRCode(resultVO.getOrderNo(), resultVO.getOrderName()));
        // 收货完毕按钮是否可见
        Optional.ofNullable(companyProxy.findSimpleCompanyName(orderDTO.getCompanyId())).ifPresent(resultVO::setNeedCompany);
        if (ObjectUtil.isNotEmpty(authUserHolder.getCurrentUser())) {
            resultVO.setEnable(authUserHolder.getCurrentUser().getId().equals(orderDTO.getCreateId()) && !orderDTO.getStatus().equals(PurchaseOrderStatusEnum.FINISH.value()));
        }
        resultVO.setProjectTitle(project.getProjectTitle());
        resultVO.setAddress(project.getAddress());
        resultVO.setOrderDate(LocalDateTimeUtil.format(resultVO.getOrderTime(), "yyyy-MM-dd"));
        resultVO.setReceiveDate(LocalDateTimeUtil.format(resultVO.getReceiveTime(), "yyyy-MM-dd HH:mm:dd"));
        //明细列表
        List<PurchaseOrderDetailDTO> list = purchaseOrderDetailService.listOrderDetailById(id);
        list.forEach(purchaseOrderDetailDTO -> {
            if (StrUtil.isNotEmpty(purchaseOrderDetailDTO.getMaterialId())) {
                MaterialDto materialDto = materialServiceProxy.materialById(Integer.valueOf(purchaseOrderDetailDTO.getMaterialId()));
                if (materialDto != null) {
                    purchaseOrderDetailDTO.setMaterialNameSpec(materialDto.getMaterialName() + "/" + materialDto.getMaterialSpec());
                }
            } else {
                purchaseOrderDetailDTO.setMaterialNameSpec(purchaseOrderDetailDTO.getMaterialName());
            }
            // 参数要求解析
            Integer categoryId = purchaseOrderDetailDTO.getCategoryId();
            String parameterRequirements = purchaseOrderDetailDTO.getParameterRequirements();
            purchaseOrderDetailDTO.setParameterRequirementsDesc(parseParameterRequirements(categoryId, parameterRequirements));
        });
        resultVO.setList(list);
        String positions = list.stream().filter(e -> e != null && StrUtil.isNotBlank(e.getRemark())).map(PurchaseOrderDetailDTO::getRemark).collect(Collectors.joining(";"));
        if (StrUtil.isNotBlank(positions)) {
            resultVO.setPosition(positions);
        }

        // 收料明细
        List<MaterialDetailVO> materialDetailByOrderId = getMaterialDetailByOrderId(id);
        resultVO.setMaterialDetailVOList(materialDetailByOrderId);
        if (CollUtil.isNotEmpty(materialDetailByOrderId)) {
            int receiveDataSize = materialDetailByOrderId.size();
            resultVO.setMaterialCount(receiveDataSize);
            // 是否关联收料记录
            resultVO.setReceiveDataRelation(receiveDataSize > 0);
        } else {
            resultVO.setMaterialCount(0);
        }
        // 是否推送基石平台
        PurchaseContract contractDTO = contractService.getById(orderDTO.getContractId());
        if (contractDTO != null && contractDTO.getIsPush() != null) {
            resultVO.setContractPush(contractDTO.getIsPush());
        }
        return resultVO;
    }

    @Override
    public List<MaterialDetailVO> getMaterialDetailByOrderId(String id) {
        List<MaterialDetailVO> materialDetailVOList = materialDataMapper.selectByOrderId(id);
        Optional.ofNullable(materialDetailVOList).ifPresent(list -> list.forEach(materialDetailVO -> {
            // 偏差状态
            Byte deviationStatus = null;
            if (null != materialDetailVO.getDeviationStatus()) {
                deviationStatus = Byte.parseByte(materialDetailVO.getDeviationStatus());
            }
            materialDetailVO.setDeviationStatus(DeviationStatusEnum.chooseDeviationStatus(deviationStatus));

            // 规格型号
            if (StrUtil.isNotEmpty(materialDetailVO.getMaterialId())) {
                MaterialDto materialDto = materialServiceProxy.materialById(Integer.parseInt(materialDetailVO.getMaterialId()));
                if (materialDto != null) {
                    materialDetailVO.setMaterialNameSpec(materialDto.getMaterialName() + "/" + materialDto.getMaterialSpec());
                } else {
                    materialDetailVO.setMaterialNameSpec(materialDetailVO.getMaterialName());
                }
            }

        }));
        return materialDetailVOList;
    }

    @Override
    public Boolean receiveDataRelation(String id) {
        Integer count = materialDataMapper.receiveDataRelation(id);
        return count > 0;
    }

    @Override
    public Map<String, Boolean> receiveDataRelation(List<String> orderIds, Integer companyId) {
        List<ReceiveDataRelationDTO> list = materialDataMapper.receiveDataRelationList(orderIds, companyId);
        Map<String, Boolean> result = new HashMap<>();
        if (CollUtil.isNotEmpty(list)) {
            result = list.stream().collect(Collectors.toMap(ReceiveDataRelationDTO::getOrderId, x -> x.getCount() > 0));
        }
        return result;
    }


    @Override
    public void cancelById(String id) {
        this.remove(new LambdaQueryWrapper<PurchaseOrder>().eq(PurchaseOrder::getId, id)
                .eq(PurchaseOrder::getStatus, PurchaseOrderStatusEnum.APPROVAL.value()));
    }

    @Override
    public List<String> listOrderById(Integer companyId, Integer projectId) {
        return this.getBaseMapper().selectOrderById(companyId, projectId);
    }

    /**
     * 获取采购信息
     *
     * @param orderId
     * @return
     */
    @Override
    public PurchaseSimpleVO selectOrderInfo(String orderId, Integer companyId) {
        AuthUser user = authUserHolder.getCurrentUser();
        Integer coId;
        if (ObjectUtil.isNotEmpty(user)) {
            coId = user.getCurrentCompanyId();
        } else {
            coId = companyId;
        }
        PurchaseSimpleVO vo = this.getBaseMapper().selectOrderInfo(orderId);

        if (ObjectUtil.isNotEmpty(vo)) {
//          收货项目
            SimpleConstructionProjectDto projectDto = projectServiceProxy.findSimpleProject(vo.getReceiverProject());
            if (ObjectUtil.isNotEmpty(projectDto)) {
                vo.setReceiverProjectTitle(projectDto.getProjectTitle());
            }
//          下单人
            EmployeeDto employeeDto = employeeServiceProxy.findEmployee(coId, vo.getCreateId());
            if (ObjectUtil.isNotEmpty(employeeDto)) {
                vo.setCreateName(employeeDto.getMemberName());
            }
//          供应商
            supplierWrapper.wrap(vo, coId);
//          各材料采购信息
            List<GoodsSimpleVO> goodsVOs = orderDetailMapper.selectMaterialInfo(orderId);
            if (CollUtil.isNotEmpty(goodsVOs)) {
                goodsVOs.forEach(e -> {
                    String str = StrUtil.format("{}/{}/{}", e.getCategoryName(), e.getMaterialName(), e.getMaterialSpec());
                    e.setType(str);
                });
            }
            vo.setList(goodsVOs);
            String positions = goodsVOs.stream().filter(e -> StrUtil.isNotBlank(e.getRemark())).map(GoodsSimpleVO::getRemark).collect(Collectors.joining(";"));
            if (StrUtil.isNotBlank(positions)) {
                vo.setPosition(positions);
            }
        }
        return vo;
    }

    /**
     * 采购单编号校验
     *
     * @param orderNo,user
     */
    @Override
    public void checkOut(String orderNo, AuthUser user) {
        PurchaseOrder purchaseOrder = this.lambdaQuery()
                .eq(PurchaseOrder::getCompanyId, user.getCurrentCompanyId())
                .eq(PurchaseOrder::getProjectId, user.getCurrentProjectId())
                .eq(PurchaseOrder::getOrderNo, orderNo)
                .eq(PurchaseOrder::getIsDeleted, DeleteEnum.NORMAL.value())
                .one();
        if (purchaseOrder.getStatus() == PurchaseOrderStatusEnum.FINISH.value()) {
            throw new BOException(BOExceptionEnum.PURCHASE_IS_FINISH);
        }
        if (purchaseOrder.getStatus() == PurchaseOrderStatusEnum.APPROVAL.value()) {
            throw new BOException(BOExceptionEnum.CHOOSE_CURRENT_PURCHASE_AGAIN);
        }
        if (purchaseOrder.getStatus() == PurchaseOrderStatusEnum.REVIEW.value()) {
            throw new BOException(BOExceptionEnum.CHOOSE_CURRENT_PURCHASE);
        }
        if (ObjectUtil.isEmpty(purchaseOrder)) {
            throw new BOException(BOExceptionEnum.PURCHASE_IS_NOT_EXIST);
        }
    }

    @Override
    public String getPurchaseOrderUrl(String id) {
        //采购单详情
        PurchaseOrderVO resultVO = this.queryDetailById(id, null);
        //生成二维码
        QrConfig config = new QrConfig();
        //高纠错级别
        config.setErrorCorrection(ErrorCorrectionLevel.H);
        resultVO.setQrcode(Pictures.ofBytes(QrCodeUtil.generatePng(resultVO.getOrderNo(), config), PictureType.JPEG)
                .size(120, 120).create());
        //获取url
        return preview("采购单详情", resultVO, resultVO.getOrderNo());
    }

    /**
     * 预览pdf
     *
     * @param fileName 文件名称
     * @param data
     * @param order
     * @return
     */
    private String preview(String fileName, Object data, String order) {
        String finalPath = temporaryPath + StrUtil.format("{}.docx", order + System.currentTimeMillis());
        InputStream inputStream = WordUtils.getTemplatePath(fileName);

        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        Configure config = Configure.builder().bind("list", policy).bind("materialDetailVOList", policy).useSpringEL(false).build();
        //渲染word
        WordUtils.render(inputStream, finalPath, data, config);
        File file = null;
        try (UploadComponent uploadComponent = fileServiceProxy.getDynamicUploadComponent()) {
            file = new File(finalPath);
            OssFile ossFile = new OssFile(file, "application/octet-stream", FileTypeEnums.OTHER);
            UploadConfig uploadConfig = new UploadConfig("ff8080815afee284015afee408680001");
            //上传到oss
            String uuid = uploadComponent.uploadFile(ossFile, uploadConfig);
            //生产uuid
            fileServiceProxy.confirmFile(uuid, "material-management");
            FileDto filesByUUID = fileServiceProxy.findFilesByUUID(uuid);
            FilePreviewDto filePreview = fileServiceProxy.findFilePreview(uuid);
            return filePreview.getFileUrl();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            file.deleteOnExit();
        }
        return null;
    }

    @Override
    public List<PrePurchaseVO> listPurchase(Integer supplierId) {
        AuthUser user = authUserHolder.getCurrentUser();
        List<PrePurchaseVO> vos = new ArrayList<>();

//      供应商id和项目去找所有的采购单
        List<PrePurchaseDTO> dto = orderMapper.listPurchase(supplierId, user.getCurrentProjectId(), user.getCurrentCompanyId());

        projectNameWrapper.wrap(dto);
        createNameWrapper.wrap(dto, user.getCurrentCompanyId());
        supplierWrapper.wrap(dto, user.getCurrentCompanyId());

        if (CollUtil.isNotEmpty(dto)) {
            vos = dto.stream().map(e -> {
                PrePurchaseVO vo = new PrePurchaseVO();
                BeanUtils.copyProperties(e, vo);

                if (StrUtil.isNotBlank(e.getSupplierTitle())) {
                    vo.setSupplierName(e.getSupplierTitle());
                }
                vo.setReceiverProjectName(e.getProjectTitle());

//              采购单id找采购单明细
                List<PreGoodsDTO> goodsDTOS = orderDetailMapper.listGoods(e.getId());
                if (CollUtil.isNotEmpty(goodsDTOS)) {
                    List<PreGoodsVO> list = new ArrayList<>();
                    goodsDTOS.forEach(m -> {
                        PreGoodsVO goodsVO = new PreGoodsVO();
                        BeanUtils.copyProperties(m, goodsVO);
                        goodsVO.setType(StrUtil.format("{}/{}/{}", m.getCategoryName(), m.getMaterialName(), m.getMaterialSpec()));
                        list.add(goodsVO);
                    });
                    vo.setList(list);
                }
                return vo;
            }).collect(Collectors.toList());
        }

        return vos;
    }

    @Override
    public PurchaseOrderVO finalDetailById(String id, Integer companyId) {
        PurchaseOrderDTO orderDTO = this.getBaseMapper().selectPurchaseOrderById(id);
        Optional.ofNullable(orderDTO).orElseThrow(() -> new BOException(BOExceptionEnum.PURCHASE_ORDER_NOT_EXISTS));
        if (ObjectUtil.isNotEmpty(authUserHolder.getCurrentUser())) {
            supplierWrapper.wrap(orderDTO, getAuthUser().getCurrentCompanyId());
            memberNameWrapper.wrap(orderDTO, getAuthUser().getCurrentCompanyId());
//            projectTitleWrapper.wrap(orderDTO, getAuthUser().getCurrentCompanyId());
        } else {
            supplierWrapper.wrap(orderDTO, companyId);
            memberNameWrapper.wrap(orderDTO, companyId);
        }
        ProjectVO project = projectServiceProxy.getProjectById(orderDTO.getProjectId());
        PurchaseOrderVO resultVO = new PurchaseOrderVO();
        BeanUtils.copyProperties(orderDTO, resultVO);
        resultVO.setProjectTitle(project.getProjectTitle());
        resultVO.setAddress(project.getAddress());
        resultVO.setOrderDate(LocalDateTimeUtil.format(resultVO.getOrderTime(), "yyyy-MM-dd"));
        resultVO.setReceiveDate(LocalDateTimeUtil.format(resultVO.getReceiveTime(), "yyyy-MM-dd HH:mm:dd"));

        List<PurchaseOrderDetailDTO> dtos = purchaseOrderDetailMapper.purchaseDetail(id);
        resultVO.setList(dtos);
        return resultVO;
    }


    @Override
    public void updatePurchaseOrderReceiving(String purchaseId, Integer companyId, Integer projectId) {
        if (log.isInfoEnabled()) {
            log.info("updatePurchaseOrder purchaseId:{}, companyId:{}, projectId:{}", purchaseId, companyId, projectId);
        }
        orderMapper.updateStatusById(purchaseId, companyId, projectId, PurchaseOrderStatusEnum.RECEIVING.value());
    }

    @Override
    public List<PurchaseContractDetail> selectMaterialIdInfo(String purchaseId, Integer projectId) {
        return orderMapper.selectMaterialIdInfo(purchaseId, projectId);
    }

    @Override
    public PurchaseOrder selectIdByPurchaseNo(String purchaseNo) {
        if (StrUtil.isEmpty(purchaseNo)) {
            return null;
        }
        return this.getBaseMapper().selectIdByPurchaseNo(purchaseNo);
    }

    @Override
    public List<ContractDecisionDTO> findDecisionFactor(String purchaseNo) {
        return this.getBaseMapper().findDecisionFactor(purchaseNo);
    }

    @Override
    public List<PurchaseForReviseVO> listForRevise(String contractId) {
        AuthUser user = authUserHolder.getCurrentUser();
        List<PurchaseForReviseVO> list = this.getBaseMapper().listForRevise(contractId, user.getCurrentProjectId());
        list.stream().forEach(e -> {
            EmployeeDto employee = employeeServiceProxy.findEmployee(user.getCurrentCompanyId(), e.getCreateId());
            if (ObjectUtil.isNotNull(employee)) {
                e.setCreateName(employee.getMemberName());
            }
        });
        return list;
    }

    @Override
    public PurchaseSimpleVO findByPurNo(String pruNo, Boolean scan) {
        Integer projectId = getAuthUser().getCurrentProjectId();
        String completePruNo = "";
        if (scan) {
            //扫码输入
            if (StrUtil.isBlank(pruNo)) {
                throw new BOException(BOExceptionEnum.ILLEGAL_PARAM.errorCode(), "无法识别此采购单!");
            }
            completePruNo = pruNo;
        } else {
            //手动输入
            if (StrUtil.length(pruNo) != 11) {
                throw new BOException(BOExceptionEnum.ILLEGAL_PARAM.errorCode(), "请输入采购单后11位!");
            }
            completePruNo = StrUtil.format("CGXQ-{}-{}", projectId, pruNo);
        }
        //查询采购单
        PurchaseOrder purchaseOrder = this.getBaseMapper().selectOrderByPruNo(completePruNo, projectId);
        if (ObjectUtil.isEmpty(purchaseOrder)) {
            throw new BOException(BOExceptionEnum.ILLEGAL_PARAM.errorCode(), "非本项目采购单!");
        }
        if (purchaseOrder.getStatus() == PurchaseOrderStatusEnum.FINISH.value()) {
            throw new BOException(BOExceptionEnum.PURCHASE_IS_FINISH);
        }
        if (purchaseOrder.getStatus() == PurchaseOrderStatusEnum.APPROVAL.value()) {
            throw new BOException(BOExceptionEnum.CHOOSE_CURRENT_PURCHASE_AGAIN);
        }
        if (purchaseOrder.getStatus() == PurchaseOrderStatusEnum.REVIEW.value()) {
            throw new BOException(BOExceptionEnum.CHOOSE_CURRENT_PURCHASE);
        }
        return this.selectOrderInfo(purchaseOrder.getId(), null);
    }

    @Override
    public PurchaseSimpleVO history() {
        AuthUser user = authUserHolder.getCurrentUser();
        Integer projectId = user.getCurrentProjectId();
        Integer coId = user.getCurrentCompanyId();
        String updateId = user.getId();
        PurchaseSimpleVO vo = this.getBaseMapper().getPurchaseHistory(coId, projectId, updateId);
        if (ObjectUtil.isNotEmpty(vo)) {
//          收货项目
            SimpleConstructionProjectDto projectDto = projectServiceProxy.findSimpleProject(vo.getReceiverProject());
            if (ObjectUtil.isNotEmpty(projectDto)) {
                vo.setReceiverProjectTitle(projectDto.getProjectTitle());
            }
//          下单人
            EmployeeDto employeeDto = employeeServiceProxy.findEmployee(coId, vo.getCreateId());
            if (ObjectUtil.isNotEmpty(employeeDto)) {
                vo.setCreateName(employeeDto.getMemberName());
            }
//          供应商
            supplierWrapper.wrap(vo, coId);
//          各材料采购信息-采购单
            if (StrUtil.isNotBlank(vo.getId())) {
                List<GoodsSimpleVO> goodsVOs = orderDetailMapper.selectMaterialInfo(vo.getId());
                if (CollUtil.isNotEmpty(goodsVOs)) {
                    goodsVOs.forEach(e -> {
                        String str = StrUtil.format("{}/{}/{}", e.getCategoryName(), e.getMaterialName(), e.getMaterialSpec());
                        e.setType(str);
                    });
                }
                vo.setList(goodsVOs);
                String positions = goodsVOs.stream().filter(e -> StrUtil.isNotBlank(e.getRemark())).map(GoodsSimpleVO::getRemark).collect(Collectors.joining(";"));
                if (StrUtil.isNotBlank(positions)) {
                    vo.setPosition(positions);
                }
            }
//          材料采购信息-合同
            if (StrUtil.isEmpty(vo.getId()) && StrUtil.isNotBlank(vo.getContractDetailId())) {
                List<GoodsSimpleVO> goodsSimpleVOList = purchaseContractDetailMapper.history(vo.getContractId());
                if (CollUtil.isNotEmpty(goodsSimpleVOList)) {
                    goodsSimpleVOList.stream().forEach(e -> {
                        e.setType(StrUtil.format("{}/{}/{}", e.getCategoryName(), e.getMaterialName(), e.getMaterialSpec()));
                    });
                }
                vo.setList(goodsSimpleVOList);
            }
        }
        return vo;
    }

    @Override
    public void updateReceiveStatus(String id, PurchaseOrderStatusEnum finish) {
        this.lambdaUpdate().eq(PurchaseOrder::getId, id).set(PurchaseOrder::getStatus, finish.value()).update();
    }

    @Override
    public String getPurchaseNameById(String id) {
        PurchaseOrder purchaseOrder = orderMapper.selectById(id);
        return purchaseOrder.getOrderName();
    }

    @Override
    public String getPrintPurchaseOrderUrl(String id) {
        //采购单详情
        PurchaseOrderVO resultVO = this.queryDetailById(id, null);
        //生成二维码
        QrConfig config = new QrConfig();
        //高纠错级别
        config.setErrorCorrection(ErrorCorrectionLevel.H);
        resultVO.setQrcode(Pictures.ofBytes(QrCodeUtil.generatePng(resultVO.getOrderNo(), config), PictureType.JPEG)
                .size(120, 120).create());
        //获取url
        return preview("打印采购单详情", resultVO, resultVO.getOrderNo());
    }

    @Override
    public PurchaseOrderReceiveInfoVO lastReceiveInfo() {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        PurchaseOrderReceiveInfoVO purchaseOrderReceiveInfoVO = orderMapper.lastReceiveInfo(currentUser.getId());
        return purchaseOrderReceiveInfoVO;
    }

    private LoadingCache<Integer, MaterialCategoryDto> cache = CacheBuilder.newBuilder().maximumSize(1000).expireAfterWrite(1, TimeUnit.HOURS)
            .refreshAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<Integer, MaterialCategoryDto>() {
                       @Override
                       public MaterialCategoryDto load(Integer key) throws InterruptedException {
                           MaterialCategoryDto categoryDetail = materialService.getCategoryDetail(key);
                           if (ObjectUtil.isNotNull(categoryDetail) && CollectionUtil.isNotEmpty(categoryDetail.getExtList())) {
                               List<InfoItem> extList = categoryDetail.getExtList();
                               List<InfoItem> itemList = extList.stream().filter(item -> "1".equals(item.getParamType()) || "2".equals(item.getParamType())).collect(Collectors.toList());
                               categoryDetail.setExtList(itemList);
                           }
                           return categoryDetail;
                       }
                   }
            );

    @Override
    public List<InfoItem> parameterRequirements(Integer categoryId) {
        MaterialCategoryDto categoryDetail = new MaterialCategoryDto();
        try {
            categoryDetail = cache.get(categoryId);
        } catch (Exception e) {
            log.error("parameterRequirements cache load error.", e);
        }
        if (ObjectUtil.isNotNull(categoryDetail)) {
            return categoryDetail.getExtList();
        }
        return Lists.newArrayList();
    }

    @Override
    public String qrCode(String purchaseId) throws IOException {
//        ZXingCode zXingCode = new ZXingCode();

        String code = IdUtil.fastSimpleUUID();

        String newQrCodeuri = StrUtil.format(qrCodeuri, purchaseId, code);
        URLEncoder urlEncoder = new URLEncoder();
        String encode = urlEncoder.encode(newQrCodeuri, Charset.forName("utf-8"));
        String str = StrUtil.format(qrCodeUrl, encode);

//        BufferedImage img = zXingCode.getQR_CODEBufferedImage(str, BarcodeFormat.QR_CODE, 400, 400, zXingCode.getDecodeHintType());
//        ByteArrayOutputStream out = new ByteArrayOutputStream();
//        ImageIO.write(img,"jpg", out);
//        byte[] file = out.toByteArray();
//        String uuid = this.uploadFile(file);
//
//        List<String> list = new ArrayList<>();
//        if (StrUtil.isNotBlank(uuid)) {
//            List<String> uuids = StrUtil.split(uuid, ",");
//            uuids = uuids.parallelStream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
//            list = fileServiceProxy.fileDownloadUrlByUUIDs(uuids);
//        }
        QrConfig qrConfig = new QrConfig();
        String base64 = QrCodeUtil.generateAsBase64(str, qrConfig, "jpg");

        QrcodeRedisDTO dto = new QrcodeRedisDTO();
        dto.setCode(code);
        dto.setPurchaseId(purchaseId);
        redisUtil.set(StrUtil.format(AppConstant.QRCODE_VIEW_PREFIX, code), JSON.toJSONString(dto), 72 * 3600);

        return base64;
    }

    @Override
    public void check(String code) {
        String key = StrUtil.format(AppConstant.QRCODE_VIEW_PREFIX, code);
        long expire = redisUtil.getExpire(key);
        if (expire < 0) {
            redisUtil.del(key);
            throw new BOException(BOExceptionEnum.QRCODE_EXPIRE);
        }
    }


    @Override
    public PurchasePdfVO selectPdf(PurchaseQuery query) {
        PurchasePdfVO vo = new PurchasePdfVO();

        List<PurchasePdfDetailVO> list = this.getBaseMapper().selectDetail(query.getPurchaseId());

        if (CollUtil.isNotEmpty(list)) {
            List<Integer> materialIdList = list.stream().map(PurchasePdfDetailVO::getMaterialId).distinct().collect(Collectors.toList());
            Map<Integer, MaterialDto> map = materialToMapUtil.materialDtoMap(materialIdList);
            if (CollUtil.isNotEmpty(map)) {
                list.stream().forEach(e -> {
                    MaterialDto materialDto = map.get(e.getMaterialId());
                    if (ObjectUtil.isNotNull(materialDto)) {
                        e.setName(StrUtil.format("{}/{}", materialDto.getMaterialName(), materialDto.getMaterialSpec()));
                    }
                });
            }

            vo.setCompanyId(list.get(0).getCompanyId());
            vo.setProjectId(list.get(0).getProjectId());
            vo.setUnit(list.get(0).getUnit());
            // 采购单明细列表
            vo.setMaterialList(list);

            Object redisEntity = redisUtil.get(StrUtil.format(AppConstant.QRCODE_VIEW_PREFIX, query.getCode()));
            if (ObjectUtil.isNull(redisEntity)) {
                throw new BOException(BOExceptionEnum.QRCODE_EXPIRE);
            }
            QrcodeRedisDTO redisDTO = JSONUtil.toBean(redisEntity.toString(), QrcodeRedisDTO.class);
            String preWeighReportId = redisDTO.getPreWeighReportId();

            // 报备过磅历史
            if (StrUtil.isNotBlank(preWeighReportId) && StrUtil.isNotBlank(query.getWeChatId())) {
                PreReportHistoryVO preReportHistoryVO = preTruckReportDetailMapper.reportHistory(preWeighReportId, query.getWeChatId());
                if (ObjectUtil.isNotNull(preReportHistoryVO)) {
                    MaterialDto materialDto = materialServiceProxy.materialById(preReportHistoryVO.getMaterialId());
                    if (ObjectUtil.isNotNull(materialDto)) {
                        preReportHistoryVO.setMaterialNameSpec(StrUtil.format("{}/{}", materialDto.getMaterialName(), materialDto.getMaterialSpec()));
                    }
                }
                vo.setPreReportHistoryVO(preReportHistoryVO);


                List<String> preTruckReportResult = new ArrayList<>();
                QueryWrapper<PreTruckReport> queryWrapper = new QueryWrapper<>();
                queryWrapper.select("distinct truck_no").lambda()
                        .eq(PreTruckReport::getWeChatId, query.getWeChatId())
                        .orderByDesc(PreTruckReport::getCreateTime);
                List<PreTruckReport> preTruckReportList = preTruckReportService.list(queryWrapper);
                preTruckReportResult = preTruckReportList.stream().map(PreTruckReport::getTruckNo).limit(3).collect(Collectors.toList());
                vo.setTruckNoList(preTruckReportResult);
            }

            vo.setIsInvalid(false);
            String key = StrUtil.format(AppConstant.QRCODE_VIEW_PREFIX, query.getCode());
            long expire = redisUtil.getExpire(key);

            if (expire < 0) {
                redisUtil.del(key);
                vo.setIsInvalid(true);
            }

            vo.setTime(expire);
        }

        return vo;
    }

    @Override
    public List<PurchaseOrderDetailVO> getPurchaseOrderDetails(List<String> purchaseOrderDetailIdList) {
        List<PurchaseOrderDetailVO> result = new ArrayList<>();
        if (CollUtil.isEmpty(purchaseOrderDetailIdList)) {
            return result;
        }

        result = orderMapper.getPurchaseOrderDetail(purchaseOrderDetailIdList);
        if (CollUtil.isNotEmpty(result)) {
            Integer companyId = result.get(0).getCompanyId();

            Map<Integer, String> supplierMap = new HashMap<>();
            Map<String, SimpleContractDetailDTO> contractDetailDTOMap = new HashMap<>();
            Map<Integer, String> projectMap = new HashMap<>();
            Map<String, String> createMap = new HashMap<>();
            Map<Integer, String> companyMap = new HashMap<>();
            List<Integer> supplierIdList = result.stream().map(PurchaseOrderDetailVO::getSupplierId).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(supplierIdList)) {
                List<SupplierDTO> supplierDTOS = supplierService.findListByCompanyIdAndSupplierIds(companyId, supplierIdList);
                if (CollUtil.isNotEmpty(supplierDTOS)) {
                    supplierMap = supplierDTOS.stream().collect(Collectors.toMap(SupplierDTO::getId, SupplierDTO::getName));
                }
            }
            List<String> contractDetailIdList = result.stream().map(PurchaseOrderDetailVO::getContractDetailId).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(contractDetailIdList)) {
                List<SimpleContractDetailDTO> simpleContractDetailDTOS = purchaseContractDetailService.querySimpleContractDetails(contractDetailIdList);
                if (CollUtil.isNotEmpty(simpleContractDetailDTOS)) {
                    contractDetailDTOMap = simpleContractDetailDTOS.stream().collect(Collectors.toMap(SimpleContractDetailDTO::getContractDetailId, e -> e));
                }
            }
            List<Integer> projectIdList = result.stream().map(PurchaseOrderDetailVO::getReceiverProject).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(projectIdList)) {
                List<ProjectVO> simpleProjects = projectServiceProxy.getSimpleProjects(projectIdList);
                if (CollUtil.isNotEmpty(simpleProjects)) {
                    projectMap = simpleProjects.stream().collect(Collectors.toMap(ProjectVO::getProjectId, ProjectVO::getProjectTitle));
                }
            }
            List<String> createIdList = result.stream().map(PurchaseOrderDetailVO::getCreateId).distinct().collect(Collectors.toList());
            List<EmployeeSimpleDTO> employeeSimpleDTOS = employeeServiceProxy.employeeList(companyId, createIdList);
            if (CollUtil.isNotEmpty(employeeSimpleDTOS)) {
                createMap = employeeSimpleDTOS.stream().collect(Collectors.toMap(EmployeeSimpleDTO::getMemberId, EmployeeSimpleDTO::getMemberName));
            }
            List<Integer> companyIdList = result.stream().map(PurchaseOrderDetailVO::getCompanyId).distinct().collect(Collectors.toList());
            List<CompanySimpleDTO> companySimpleDTOS = findSimpleCompanyByIds(companyIdList);
            if (CollUtil.isNotEmpty(companySimpleDTOS)) {
                companyMap = companySimpleDTOS.stream().collect(Collectors.toMap(CompanySimpleDTO::getCompanyId, CompanySimpleDTO::getCompanyName));
            }


            Map<Integer, String> finalSupplierMap = supplierMap;
            Map<String, SimpleContractDetailDTO> finalContractDetailDTOMap = contractDetailDTOMap;
            Map<Integer, String> finalProjectMap = projectMap;
            Map<String, String> finalCreateMap = createMap;
            Map<Integer, String> finalCompanyMap = companyMap;
            result.forEach(e -> {
                if (CollUtil.isNotEmpty(finalSupplierMap) && StrUtil.isNotBlank(finalSupplierMap.get(e.getSupplierId()))) {
                    e.setSupplierTitle(finalSupplierMap.get(e.getSupplierId()));
                }
                if (CollUtil.isNotEmpty(finalContractDetailDTOMap) && ObjectUtil.isNotNull(finalContractDetailDTOMap.get(e.getContractDetailId()))) {
                    SimpleContractDetailDTO dto = finalContractDetailDTOMap.get(e.getContractDetailId());
                    e.setMaterialId(String.valueOf(dto.getMaterialId()));
                    e.setCategoryId(dto.getCategoryId());
                    e.setCategoryName(dto.getCategoryName());
                    e.setMaterialName(dto.getMaterialName());
                    e.setMaterialSpec(dto.getMaterialSpec());
                    e.setMaterialNameSpec(StrUtil.format(dto.getMaterialName() + '/' + dto.getMaterialSpec()));
                    e.setConversionRate(dto.getConversionRate());
                    e.setDeviationCeiling(dto.getDeviationCeiling());
                    e.setDeviationFloor(dto.getDeviationFloor());
                }
                if (CollUtil.isNotEmpty(finalProjectMap) && StrUtil.isNotBlank(finalProjectMap.get(e.getReceiverProject()))) {
                    e.setReceiverProjectStr(finalProjectMap.get(e.getReceiverProject()));
                }
                if (CollUtil.isNotEmpty(finalCreateMap)) {
                    e.setCreateName(finalCreateMap.get(e.getCreateId()));
                }
                if (CollUtil.isNotEmpty(finalCompanyMap)) {
                    e.setCompanyName(finalCompanyMap.get(e.getCompanyId()));
                }
            });
        }
        return result;
    }

}
