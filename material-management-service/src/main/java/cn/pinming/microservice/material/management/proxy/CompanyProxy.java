package cn.pinming.microservice.material.management.proxy;

import cn.pinming.v2.company.api.dto.CompanyDto;

import java.util.List;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2022/4/13 09:32
 */
public interface CompanyProxy {

    CompanyDto findSimpleCompanyInfo(Integer companyId);

    String findSimpleCompanyName(Integer companyId);

    List<CompanyDto> findSimpleCompanyByCompanyList(List<Integer> companyList);
}
