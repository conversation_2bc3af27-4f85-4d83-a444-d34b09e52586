package cn.pinming.microservice.material.management.biz.controller;


import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.form.IngredientForm;
import cn.pinming.microservice.material.management.biz.form.NoticeConfirmForm;
import cn.pinming.microservice.material.management.biz.form.NoticeForm;
import cn.pinming.microservice.material.management.biz.mapper.IngredientListMapper;
import cn.pinming.microservice.material.management.biz.query.IngredientQuery;
import cn.pinming.microservice.material.management.biz.service.IIngredientListService;
import cn.pinming.microservice.material.management.biz.vo.IngredientListVO;
import cn.pinming.microservice.material.management.biz.vo.IngredientPageVO;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.annotation.NoticeConfirm;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 拌合站配料单 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 09:54:01
 */
@Api(tags = "配料单管理-controller",value = "zh")
@RestController
@RequestMapping("api/biz/ingredient-list")
public class IngredientListController {
    @Resource
    private IIngredientListService iIngredientListService;
    @Resource
    private IngredientListMapper ingredientListMapper;
    @Resource
    private AuthUserHolder authUserHolder;

    /**
     * 新增/编辑配料单+申请单
     * @return
     */
    @ApiOperation("新增/编辑配料单+申请单")
    @Log(title = "新增/编辑配料单+申请单", businessType = BusinessType.INSERT)
    @PostMapping("/addFirst")
    public ResponseEntity<Response> addOrUpdateFirst(@RequestBody @Validated @Valid IngredientForm ingredientForm) {
        String ingredientId = iIngredientListService.addOrUpdateFirst(ingredientForm);
        return ResponseEntity.ok(new SuccessResponse(ingredientId));
    }

    /**
     * 新增/编辑通知单
     * @return
     */
    @ApiOperation("新增/编辑通知单")
    @Log(title = "新增/编辑通知单", businessType = BusinessType.INSERT)
    @PostMapping("/addSecond")
    public ResponseEntity<Response> addOrUpdateSecond(@RequestBody @Validated @Valid NoticeForm form) {
        iIngredientListService.addOrUpdateSecond(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

    /**
     * 新增/编辑确认单
     * @return
     */
    @ApiOperation("新增/编辑确认单")
    @Log(title = "新增/编辑确认单", businessType = BusinessType.INSERT)
    @PostMapping("/addThird")
    public ResponseEntity<Response> addOrUpdateThird(@RequestBody @Validated(NoticeConfirm.class) @Valid NoticeConfirmForm form) {
        iIngredientListService.addOrUpdateThird(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

    /**
     * 分页
     */
    @ApiOperation(value = "分页",response = IngredientPageVO.class)
    @Log(title = "分页", businessType = BusinessType.QUERY)
    @PostMapping("/page")
    public ResponseEntity<Response> page(@RequestBody IngredientQuery query) {
        IPage<IngredientPageVO> page = iIngredientListService.selectPage(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    /**
     * 各状态统计
     * @return
     */
    @ApiOperation(value = "各状态统计")
    @Log(title = "各状态统计", businessType = BusinessType.QUERY)
    @GetMapping("/statistics")
    public ResponseEntity<Response> statistics() {
        Map<Byte,Integer> map = iIngredientListService.statistics();
        return ResponseEntity.ok(new SuccessResponse(map));
    }

    /**
     * 详情
     * @return
     */
    @ApiOperation(value = "详情",response = IngredientListVO.class)
    @Log(title = "详情", businessType = BusinessType.QUERY)
    @GetMapping("/detail/{id}")
    public ResponseEntity<Response> detail(@PathVariable("id") String id) {
        IngredientListVO vo = iIngredientListService.detail(id);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    /**
     * 申请单编号校重
     * @return
     */
    @ApiOperation("申请单编号校重")
    @Log(title = "申请单编号校重", businessType = BusinessType.QUERY)
    @GetMapping("/check/{no}")
    public ResponseEntity<Response> check(@PathVariable("no") String no) {
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
        Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();
        Boolean flag = false;

        String applyNo = ingredientListMapper.applyNoCheck(companyId,projectId,no);
        if (StrUtil.isNotBlank(applyNo)) {
            flag = true;
        }
        return ResponseEntity.ok(new SuccessResponse(flag));
    }

    /**
     * 归档
     * @return
     */
    @ApiOperation("归档")
    @Log(title = "归档", businessType = BusinessType.UPDATE)
    @GetMapping("/file/{id}")
    public ResponseEntity<Response> file(@PathVariable("id") String id) {
        iIngredientListService.file(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    /**
     * "参数要求"快捷获取值
     * @return
     */
    @ApiOperation("\"参数要求\"快捷获取值")
    @Log(title = "\"参数要求\"快捷获取值", businessType = BusinessType.QUERY)
    @GetMapping("/requirement")
    public ResponseEntity<Response> requirement() {
        Map<String,List<String>> map =iIngredientListService.requirement();
        return ResponseEntity.ok(new SuccessResponse(map));
    }
}
