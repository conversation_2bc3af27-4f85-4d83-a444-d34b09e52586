package cn.pinming.microservice.material.management.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.MaterialMatchingScore;
import cn.pinming.microservice.material.management.biz.dto.OCRConvertDTO;
import cn.pinming.microservice.material.management.biz.dto.SingelOcrMaterialDTO;
import cn.pinming.microservice.material.management.biz.entity.ProjectConfig;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContract;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContractDetail;
import cn.pinming.microservice.material.management.biz.entity.Supplier;
import cn.pinming.microservice.material.management.biz.enums.OcrMappingEnum;
import cn.pinming.microservice.material.management.biz.enums.ProjectConfigEnum;
import cn.pinming.microservice.material.management.biz.form.MobileMaterialBatchForm;
import cn.pinming.microservice.material.management.biz.form.WordsMatchForm;
import cn.pinming.microservice.material.management.biz.mapper.PurchaseContractDetailMapper;
import cn.pinming.microservice.material.management.biz.mapper.PurchaseContractMapper;
import cn.pinming.microservice.material.management.biz.service.IProjectConfigService;
import cn.pinming.microservice.material.management.biz.service.IPurchaseContractDetailService;
import cn.pinming.microservice.material.management.biz.service.ISupplierService;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.material_unit.api.material.service.MaterialService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OCRConvertUtil {
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private ISupplierService supplierService;
    @Resource
    private IProjectConfigService projectConfigService;
    @Resource
    private PurchaseContractMapper purchaseContractMapper;
    @Resource
    private IPurchaseContractDetailService purchaseContractDetailService;
    @Resource
    private PurchaseContractDetailMapper purchaseContractDetailMapper;
    @DubboReference
    private MaterialService materialService;
    @Value("${imatchocr.url}")
    private String imatchocrUlr;

    public MobileMaterialBatchForm convert(String json, boolean isBatch) throws IOException {
        log.info("ocr convert json:{}, isBatch: {}", json, isBatch);
        MobileMaterialBatchForm form = new MobileMaterialBatchForm();
        if (StrUtil.isBlank(json)) {
            return form;
        }
        // json 转换
        OCRConvertDTO ocrConvertDTO = JSONObject.parseObject(json, OCRConvertDTO.class);
        if (StrUtil.isBlank(ocrConvertDTO.getId()) || CollUtil.isEmpty(ocrConvertDTO.getResult())) {
            return form;
        }
        Map<String, String> resultMap = convertResultMap(ocrConvertDTO);
        AuthUser user = authUserHolder.getCurrentUser();
        if (isBatch) {
            // 批量OCR转换
            validOcrTemplate(ocrConvertDTO, user);
            batchOcrConvert(ocrConvertDTO, form, resultMap, user);
        } else {
            // 单个OCR转换
            validOcrTemplate(ocrConvertDTO, user);
            singleOcrConvert(ocrConvertDTO, form, resultMap, user);
        }
        return form;
    }

    private void batchOcrConvert(OCRConvertDTO ocrConvertDTO, MobileMaterialBatchForm form, Map<String, String> resultMap, AuthUser user) {
        // 第三方id判断
        if (StrUtil.isNotBlank(ocrConvertDTO.getOther_id())) {
            PurchaseContract one = purchaseContractMapper.ocrCheck(user.getCurrentCompanyId(), user.getCurrentProjectId(), ocrConvertDTO.getOther_id());
            if (ObjectUtil.isNull(one)) {
                throw new BOException(BOExceptionEnum.DOCUMENT_ERROR);
            }
        }
        if (resultMap.containsKey(OcrMappingEnum.WEIGHT_SEND.getMapping())) {
            form.setWeightSend(new BigDecimal(resultMap.get(OcrMappingEnum.WEIGHT_SEND.getMapping())));
        }
        if (resultMap.containsKey(OcrMappingEnum.POSITION.getMapping())) {
            form.setPosition(resultMap.get(OcrMappingEnum.POSITION.getMapping()));
        }
    }

    public MobileMaterialBatchForm  singleOcrConvert(OCRConvertDTO ocrConvertDTO, MobileMaterialBatchForm form, Map<String, String> resultMap, AuthUser user) {
        // 第三方id判断
        Supplier supplier = null;
        PurchaseContract purchaseContract;
        String ocrMaterialName = resultMap.get(OcrMappingEnum.GOODS.getMapping());
        String ocrMaterialSpec = resultMap.get(OcrMappingEnum.SPEC.getMapping());

        // 材料及供应商
        if (StrUtil.isNotBlank(ocrMaterialName) || StrUtil.isNotBlank(ocrMaterialSpec)) {
            // 材料有识别结果
            ocrMaterialName = StrUtil.isNotBlank(ocrMaterialName) ? ocrMaterialName : "";
            ocrMaterialSpec = StrUtil.isNotBlank(ocrMaterialSpec) ? ocrMaterialSpec : "";
            decideMaterial(form, ocrMaterialName, ocrMaterialSpec);

            if (StrUtil.isNotBlank(ocrConvertDTO.getOther_id())) {
                // 有other_id
                purchaseContract = purchaseContractMapper.ocrCheck(user.getCurrentCompanyId(), user.getCurrentProjectId(), ocrConvertDTO.getOther_id());
                if (ObjectUtil.isNull(purchaseContract) || ObjectUtil.isNull(purchaseContract.getSupplierId())) {
                    throw new BOException(BOExceptionEnum.DOCUMENT_ERROR);
                }

                List<PurchaseContractDetail> list = purchaseContractDetailService.lambdaQuery()
                        .eq(PurchaseContractDetail::getContractId, purchaseContract.getId())
                        .list();
                // 数据库过滤
                List<PurchaseContractDetail> purchaseContractDetailList = purchaseContractDetailMapper.ocrFilter(purchaseContract.getId(), ocrMaterialName, ocrMaterialSpec);
                List<String> contractMaterial = new ArrayList<>();
                if (CollUtil.isNotEmpty(purchaseContractDetailList)) {
                    contractMaterial = purchaseContractDetailList.stream().map(e -> e.getMaterialName() + e.getMaterialSpec()).collect(Collectors.toList());
                } else {
                    contractMaterial = list.stream().map(e -> e.getMaterialName() + e.getMaterialSpec()).collect(Collectors.toList());
                }

                // 语义相似度过滤
                try {
                    List<MaterialMatchingScore> materialMatchingScores = materialMatching(ocrMaterialName + ocrMaterialSpec, contractMaterial);
                    log.info(materialMatchingScores.toString());
                    if (CollUtil.isNotEmpty(materialMatchingScores)) {
                        List<String> material = materialMatchingScores.stream().map(MaterialMatchingScore::getMaterialName).distinct().collect(Collectors.toList());
                        List<SingelOcrMaterialDTO> collect = list.stream().filter(e -> material.contains(e.getMaterialName() + e.getMaterialSpec())).map(e -> {
                            SingelOcrMaterialDTO singelOcrMaterialDTO = new SingelOcrMaterialDTO();
                            BeanUtils.copyProperties(e, singelOcrMaterialDTO);
                            singelOcrMaterialDTO.setTargetUnitName(e.getUnit());
                            singelOcrMaterialDTO.setSourceUnitName(e.getTransformUnit());
                            singelOcrMaterialDTO.setContractDetailId(e.getId());
                            return singelOcrMaterialDTO;
                        }).collect(Collectors.toList());
                        form.setMaterialChooseList(collect);
                    }
                } catch (Exception ignored) {}

                // 供应商
                supplier = supplierService.getById(purchaseContract.getSupplierId());
            } else {
                // 无other_id
                ProjectConfig one = projectConfigService.lambdaQuery()
                        .eq(ProjectConfig::getCompanyId, user.getCurrentCompanyId())
                        .eq(ProjectConfig::getProjectId, user.getCurrentProjectId())
                        .eq(ProjectConfig::getType, ProjectConfigEnum.FOUR.value())
                        .eq(ProjectConfig::getOcrModuleId, ocrConvertDTO.getId())
                        .isNotNull(ProjectConfig::getOcrMaterialType)
                        .one();
                if (ObjectUtil.isNotNull(one)) {
                    // 项目设置品种列表
                    List<String> split = StrUtil.split(one.getOcrMaterialType(), ",");
                    List<String> materialNameConfigs = new ArrayList<>();
                    split.forEach(e -> {
                        String s = StrUtil.split(e, "/").get(2);
                        materialNameConfigs.add(s);
                    });
                    // 根据品种名称找规格列表
                    List<MaterialDto> materialDtos = materialService.listMaterialByMaterials(user.getCurrentCompanyId(), materialNameConfigs);
                    if (CollUtil.isNotEmpty(materialDtos)) {
                        List<String> collect = materialDtos.stream().map(e -> e.getMaterialName() + e.getMaterialSpec()).distinct().collect(Collectors.toList());
                        try {
                            List<MaterialMatchingScore> materialMatchingScores = materialMatching(ocrMaterialName + ocrMaterialSpec, collect);
                            if (CollUtil.isNotEmpty(materialMatchingScores)) {
                                log.info(materialMatchingScores.toString());
                                List<String> material = materialMatchingScores.stream().map(MaterialMatchingScore::getMaterialName).distinct().collect(Collectors.toList());
                                List<SingelOcrMaterialDTO> result = materialDtos.stream().filter(e -> material.contains(e.getMaterialName() + e.getMaterialSpec())).map(e -> {
                                    SingelOcrMaterialDTO singelOcrMaterialDTO = new SingelOcrMaterialDTO();
                                    BeanUtils.copyProperties(e, singelOcrMaterialDTO);
                                    return singelOcrMaterialDTO;
                                }).collect(Collectors.toList());
                                form.setMaterialChooseList(result);
                            }
                        } catch (Exception ignored) {}
                    }
                }

                // 供应商
                if (!resultMap.containsKey(OcrMappingEnum.SUPPLIER_NAME.getMapping())) {
                    throw new BOException(BOExceptionEnum.SUPPLIER_NONE);
                }
                supplier = supplierService.lambdaQuery()
                        .eq(Supplier::getName, resultMap.get(OcrMappingEnum.SUPPLIER_NAME.getMapping()))
                        .eq(Supplier::getCompanyId, user.getCurrentCompanyId())
                        .eq(Supplier::getProjectId, user.getCurrentProjectId())
                        .one();
            }
        }

        // 发货量/计划使用部位
        if (resultMap.containsKey(OcrMappingEnum.WEIGHT_SEND.getMapping())) {
            form.setWeightSend(new BigDecimal(resultMap.get(OcrMappingEnum.WEIGHT_SEND.getMapping()).replaceAll(" ","")));
        }
        if (resultMap.containsKey(OcrMappingEnum.POSITION.getMapping())) {
            form.setPosition(resultMap.get(OcrMappingEnum.POSITION.getMapping()));
        }

        // 供应商
        if (supplier != null) {
            form.setSupplierId(supplier.getId());
            form.setSupplierName(supplier.getName());
        }else if (resultMap.containsKey(OcrMappingEnum.SUPPLIER_NAME.getMapping())) {
            form.setSupplierName(resultMap.get(OcrMappingEnum.SUPPLIER_NAME.getMapping()));
        }

        return form;
    }

    private void decideMaterial(MobileMaterialBatchForm form, String ocrMaterialName, String ocrMaterialSpec) {
        SingelOcrMaterialDTO singelOcrMaterialDTO = new SingelOcrMaterialDTO();
        singelOcrMaterialDTO.setMaterialName(ocrMaterialName);
        singelOcrMaterialDTO.setMaterialSpec(ocrMaterialSpec);
        form.setMaterialChooseList(Collections.singletonList(singelOcrMaterialDTO));
    }

    private void validOcrTemplate(OCRConvertDTO ocrConvertDTO, AuthUser user) {
        // 判断项目基石单据模板
        ProjectConfig projectConfig = projectConfigService.lambdaQuery()
                .eq(ProjectConfig::getCompanyId, user.getCurrentCompanyId())
                .eq(ProjectConfig::getProjectId, user.getCurrentProjectId())
                .eq(ProjectConfig::getOcrModuleId, ocrConvertDTO.getId())
                .eq(ProjectConfig::getType, 4)
                .one();
        if (ObjectUtil.isNull(projectConfig)) {
            throw new BOException(BOExceptionEnum.MODULE_ID_NONE);
        }
        if (projectConfig.getOcrModuleType() == (byte) 2) {
            // 暂不支持一车多料
            throw new BOException(BOExceptionEnum.MATERIALS_REFUSED);
        }
    }

    private static Map<String, String> convertResultMap(OCRConvertDTO ocrConvertDTO) {
        Map<String, String> resultMap = new HashMap<>();
        for (Map<String, String[]> result : ocrConvertDTO.getResult().values()) {
            result.forEach((m, n) -> {
                if (n != null && n.length > 0) {
                    resultMap.put(m, n[0]);
                }
            });
        }
        return resultMap;
    }

    /**
     * 语义相似度匹配
     *
     * @param material,materialList
     * @return
     */
    private List<MaterialMatchingScore> materialMatching(String material, List<String> materialList) {
        WordsMatchForm form = new WordsMatchForm();
        form.setWord(material);
        form.setWords(materialList);
        try {
            String res = HttpUtil.post(imatchocrUlr, JSON.toJSONString(form));
            return JSONObject.parseArray(res,MaterialMatchingScore.class);
        } catch (Exception e) {
            throw new BOException(BOExceptionEnum.WORDS_MATCH_ERROR);
        }
    }
}
