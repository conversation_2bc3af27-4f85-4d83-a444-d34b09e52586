package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.StatisticsDataDTO;
import cn.pinming.microservice.material.management.biz.dto.WeighInfoDTO;
import cn.pinming.microservice.material.management.biz.dto.WeighInfosDTO;
import cn.pinming.microservice.material.management.biz.dto.WeighTruckChangeDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialSendReceive;
import cn.pinming.microservice.material.management.biz.query.DeviationInfoQuery;
import cn.pinming.microservice.material.management.biz.query.SummaryDeliveryQuery;
import cn.pinming.microservice.material.management.biz.query.WeighInfoQuery;
import cn.pinming.microservice.material.management.biz.vo.*;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 收货/发货单 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface MaterialSendReceiveMapper extends BaseMapper<MaterialSendReceive> {

    /**
     * 过磅情况
     *
     * @param query
     * @return
     */
    WeighInfoDTO selectWeightInfo(WeighInfoQuery query);

    /**
     * 按天显示过磅情况
     *
     * @param query
     * @return
     */
    List<WeighTruckChangeDTO> selectWeightInfoByDay(WeighInfoQuery query);

    /**
     * 过磅情况-查看更多
     *
     * @param query
     * @return
     */
    List<WeighInfosDTO> selectWeightInfos(WeighInfoQuery query);

    @InterceptorIgnore(tenantLine = "true")
    SummaryAnalysisVO querySummary(@Param("query") SummaryDeliveryQuery query);

    StatisticsDataDTO selectStatistics(@Param("query") DeviationInfoQuery query);

    IPage<WeighDeviationDetailVO> selectDeviationPage(@Param("query") DeviationInfoQuery query);



}
