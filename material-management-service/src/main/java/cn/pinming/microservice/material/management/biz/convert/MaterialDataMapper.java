package cn.pinming.microservice.material.management.biz.convert;

import cn.pinming.microservice.material.management.biz.dto.MaterialDataDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialData;

import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 *
 * <AUTHOR>
 * @version 2021/9/9 4:43 下午
 */
@Mapper
public interface MaterialDataMapper {

    MaterialDataMapper INSTANCE = Mappers.getMapper(MaterialDataMapper.class);

    @Mappings({
        @Mapping(source = "dto.actualCount",target = "actualCount"),
        @Mapping(source = "dto.weightActual",target = "weightActual"),
        @Mapping(source = "dto.weightGross",target = "weightGross"),
        @Mapping(source = "dto.weightDeduct",target = "weightDeduct"),
        @Mapping(source = "dto.weightNet",target = "weightNet"),
        @Mapping(source = "dto.weightTare",target = "weightTare"),
        @Mapping(source = "dto.weightUnit",target = "weightUnit")
    })
    MaterialData fromDTO(MaterialDataDTO dto);

    List<MaterialData> fromDTOS(List<MaterialDataDTO> list);
}
