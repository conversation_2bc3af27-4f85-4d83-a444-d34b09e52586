package cn.pinming.microservice.material.management.common.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.management.biz.dto.ParameterRequirementsDTO;
import cn.pinming.microservice.material.management.biz.enums.ParameterRequirementsTypeEnum;
import cn.pinming.microservice.material_unit.api.material.dto.InfoItem;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialCategoryDto;
import cn.pinming.microservice.material_unit.api.material.dto.SelectItem;
import cn.pinming.microservice.material_unit.api.material.service.MaterialService;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 参数要求解析
 */
@Component
@Slf4j
public class ParameterRequirementUtil {
    @DubboReference
    private MaterialService materialService;

    public String parseParameterRequirements(Integer categoryId, String parameterRequirements) {
        List<String> infoList = Lists.newArrayList();
        if (ObjectUtil.isNull(categoryId) || StringUtils.isBlank(parameterRequirements)) {
            return "";
        }
        List<InfoItem> infoItems = parameterRequirements(categoryId);
        if (CollectionUtil.isNotEmpty(infoItems)) {
            // 按paramCode转map
            Map<String, InfoItem> infoItemMap = infoItems.stream().collect(Collectors.toMap(InfoItem::getParamCode, Function.identity()));
            List<ParameterRequirementsDTO> parseArray = JSONObject.parseArray(parameterRequirements, ParameterRequirementsDTO.class);
            for (ParameterRequirementsDTO dto : parseArray) {
                String paramType = dto.getParamType();
                String paramCode = dto.getParamCode();
                String paramValue = dto.getParamValue();
                InfoItem infoItem = infoItemMap.getOrDefault(paramCode, new InfoItem());
                if (ParameterRequirementsTypeEnum.INPUT.code().equals(paramType) && dto.getKind() == null) {
                    infoList.add(infoItem.getParamName() + "：" + paramValue);
                } else if (ParameterRequirementsTypeEnum.SELECT.code().equals(paramType) && dto.getKind() == null) {
                    List<SelectItem> selectItem = Optional.ofNullable(infoItem.getSelectItem()).orElse(Lists.newArrayList());
                    Optional<SelectItem> any = selectItem.stream().filter(item -> item.getValue().equals(paramValue)).findAny();
                    infoList.add(infoItem.getParamName() + "：" + (any.isPresent() ? any.get().getText() : ""));
                } else if (ParameterRequirementsTypeEnum.INPUT.code().equals(paramType) && dto.getKind() == 1) {
                    // bom包的参数要求只有参数项，没有参数内容
                    infoList.add(dto.getParamName() + "：");
                }
            }
        }
        return infoList.stream().collect(Collectors.joining("，"));
    }

    public List<InfoItem> parameterRequirements(Integer categoryId) {
        try {
            return unitCache.get(categoryId);
        } catch (Exception e) {
            log.error("parameterRequirements cache load error.", e);
        }
        return null;
    }

    /**
     * 缓存 材料字典-质量及生产工艺参数
     */
    public LoadingCache<Integer, List<InfoItem>> unitCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .refreshAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<Integer, List<InfoItem>>() {
                public List<InfoItem> load(Integer categoryId) throws Exception {
                    MaterialCategoryDto categoryDetail = materialService.getCategoryDetail(categoryId);
                    if (ObjectUtil.isNotNull(categoryDetail)) {
                        return categoryDetail.getExtList();
                    }

                    return null;
                }
            });

}
