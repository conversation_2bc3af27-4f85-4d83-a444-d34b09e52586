package cn.pinming.microservice.material.management.biz.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.management.biz.entity.PlantProjectConfig;
import cn.pinming.microservice.material.management.biz.entity.StatisticsPlantConfig;
import cn.pinming.microservice.material.management.biz.form.StatisticsPlantConfigForm;
import cn.pinming.microservice.material.management.biz.form.StatisticsPlantProjectConfigForm;
import cn.pinming.microservice.material.management.biz.service.IPlantProjectConfigService;
import cn.pinming.microservice.material.management.biz.service.IStatisticsPlantConfigService;
import cn.pinming.microservice.material.management.biz.vo.ProjectVO;
import cn.pinming.microservice.material.management.biz.vo.StatisticsPlantConfigVO;
import cn.pinming.microservice.material.management.biz.vo.StatisticsPlantProjectVO;
import cn.pinming.microservice.material.management.common.SuccessResponse;
import cn.pinming.microservice.material.management.common.util.IfBranchUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <p>
 * 统计配置表-拌合站 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-21 10:09:58
 */
@Api(tags = "拌合站-大宗材统计配置")
@RestController
@RequestMapping("/api/statistics/plant/config")
public class StatisticsPlantConfigController {

    @Resource
    private IStatisticsPlantConfigService plantConfigService;

    @Resource
    private IPlantProjectConfigService plantProjectConfigService;


    @ApiOperation(value = "查询产能品类统计设置-二级分类", response = StatisticsPlantConfigVO.class)
    @GetMapping("/material")
    public ResponseEntity<SuccessResponse> queryPlantMaterialConfig() {
        StatisticsPlantConfigVO result = new StatisticsPlantConfigVO();

        StatisticsPlantConfig one = plantConfigService.queryOneConfig();

        IfBranchUtil.isTrue(ObjectUtil.isNotNull(one) && StringUtils.isNotBlank(one.getCategoryId()))
                .trueHandle(() -> result.setCategoryIds(Arrays.stream(one.getCategoryId().split(","))
                        .map(Integer::parseInt).collect(Collectors.toList())));
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "编辑产能品类统计设置-二级分类")
    @PostMapping("/material")
    public ResponseEntity<SuccessResponse> updatePlantMaterialConfig(@RequestBody StatisticsPlantConfigForm configForm) {

        String categoryIds = configForm.getCategoryIds();

        StatisticsPlantConfig one = plantConfigService.queryOneConfig();

        if (ObjectUtil.isNotNull(one)) {
            // 删除
            plantConfigService.removeById(one.getId());
        }
        // 新增
        StatisticsPlantConfig entity = new StatisticsPlantConfig();
        entity.setCategoryId(categoryIds);
        plantConfigService.save(entity);
        return ResponseEntity.ok(new SuccessResponse(true));
    }



    @ApiOperation(value = "查询场站统计范围设置-项目", response = ProjectVO.class)
    @GetMapping("/project")
    public ResponseEntity<SuccessResponse> queryPlantProjectConfig() {
        List<ProjectVO> projectVOS = plantProjectConfigService.queryPlantProjectConfig();
        return ResponseEntity.ok(new SuccessResponse(projectVOS));
    }

    @ApiOperation(value = "编辑场站统计范围设置-项目")
    @PostMapping("/project")
    public ResponseEntity<SuccessResponse> updatePlantProjectConfig(@RequestBody StatisticsPlantProjectConfigForm configForm) {

        PlantProjectConfig one = plantProjectConfigService.queryOneConfig();

        String projectIds = configForm.getProjectIds();
        String departmentIds = configForm.getDepartmentIds();

        if (ObjectUtil.isNull(one)) {
            PlantProjectConfig entity = new PlantProjectConfig();
            entity.setBelongProjectId(projectIds);
            entity.setBelongDepartmentId(departmentIds);
            plantProjectConfigService.save(entity);
        } else {
            PlantProjectConfig entity = new PlantProjectConfig();
            entity.setId(one.getId());
            entity.setBelongProjectId(projectIds);
            entity.setBelongDepartmentId(departmentIds);
            plantProjectConfigService.updateById(entity);
        }

        return ResponseEntity.ok(new SuccessResponse(true));
    }


}
