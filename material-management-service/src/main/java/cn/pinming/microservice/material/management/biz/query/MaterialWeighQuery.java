package cn.pinming.microservice.material.management.biz.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 地磅收货列表VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialWeighQuery extends BaseQuery {
    @ApiModelProperty(value = "data明细id")
    private String id;

    @ApiModelProperty(value = "收货单id")
    private String receiveId;

    @ApiModelProperty(value = "收货单号")
    private String receiveNo;

    @ApiModelProperty(value = "收货项目id")
    private Integer projectId;

    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;

    @ApiModelProperty(value = "车牌号")
    private String truckNo;

    @ApiModelProperty(value = "排序方式 1 降序，2 升序")
    private Byte sort;

    @ApiModelProperty(value = "收货方式 1 临时收料，2 报备收料 3 无归属收料 0 无效收料")
    private Byte receiveMode;

    @ApiModelProperty(value = "记录状态 1 完整记录 2 不完整记录")
    private Byte recordStatus;

    @ApiModelProperty(value = "物资名称、物料名称")
    private String materialName;

    @ApiModelProperty(value = "偏差状态 0 正常 1 负偏差 2 正偏差 ")
    private Byte deviationStatus;

    @ApiModelProperty(value = "毛皮重完整性 1，完整 2，不完整")
    private Byte isWeightIntegrality;

    @ApiModelProperty(value = "是否使用合同转换系数 1，是 2，否")
    private Byte isContractRateUsed;

    @ApiModelProperty(value = "是否使用合同结算单位 1，是 2，否")
    private Byte isContractUnitUsed;

    @ApiModelProperty(value = "材料id存在采购单中的状态: 1 存在 2 不存在")
    private Byte materialExist;

    @ApiModelProperty(value = "是否使用采购单: 1 是 2 否")
    private Byte isUsedPurchaseOrder;

    @ApiModelProperty(value = "是否修订: 1.有修订 2.未修订")
    private Byte isRevise;

    @ApiModelProperty(value = "是否对账: 1.已对账 2.对账中")
    private Byte isVerify;

    @ApiModelProperty(value = "收料单号搜索模式  1 精准搜索")
    private Byte searchMethod;

    @ApiModelProperty(value = "是否为手工补单 1 否 2 是")
    private Byte isAddition;

    @ApiModelProperty(value = "物料ID")
    private Integer materialId;

}
