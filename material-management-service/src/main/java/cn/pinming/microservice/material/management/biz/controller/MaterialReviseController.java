package cn.pinming.microservice.material.management.biz.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContractDetail;
import cn.pinming.microservice.material.management.biz.form.MaterialReviseForm;
import cn.pinming.microservice.material.management.biz.query.ReviseInfoQuery;
import cn.pinming.microservice.material.management.biz.service.IMaterialReviseService;
import cn.pinming.microservice.material.management.biz.service.IPurchaseContractDetailService;
import cn.pinming.microservice.material.management.biz.service.IPurchaseContractService;
import cn.pinming.microservice.material.management.biz.vo.AppContractChooseVO;
import cn.pinming.microservice.material.management.biz.vo.ContractForReviseVO;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import io.swagger.annotations.Api;
import cn.pinming.microservice.material.management.biz.vo.MaterialReviseDetailVO;
import cn.pinming.microservice.material.management.biz.vo.MaterialReviseVO;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 数据修订 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-06 14:22:51
 */
@Api(tags = "数据修订-controller", value = "zh")
@RestController
@RequestMapping("api/biz/material-revise")
public class MaterialReviseController {

    @Resource
    private IMaterialReviseService materialReviseService;
    @Resource
    private IPurchaseContractService purchaseContractService;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private IPurchaseContractDetailService purchaseContractDetailService;

    @ApiOperation(value = "修订数据")
    @PostMapping("/add")
    public ResponseEntity<Response> add(@Validated @Valid @RequestBody MaterialReviseForm materialReviseForm) {
        materialReviseService.add(materialReviseForm);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "修订数据列表", response = MaterialReviseVO.class)
    @Log(title = "修订数据列表", businessType = BusinessType.QUERY)
    @PostMapping("/list")
    public ResponseEntity<Response> list(@RequestBody ReviseInfoQuery ReviseInfoQuery) {
        MaterialReviseVO materialReviseVO =  materialReviseService.getMaterialRevise(ReviseInfoQuery);
        return ResponseEntity.ok(new SuccessResponse(materialReviseVO));
    }

    @ApiOperation(value = "数据修正详情页", response = MaterialReviseDetailVO.class)
    @PostMapping("/detail")
    public ResponseEntity<Response> detail(@RequestBody ReviseInfoQuery reviseInfoQuery) {
        AuthUser user = authUserHolder.getCurrentUser();
        reviseInfoQuery.setCompanyId(user.getCurrentCompanyId());
        MaterialReviseDetailVO materialReviseDetailVO = materialReviseService.getMaterialReviseDetail(reviseInfoQuery);
        return ResponseEntity.ok(new SuccessResponse(materialReviseDetailVO));
    }

    @ApiOperation(value = "合同选择-根据供应商id",response = ContractForReviseVO.class)
    @PostMapping("/contract/{supplierId}")
    public ResponseEntity<Response> contract(@PathVariable("supplierId") Integer supplierId) {
        List<ContractForReviseVO> list = purchaseContractService.getContractForRevise(supplierId);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "判断材料是否在采购单中")
    @GetMapping("/judge/{contractDetailId}/{materialId}")
    public ResponseEntity<Response> judge(@PathVariable("contractDetailId") String contractDetailId,@PathVariable("materialId") String materialId) {
        Boolean result = false;
        PurchaseContractDetail purchaseContractDetail = purchaseContractDetailService.lambdaQuery()
                .select(PurchaseContractDetail::getMaterialId)
                .eq(PurchaseContractDetail::getId,contractDetailId)
                .one();

        if(ObjectUtil.isNotEmpty(purchaseContractDetail) && purchaseContractDetail.getMaterialId().equals(materialId)){
            result = true;
        }
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "App-根据合同id、二级材料id选择材料")
    @GetMapping("/appchoose/{contractId}/{categoryId}")
    public ResponseEntity<Response> appchoose(@PathVariable("contractId") String contractId,@PathVariable("categoryId") String categoryId) {
        List<AppContractChooseVO> list = purchaseContractService.appchoose(contractId,categoryId);
        return ResponseEntity.ok(new SuccessResponse(list));
    }


}
