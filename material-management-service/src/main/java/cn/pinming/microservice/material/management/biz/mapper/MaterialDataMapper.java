package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.MaterialData;
import cn.pinming.microservice.material.management.biz.query.*;
import cn.pinming.microservice.material.management.biz.vo.*;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 收货/发货明细 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:32
 */
public interface MaterialDataMapper extends BaseMapper<MaterialData> {

    /**
     * 地磅收货明细
     *
     * @param query
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    MaterialDataDetailDTO selectWeightDetail(MaterialWeighQuery query);

    /**
     * 地磅收货列表明细（关联采购单）
     *
     * @param query
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<MaterialDatasVO> selectWeighDetailsWithPurchase(MaterialWeighQuery query);

    /**
     * 地磅收货列表
     *
     * @param query
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<MaterialWeighInfoDTO> queryWeightReceiveInfo(MaterialWeighQuery query);

    IPage<CrccMaterialWeighInfoDto> queryCrccWeightReceiveInfo(@Param("projectId") Integer projectId, @Param("materialId") Integer materialId, @Param("query") ztcjExtWeightListQuery query);

    SupplierAnalysisVO selectSupplierAnalysisByQuery(SupplierAnalysisQuery query);

    List<SupplierAnalysisDetailVO> selectSupplierAnalysisPageVO(SupplierAnalysisQuery query);

    /**
     * 地磅收货列表明细（不关联采购单）
     *
     * @param query
     * @return
     */
    List<MaterialDatasVO> selectWeighDetailsWithoutPurchase(MaterialWeighQuery query);

    List<CategoryReceiveVO> selectReceiveListByQuery(SupplierAnalysisQuery query);

    /**
     * sql 原因 需要忽略租户权限判断
     *
     * @param query
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ReceiveDeviationVO> selectDeviationByQuery(@Param("query") SupplierAnalysisQuery query, @Param("receiveModes") List<Byte> receiveMode);

    List<ReceiveDeviationSummaryVO> selectDeviationSummaryByQuery(@Param("query") SupplierAnalysisQuery query, @Param("receiveModes") List<Byte> receiveMode);

    List<String> selectUnitListByCompanyId(@Param("companyId") Integer companyId);

    ActTruckReportInfoDTO selectWeightInfo(@Param("id") String materialDataId);

    SupplierAnalysisVO selectSupplierAnalysisUnionByQuery(SupplierAnalysisQuery query);

    List<SupplierAnalysisDetailVO> selectSupplierUnionAnalysisPageVO(SupplierAnalysisQuery query);

    /**
     * 地磅发货列表
     *
     * @param query
     * @return
     */
    IPage<WeighbridgeSendDTO> queryWeighbridgeSendInfo(WeighbridgeSendQuery query);

    /**
     * 地磅发货明细
     *
     * @param query
     * @return
     */
    WeighbridgeSendDetailDTO selectWeighbridgeSendDetail(WeighbridgeSendQuery query);

    /**
     * 地磅发货物资明细
     *
     * @param query
     * @return
     */
    MaterialSendDetailDTO selectWeighbridgeSendMaterialDetail(WeighbridgeSendQuery query);

    List<MaterialDataForFY> getDataForFuYang();

    PicDTO checkByWeighId(String weighId);

    @InterceptorIgnore(tenantLine = "true")
    InfoByWeighIdDTO selectByWeighId(String weighId);

    /**
     * 根据 weighId 查询是否已解析地磅信息
     *
     * @param weighId
     * @return
     */
    int findWeighDataByWeighId(String weighId);

    /**
     * 查询地磅数据(地磅信息已解析)weighId集合
     *
     * @return
     */
    List<String> findAllWeighId();

    List<SummaryDeliveryVO> listSummaryDeliveryByQuery(SummaryDeliveryQuery query);

    List<Integer> listCategoryIdList(SummaryDeliveryQuery query);

    List<SummaryDeliverySecondDetailDTO> listDetailList(@Param("query") SummaryDeliveryQuery query, @Param("categoryIdList") List<Integer> categoryIdList);

    /**
     * 收/发料总览下钻数据
     *
     * @param query
     * @return
     */
    List<ReceiveOverviewSecondDTO> selectReceiveOverviewSecondByQuery(@Param("query") SummaryDeliveryQuery query, @Param("type") byte type);

    List<SupplierRankVO> negativeFrequencyRankListByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankVO> negativeFrequencyMobileRankListByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankVO> negativeFrequencyAllRankListByQuery(@Param("query") SupplierRankQuery query);

    List<WeighCarDTO> showWeighCar(@Param("query") WeighInfoQuery query);

    List<WeighCarDetailDTO> showWeighCarDetail(@Param("query") WeighInfoQuery query);

    List<SupplierRankVO> negativeFrequencyProportionByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankVO> negativeFrequencyMobileProportionByQuery(@Param("query") SupplierRankQuery query);

    @InterceptorIgnore(tenantLine = "true")
    List<SupplierRankVO> negativeFrequencyAllProportionByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankVO> deductRankByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankVO> deductProportionByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankVO> deductTotalRankByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankVO> deductTotalProportionByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalRankByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalMobileRankByQuery(@Param("query") SupplierRankQuery query);

    @InterceptorIgnore(tenantLine = "true")
    List<SupplierRankDeviationVO> negativeTotalAllRankByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalProportionByQuery(@Param("query") SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalMobileProportionByQuery(@Param("query") SupplierRankQuery query);

    @InterceptorIgnore(tenantLine = "true")
    List<SupplierRankDeviationVO> negativeTotalAllProportionByQuery(@Param("query") SupplierRankQuery query);

    List<StatisticsUnitDTO> listUnitByCategoryIds(@Param("categoryIds") List<String> categoryIds);

    /**
     * 总览-收料总览-大宗材-地磅收料条件
     *
     * @param start       开始时间(yyyy-MM-dd)
     * @param end         结束时间(yyyy-MM-dd)
     * @param receiveMode 地磅收料类型
     * @param categoryIds 二级分类ID
     * @param projectIds  项目ID
     * @return
     */
    List<ReceiveOverviewCardDTO> wagonReceiveOverviewCard(@Param("start") String start,
                                                          @Param("end") String end,
                                                          @Param("receiveModes") List<Byte> receiveMode,
                                                          @Param("categoryIds") List<String> categoryIds,
                                                          @Param("projectIds") List<Integer> projectIds);

    /**
     * 总览-收料总览-大宗材-移动收料条件
     *
     * @param start       开始时间(yyyy-MM-dd)
     * @param end         结束时间(yyyy-MM-dd)
     * @param receiveType 移动收料类型
     * @param categoryIds 二级分类ID
     * @param projectIds  项目ID
     * @return
     */
    List<ReceiveOverviewCardDTO> mobileReceiveOverviewCard(@Param("start") String start,
                                                           @Param("end") String end,
                                                           @Param("receiveTypes") List<Byte> receiveType,
                                                           @Param("categoryIds") List<String> categoryIds,
                                                           @Param("projectIds") List<Integer> projectIds);

    /**
     * 总览-收料总览-大宗材-柱状图下钻-地磅收料条件
     *
     * @param start       开始时间(yyyy-MM-dd)
     * @param end         结束时间(yyyy-MM-dd)
     * @param unit        单位
     * @param receiveMode 地磅收料类型
     * @param materialIds 材料ID
     * @param projectIds  项目ID
     * @return
     */
    List<ReceiveOverviewCardSecondDTO> wagonReceiveOverviewCardSecond(@Param("start") String start,
                                                                      @Param("end") String end,
                                                                      @Param("unit") String unit,
                                                                      @Param("receiveModes") List<Byte> receiveMode,
                                                                      @Param("materialIds") Set<Integer> materialIds,
                                                                      @Param("projectIds") List<Integer> projectIds);

    /**
     * 总览-收料总览-大宗材-柱状图下钻-移动收料条件
     *
     * @param start       开始时间(yyyy-MM-dd)
     * @param end         结束时间(yyyy-MM-dd)
     * @param unit        单位
     * @param receiveType 移动收料类型
     * @param materialIds 材料ID
     * @param projectIds  项目ID
     * @return
     */
    List<ReceiveOverviewCardSecondDTO> mobileReceiveOverviewCardSecond(@Param("start") String start,
                                                                       @Param("end") String end,
                                                                       @Param("unit") String unit,
                                                                       @Param("receiveTypes") List<Byte> receiveType,
                                                                       @Param("materialIds") Set<Integer> materialIds,
                                                                       @Param("projectIds") List<Integer> projectIds);

    /**
     * 总览-收料总览-其他-地磅收料条件
     *
     * @param start       开始时间(yyyy-MM-dd)
     * @param end         结束时间(yyyy-MM-dd)
     * @param receiveMode 地磅收料类型
     * @param categoryIds 二级分类ID
     * @param projectIds  项目ID
     * @return
     */
    List<ReceiveOverviewCardDTO> wagonReceiveOverviewOther(@Param("start") String start,
                                                           @Param("end") String end,
                                                           @Param("receiveModes") List<Byte> receiveMode,
                                                           @Param("categoryIds") List<String> categoryIds,
                                                           @Param("projectIds") List<Integer> projectIds);

    /**
     * 总览-收料总览-其他-移动收料条件
     *
     * @param start       开始时间(yyyy-MM-dd)
     * @param end         结束时间(yyyy-MM-dd)
     * @param receiveType 移动收料类型
     * @param categoryIds 二级分类ID
     * @param projectIds  项目ID
     * @return
     */
    List<ReceiveOverviewCardDTO> mobileReceiveOverviewOther(@Param("start") String start,
                                                            @Param("end") String end,
                                                            @Param("receiveTypes") List<Byte> receiveType,
                                                            @Param("categoryIds") List<String> categoryIds,
                                                            @Param("projectIds") List<Integer> projectIds);

    /**
     * 总览-收料总览-其他-柱状图下钻-地磅收料条件
     *
     * @param start       开始时间(yyyy-MM-dd)
     * @param end         结束时间(yyyy-MM-dd)
     * @param unit        单位
     * @param receiveMode 地磅收料类型
     * @param categoryId  二级分类ID
     * @param projectIds  项目ID
     * @return
     */
    List<ReceiveOverviewCardSecondDTO> wagonReceiveOverviewOtherSecond(@Param("start") String start,
                                                                       @Param("end") String end,
                                                                       @Param("unit") String unit,
                                                                       @Param("receiveModes") List<Byte> receiveMode,
                                                                       @Param("categoryId") Integer categoryId,
                                                                       @Param("projectIds") List<Integer> projectIds);

    /**
     * 总览-收料总览-其他-柱状图下钻-移动收料条件
     *
     * @param start       开始时间(yyyy-MM-dd)
     * @param end         结束时间(yyyy-MM-dd)
     * @param unit        单位
     * @param receiveType 移动收料类型
     * @param categoryId  二级分类ID
     * @param projectIds  项目ID
     * @return
     */
    List<ReceiveOverviewCardSecondDTO> mobileReceiveOverviewOtherSecond(@Param("start") String start,
                                                                        @Param("end") String end,
                                                                        @Param("unit") String unit,
                                                                        @Param("receiveTypes") List<Byte> receiveType,
                                                                        @Param("categoryId") Integer categoryId,
                                                                        @Param("projectIds") List<Integer> projectIds);

    @InterceptorIgnore(tenantLine = "true")
    List<MaterialIdDTO> solve();

    BigDecimal selectUsageRate(List<Integer> projectIdList, @Param("day") Integer day);

    List<Integer> selectUsageList(List<Integer> projectIdList, @Param("day") Integer day);


    List<DeviationOverviewSecondDTO> selectDeviationSecondByQuery(@Param("query") SummaryDeliveryQuery query);

    List<ReceiveOverviewSecondDTO> selectReceiveOverviewSecondByQuery(SupplierAnalysisQuery query);

    /**
     *
     * @param tUuid  weighId 地磅上传唯一id
     * @param flag  true 高拍仪  false 非高拍仪
     * @return
     */
    int selectCountByWeighId(@Param("weighId") String tUuid,@Param("companyId") Integer companyId,@Param("flag") boolean flag);

    MaterialData selectGunByWeighId(@Param("weighId") String weighId);

    SendReceiveByWeighIdDTO selectSendReceiveByWeighId(@Param("weighId") String weighId);

    int selectVerifyByWeighId(@Param("weighId") String weighId,@Param("companyId") Integer companyId);

    FeedBackDTO selectFeedBack(@Param("weighId") String tUuid);

    /**
     * 根据采购单id获取材料详情
     * @param id 采购单
     * @return MaterialDetailVO
     */
    List<MaterialDetailVO> selectByOrderId(@Param("id") String id);

    /**
     * 根据采购单ID查询是否关联收料记录
     * @param id
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    int receiveDataRelation(@Param("id") String id);


    /**
     * 根据采购单ID查询是否关联收料记录
     * @param id
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ReceiveDataRelationDTO> receiveDataRelationList(@Param("orderIds") List<String> orderIds,@Param("companyId") Integer companyId);

    /**
     * 查询需要推动的data集合
     * @return ids
     */
    List<String> selectNeedPushDataIds();

    /**
     * 更新推送状态
     * @param ids ids
     * @param status 状态
     */
    void updatePushState(@Param("ids") List<String> ids, @Param("status") byte status);

    List<String> sendCompany(@Param("companyId")Integer companyId,@Param("projectId") Integer projectId);

    @InterceptorIgnore(tenantLine = "true")
    void updateDataByWeighIds(@Param("ids") List<String> weighIds);

    ThresholdDTO selectThreshold(@Param("id") String id);

    List<PlantDeductDTO> queryPlantDeduct(@Param("ids") List<String> ids);

    int selectVerifyByDataId(@Param("id") String id,@Param("companyId") Integer currentCompanyId);

    List<ContractMapDTO> selectContractByIds(@Param("ids") List<String> ids);

    List<MaterialData> needPushDataList(@Param("companyId") Integer companyId, @Param("projectId") Integer projectId);

    MaterialData mobileHistoryByType(@Param("type") Byte type,@Param("receiveType") Byte receiveType,@Param("companyId") Integer companyId, @Param("projectId") Integer projectId);

    List<RecycleDataIdDTO> selectDataIdsByRecycleId(@Param("ids")List<Long> recycleIds, @Param("projectId") Integer projectId);

    CargoDetailInfoDTO getCargoFromPurchase(@Param("cargoId") String cargoId);

    CargoDetailInfoDTO getCargoFromContract(@Param("cargoId") String cargoId);

    CargoDetailInfoDTO getCargo(@Param("cargoId") String cargoId);

    CargoDetailInfoDTO getCargoFromMixing(@Param("cargoId") String cargoId);
}
