package cn.pinming.microservice.material.management.biz.enums;

/**
 * 毛皮重完整性 枚举
 */
public enum IsWeightIntegralityEnum {
    COMPLETE((byte) 1, "完整"),
    INCOMPLETE((byte) 2, "不完整");

    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    IsWeightIntegralityEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }

    public String description() {
        return description;
    }

    public static String desc(Byte value){
        if (value == null) {return null;}
        for (IsWeightIntegralityEnum statusEnum : IsWeightIntegralityEnum.values()) {
            if (statusEnum.value == value) {
                return statusEnum.description;
            }
        }
        return null;
    }
}
