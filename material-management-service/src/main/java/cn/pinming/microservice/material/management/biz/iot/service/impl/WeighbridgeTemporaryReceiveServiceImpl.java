package cn.pinming.microservice.material.management.biz.iot.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.management.biz.dto.ContractDecisionDTO;
import cn.pinming.microservice.material.management.biz.dto.MaterialDataDTO;
import cn.pinming.microservice.material.management.biz.dto.WeighbridgeAcceptDataDTO;
import cn.pinming.microservice.material.management.biz.entity.*;
import cn.pinming.microservice.material.management.biz.enums.MaterialExistEnum;
import cn.pinming.microservice.material.management.biz.enums.ReceiveModeEnum;
import cn.pinming.microservice.material.management.biz.enums.WarningSourceEnum;
import cn.pinming.microservice.material.management.biz.enums.WarningTypeEnum;
import cn.pinming.microservice.material.management.biz.iot.service.IWeighbridgeDecisionService;
import cn.pinming.microservice.material.management.biz.service.IMaterialDataService;
import cn.pinming.microservice.material.management.biz.service.IMaterialVerifyRelationService;
import cn.pinming.microservice.material.management.biz.service.IMaterialVerifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Transactional
@Service(value = "temporaryReceive")
public class WeighbridgeTemporaryReceiveServiceImpl extends AbstractWeighbridgeDecisionService implements IWeighbridgeDecisionService {

    @Autowired
    private WeighbridgeUnbelongReceiveServiceImpl weighbridgeUnbelongReceiveService;
    @Resource
    private IMaterialVerifyService materialVerifyService;
    @Resource
    private IMaterialVerifyRelationService materialVerifyRelationService;
    @Resource
    private IMaterialDataService materialDataService;

    @Override
    public WeighbridgeAcceptDataDTO getAcceptDataDTO() {
        return super.getAcceptDataDTO();
    }

    @Override
    public void setAcceptDataDTO(WeighbridgeAcceptDataDTO acceptDataDTO) {
        super.setAcceptDataDTO(acceptDataDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WeighbridgeAcceptDataDTO decisionWeighbridgeReceive(WeighbridgeAcceptDataDTO acceptDataDTO) {
        acceptDataDTO.setReceiveMode(ReceiveModeEnum.TEMPORARY);
        setAcceptDataDTO(acceptDataDTO);

        handlePurchaseOrder();
        handleValidity();
        saveWeighbridgeReceive();
        saveWeighbridgeMaterialData();

        if (acceptDataDTO.getIsAutoVerify() != null && acceptDataDTO.getIsAutoVerify() == 1) {
            String contractId = null;
            if (ObjectUtil.isNotNull(acceptDataDTO.getPurchaseOrder())) {
                contractId = acceptDataDTO.getPurchaseOrder().getContractId();
            }
            // 按采购单收料 按合同收料 合同id来源不同
            if (StrUtil.isBlank(contractId)) {
                for (Map.Entry<Integer, ContractDecisionDTO> entry : acceptDataDTO.getContractDecisionDTOMap().entrySet()) {
                    contractId = entry.getValue().getContractId();
                    break;
                }
            }
            if (CollUtil.isNotEmpty(acceptDataDTO.getDataIdList())) {
                MaterialVerify materialVerify = materialVerifyService.lambdaQuery()
                        .eq(MaterialVerify::getContractId, contractId)
                        .eq(MaterialVerify::getIsOrigin, 1)
                        .eq(MaterialVerify::getProjectId,acceptDataDTO.getProjectId())
                        .one();
                if (ObjectUtil.isNotNull(materialVerify)) {
                    List<MaterialVerifyRelation> collect = acceptDataDTO.getDataIdList().stream().map(e -> {
                        MaterialVerifyRelation materialVerifyRelation = new MaterialVerifyRelation();
                        materialVerifyRelation.setVerifyId(materialVerify.getId());
                        materialVerifyRelation.setReceiveDataId(e);
                        materialVerifyRelation.setProjectId(acceptDataDTO.getProjectId());
                        materialVerifyRelation.setCompanyId(acceptDataDTO.getCompanyId());

                        return materialVerifyRelation;
                    }).collect(Collectors.toList());

                    materialVerifyRelationService.saveBatch(collect);

                    // 更新收料明细的对账id
                    List<MaterialData> materialDataList = acceptDataDTO.getDataIdList().stream().map(e -> {
                        MaterialData materialData = new MaterialData();

                        materialData.setId(e);
                        materialData.setReconciliationId(materialVerify.getId());

                        return materialData;
                    }).collect(Collectors.toList());
                    materialDataService.updateBatchById(materialDataList);
                }
            }
        }
        return null;
    }

    @Override
    protected WeighbridgeAcceptDataDTO getMaterialData(Integer companyId, Integer projectId
            , WeighbridgeAcceptDataDTO acceptDataDTO, PurchaseOrder purchaseOrder) {
        super.getMaterialData(companyId, projectId, acceptDataDTO, purchaseOrder);

        List<MaterialData> materialDataList = acceptDataDTO.getReceiveMaterialList();
        if (CollUtil.isEmpty(materialDataList)) {
            return acceptDataDTO;
        }
        List<MaterialData> list = materialDataList.stream().map(obj -> {
            Byte materialExist = obj.getMaterialExist();
            if (materialExist == null || materialExist.intValue() == MaterialExistEnum.NO.value()) {
                // 物料不存在设置成无归属
                acceptDataDTO.setReceiveMode(ReceiveModeEnum.UNBELOGN);
            }
            return obj;
        }).collect(Collectors.toList());

        acceptDataDTO.setReceiveMaterialList(list);
        return acceptDataDTO;
    }


    /**
     * 收料物料数据转换
     * 无归属收料特殊处理
     *
     * @param
     * @return
     */
    @Override
    protected void handleReceiveMaterialData(MaterialData materialData) {
        if (ReceiveModeEnum.UNBELOGN.equals(getAcceptDataDTO().getReceiveMode())) {
            weighbridgeUnbelongReceiveService.setAcceptDataDTO(getAcceptDataDTO());
            weighbridgeUnbelongReceiveService.handleReceiveMaterialData(materialData);
        } else {
            super.handleReceiveMaterialData(materialData);
        }
    }
}
