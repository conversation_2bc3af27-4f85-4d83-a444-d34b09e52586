package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 预警信息
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 10:46:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_material_warning")
@ApiModel(value = "MaterialWarning对象", description = "预警信息")
public class MaterialWarning implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "预警编号")
    @TableField("warning_no")
    private String warningNo;

    @ApiModelProperty(value = "1、称重转换系数异常 2、毛/皮重异常 3、面单应收数量异常 4、结算单位异常 5、超负差 6、无效称重 7、物料名称异常 8、超时未出场 9、超期未进场 10、皮重偏差异常")
    @TableField("warning_type")
    private Byte warningType;

    @ApiModelProperty(value = "预警大类 1、数据完整性 2、业务规范性")
    @TableField("warning_kind")
    private Byte warningKind;

    @ApiModelProperty(value = "预警类型子分类")
    @TableField("warning_sub_type")
    private Byte warningSubType;

    @ApiModelProperty(value = "预警信息")
    @TableField("warning_info")
    private String warningInfo;

    @ApiModelProperty(value = "预警来源")
    @TableField("warning_source")
    private String warningSource;

    @ApiModelProperty(value = "预警来源记录id")
    @TableField("warning_source_id")
    private String warningSourceId;

    @ApiModelProperty(value = "预警来源记录编号")
    @TableField("warning_source_no")
    private String warningSourceNo;

    @ApiModelProperty(value = "发生项目")
    @TableField("source_project_id")
    private Integer sourceProjectId;

    @ApiModelProperty(value = "处理人id")
    @TableField("handler_id")
    private String handlerId;

    @ApiModelProperty(value = "处理人姓名")
    @TableField("handler_name")
    private String handlerName;

    @ApiModelProperty(value = "处理人建议id")
    @TableField("handler_advice_id")
    private String handlerAdviceId;

    @ApiModelProperty(value = "处理时间")
    @TableField("handler_time")
    private LocalDateTime handlerTime;

    @ApiModelProperty(value = "预警状态: 1 待处理,2 已处理")
    @TableField("warning_status")
    private Byte warningStatus;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
