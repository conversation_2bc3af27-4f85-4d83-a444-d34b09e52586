package cn.pinming.microservice.material.management.biz.vo;

import cn.pinming.microservice.material.management.biz.dto.SimpleSupplierDTO;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> hao
 * @version 2021/8/25 10:10 上午
 */
@Data
public class PurchaseContractVO extends SimpleSupplierDTO {

    @ApiModelProperty(value = "合同ID")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编号")
    private String no;

    @ApiModelProperty(value = "类型， 1：混凝土采购合同；2：材料采购合同 ....")
    private String type;

    @ApiModelProperty(value = "外部系统代码")
    private String extCode;

    @ApiModelProperty(value = "所属项目，多个")
    private String belongProjectId;

    @ApiModelProperty(value = "所属项目，多个")
    private String belongProject;

    @ApiModelProperty(value = "物资品种，多个")
    private String category;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "对账归档后是否同步仓库 1 是 2 否")
    private Byte isUpload;

    @ApiModelProperty(value = "合同商品列表")
    private List<PurchaseContractDetailVO> list;

    @ApiModelProperty(value = "合同商品map")
    private List<ContractDetailVO> listApp;

    @ApiModelProperty("推送至基石“订单/发货”系统 1 是 0 否")
    private Byte isPush;
}
