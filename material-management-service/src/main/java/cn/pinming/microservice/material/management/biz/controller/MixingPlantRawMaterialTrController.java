package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.form.RawMaterialLabReportForm;
import cn.pinming.microservice.material.management.biz.service.IMixingPlantRawMaterialTrService;
import cn.pinming.microservice.material.management.biz.vo.MixingPlantRawMaterialTrVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>
 * 拌合站—原料实验报告 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 16:30:16
 */
@RestController
@Api(tags = "拌合站-原料实验报告",value = "zh")
@RequestMapping("/api/plant/raw/material/tr")
public class MixingPlantRawMaterialTrController {

    @Resource
    private IMixingPlantRawMaterialTrService trService;

    @ApiOperation(value = "分页查询", response = MixingPlantRawMaterialTrVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "current", value = "当前页数", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页多少条数据", required = true, dataType = "Integer", paramType = "query")
    })
    @GetMapping
    public ResponseEntity<Response> trPageList(@RequestParam("current") Integer current,
                                               @RequestParam("size") Integer size) {
        Page<MixingPlantRawMaterialTrVO> voPage = trService.trPageList(current, size);
        return ResponseEntity.ok(new SuccessResponse(voPage));
    }

    @ApiOperation(value = "根据ID查询", response = MixingPlantRawMaterialTrVO.class)
    @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "String", paramType = "path")
    @GetMapping("/{id}")
    public ResponseEntity<Response> getLabReportById(@PathVariable("id") String id) {
        MixingPlantRawMaterialTrVO vo = trService.getLabReportById(id);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "新增")
    @PostMapping
    public ResponseEntity<Response> addLabReport(@RequestBody RawMaterialLabReportForm reportForm) {
        trService.addLabReport(reportForm);
        return ResponseEntity.ok(new SuccessResponse(true));
    }

    @ApiOperation(value = "修改")
    @PutMapping
    public ResponseEntity<Response> updateLabReport(@RequestBody RawMaterialLabReportForm reportForm) {
        trService.updateLabReport(reportForm);
        return ResponseEntity.ok(new SuccessResponse(true));
    }

    @ApiOperation(value = "根据ID删除")
    @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "String", paramType = "path")
    @DeleteMapping("/{id}")
    public ResponseEntity<Response> deleteLabReportById(@PathVariable("id") String id) {
        trService.removeById(id);
        return ResponseEntity.ok(new SuccessResponse(true));
    }

    @ApiOperation(value = "报告编号 or 试验单位 模糊匹配")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型:1-报告编号 2-试验单位", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "keyword", value = "关键字", required = false, dataType = "String", paramType = "query")
    })
    @GetMapping("/keyword")
    public ResponseEntity<Response> queryHistoryTrNoOrUnit(@RequestParam("type") Integer type,
                                                           @RequestParam(value = "keyword", required = false) String keyword) {
        List<String> result = trService.queryHistoryTrNoOrUnit(type, keyword);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "试验编号重复校验, true:表示重复, false:表示不重复")
    @ApiImplicitParam(name = "trNo", value = "试验编号", required = true, dataType = "String", paramType = "query")
    @GetMapping("/no/validate")
    public ResponseEntity<Response> trNoValidate(@RequestParam("trNo") String trNo) {
        return ResponseEntity.ok(new SuccessResponse(trService.trNoValidate(trNo)));
    }

    @ApiOperation(value = "材料id -> 试验报告列表")
    @PostMapping("/seek")
    public ResponseEntity<Response> seek(@RequestBody List<Integer> materialIdList) {
        Map<Integer,List<MixingPlantRawMaterialTrVO>> map = trService.seek(materialIdList);
        return ResponseEntity.ok(new SuccessResponse(map));
    }

}
