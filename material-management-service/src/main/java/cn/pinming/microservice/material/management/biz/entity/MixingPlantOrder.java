package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 拌合站订单
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-28 15:26:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_mixing_plant_order")
@ApiModel(value = "MixingPlantOrder对象", description = "拌合站订单")
public class MixingPlantOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "订单编号")
    @TableField("order_no")
    private String orderNo;

    @ApiModelProperty(value = "接单拌合站id")
    @TableField("plant_id")
    private Integer plantId;

    @ApiModelProperty(value = "收货项目（拌合站下单客户）")
    @TableField("receiver_project")
    private Integer receiverProject;

    @ApiModelProperty(value = "收货项目（拌合站下单客户）客户名")
    @TableField("receiver_project_title")
    private String receiverProjectTitle;

    @ApiModelProperty(value = "收货地址（拌合站）")
    @TableField("receiver_address")
    private String receiverAddress;

    @ApiModelProperty(value = "收货人（拌合站）")
    private String receiver;

    @ApiModelProperty(value = "收货电话（拌合站）")
    @TableField("receiver_tel")
    private String receiverTel;

    @ApiModelProperty(value = "要货日期（拌合站）")
    @TableField("receive_time")
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "备注（拌合站）")
    private String remark;

    @ApiModelProperty(value = "采购单id")
    @TableField("purchase_order_id")
    private String purchaseOrderId;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
