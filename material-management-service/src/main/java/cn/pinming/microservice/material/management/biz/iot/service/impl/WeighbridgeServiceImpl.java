package cn.pinming.microservice.material.management.biz.iot.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.management.biz.dto.WeighbridgeAcceptDataDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialWeighbridge;
import cn.pinming.microservice.material.management.biz.entity.PurchaseOrder;
import cn.pinming.microservice.material.management.biz.enums.ReceiveModeEnum;
import cn.pinming.microservice.material.management.biz.enums.WeighTypeEnum;
import cn.pinming.microservice.material.management.biz.enums.WeighbridgeTypeEnum;
import cn.pinming.microservice.material.management.biz.iot.service.IWeighbridgeDecisionService;
import cn.pinming.microservice.material.management.biz.iot.service.IWeighbridgeService;
import cn.pinming.microservice.material.management.biz.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.biz.mapper.MaterialSendReceiveMapper;
import cn.pinming.microservice.material.management.biz.service.IMaterialWeighbridgeService;
import cn.pinming.microservice.material.management.biz.service.IPreTruckReportDetailService;
import cn.pinming.microservice.material.management.biz.service.IPurchaseOrderService;
import cn.pinming.microservice.material.management.common.util.RedisUtil;
import cn.pinming.microservice.material.management.wrapper.SupplierWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WeighbridgeServiceImpl implements IWeighbridgeService, SmartInitializingSingleton {

    @Autowired
    private Map<String, IWeighbridgeDecisionService> decisionServiceMap;

    @Autowired
    private IMaterialWeighbridgeService materialWeighbridgeService;

    @Resource
    private IPurchaseOrderService purchaseOrderService;

    @Resource
    private IPreTruckReportDetailService preTruckReportDetailService;

    @Resource
    private MaterialDataMapper materialDataMapper;


    @Resource
    private SupplierWrapper supplierWrapper;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private MaterialSendReceiveMapper materialSendReceiveMapper;

    /**
     * @param acceptDataDTO
     */
    @Override
    @Transactional
    public void processWeighbridgeReceive(WeighbridgeAcceptDataDTO acceptDataDTO) {

        String preTruckReportId = acceptDataDTO.getPreTruckReportDetailId();
        String purchaseNo = acceptDataDTO.getPurchaseId();
        Integer companyId = acceptDataDTO.getCompanyId();
        Integer projectId = acceptDataDTO.getProjectId();

        if (StrUtil.isNotEmpty(purchaseNo)) {
            PurchaseOrder purchaseOrder = getReceiveIdByPurchaseNo(purchaseNo);
            acceptDataDTO.setPurchaseOrder(purchaseOrder);
        }

        boolean flag = acceptDataDTO.getType() == WeighTypeEnum.OTHER.value() || acceptDataDTO.getType() == WeighTypeEnum.DELIVERY.value() || acceptDataDTO.getType() == WeighTypeEnum.RECEIVE.value();
        if (companyId == null || projectId == null || !flag) {
            acceptDataDTO.setReceiveMode(ReceiveModeEnum.INVALID);
            decisionServiceMap.get(WeighbridgeTypeEnum.INVALID.getValue()).decisionWeighbridgeReceive(acceptDataDTO);
            return;
        }

        //根据设备sn获取项目和企业信息
        MaterialWeighbridge weighbridge = materialWeighbridgeService.getWeighbridge(companyId, projectId, acceptDataDTO.getDeviceSn());
        // 地磅信息不存在或者异常，存为按无效落库x
        if (weighbridge == null) {
            log.warn("processWeighbridgeReceive weighbridge is empty, companyId:{}, projectId:{}, deviceSn:{}"
                    , companyId, projectId, acceptDataDTO.getDeviceSn());
        }

        // 预报备id不为空 走预报备流程
        if (StrUtil.isNotEmpty(preTruckReportId) && acceptDataDTO.getPurchaseOrder() != null) {
            acceptDataDTO.setReceiveMode(ReceiveModeEnum.REPORT);
            decisionServiceMap.get(WeighbridgeTypeEnum.TRUCK_REPORT.getValue()).decisionWeighbridgeReceive(acceptDataDTO);
            return;
        }
        // 采购单不为空 走临时货无归属流程
        if (acceptDataDTO.getPurchaseOrder() != null  || (acceptDataDTO.getReceiveModeType() != null && acceptDataDTO.getReceiveModeType() == ReceiveModeEnum.TEMPORARY.value())) {
            acceptDataDTO.setReceiveMode(ReceiveModeEnum.TEMPORARY);
            decisionServiceMap.get(WeighbridgeTypeEnum.TEMPORARY.getValue()).decisionWeighbridgeReceive(acceptDataDTO);
            return;
        }
        // 其他情况 走无归属
        acceptDataDTO.setReceiveMode(ReceiveModeEnum.UNBELOGN);
        decisionServiceMap.get(WeighbridgeTypeEnum.UNBELONG.getValue()).decisionWeighbridgeReceive(acceptDataDTO);

    }

    /**
     * 历史存量 weighId 数据缓存初始化(地磅信息已解析)
     */
    @Override
    public void afterSingletonsInstantiated() {
        List<String> allWeighId = materialDataMapper.findAllWeighId();
        //allWeighId.forEach(weighId -> redisUtil.set(weighId, weighId, 30));
        Map<String, String> map = allWeighId.stream().collect(Collectors.toMap(t -> t, t -> t));
        redisUtil.batchSetOrExpire(map, 30L);
    }

    @Override
    public boolean weighIdIdempotentCheck(String weighId) {
        // 一级检测：缓存
        Object weightIdCache = redisUtil.get(weighId);
        if (ObjectUtil.isNotNull(weightIdCache)) {
            // 缓存重置
            redisUtil.set(weighId, weighId, 30);
            return true;
        }
        // 二级检测：数据库
        int weighDataByWeighId = materialDataMapper.findWeighDataByWeighId(weighId);
        if (weighDataByWeighId > 0) {
            // 缓存重置
            redisUtil.set(weighId, weighId, 30);
            return true;
        }
        return false;
    }

    private PurchaseOrder getReceiveIdByPurchaseNo(String purchaseNo) {
        PurchaseOrder purchaseOrder = purchaseOrderService.selectIdByPurchaseNo(purchaseNo);
        if (Objects.isNull(purchaseOrder)) {
            return null;
        }
        return purchaseOrder;
    }

    /**
     * 处理地磅发料
     *
     * @param acceptDataDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processWeighbridgeDelivery(WeighbridgeAcceptDataDTO acceptDataDTO) {
        if (acceptDataDTO.getCompanyId() == null || acceptDataDTO.getProjectId() == null) {
            acceptDataDTO.setReceiveMode(ReceiveModeEnum.INVALID);
            decisionServiceMap.get(WeighbridgeTypeEnum.INVALID.getValue()).decisionWeighbridgeReceive(acceptDataDTO);
            return;
        }
        decisionServiceMap.get(WeighbridgeTypeEnum.DELIVERY.getValue()).decisionWeighbridgeReceive(acceptDataDTO);
    }


}
