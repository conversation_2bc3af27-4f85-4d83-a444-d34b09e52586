package cn.pinming.microservice.material.management.biz.iot.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.*;
import cn.pinming.microservice.material.management.biz.enums.*;
import cn.pinming.microservice.material.management.biz.form.MaterialWarningForm;
import cn.pinming.microservice.material.management.biz.iot.service.IWeighbridgeDecisionService;
import cn.pinming.microservice.material.management.biz.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.biz.mapper.MaterialSendReceiveMapper;
import cn.pinming.microservice.material.management.biz.mapper.MaterialVerifyRelationMapper;
import cn.pinming.microservice.material.management.biz.mapper.PurchaseContractDetailMapper;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.proxy.FileServiceProxy;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialUnitConversionDto;
import cn.pinming.microservice.material_unit.api.material.service.MaterialService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static cn.pinming.microservice.material.management.biz.enums.WarningSubTypeEnum.*;

@Slf4j
public abstract class AbstractWeighbridgeDecisionService implements IWeighbridgeDecisionService {

    private BigDecimal zero = BigDecimal.ZERO;

    @Autowired
    private ITruckReportService truckReportService;

    @Autowired
    private IPreTruckReportDetailService preTruckReportDetailService;

    @Resource
    private PurchaseContractDetailMapper purchaseContractDetailMapper;

    @Autowired
    private IPurchaseOrderService purchaseOrderService;

    @Autowired
    private IMaterialSendReceiveService materialSendReceiveService;

    @Autowired
    private IMaterialDataService materialDataService;

    @Autowired
    private IMaterialWeightService materialWeightService;

    @Resource
    private MaterialServiceProxy materialServiceProxy;

    @Resource
    private IMaterialWarningService materialWarningService;

    @Resource
    private AuthUserHolder authUserHolder;

    @Resource
    private MaterialSendReceiveMapper materialSendReceiveMapper;

    @Resource
    private IMaterialReviseService materialReviseService;

    @Resource
    private MaterialDataMapper materialDataMapper;

    @Resource
    private ApplicationContext applicationEventPublisher;

    @Resource
    private MaterialVerifyRelationMapper materialVerifyRelationMapper;

    @Resource
    private ISupplierService supplierService;

    @Resource
    private IProjectExtCodeService projectExtCodeService;

    @Resource
    private IMaterialWeighbridgeService materialWeighbridgeService;

    @DubboReference
    private MaterialService materialService;

    private Integer companyId;

    private Integer projectId;

    private WeighbridgeAcceptDataDTO acceptDataDTO;
    @Resource
    private FileServiceProxy fileServiceProxy;

    public WeighbridgeAcceptDataDTO getAcceptDataDTO() {
        return acceptDataDTO;
    }

    public void setAcceptDataDTO(WeighbridgeAcceptDataDTO acceptDataDTO) {
        this.acceptDataDTO = acceptDataDTO;
        this.companyId = acceptDataDTO.getCompanyId();
        this.projectId = acceptDataDTO.getProjectId();
    }

    public IPurchaseOrderService getPurchaseOrderService() {
        return purchaseOrderService;
    }

    /**
     * 车辆报备判定
     *
     * @param
     * @return
     */
    protected WeighbridgeAcceptDataDTO handleTruckReport() {
        TruckReport truckInfo = getTruckInfo(acceptDataDTO.getTruckNo(), companyId, projectId);
        acceptDataDTO.setTruckInfo(truckInfo);

        return acceptDataDTO;
    }

    /**
     * 采购单判定：采购单号，采购单明细
     *
     * @param
     * @return
     */
    protected WeighbridgeAcceptDataDTO handlePurchaseOrder() {
        String purchaseId = acceptDataDTO.getPurchaseId();
        List<String> contractDetailIds = acceptDataDTO.getMaterialDataList().stream().map(MaterialDataDTO::getContractDetailId).distinct().collect(Collectors.toList());
        if (purchaseId != null) {
            if (acceptDataDTO.getPurchaseOrder() == null) {
                PurchaseOrder purchaseOrder = purchaseOrderService.selectIdByPurchaseNo(purchaseId);
                acceptDataDTO.setPurchaseOrder(purchaseOrder);
                if (ObjectUtil.isNotEmpty(purchaseOrder)) {
                    purchaseOrderService.updatePurchaseOrderReceiving(purchaseOrder.getId(), companyId, projectId);
                }
            }

            //      塞合同信息
            handleContract();
        }

//      只有按合同扫码收料才会进入此判断
        if(CollUtil.isNotEmpty(contractDetailIds) && StrUtil.isBlank(purchaseId)){
            //      塞合同信息
            handleContractDetail(contractDetailIds);
        }


        return acceptDataDTO;
    }

    /**
     * 合同信息，材料id -> 合同材料信息
     *
     * @param
     * @return
     */
    private WeighbridgeAcceptDataDTO handleContract() {
        Map<Integer, ContractDecisionDTO> map = new HashMap<>();

        String purchaseNo = acceptDataDTO.getPurchaseId();
        if (StrUtil.isNotBlank(purchaseNo)) {
            List<ContractDecisionDTO> dtos = purchaseOrderService.findDecisionFactor(purchaseNo);
            if (CollUtil.isNotEmpty(dtos)) {
                map = dtos.stream().collect(Collectors.toMap(ContractDecisionDTO::getMaterialId, e -> e));
            }
            acceptDataDTO.setContractDecisionDTOMap(map);
        }

        return acceptDataDTO;
    }

    /**
     * 合同信息，材料id -> 合同材料信息
     *
     * @param
     * @return
     */
    private WeighbridgeAcceptDataDTO handleContractDetail(List<String> contractDetailIds) {
        Map<Integer, ContractDecisionDTO> map = new HashMap<>();

        List<ContractDecisionDTO> details = purchaseContractDetailMapper.selectByContractDetailIdList(contractDetailIds);

        if (CollUtil.isNotEmpty(details)) {
            map = details.stream().collect(Collectors.toMap(ContractDecisionDTO::getMaterialId, e -> e));
            acceptDataDTO.setContractDecisionDTOMap(map);
        }

        return acceptDataDTO;
    }
    /**
     * 有效性判断
     *
     * @param
     * @return
     */
    protected WeighbridgeAcceptDataDTO handleValidity() {
        if (CollUtil.isNotEmpty(acceptDataDTO.getMaterialDataList())) {
            // 设置明细
            getMaterialData(companyId, projectId, acceptDataDTO, acceptDataDTO.getPurchaseOrder());
        }
        List<MaterialData> materialDataList = acceptDataDTO.getReceiveMaterialList();
        if (CollUtil.isEmpty(materialDataList)) {
            acceptDataDTO.setMaterialValidity(MaterialValidityEnum.INVALID);
            return acceptDataDTO;
        }
        ReceiveModeEnum receiveMode = acceptDataDTO.getReceiveMode();
        AtomicReference<MaterialValidityEnum> receiveValidity = new AtomicReference<>(MaterialValidityEnum.INVALID);
        materialDataList.forEach(materialData -> {
            if (receiveMode != null) {
                materialData.setReceiveMode(receiveMode.value());
            }
            // 毛重
            BigDecimal weightGross = materialData.getWeightGross();
            // 皮重
            BigDecimal weightTare = materialData.getWeightTare();
            // 判断是否无效
            boolean isInvalid = (materialData.getMaterialId() == null && StrUtil.isEmpty(materialData.getMaterialName()))
                    || NumberUtil.isLess(weightGross, BigDecimal.ZERO) || NumberUtil.isLess(weightTare, BigDecimal.ZERO)
                    || NumberUtil.equals(weightGross, weightTare);

            if (acceptDataDTO.getType() == WeighTypeEnum.RECEIVE.value()) {
                warn(materialData, weightGross, weightTare);
            }

            if (materialData.getReceiveMode() != null) {
                if (isInvalid || materialData.getReceiveMode() == ReceiveModeEnum.INVALID.value()) {
                    materialData.setMaterialValidity(MaterialValidityEnum.INVALID.value());
                    if (acceptDataDTO.getType() == WeighTypeEnum.RECEIVE.value()) {
                        materialData.setIsContractUnitUsed(IsContractUnitUsedEnum.NO.value());
                        materialData.setIsContractRateUsed(IsContractRateUsedEnum.NO.value());
                        Map<Integer, ContractDecisionDTO> map = acceptDataDTO.getContractDecisionDTOMap();
                        boolean flag = CollUtil.isNotEmpty(map) && ObjectUtil.isNotEmpty(materialData.getMaterialId()) && ObjectUtil.isNotEmpty(map.get(materialData.getMaterialId())) && StrUtil.isNotBlank(materialData.getWeightUnit());
                        boolean flag1 = CollUtil.isNotEmpty(map) && ObjectUtil.isNotEmpty(materialData.getMaterialId()) && ObjectUtil.isNotEmpty(map.get(materialData.getMaterialId())) && ObjectUtil.isNotEmpty(materialData.getRatio());
                        if (flag) {
                            if (map.get(materialData.getMaterialId()).getSettlementUnit().equals(materialData.getWeightUnit())) {
                                materialData.setIsContractUnitUsed(IsContractUnitUsedEnum.YES.value());
                            }
                        }
                        if (flag1) {
                            if (map.get(materialData.getMaterialId()).getConversionRate().compareTo(materialData.getRatio()) == 0) {
                                materialData.setIsContractRateUsed(IsContractRateUsedEnum.YES.value());
                            }
                        }
                    }
                    boolean flag = materialData.getWeightGross() != null && materialData.getWeightTare() != null && materialData.getWeightGross().compareTo(zero) > 0 && materialData.getWeightTare().compareTo(zero) > 0;
                    if (flag) {
                        materialData.setIsWeightIntegrality(IsWeightIntegralityEnum.COMPLETE.value());
                    } else {
                        materialData.setIsWeightIntegrality(IsWeightIntegralityEnum.INCOMPLETE.value());
                    }
                    normalDeductSet(materialData);
                    // TODO: 2022/4/29 兼容一车多料
                    return;
                }
            }
            // materialData为发料时，该无效无效，该有效有效
            if (isInvalid) {
                materialData.setMaterialValidity(MaterialValidityEnum.INVALID.value());
                receiveValidity.set(MaterialValidityEnum.INVALID);
            }else {
                materialData.setMaterialValidity(MaterialValidityEnum.VALID.value());
                receiveValidity.set(MaterialValidityEnum.VALID);
            }

            handleReceiveMaterialData(materialData);
        });
        acceptDataDTO.setMaterialValidity(receiveValidity.get());
        return acceptDataDTO;
    }

    private void warn(MaterialData materialData, BigDecimal weightGross, BigDecimal weightTare) {
        // 货物为空预警
        if (materialData.getMaterialId() == null && StrUtil.isBlank(materialData.getMaterialName())) {
            triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.INVALID_WEIGHT.value(),
                    INVALID_WEIGHT_ONE.subType(), INVALID_WEIGHT_ONE.desc());
        }
        // 材料id为空，材料名称不为空预警
        if (materialData.getMaterialId() == null && StrUtil.isNotBlank(materialData.getMaterialName()) && ObjectUtil.isNotEmpty(acceptDataDTO.getPurchaseOrder())) {
            triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.MATERIAL_NAME.value(),
                    MATERIAL_NAME_TWO.subType(), MATERIAL_NAME_TWO.desc());
            triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.MATERIAL_NAME.value(),
                    MATERIAL_NAME_ONE.subType(), MATERIAL_NAME_ONE.desc());
        }
        if (materialData.getMaterialId() == null && StrUtil.isNotBlank(materialData.getMaterialName()) && ObjectUtil.isEmpty(acceptDataDTO.getPurchaseOrder())) {
            triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.MATERIAL_NAME.value(),
                    MATERIAL_NAME_ONE.subType(), MATERIAL_NAME_ONE.desc());
        }
        // 货物不在材料库预警
        if (materialData.getMaterialId() != null) {
            MaterialDto materialDto = materialServiceProxy.materialById(materialData.getMaterialId());
            if (ObjectUtil.isEmpty(materialDto)) {
                triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.MATERIAL_NAME.value(),
                        MATERIAL_NAME_ONE.subType(), MATERIAL_NAME_ONE.desc());
            }
        }
        // 毛重为负数预警
        if (NumberUtil.isLess(weightGross, BigDecimal.ZERO)) {
            triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.INVALID_WEIGHT.value(),
                    INVALID_WEIGHT_TWO.subType(), INVALID_WEIGHT_TWO.desc());
        }
        // 皮重为负数预警
        if (NumberUtil.isLess(weightTare, BigDecimal.ZERO)) {
            triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.INVALID_WEIGHT.value(),
                    INVALID_WEIGHT_THREE.subType(), INVALID_WEIGHT_THREE.desc());
        }
        // 皮重等于毛重预警
        if (NumberUtil.equals(weightGross, weightTare)) {
            triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.INVALID_WEIGHT.value(),
                    INVALID_WEIGHT_FOUR.subType(), INVALID_WEIGHT_FOUR.desc());
        }
    }


    /**
     * 收料物料数据转换
     *
     * @param
     * @return
     */
    protected void handleReceiveMaterialData(MaterialData materialData) {

//      转换系数 + 结算单位判断
        deviationRateAndSettlementUnitDecision(materialData);

//      毛皮重完整性判断
        boolean flag = materialData.getWeightGross() != null && materialData.getWeightTare() != null && materialData.getWeightGross().compareTo(zero) > 0 && materialData.getWeightTare().compareTo(zero) > 0;
        if (flag) {
            materialData.setIsWeightIntegrality(IsWeightIntegralityEnum.COMPLETE.value());
        } else {
            materialData.setIsWeightIntegrality(IsWeightIntegralityEnum.INCOMPLETE.value());
        }

        boolean isIntegrality = isDataIntegrality(materialData);
        boolean tareJudge = materialData.getWeightTare().compareTo(zero) <= 0;
        boolean grossJudge = materialData.getWeightGross().compareTo(zero) <= 0;
//      完整性判断
        if (isIntegrality) {
//      应收数量判断
            weightSendDecision(materialData);
        } else {
            if (tareJudge) {
                triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.TARE_GROSS_WEIGH.value(),
                        TARE_GROSS_WEIGH_TWO.subType(), TARE_GROSS_WEIGH_TWO.desc());
            } else if (grossJudge) {
                triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.TARE_GROSS_WEIGH.value(),
                        TARE_GROSS_WEIGH_ONE.subType(), TARE_GROSS_WEIGH_ONE.desc());
            }
//          不完整时，实重，净重，实收数量，偏差率全部置为null;扣重，含水率按扣重设置
            normalDeductSet(materialData);
            materialData.setActualCount(null);
            materialData.setWeightActual(null);
            materialData.setWeightNet(null);
            materialData.setDeviationRate(null);
        }
//      负偏差预警
        if (materialData.getDeviationStatus() != null && materialData.getDeviationStatus() == DeviationStatusEnum.NEGATIVEDIFFERENCE.value()) {
            Map<Integer, ContractDecisionDTO> contractDecisionDTOMap = acceptDataDTO.getContractDecisionDTOMap();
            if (CollUtil.isNotEmpty(contractDecisionDTOMap)) {
                ContractDecisionDTO dto = contractDecisionDTOMap.get(materialData.getMaterialId());
                if (ObjectUtil.isNotEmpty(dto)) {
                    String str = StrUtil.format(NEGATIVE_DEVIATION_ONE.desc(), dto.getDeviationFloor(), dto.getDeviationCeiling(), materialData.getDeviationRate());
                    triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.NEGATIVE_DEVIATION.value(),
                            NEGATIVE_DEVIATION_ONE.subType(), str);
                }
            }
        }
    }

    protected boolean isDataIntegrality(MaterialData materialData) {
        boolean isIntegrality = (materialData.getWeightGross().compareTo(zero) > 0) && (materialData.getWeightTare().compareTo(zero) > 0);
        return isIntegrality;
    }

    /**
     * 保存地磅收料
     *
     * @param
     * @return
     */
    protected WeighbridgeAcceptDataDTO saveWeighbridgeReceive() {
        //根据单号查询id
        PurchaseOrder purchaseOrder = acceptDataDTO.getPurchaseOrder();
        //根据单号查询id
        TruckReport truckInfo = acceptDataDTO.getTruckInfo();
        // TODO: 2022/5/30 后期兼容一车多料
        BigDecimal weightGross = acceptDataDTO.getMaterialDataList().get(0).getWeightGross();
        BigDecimal weightTare = acceptDataDTO.getMaterialDataList().get(0).getWeightTare();
        Byte isWebPushed = acceptDataDTO.getIsWebPushed();

        MaterialSendReceive sendReceive = new MaterialSendReceive();

        BeanUtils.copyProperties(acceptDataDTO, sendReceive);
        if (acceptDataDTO.getType() == WeighTypeEnum.RECEIVE.value()) {
            sendReceive.setReceiveMode(acceptDataDTO.getReceiveMode().value());
        }
        sendReceive.setMaterialValidity(acceptDataDTO.getMaterialValidity().value());
        sendReceive.setCompanyId(companyId);
        sendReceive.setProjectId(projectId);
        sendReceive.setExtNo(acceptDataDTO.getExtNo());
        sendReceive.setTypeDetail(acceptDataDTO.getTypeDetail());
        if (truckInfo != null) {
            sendReceive.setTruckId(truckInfo.getId());
        }
        if (purchaseOrder != null) {
            sendReceive.setSupplierId(purchaseOrder.getSupplierId());
            sendReceive.setPurchaseId(purchaseOrder.getId());
            sendReceive.setOrderNo(purchaseOrder.getOrderNo());
        }

        MaterialData dto = materialDataMapper.selectGunByWeighId(acceptDataDTO.getWeighId());
        SendReceiveByWeighIdDTO data = materialDataMapper.selectSendReceiveByWeighId(acceptDataDTO.getWeighId());

        if (ObjectUtil.isNull(isWebPushed)) {
            // 终端
            if (ObjectUtil.isNotNull(dto)) {
                if (ObjectUtil.isNull(dto.getWeightGross())) {
                    // 先照片，后终端
                    sendReceive.setId(dto.getReceiveId());
                    materialSendReceiveService.updateById(sendReceive);
                } else {
                    // 先web,后终端
                    sendReceive.setId(data.getId());
                    sendReceive.setReceiveMode(data.getReceiveMode());
                    sendReceive.setSupplierId(data.getSupplierId());
                    if (acceptDataDTO.getReceiveMode().value() == ReceiveModeEnum.REPORT.value()) {
                        sendReceive.setReceiveMode(ReceiveModeEnum.REPORT.value());
                    }
                    sendReceive.setPurchaseId(data.getPurchaseId());
                    sendReceive.setOrderNo(data.getOrderNo());
                    // 只有当终端的毛皮重相等时，才置为无效，否则信任扫码上传的数据
                    if(weightGross.compareTo(weightTare) == 0){
                        sendReceive.setMaterialValidity(MaterialValidityEnum.INVALID.value());
                    }else {sendReceive.setMaterialValidity(MaterialValidityEnum.VALID.value());}

                    materialSendReceiveService.updateById(sendReceive);
                }
            } else {
                // 库里没数据
                materialSendReceiveService.save(sendReceive);
            }
        } else {
            // web端/小程序
            if ((ObjectUtil.isNotNull(dto) && ObjectUtil.isNull(dto.getWeightGross())) || ObjectUtil.isNull(dto)) {
                if (ObjectUtil.isNotNull(dto)) {sendReceive.setId(dto.getReceiveId());}
                materialSendReceiveService.saveOrUpdate(sendReceive);
            }
        }

        if (CollUtil.isNotEmpty(acceptDataDTO.getMaterialWeightList())) {
            setMaterialWeightReceiveId(companyId, projectId, acceptDataDTO, sendReceive);
        }

        acceptDataDTO.setSendReceiveInfo(sendReceive);

        return acceptDataDTO;
    }

    /**
     * 保存地磅收料
     *
     * @param
     * @return
     */
    protected WeighbridgeAcceptDataDTO saveWeighbridgeMaterialData() {
        String receiverName = acceptDataDTO.getReceiverName();
        Byte isWebPushed = acceptDataDTO.getIsWebPushed();
        Byte isDevice = acceptDataDTO.getIsDevice();
        String enterPic = null;
        String leavePic = null;
        String docPic = null;
        List<String> pics = new ArrayList<>();
        if (CollUtil.isNotEmpty(acceptDataDTO.getEnterPicList())) {
            enterPic = acceptDataDTO.getEnterPicList().stream().collect(Collectors.joining(","));
            pics.addAll(acceptDataDTO.getEnterPicList());
        }
        if (CollUtil.isNotEmpty(acceptDataDTO.getLeavePicList())) {
            leavePic = acceptDataDTO.getLeavePicList().stream().collect(Collectors.joining(","));
            pics.addAll(acceptDataDTO.getLeavePicList());
        }
        if (CollUtil.isNotEmpty(acceptDataDTO.getDocumentPicList())) {
            docPic = acceptDataDTO.getDocumentPicList().stream().collect(Collectors.joining(","));
            pics.addAll(acceptDataDTO.getDocumentPicList());
        }
        if (CollUtil.isNotEmpty(pics)) {
            fileServiceProxy.confirmFiles(pics,"material-management");
        }

        MaterialSendReceive sendReceive = acceptDataDTO.getSendReceiveInfo();
        List<MaterialData> materialDataList = acceptDataDTO.getReceiveMaterialList();

        List<String> dataIdList = new ArrayList<>();
        List<MaterialData> materialListResult = new ArrayList<>();
        if (CollUtil.isNotEmpty(materialDataList)) {
            String finalEnterPic = enterPic;
            String finalLeavePic = leavePic;
            String finalDocPic = docPic;
            materialDataList.forEach(materialData -> {
                MaterialData data = materialDataMapper.selectGunByWeighId(acceptDataDTO.getWeighId());

                materialData.setEnterTime(acceptDataDTO.getEnterTime());
                materialData.setLeaveTime(acceptDataDTO.getLeaveTime());
                materialData.setReceiveTime(acceptDataDTO.getReceiveTime());
                materialData.setIsDevice(isDevice);
                materialData.setPosition(acceptDataDTO.getPosition());

                if (ObjectUtil.isNull(isWebPushed)) {
                    // 终端
                    materialData.setIsMachinePushed((byte) 1);

                    if (ObjectUtil.isNotNull(data)) {
                        if (ObjectUtil.isNull(data.getWeightGross())) {
                            // 先照片，后终端
                            materialData.setId(data.getId());
                            materialData.setReceiveId(sendReceive.getId());
                            materialData.setWeighId(acceptDataDTO.getWeighId());

                            log.info("此次称重数据保存为:{}",materialData);
                            materialDataService.updateById(materialData);
                            if(acceptDataDTO.getType() == WeighTypeEnum.DELIVERY.value()){materialData.setSupplierName(receiverName);}

                            // 保存终端称重原始数据
                            saveOriginalData(materialData);

                            materialListResult.add(materialData);
                        } else {
                            // 先web,后终端
                            String str = "";
                            MaterialData entity = new MaterialData();
                            if (materialData.getReceiveMode() == ReceiveModeEnum.REPORT.value()) {
                                entity.setReceiveMode(ReceiveModeEnum.REPORT.value());
                            }
                            entity.setId(data.getId());
                            if (materialData.getWeightGross().compareTo(materialData.getWeightTare()) == 0) {
                                entity.setMaterialValidity(MaterialValidityEnum.INVALID.value());
                            }
                            // 单位/供应商  终端选的和扫码选的不一致情况
                            entity.setWeightGross(materialData.getWeightGross());
                            entity.setWeightTare(materialData.getWeightTare());
                            entity.setWeightDeduct(materialData.getWeightDeduct());
                            entity.setWeightNet(materialData.getWeightNet());
                            entity.setWeightActual(materialData.getWeightActual());
                            entity.setRatio(data.getRatio());
                            entity.setWeightSend(data.getWeightSend());
                            entity.setEnterPic(finalEnterPic);
                            entity.setLeavePic(finalLeavePic);
                            entity.setDocumentPic(finalDocPic);
                            this.calculation(entity);
                            entity.setMaterialId(data.getMaterialId());
                            entity.setEnterTime(acceptDataDTO.getEnterTime());
                            entity.setLeaveTime(acceptDataDTO.getLeaveTime());
                            entity.setReceiveTime(acceptDataDTO.getReceiveTime());
                            entity.setIsMachinePushed((byte) 1);

                            ContractDecisionDTO thresholdDTO = purchaseContractDetailMapper.selectThreshold(data.getContractDetailId());
                            Map<Integer, ContractDecisionDTO> map = new HashMap<>();
                            map.put(entity.getMaterialId(), thresholdDTO);
                            acceptDataDTO.setContractDecisionDTOMap(map);
                            this.dateDecision(entity);

                            this.warn(str,entity,data);

                            if (StrUtil.isNotBlank(str)) {
                                MaterialRevise materialRevise = new MaterialRevise();
                                materialRevise.setReceiveId(sendReceive.getId());
                                materialRevise.setMaterialDataId(data.getId());
                                materialRevise.setReviseDetail(str);
                                materialRevise.setReviseRemark("终端推送数据");
                                materialReviseService.save(materialRevise);
                            }

                            entity.setUnitPrice(materialData.getUnitPrice());
                            entity.setTotalPrice(materialData.getTotalPrice());
                            entity.setIsWeightIntegrality(materialData.getIsWeightIntegrality());
                            entity.setIsRevise(MaterialReviseEnum.ALREADY_REVISE.value());
                            log.info("此次称重数据保存为:{}",materialData);
                            materialDataService.updateById(entity);

                            dataIdList.add(entity.getId());
                            materialListResult.add(entity);
                        }
                    } else {
                        // 库里没数据
                        materialData.setReceiveId(sendReceive.getId());
                        materialData.setWeighId(acceptDataDTO.getWeighId());
                        materialData.setLeavePic(finalLeavePic);
                        materialData.setEnterPic(finalEnterPic);
                        materialData.setDocumentPic(finalDocPic);
                        if(acceptDataDTO.getType() == WeighTypeEnum.DELIVERY.value()){materialData.setSupplierName(receiverName);}
                        log.info("此次称重数据保存为:{}",materialData);
                        materialDataService.save(materialData);
                        // 保存终端称重原始数据
                        saveOriginalData(materialData);

                        dataIdList.add(materialData.getId());
                        materialListResult.add(materialData);
                    }
                } else {
                    // web端/小程序
                    if ((ObjectUtil.isNotNull(data) && ObjectUtil.isNull(data.getWeightGross())) || ObjectUtil.isNull(data)) {
                        materialData.setReceiveId(sendReceive.getId());
                        materialData.setWeighId(acceptDataDTO.getWeighId());
                        materialData.setEnterPic(finalEnterPic);
                        materialData.setLeavePic(finalLeavePic);
                        materialData.setDocumentPic(finalDocPic);
                        materialData.setRecordId1(acceptDataDTO.getRecordId1());
                        materialData.setRecordId2(acceptDataDTO.getRecordId2());
                        if (acceptDataDTO.getType() == WeighTypeEnum.DELIVERY.value()){materialData.setSupplierName(receiverName);}
                        if (ObjectUtil.isNotNull(data)) {materialData.setId(data.getId());}
                        log.info("此次称重数据保存为:{}",materialData);
                        materialDataService.saveOrUpdate(materialData);
                        // 保存终端称重原始数据
                        saveOriginalData(materialData);

                        dataIdList.add(materialData.getId());
                        materialListResult.add(materialData);
                    }
                }
            });

            acceptDataDTO.setDataIdList(dataIdList);
            acceptDataDTO.setMaterialListResult(materialListResult);

            String preTruckId = acceptDataDTO.getPreTruckReportDetailId();
            if (StrUtil.isNotEmpty(preTruckId)) {
                log.error("preTruckId:{},list:{}", preTruckId, JSONUtil.toJsonStr(materialDataList));

                materialDataList.forEach(e -> {
                    String id = e.getId();
                    preTruckReportDetailService.lambdaUpdate().set(PreTruckReportDetail::getMaterialDataId, id).eq(PreTruckReportDetail::getId, preTruckId).update();
                });
            }
        }

        List<MaterialWeight> materialWeightList = acceptDataDTO.getReceiveMaterialWeightList();
        if (CollUtil.isNotEmpty(materialWeightList)) {
            materialWeightService.saveBatch(materialWeightList);
        }

        return acceptDataDTO;
    }

    private String warn(String str,MaterialData entity,MaterialData data){
        boolean flag1 = entity.getActualReceive() != null && entity.getActualReceive().compareTo(data.getActualReceive()) != 0;
        boolean flag2 = entity.getWeightGross().compareTo(data.getWeightGross()) != 0;
        boolean flag3 = entity.getWeightTare().compareTo(data.getWeightTare()) != 0;
        boolean flag4 = entity.getWeightDeduct().compareTo(data.getWeightDeduct()) != 0;

        if (flag1) {
            str = str + StrUtil.format("终端将实收数量从{}更新为{};", data.getActualReceive(), entity.getActualReceive());
        }
        if (flag2) {
            str = str + StrUtil.format("终端将毛重从{}更新为{};", data.getWeightGross(), entity.getWeightGross());
        }
        if (flag3) {
            str = str + StrUtil.format("终端将皮重从{}更新为{};", data.getWeightTare(), entity.getWeightTare());
        }
        if (flag4) {
            str = str + StrUtil.format("终端将扣重从{}更新为{};", data.getWeightDeduct(), entity.getWeightDeduct());
        }

        return str;
    }

    private void calculation(MaterialData entity){
        entity.setActualCount(NumberUtil.mul(entity.getWeightActual(), entity.getRatio()));
        ThresholdDTO thresholdDTO = materialDataMapper.selectThreshold(entity.getId());
        if (Objects.isNull(entity.getWeightSend()) || entity.getWeightSend().equals(BigDecimal.ZERO)) {
            return;
        }

        BigDecimal deviation = NumberUtil.mul(NumberUtil.div(NumberUtil.sub(entity.getActualCount(), entity.getWeightSend()), entity.getWeightSend(), 4), 100);
        entity.setDeviationRate(deviation);
        BigDecimal deviationCeiling = thresholdDTO.getDeviationCeiling();
        BigDecimal deviationFloor = thresholdDTO.getDeviationFloor();
        int flag;
        int flag1;
        flag = deviation.compareTo(deviationFloor);
        flag1 = deviation.compareTo(deviationCeiling);
        if (flag < 0) {
            entity.setDeviationStatus(DeviationStatusEnum.NEGATIVEDIFFERENCE.value());
        }
        if (flag1 > 0) {
            entity.setDeviationStatus(DeviationStatusEnum.POSITIVEDIFFERENCE.value());
        }
        if (flag >= 0 && flag1 <= 0) {
            entity.setDeviationStatus(DeviationStatusEnum.NORMAL.value());
        }
//      实收数量
        entity.setActualReceive(entity.getActualCount());
        boolean flag2 = entity.getDeviationStatus() == DeviationStatusEnum.POSITIVEDIFFERENCE.value() || entity.getDeviationStatus() == DeviationStatusEnum.NORMAL.value();
        if (flag2) {
            entity.setActualReceive(entity.getWeightSend());
        }
    }

    private void saveOriginalData(MaterialData materialData) {
        MaterialRevise materialRevise = new MaterialRevise();
        materialRevise.setReceiveId(acceptDataDTO.getSendReceiveInfo().getId());
        materialRevise.setMaterialDataId(materialData.getId());
        String str = JSONObject.toJSONString(materialData);
        materialRevise.setOriginalData(str);
        materialRevise.setCompanyId(companyId);
        materialRevise.setProjectId(projectId);
        materialReviseService.save(materialRevise);
    }


    public TruckReport getTruckInfo(String truckNo, Integer companyId, Integer projectId) {
        TruckReport truckReport = truckReportService.gettTruckInfo(projectId, truckNo);
        if (Objects.isNull(truckReport)) {
            truckReport = new TruckReport();
            truckReport.setTruckNo(truckNo);
            truckReport.setCompanyId(companyId);
            truckReport.setProjectId(projectId);
            truckReportService.addTruckReport(truckReport);
        }
        return truckReport;
    }


    protected WeighbridgeAcceptDataDTO getMaterialData(Integer companyId, Integer projectId
            , WeighbridgeAcceptDataDTO acceptDataDTO, PurchaseOrder purchaseOrder) {
        List<MaterialDataDTO> materialDataList = acceptDataDTO.getMaterialDataList();
        if (CollUtil.isEmpty(materialDataList)) {
            return acceptDataDTO;
        }
        String purchaseId = acceptDataDTO.getPurchaseId();
        Map<Integer, PurchaseContractDetail> collectMap = new HashMap<>();
        if (StrUtil.isNotBlank(purchaseId)) {
            List<PurchaseContractDetail> contractDetails = purchaseOrderService.selectMaterialIdInfo(purchaseId, projectId);
            collectMap = contractDetails.stream().collect(Collectors.toMap(PurchaseContractDetail::getMaterialId, e -> e));
        }

        Map<Integer, PurchaseContractDetail> finalCollectMap = collectMap;
        List<MaterialData> list = materialDataList.stream().map(obj -> {
            MaterialData data = new MaterialData();
            BeanUtils.copyProperties(obj, data);
            data.setId(IdUtil.simpleUUID());
            if (purchaseOrder != null) {
                data.setSupplierId(purchaseOrder.getSupplierId());
                data.setPurchaseOrderId(purchaseOrder.getId());
            }
            if (obj.getContractDetailId() != null && purchaseOrder == null) {
                ContractDecisionDTO contractDecisionDTO = purchaseContractDetailMapper.selectThreshold(obj.getContractDetailId());
                if (ObjectUtil.isNotNull(contractDecisionDTO)) {
                    data.setSupplierId(contractDecisionDTO.getSupplierId());
                    data.setSupplierName(contractDecisionDTO.getSupplierName());
                }
            }
            if (ObjectUtil.isEmpty(data.getSupplierId())) {
                data.setSupplierId(acceptDataDTO.getSupplierId());
                data.setSupplierName(acceptDataDTO.getSupplierName());
            }
//          这个判断，只有按合同扫码会进
            if(StrUtil.isNotBlank(data.getContractDetailId())){
                data.setMaterialExist(MaterialExistEnum.OTHER.value());
            }
            data.setCompanyId(companyId);
            data.setProjectId(projectId);
            data.setMaterialName(obj.getMaterial());
            PurchaseContractDetail detail = finalCollectMap.get(obj.getMaterialId());
            if (detail != null) {
                data.setMaterialExist(MaterialExistEnum.YES.value());
                data.setContractDetailId(detail.getId());
            }

            Byte materialExis = data.getMaterialExist();
            if (ObjectUtil.isNotEmpty(acceptDataDTO.getPurchaseOrder()) && obj.getMaterialId() != null && (materialExis == null || materialExis.intValue() != MaterialExistEnum.YES.value())) {
                // 物料在采购单内不存在预警
                triggerWarning(data, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.MATERIAL_NAME.value(),
                        MATERIAL_NAME_TWO.subType(), MATERIAL_NAME_TWO.desc());
            }

            if (obj.getMaterialId() != null) {
                try {
                    MaterialDto materialDto = materialServiceProxy.materialById(obj.getMaterialId());
                    if (materialDto != null) {
                        data.setCategoryId(materialDto.getMaterialCategoryId());
                        data.setCategoryName(materialDto.getMaterialCategoryName());
                        data.setMaterialName(StrUtil.format("{}/{}/{}", materialDto.getMaterialId(), materialDto.getMaterialName(), materialDto.getMaterialSpec()));
                    }
                } catch (Exception e) {
                    log.error("getMaterialData error id:{}", obj.getMaterialId(), e);
                }
            }

//          实收数量
            if (StrUtil.isNotBlank(acceptDataDTO.getActualReceive()) && acceptDataDTO.getType() == WeighTypeEnum.RECEIVE.value()) {
                data.setActualReceive(new BigDecimal(acceptDataDTO.getActualReceive()));
            }
            return data;
        }).collect(Collectors.toList());

        acceptDataDTO.setReceiveMaterialList(list);
        return acceptDataDTO;
    }


    protected WeighbridgeAcceptDataDTO setMaterialWeightReceiveId(Integer companyId, Integer projectId, WeighbridgeAcceptDataDTO acceptDataDTO, MaterialSendReceive sendReceive) {
        List<MaterialWeightDTO> materialWeightList = acceptDataDTO.getMaterialWeightList();
        List<MaterialWeight> weightList = materialWeightList.stream().map(obj -> {
            MaterialWeight weight = new MaterialWeight();
            BeanUtils.copyProperties(obj, weight);
            weight.setDeviceName(obj.getWeighbridgeName());
            weight.setWeightNo((byte)obj.getWeightNo().intValue());
            weight.setReceiveId(sendReceive.getId());
            weight.setCompanyId(companyId);
            weight.setProjectId(projectId);
            return weight;
        }).collect(Collectors.toList());
        acceptDataDTO.setReceiveMaterialWeightList(weightList);
        return acceptDataDTO;
    }

    /**
     * 转换系数 + 结算单位判断
     *
     * @param materialData
     * @return
     */
    private MaterialData deviationRateAndSettlementUnitDecision(MaterialData materialData) {
        boolean unitCheck = StrUtil.isBlank(materialData.getWeightUnit());
        boolean ratioCheck = materialData.getRatio() == null || (zero.compareTo(materialData.getRatio()) >= 0);
        Map<Integer, ContractDecisionDTO> map = acceptDataDTO.getContractDecisionDTOMap();
        ContractDecisionDTO dto = null;
        if (materialData.getMaterialId() != null) {
            dto = map.get(materialData.getMaterialId());
        }

//      结算单位为空
        if (unitCheck && acceptDataDTO.getReceiveModeType() == null) {
            if (CollUtil.isNotEmpty(map) && ObjectUtil.isNotEmpty(dto)) {
                String str = StrUtil.format(CHARGE_UNIT_TWO.desc(), dto.getSettlementUnit(), dto.getSettlementUnit());
                triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.CHARGE_UNIT.value(),
                        CHARGE_UNIT_TWO.subType(), str);
            } else {
                triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.CHARGE_UNIT.value(),
                        CHARGE_UNIT_THREE.subType(), CHARGE_UNIT_THREE.desc());
            }
            materialData.setIsContractUnitUsed(IsContractUnitUsedEnum.YES.value());
        } else {
            materialData.setIsContractUnitUsed(IsContractUnitUsedEnum.NO.value());
        }
//      转换系数为空或负数
        if (ratioCheck && acceptDataDTO.getReceiveModeType() == null) {
            if (CollUtil.isNotEmpty(map) && ObjectUtil.isNotEmpty(dto) && materialData.getRatio() != null) {
                String str = StrUtil.format(WEIGH_CONVERT_ONE.desc(), dto.getConversionRate(), materialData.getRatio(), dto.getConversionRate());
                triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.WEIGH_CONVERT.value(),
                        WEIGH_CONVERT_ONE.subType(), str);
            } else if (CollUtil.isNotEmpty(map) && ObjectUtil.isNotEmpty(dto) && materialData.getRatio() == null) {
                String str = StrUtil.format("合同约定系数为{}，收料时设置系数为空，实际使用系数为{}", dto.getConversionRate(), dto.getConversionRate());
                triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.WEIGH_CONVERT.value(),
                        WEIGH_CONVERT_TWO.subType(), str);
            } else {
                triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.WEIGH_CONVERT.value(),
                        WEIGH_CONVERT_THREE.subType(),  WEIGH_CONVERT_THREE.desc());
            }
            materialData.setIsContractRateUsed(IsContractRateUsedEnum.YES.value());
        } else {
            materialData.setIsContractRateUsed(IsContractRateUsedEnum.NO.value());
        }

        if (materialData.getMaterialId() != null && CollUtil.isNotEmpty(map) && ObjectUtil.isNotEmpty(dto)) {
            // 结算单位
            String settlementUnit = map.get(materialData.getMaterialId()).getSettlementUnit();
            // 转换系数
            BigDecimal conversionRate = map.get(materialData.getMaterialId()).getConversionRate();
            if (acceptDataDTO.getReceiveModeType() != null && acceptDataDTO.getReceiveModeType() == 1 && acceptDataDTO.getIsWebPushed() == null) {
                materialData.setWeightUnit(settlementUnit);
                materialData.setRatio(conversionRate);
            }
//          材料在采购单，上传值不为空，结算单位不一致 || 按合同扫码收料，上传结算单位和合同结算单位不一致
            if (StrUtil.isNotBlank(materialData.getWeightUnit()) && !materialData.getWeightUnit().equals(settlementUnit)) {
                String str = StrUtil.format(CHARGE_UNIT_ONE.desc(), dto.getSettlementUnit(), materialData.getWeightUnit(), materialData.getWeightUnit());
                triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.CHARGE_UNIT.value(),
                        CHARGE_UNIT_ONE.subType(), str);
            }
//          材料在采购单，上传值不为空，结算单位一致 || 按合同扫码收料，上传结算单位和合同结算单位一致
            if (StrUtil.isNotBlank(materialData.getWeightUnit()) && materialData.getWeightUnit().equals(settlementUnit)) {
                materialData.setIsContractUnitUsed(IsContractUnitUsedEnum.YES.value());
            }
            if (unitCheck) {
//              材料在采购单，上传值为空 || 按合同扫码收料，上传结算单位和合同结算单位一致（但不会发生，按合同收料，不会有没有结算单位为空的情况）
                materialData.setWeightUnit(settlementUnit);
            }

//          材料不在采购单，单位、转换系数直取

//          材料在采购单里，转换系数 >0 且 不为null
            if (materialData.getRatio() != null && materialData.getRatio().compareTo(zero) > 0) {
                if (conversionRate.compareTo(materialData.getRatio()) != 0) {
                    String str = StrUtil.format(WEIGH_CONVERT_ONE.desc(), dto.getConversionRate(), materialData.getRatio(), materialData.getRatio());
                    triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.WEIGH_CONVERT.value(),
                            WEIGH_CONVERT_ONE.subType(), str);
                } else {
                    materialData.setIsContractRateUsed(IsContractRateUsedEnum.YES.value());
                }
            }
//          材料在采购单里，转换系数 <=0 或 null
            if (ratioCheck) {
                materialData.setRatio(conversionRate);
                materialData.setIsContractRateUsed(IsContractRateUsedEnum.YES.value());
            }
        } else {
            materialData.setIsContractRateUsed(IsContractRateUsedEnum.NO.value());
        }

        // ocr单个落库：材料不在合同；非本项目合同
        if (ObjectUtil.isNull(dto) && materialData.getMaterialId() != null && acceptDataDTO.getReceiveModeType() != null && acceptDataDTO.getReceiveModeType() == 1) {
            MaterialUnitConversionDto materialUnitDetail = materialService.getMaterialUnitDetail(acceptDataDTO.getCompanyId(),materialData.getMaterialId());
            if (ObjectUtil.isNotNull(materialUnitDetail)) {
                materialData.setWeightUnit(materialUnitDetail.getTargetUnitName());
                materialData.setRatio(materialUnitDetail.getConversionRate());
            }
        }

        return materialData;
    }


    /**
     * 应收数量判断
     *
     * @param materialData
     * @return
     */
    private MaterialData weightSendDecision(MaterialData materialData) {

        if (materialData.getWeightSend() == null || materialData.getWeightSend().compareTo(zero) <= 0) {
            triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_RECEIVE.value(), WarningTypeEnum.RECEIVE_NUMBER.value(),
                    RECEIVE_NUMBER_ONE.subType(), RECEIVE_NUMBER_ONE.desc());
        }
        dateDecision(materialData);

        return materialData;
    }


    /**
     * 计算净重、实重、实际数量、偏差状态、实收数量
     *
     * @param materialData
     * @return
     */
    private MaterialData dateDecision(MaterialData materialData) {
        // 净重
        materialData.setWeightNet(NumberUtil.sub(materialData.getWeightGross(), materialData.getWeightTare()));
        BigDecimal weightActual = NumberUtil.sub(materialData.getWeightNet(), materialData.getWeightDeduct());
        Byte isWebPushed = acceptDataDTO.getIsWebPushed();
        Integer companyId = acceptDataDTO.getCompanyId();
        Integer projectId = acceptDataDTO.getProjectId();
        String deviceSn = acceptDataDTO.getDeviceSn();
        // 实重
        MaterialData data = materialDataMapper.selectGunByWeighId(acceptDataDTO.getWeighId());
        boolean b = ObjectUtil.isNull(isWebPushed) && (ObjectUtil.isNull(data) || (ObjectUtil.isNotNull(data) && ObjectUtil.isNull(data.getWeightGross())));
        if (b) {
            // 只有终端第一次上传才会执行如下 (无data；有data，无毛皮重)
            if (acceptDataDTO.getType() == WeighTypeEnum.RECEIVE.value()) {
                // 只有收料有
                MaterialWeighbridge weighbridge = materialWeighbridgeService.lambdaQuery()
                        .eq(MaterialWeighbridge::getCompanyId, companyId)
                        .eq(MaterialWeighbridge::getProjectId, projectId)
                        .eq(MaterialWeighbridge::getWeighSystemNo, deviceSn)
                        .one();
                if (weighbridge != null && weighbridge.getDeductSet() == 2) {
                    // 含水率取扣重值
                    // 扣重值取0
                    // 重新计算实重
                    // 如果web端已上传，含水率取web端
                    materialData.setMoistureContent(materialData.getWeightDeduct());
                    BigDecimal mul = NumberUtil.mul(materialData.getWeightNet(), NumberUtil.div(NumberUtil.sub(BigDecimal.valueOf(100), materialData.getWeightDeduct()), BigDecimal.valueOf(100)));
                    materialData.setWeightDeduct(BigDecimal.ZERO);
                    weightActual = mul;
                }
            }
        }else {
            if (ObjectUtil.isNotNull(data) && data.getMoistureContent() != null ) {
                materialData.setMoistureContent(data.getMoistureContent());
            }
            if (materialData.getMoistureContent() != null) {
                weightActual = NumberUtil.mul(NumberUtil.sub(1,NumberUtil.div(materialData.getMoistureContent(),100)), NumberUtil.sub(materialData.getWeightNet(), materialData.getWeightDeduct()));
            }
        }
        materialData.setWeightActual(weightActual);
        // 实际数量
        if (materialData.getRatio() != null) {
            materialData.setActualCount(NumberUtil.mul(materialData.getWeightActual(), materialData.getRatio()));
        }
        // 偏差率 + 偏差状态
        boolean flag2 = materialData.getActualCount() != null && materialData.getWeightSend() != null && NumberUtil.isGreater(materialData.getWeightSend(), zero);
        if (flag2) {
            BigDecimal deviation = NumberUtil.mul(NumberUtil.div(NumberUtil.sub(materialData.getActualCount(), materialData.getWeightSend()), materialData.getWeightSend(), 4), 100);
            materialData.setDeviationRate(deviation);
            Map<Integer, ContractDecisionDTO> map = acceptDataDTO.getContractDecisionDTOMap();
            if (materialData.getMaterialId() != null && CollUtil.isNotEmpty(map) && ObjectUtil.isNotEmpty(map.get(materialData.getMaterialId()))) {
                BigDecimal deviationCeiling = map.get(materialData.getMaterialId()).getDeviationCeiling();
                BigDecimal deviationFloor = map.get(materialData.getMaterialId()).getDeviationFloor();

                int flag;
                int flag1;
                flag = deviation.compareTo(deviationFloor);
                flag1 = deviation.compareTo(deviationCeiling);
                if (flag < 0) {
                    materialData.setDeviationStatus(DeviationStatusEnum.NEGATIVEDIFFERENCE.value());
                }
                if (flag1 > 0) {
                    materialData.setDeviationStatus(DeviationStatusEnum.POSITIVEDIFFERENCE.value());
                }
                if (flag >= 0 && flag1 <= 0) {
                    materialData.setDeviationStatus(DeviationStatusEnum.NORMAL.value());
                }
            }
        }
//      实收数量
        if (materialData.getActualCount() != null && materialData.getActualReceive() == null) {
            materialData.setActualReceive(materialData.getActualCount());
            boolean flag = materialData.getDeviationStatus() != null && (materialData.getDeviationStatus() == DeviationStatusEnum.POSITIVEDIFFERENCE.value() || materialData.getDeviationStatus() == DeviationStatusEnum.NORMAL.value());
            if (flag) {
                materialData.setActualReceive(materialData.getWeightSend());
            }
        }

        return materialData;
    }

    /**
     * 触发预警信息
     */
    protected void triggerWarning(MaterialData materialData, Byte warningSource, Byte warningType, Byte warningSubType, String warningInfo) {
        try {
            if (log.isInfoEnabled()) {
                log.info("triggerWarning acceptDataDTO:{}", JSONUtil.toJsonStr(acceptDataDTO));
                log.info("triggerWarning warningSource:{}, warningType:{}, warningInfo:{}, materialData:{}",
                        warningSource, warningType, warningInfo, JSONUtil.toJsonStr(materialData));
            }
            MaterialWarningForm materialWarningForm = new MaterialWarningForm();
            materialWarningForm.setWarningSourceId(materialData.getId());
            materialWarningForm.setWarningSource(warningSource);
            materialWarningForm.setWarningType(warningType);
            materialWarningForm.setWarningSubType(warningSubType);
            materialWarningForm.setWarningInfo(warningInfo);
            materialWarningForm.setSourceProjectId(materialData.getProjectId());
            materialWarningForm.setCompanyId(materialData.getCompanyId());
            materialWarningForm.setWarningSourceNo(acceptDataDTO.getReceiveNo());
            materialWarningForm.setProjectId(materialData.getProjectId());
            materialWarningService.saveMaterialWarning(materialWarningForm);
        } catch (Exception e) {
            log.error("triggerWarning acceptDataDTO:{}", JSONUtil.toJsonStr(acceptDataDTO));
            log.error("triggerWarning error", e);
        }
    }

    public void normalDeductSet(MaterialData materialData) {
        String deviceSn = acceptDataDTO.getDeviceSn();
        if (acceptDataDTO.getType() == WeighTypeEnum.RECEIVE.value()) {
            // 只有收料有
            MaterialWeighbridge weighbridge = materialWeighbridgeService.lambdaQuery()
                    .eq(MaterialWeighbridge::getCompanyId, companyId)
                    .eq(MaterialWeighbridge::getProjectId, projectId)
                    .eq(MaterialWeighbridge::getWeighSystemNo, deviceSn)
                    .one();
            if (weighbridge != null && weighbridge.getDeductSet() == 2) {
                // 含水率取扣重值
                // 扣重值取0
                materialData.setMoistureContent(materialData.getWeightDeduct());
                materialData.setWeightDeduct(BigDecimal.ZERO);
                BigDecimal mul = NumberUtil.mul(NumberUtil.sub(materialData.getWeightGross(),materialData.getWeightTare()), NumberUtil.div(NumberUtil.sub(BigDecimal.valueOf(100), materialData.getMoistureContent()), BigDecimal.valueOf(100)));
                materialData.setWeightActual(mul);
            }
        }
    }
}
