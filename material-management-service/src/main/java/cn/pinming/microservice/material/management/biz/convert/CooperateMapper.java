package cn.pinming.microservice.material.management.biz.convert;

import cn.pinming.microservice.material.management.biz.vo.CooperateVO;
import cn.pinming.microservice.material_unit.api.enterprise.dto.CooperateEnterpriseDto;

import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2021/9/7 1:56 下午
 */
@Mapper
public interface CooperateMapper {

    CooperateMapper INSTANCE = Mappers.getMapper(CooperateMapper.class);

    @Mappings({
        @Mapping(source = "id",target = "id"),
        @Mapping(source = "enterpriseName",target = "name")
    })
    CooperateVO DTO2VO(CooperateEnterpriseDto dto);

    List<CooperateVO> DTO2VOs(List<CooperateEnterpriseDto> dtos);

}
