package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 地磅信息
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("s_material_weighbridge")
@ApiModel(value = "MaterialWeighbridge对象", description = "地磅信息")
public class MaterialWeighbridge implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "地磅系统id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "地磅系统名称")
    @TableField("weigh_system_name")
    private String weighSystemName;

    @ApiModelProperty(value = "地磅供应商")
    @TableField("weigh_supplier")
    private Integer weighSupplier;

    @ApiModelProperty(value = "地磅系统编码")
    @TableField("weigh_system_no")
    private String weighSystemNo;

    @ApiModelProperty(value = "磅点名称")
    @TableField("weigh_point_name")
    private String weighPointName;

    @ApiModelProperty(value = "地磅状态 0 在线 1离线")
    private Byte status;

    @ApiModelProperty(value = "数据上传方式, 1 IOT上传 2 接口上传 ")
    private Byte uploadType;

    @ApiModelProperty(value = "扣重是否使用绝对值(1 是 2 否) (不使用则是百分比)")
    @TableField("deduct_set")
    private Byte deductSet;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
