package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.core.cookie.AuthUser;
import cn.pinming.microservice.material.management.biz.dto.CrccMaterialWeighInfoDto;
import cn.pinming.microservice.material.management.biz.dto.PicDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialSendReceive;
import cn.pinming.microservice.material.management.biz.entity.PurchaseOrder;
import cn.pinming.microservice.material.management.biz.query.*;
import cn.pinming.microservice.material.management.biz.vo.MaterialWeighInfoVO;
import cn.pinming.microservice.material.management.biz.vo.WeighDeviationVO;
import cn.pinming.microservice.material.management.biz.vo.WeighInfoVO;
import cn.pinming.microservice.material.management.biz.vo.WeighbridgeSendVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 收货/发货单 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface IMaterialSendReceiveService extends IService<MaterialSendReceive> {

    /**
     * 地磅收货列表
     *
     * @param query
     * @return
     */
    IPage<MaterialWeighInfoVO> showWeightInfo(MaterialWeighQuery query);

    /**
     * 关联采购单
     *
     * @param receiveId
     * @param orderNo
     * @param user
     */
    void relatePurchaseOrder(String receiveId, String orderNo, AuthUser user);

    /**
     * 过磅情况
     *
     * @param query
     * @return
     */
    WeighInfoVO selectWeighInfo(WeighInfoQuery query);

    /**
     * 过磅情况-更多情况
     *
     * @param query
     * @return
     */
    Map<String,WeighInfoVO> selectWeighInfos(WeighInfoQuery query);

    /**
     * 过磅收料偏差统计
     *
     * @param query
     * @return
     */
    WeighDeviationVO selectWeighDeviation(DeviationInfoQuery query);

    PicDTO checkByWeighId(String weighId);

    /**
     * 地磅发货列表
     *
     * @param query
     * @return
     */
    IPage<WeighbridgeSendVO> selectWeighbridgeSendInfo(WeighbridgeSendQuery query);

    IPage<CrccMaterialWeighInfoDto> extWeightList(Integer projectId, Integer materialId, ztcjExtWeightListQuery query);
}
