package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.query.ReceiveOverviewCardQuery;
import cn.pinming.microservice.material.management.biz.query.ReceiveOverviewModalQuery;
import cn.pinming.microservice.material.management.biz.query.SummaryDeliveryQuery;
import cn.pinming.microservice.material.management.biz.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.biz.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 物资汇总.
 *
 * <AUTHOR>
 * @version 2021/9/6 11:01 上午
 */
public interface ISummaryAnalysisService {

    SummaryAnalysisVO querySummary(SummaryDeliveryQuery query);

    List<CategoryReceiveVO> listReceiveByQuery(SupplierAnalysisQuery query);

    /**
     * 总览-收料总览-大宗材
     * @param query
     * @return
     */
    List<ReceiveOverviewCardVO> receiveOverviewCard(ReceiveOverviewCardQuery query);

    /**
     * 总览-收料总览-其他物料
     * @param query
     * @return
     */
    Map<String, List<ReceiveOverviewHistogram>> receiveOverviewOther(ReceiveOverviewCardQuery query);

    /**
     * 总览-收料总览-柱状图下钻
     * @param query
     * @return
     *  横向柱状图: ReceiveOverviewModalVO
     *  表格:      ReceiveOverviewTableVO
     */
    Map<String, Object> receiveOverviewModal(ReceiveOverviewModalQuery query);

    /**
     * 移动收料总览
     *
     * @param query
     * @return
     */
    List<CategoryReceiveVO> countMobileReceiveNum(SupplierAnalysisQuery query);

    List<ReceiveDeviationVO> listDeviationByQuery(SupplierAnalysisQuery query);

    List<ReceiveDeviationSummaryVO> listDeviationSummaryByQuery(SupplierAnalysisQuery query);

    List<SummaryDeliveryVO> listSummaryDeliveryByQuery(SummaryDeliveryQuery query);

    List<SummaryDeliverySecondVO> listSummaryDeliverySecondByQuery(SummaryDeliveryQuery query);

    List<SummaryWarningVO> listSummaryWarningByQuery(SummaryDeliveryQuery query);


    /**
     * 收料总览下钻
     * @param query SupplierAnalysisQuery
     * @return 下钻数据
     */
    List<ReceiveOverviewSecondVO> listReceiveOverviewSecondByQuery(SummaryDeliveryQuery query, byte type);

    /**
     * 预警总览下钻
     * @param query query
     * @return 下钻详情
     */
    List<WarningSecondVO> listWarningSecondByQuery(SummaryDeliveryQuery query);

    /**
     * 标题数据预警总览下钻
     * @param query query
     * @return 下钻数据
     */
    List<WarningOverviewSecondVO> listWarningOverviewSecondByQuery(SummaryDeliveryQuery query);

    /**
     * 标题偏差总览下钻
     * @param query
     * @return
     */
    List<DeviationOverviewSecondVO> listDeviationOverviewSecondByQuery(SummaryDeliveryQuery query);

    /**
     * 偏差总览下钻
     * @param query
     * @return
     */
    List<DeviationSecondVO> listDeviationSecondByQuery(SupplierAnalysisQuery query);

    /**
     * 偏差总览累计下钻
     * @param query
     * @return
     */
    List<DeviationSecondSummaryVO> listDeviationSecondSummaryByQuery(SupplierAnalysisQuery query);

    /**
     * 预警异常提示
     * @param deptId 部门id
     * @return 预警异常提示
     */
    WarningOverviewTipsVO warningOverviewTip(Integer deptId);
}
