//package cn.pinming.microservice.material.management.biz.convert;
//
//import cn.pinming.microservice.material.management.biz.entity.MaterialSendReceive;
//import java.time.Instant;
//import java.time.LocalDateTime;
//import java.time.ZoneId;
//import org.mapstruct.Mapper;
//import org.mapstruct.Mapping;
//import org.mapstruct.Mappings;
//import org.mapstruct.Named;
//import org.mapstruct.factory.Mappers;
//
///**
// *
// * <AUTHOR>
// * @version 2021/9/9 4:43 下午
// */
//@Mapper
//public interface MaterialSendReceiveMapper {
//
//    MaterialSendReceiveMapper INSTANCE = Mappers.getMapper(MaterialSendReceiveMapper.class);
//
//    @Mappings({
//        @Mapping(source = "driver",target = "driver"),
//        @Mapping(source = "type",target = "type"),
//        @Mapping(source = "enterTime",target = "enterTime",qualifiedByName = "toLocalDateTime"),
//        @Mapping(source = "leaveTime",target = "leaveTime",qualifiedByName = "toLocalDateTime"),
//        @Mapping(source = "materialManager",target = "materialManager"),
//        @Mapping(source = "operator",target = "operator"),
//        @Mapping(source = "receiveNo",target = "receiveNo"),
//        @Mapping(source = "receiver",target = "receiver"),
//        @Mapping(source = "remark",target = "remark")
//    })
//    MaterialSendReceive fromDTO(IotDTO iotDTO);
//
//    @Named("toLocalDateTime")
//    static LocalDateTime toLocalDate(final Long dateMilliseconds) {
//        if (dateMilliseconds == null) {
//            return null;
//        }
//        return Instant.ofEpochMilli(dateMilliseconds).atZone(ZoneId.systemDefault()).toLocalDateTime();
//    }
//
//}
