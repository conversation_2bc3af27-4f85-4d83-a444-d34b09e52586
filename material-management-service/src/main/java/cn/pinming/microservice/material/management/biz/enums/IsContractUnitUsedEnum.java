package cn.pinming.microservice.material.management.biz.enums;

/**
 * 是否使用合同结算单位 枚举
 */
public enum IsContractUnitUsedEnum {
    YES((byte) 1, "是"),
    NO((byte) 2, "否");

    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    IsContractUnitUsedEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }

    public String description() {
        return description;
    }

    public static String desc(Byte value){
        if (value == null) {return null;}
        for (IsContractUnitUsedEnum statusEnum : IsContractUnitUsedEnum.values()) {
            if (statusEnum.value == value) {
                return statusEnum.description;
            }
        }
        return null;
    }
}
