package cn.pinming.microservice.material.management.biz.service;

import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.management.biz.entity.MaterialRevise;
import cn.pinming.microservice.material.management.biz.form.MobileReceiveUpdateForm;

public interface MobileReviseService {

    /**
     * 修订项保存方法
     * @param form
     */
    void revise(MobileReceiveUpdateForm form);

    /**
     * 修订项新增或追加
     * @param materialRevise
     * @param str
     */
    default void isJoin(MaterialRevise materialRevise,String str) {
        if (StrUtil.isBlank(materialRevise.getId())) {
            materialRevise.setReviseDetail(str);
        }else {
            materialRevise.setReviseDetail(StrUtil.format("{}{}",materialRevise.getReviseDetail(),str));
        }
    }
}
