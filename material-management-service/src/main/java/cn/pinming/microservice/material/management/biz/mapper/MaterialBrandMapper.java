package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.entity.MaterialBrand;
import cn.pinming.microservice.material.management.biz.query.PurchaseBrandQuery;
import cn.pinming.microservice.material.management.biz.vo.MaterialBrandVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;

/**
 * <p>
 * 材料品牌 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface MaterialBrandMapper extends BaseMapper<MaterialBrand> {

    List<MaterialBrandVO> selectNameByQuery(PurchaseBrandQuery query);

}
