package cn.pinming.microservice.material.management.biz.controller;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.microservice.material.management.biz.form.HandleAdviceForm;
import cn.pinming.microservice.material.management.biz.form.MaterialWarningForm;
import cn.pinming.microservice.material.management.biz.query.WarningDetailQuery;
import cn.pinming.microservice.material.management.biz.query.WarningInfoQuery;
import cn.pinming.microservice.material.management.biz.service.IMaterialWarningService;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.SuccessResponse;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 预警信息 前端控制器
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 10:46:38
 */
@Api(tags = "物料预警", value = "ly")
@RestController
@RequestMapping("/api/warning")
@AllArgsConstructor
public class MaterialWarningController {

    @Resource
    private IMaterialWarningService materialWarningService;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private ProjectServiceProxy projectServiceProxy;

    @ApiOperation(value = "预警列表", response = WarningInfoVO.class)
    @Log(title = "预警列表", businessType = BusinessType.QUERY)
    @PostMapping("/list")
    public ResponseEntity<Response> list(@RequestBody WarningInfoQuery query) {
        AuthUser user = authUserHolder.getCurrentUser();
        if (user.getCurrentDepartmentId() != null) {
            query.setProjectIds(projectServiceProxy.getAllProjectIdByCompanyDeptId(user.getCurrentCompanyId(), user.getCurrentDepartmentId()));
            if (CollUtil.isEmpty(query.getProjectIds())) {
                return ResponseEntity.ok(new SuccessResponse(new Page<>()));
            }
        }
        IPage<WarningInfoVO> page = materialWarningService.pageListByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "预警处理")
    @Log(title = "预警处理", businessType = BusinessType.INSERTORUPDATE)
    @PostMapping("/handle")
    public ResponseEntity<Response> handle(@RequestBody @Valid @Validated HandleAdviceForm handleAdviceForm) {
        materialWarningService.warningHandle(handleAdviceForm);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "添加预警")
    @Log(title = "添加预警", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public ResponseEntity<Response> addMaterialWarning(@RequestBody @Valid @Validated MaterialWarningForm materialWarningForm) {
        materialWarningService.saveMaterialWarning(materialWarningForm);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "预警状态列表", response = WarningStatusVO.class)
    @Log(title = "预警状态列表", businessType = BusinessType.OTHER)
    @GetMapping("/status/list")
    public ResponseEntity<Response> warningStatusList() {
        List<WarningStatusVO> warningStatusVOList = materialWarningService.listWarningStatus();
        return ResponseEntity.ok(new SuccessResponse(warningStatusVOList));
    }


    @ApiOperation(value = "预警类型列表", response = WarningTypeVO.class)
    @Log(title = "预警类型列表", businessType = BusinessType.OTHER)
    @GetMapping("/type/list")
    public ResponseEntity<Response> warningTypeList() {
        List<WarningTypeVO> warningTypeVOList = materialWarningService.listWarningType();
        return ResponseEntity.ok(new SuccessResponse(warningTypeVOList));
    }

    @ApiOperation(value = "预警来源列表", response = WarningSourceVO.class)
    @Log(title = "预警来源列表", businessType = BusinessType.OTHER)
    @GetMapping("/source/list")
    public ResponseEntity<Response> warningSourceList() {
        List<WarningSourceVO> warningSourceVOList = materialWarningService.listWarningSource();
        return ResponseEntity.ok(new SuccessResponse(warningSourceVOList));
    }

    @ApiOperation(value = "预警详情")
    @Log(title = "预警详情", businessType = BusinessType.QUERY)
    @PostMapping("/detail")
    public ResponseEntity<Response> detail(@RequestBody WarningDetailQuery warningDetailQuery) {
        List<WarningDetailVO> warningDetailVOList = materialWarningService.getWarningDetail(warningDetailQuery);
        return ResponseEntity.ok(new SuccessResponse(warningDetailVOList));
    }
}
