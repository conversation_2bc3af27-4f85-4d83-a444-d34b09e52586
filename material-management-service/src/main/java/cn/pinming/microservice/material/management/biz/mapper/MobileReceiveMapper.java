package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.MobileReceive;
import cn.pinming.microservice.material.management.biz.query.*;
import cn.pinming.microservice.material.management.biz.vo.*;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 移动收料表 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:30:10
 */
public interface MobileReceiveMapper extends BaseMapper<MobileReceive> {

    /**
     * 移动收料卡片列表分页查询
     *
     * @param query
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<MobileReceiveCardVO> selectReceivePage(MobileCardQuery query);

    /**
     * 收料单详情
     *
     * @param receiveId
     * @return
     */
    ReceiveCardDetailVO detail(@Param("receiveId") String receiveId);

    @InterceptorIgnore(tenantLine = "true")
    SummaryAnalysisVO countSummary(@Param("query") SummaryDeliveryQuery query);

    WeighInfoDTO select(MobileWeighInfoQuery query);

    StatisticsDataDTO selectStatistics(DeviationInfoQuery query);

    List<SupplierAnalysisDetailVO> selectSupplierMobileAnalysisPageVO(SupplierAnalysisQuery query);

    SupplierAnalysisVO selectSupplierAnalysisByMobileQuery(SupplierAnalysisQuery query);

    /**
     * 根据收料id获取收料类型
     * @param receiveId
     * @return
     */
    Byte selectReceiveTypeByReceiveId(@Param("receiveId") String receiveId);

    List<WeighTruckChangeDTO> selectMobileList(MobileWeighInfoQuery query);

    Integer selectUsageNum(List<Integer> projectIdList, Integer day);

    List<Integer> selectMobileUsageList(List<Integer> projectIdList, Integer day);

    List<WeighCarDTO> showWeighCar(@Param("query") WeighInfoQuery query);

    List<WeighCarDetailDTO> showWeighCarDetail(@Param("query") WeighInfoQuery query);
}
