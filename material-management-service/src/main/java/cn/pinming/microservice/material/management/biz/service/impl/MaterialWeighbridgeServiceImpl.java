package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.core.common.model.PageList;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.materialManagement.dto.WeighInfoDTO;
import cn.pinming.microservice.material.management.biz.dto.ClientStatusResultDTO;
import cn.pinming.microservice.material.management.biz.dto.ResultDTO;
import cn.pinming.microservice.material.management.biz.dto.SimpleWeighbridgeDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialWeighbridge;
import cn.pinming.microservice.material.management.biz.entity.MaterialWeight;
import cn.pinming.microservice.material.management.biz.entity.ProjectExtCode;
import cn.pinming.microservice.material.management.biz.enums.ClientStatusEnum;
import cn.pinming.microservice.material.management.biz.enums.DeleteEnum;
import cn.pinming.microservice.material.management.biz.form.MaterialWeighbridgeForm;
import cn.pinming.microservice.material.management.biz.mapper.MaterialWeighbridgeMapper;
import cn.pinming.microservice.material.management.biz.query.MaterialWeighbridgeQuery;
import cn.pinming.microservice.material.management.biz.service.IMaterialWeighbridgeService;
import cn.pinming.microservice.material.management.biz.service.IMaterialWeightService;
import cn.pinming.microservice.material.management.biz.service.IProjectExtCodeService;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material.management.proxy.SysDictServiceProxy;
import cn.pinming.v2.company.api.dto.EmployeeDto;
import cn.pinming.v2.company.api.dto.department.DepartmentDto;
import cn.pinming.v2.company.api.service.DepartmentService;
import cn.pinming.v2.project.api.dto.ConstructionProjectDto;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import cn.pinming.zhuang.api.system.dto.SysDictDto;
import cn.pinming.zhuang.api.system.dto.SysDictQueryDto;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 地磅信息 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Slf4j
@Service
public class MaterialWeighbridgeServiceImpl extends ServiceImpl<MaterialWeighbridgeMapper, MaterialWeighbridge> implements IMaterialWeighbridgeService {

    @Resource
    private MaterialWeighbridgeMapper weighbridgeMapper;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;
    @Resource
    private IMaterialWeightService materialWeightService;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private SysDictServiceProxy sysDictServiceProxy;
    @Resource
    private IProjectExtCodeService projectExtCodeService;
    @DubboReference
    private DepartmentService departmentService;
    @NacosValue(value = "${iot.url}", autoRefreshed = true)
    public String iotUrl;

    /**
     * 新增、编辑地磅信息
     *
     * @param form
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateWeighbridge(MaterialWeighbridgeForm form) {
        if (form.getProjectId() == null || form.getProjectId() == 0) {
            throw new BOException(BOExceptionEnum.PROJECTID_CAN_NOT_NULL);
        }
        if (StrUtil.isNotBlank(form.getWeighSystemId())) {
            //编辑
            checkWeighNoExist(form);
            MaterialWeighbridge materialWeighbridge = new MaterialWeighbridge();
            materialWeighbridge.setWeighSystemNo(form.getWeighSystemNo());
            materialWeighbridge.setId(form.getWeighSystemId());
            this.saveOrUpdate(materialWeighbridge);
        } else {
            //接口上传方式 一个项目下 只能存在一个
            //checkApiUploadType(form);
            //新增
            checkWeighNameExist(form);
            checkWeighNoExist(form);
            MaterialWeighbridge materialWeighbridge = new MaterialWeighbridge();
            BeanUtils.copyProperties(form, materialWeighbridge);
            String result = String.join(",", form.getWeighPointList());
            materialWeighbridge.setWeighPointName(result);
            this.save(materialWeighbridge);
        }
    }

//    private void checkApiUploadType(MaterialWeighbridgeForm form) {
//        if (Objects.equals(UpdateTypeEnum.API.getValue(), form.getUploadType())) {
//            int count = this.lambdaQuery().eq(MaterialWeighbridge::getUploadType, form.getUploadType()).eq(MaterialWeighbridge::getCompanyId, form.getCompanyId()).eq(MaterialWeighbridge::getProjectId, form.getProjectId()).count();
//            if (count > 0) {
//                throw new BOException(BOExceptionEnum.API_PUSH_LIMIT);
//            }
//        }
//    }

    /**
     * 删除地磅信息
     *
     * @param user
     * @param weighbridgeId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWeighbridge(AuthUser user, String weighbridgeId) {
        List<MaterialWeight> materialWeights = materialWeightService.lambdaQuery()
                .select(MaterialWeight::getReceiveId)
                .eq(MaterialWeight::getCompanyId, user.getCurrentCompanyId())
                .eq(MaterialWeight::getProjectId, user.getCurrentProjectId())
                .eq(MaterialWeight::getWeighbridgeId, weighbridgeId)
                .list();
        if (CollUtil.isNotEmpty(materialWeights)) {
            throw new BOException(BOExceptionEnum.WEIGH_CAN_NOT_DELETE);
        }
        MaterialWeighbridge weighbridge = this.lambdaQuery()
                .eq(MaterialWeighbridge::getId, weighbridgeId)
                .eq(MaterialWeighbridge::getCompanyId, user.getCurrentCompanyId())
                .one();
        if (ObjectUtil.isEmpty(weighbridge)) {
            throw new BOException(BOExceptionEnum.WEIGHBRIDGE_IS_NOT_EXIST);
        }
        this.lambdaUpdate()
                .eq(MaterialWeighbridge::getId, weighbridgeId)
                .eq(MaterialWeighbridge::getCompanyId, user.getCurrentCompanyId())
                .set(MaterialWeighbridge::getIsDeleted, DeleteEnum.DELETE.value())
                .update(weighbridge);
    }

    /**
     * 显示企业地磅信息
     *
     * @param query
     * @return
     */
    @Override
    public IPage<MaterialWeighbridgeVO> showWeighbridgeInCompany(MaterialWeighbridgeQuery query) {
        PageList<ConstructionProjectDto> projectPage = projectServiceProxy.getProjectsPageByQuery(query);
        List<ConstructionProjectDto> projectList = projectPage.getDataList();
        IPage<MaterialWeighbridgeVO> page = new Page<>();
        if (CollUtil.isEmpty(projectList)) {
            return page;
        }

        List<Integer> projectIds = projectList.stream().map(ConstructionProjectDto::getProjectId).collect(Collectors.toList());
        MaterialWeighbridgeQuery weighbridgeQuery = new MaterialWeighbridgeQuery();
        weighbridgeQuery.setProjectIds(projectIds);
        weighbridgeQuery.setCompanyId(query.getCompanyId());
        List<SimpleWeighbridgeDTO> weighbridgeDTOList = weighbridgeMapper.selectSimpleWeighbridge(weighbridgeQuery);
        Map<Integer, SimpleWeighbridgeDTO> weighbridgeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(weighbridgeDTOList)) {
            weighbridgeMap = weighbridgeDTOList.stream().collect(Collectors.toMap(SimpleWeighbridgeDTO::getProjectId, e -> e));
        }

        List<ProjectExtCode> projectExtCodes = projectExtCodeService.getBaseMapper().selectList(null);
        Map<Integer, ProjectExtCode> extCodeMap = projectExtCodes.stream().collect(Collectors.toMap(ProjectExtCode::getProjectId, e -> e));

        if (CollUtil.isNotEmpty(projectList)) {
            Map<Integer, SimpleWeighbridgeDTO> finalWeighbridgeMap = weighbridgeMap;
            List<MaterialWeighbridgeVO> list = projectList.stream().map(e -> {
                MaterialWeighbridgeVO vo = new MaterialWeighbridgeVO();
                BeanUtils.copyProperties(e, vo);
                if (vo.getDepartmentId() != null) {
                    DepartmentDto department = departmentService.findDepartmentById(vo.getDepartmentId());
                    Optional.ofNullable(department).ifPresent(obj -> vo.setOrganizationName(obj.getDepartmentName()));
                }
                //dubbo 取项目状态
                SysDictQueryDto queryDto = new SysDictQueryDto();
                queryDto.setDictType(6001);
                queryDto.setDictKey("project_type");
                if (e.getProjectType() != null && e.getProjectType() != 0) {
                    queryDto.setDictId(e.getProjectType().toString());
                    SysDictDto result = sysDictServiceProxy.selectSysDict(queryDto);
                    if (ObjectUtil.isNotEmpty(result)) {
                        vo.setProjectType(result.getDictValue());
                    }
                }

                SimpleWeighbridgeDTO weighbridgeDTO = finalWeighbridgeMap.get(e.getProjectId());
                if (weighbridgeDTO != null) {
                    Integer weighbridgeCount = weighbridgeDTO.getWeighbridgeCount();
                    Integer onlineCount = weighbridgeDTO.getOnlineCount();
                    vo.setWeighbridgeCount(weighbridgeCount);
                    if (weighbridgeCount != 0) {
                        if (onlineCount != null && weighbridgeCount != null) {
                            vo.setIsOnline(onlineCount.intValue() == weighbridgeCount.intValue());
                        }
                    }
                }
                ProjectExtCode projectExtCode = extCodeMap.get(e.getProjectId());
                Optional.ofNullable(projectExtCode).ifPresent(obj -> vo.setExtCode(obj.getExtCode()));
                return vo;

            }).collect(Collectors.toList());

            BeanUtils.copyProperties(projectPage, page);
            page.setSize(projectPage.getPageSize());
            page.setCurrent(projectPage.getPageNum());
            page.setRecords(list);
        }
        return page;
    }

    /**
     * 显示项目地磅信息
     *
     * @param query
     * @return
     */
    @Override
    public List<MaterialWeighbridgeVO> showWeighbridgeInProject(MaterialWeighbridgeQuery query) {
        if (query.getProjectId() == null || query.getProjectId() == 0) {
            throw new BOException(BOExceptionEnum.PROJECTID_CAN_NOT_NULL);
        }

        List<MaterialWeighbridgeVO> materialWeighbridgeVOList = new ArrayList<>();

        List<MaterialWeighbridge> materialWeighbridgeList = this.lambdaQuery()
                .eq(MaterialWeighbridge::getCompanyId, query.getCompanyId())
                .eq(MaterialWeighbridge::getProjectId, query.getProjectId())
                .orderByDesc(MaterialWeighbridge::getCreateTime)
                .list();
        if (CollUtil.isNotEmpty(materialWeighbridgeList)) {
            List<ClientStatusResultDTO> clientStatusResultDTOS = queryForClientOnlineStatusList(query.getCompanyId());
            Set<Integer> clientStatusSet = clientStatusResultDTOS.stream().map(ClientStatusResultDTO::getProjectId).collect(Collectors.toSet());
            log.error("clientStatusSet:{}", JSONUtil.toJsonStr(clientStatusSet));
            Map<Integer, WeighbridgeSupplierVO> weighbridgeSupplierMap = getWeighbridgeSupplierMap();
            materialWeighbridgeVOList = materialWeighbridgeList.parallelStream().map(e -> {
                MaterialWeighbridgeVO materialWeighbridgeVO = new MaterialWeighbridgeVO();
                BeanUtils.copyProperties(e, materialWeighbridgeVO);
                WeighbridgeSupplierVO weighbridgeSupplierVO = weighbridgeSupplierMap.get(e.getWeighSupplier());
                Optional.ofNullable(weighbridgeSupplierVO).map(WeighbridgeSupplierVO::getCompanyName).ifPresent(materialWeighbridgeVO::setWeighSupplierName);
                List<String> result = StrUtil.split(e.getWeighPointName(), ",", true, true);
                materialWeighbridgeVO.setWeighCount(result.size());
                log.error("sn:{}", e.getWeighSystemNo());
                materialWeighbridgeVO.setStatus(clientStatusSet.contains(e.getProjectId()) ? (byte) 0 : (byte) 1);
                return materialWeighbridgeVO;
            }).collect(Collectors.toList());

            materialWeighbridgeVOList.forEach(e -> {
                EmployeeDto dto = employeeServiceProxy.findEmployee(query.getCompanyId(), e.getCreateId());
                if (ObjectUtil.isNotEmpty(dto)) {
                    e.setCreateName(dto.getMemberName());
                }
            });
        }
        return materialWeighbridgeVOList;
    }

    @Override
    public List<WeighInfoDTO> selectWeighInfo(Integer companyId, Integer projectId) {
        return this.getBaseMapper().selectWeighInfo(companyId, projectId);
    }

    /**
     * 地磅系统名称查重
     *
     * @param form
     */
    private void checkWeighNameExist(MaterialWeighbridgeForm form) {
//        项目内检查名字是否重复
//        企业内检查编码是否重复
        MaterialWeighbridge weighbridge = this.lambdaQuery()
                .eq(MaterialWeighbridge::getCompanyId, form.getCompanyId())
                .eq(MaterialWeighbridge::getProjectId, form.getProjectId())
                .eq(MaterialWeighbridge::getWeighSystemName, form.getWeighSystemName())
                .one();
        if (ObjectUtil.isNotEmpty(weighbridge)) {
            throw new BOException(BOExceptionEnum.WEIGHSYSTEMNAME_IS_REPEAT);
        }
    }

    private Map<Integer, WeighbridgeSupplierVO> getWeighbridgeSupplierMap() {
        Map<String, Object> requestMap = MapUtil.newHashMap();
        requestMap.put("deviceType", 3023);
        String url = StrUtil.format("{}/zhgd/openSite/api/productization/getDeviceVendorList", iotUrl);
        String resData = HttpUtil.post(url, JSONUtil.toJsonStr(requestMap));
        JSONArray jsonArray = (JSONArray) JSONObject.parseObject(resData).get("data");
        List<WeighbridgeSupplierVO> weighbridgeSupplierVOList = jsonArray.toJavaList(WeighbridgeSupplierVO.class);
        Map<Integer, WeighbridgeSupplierVO> weighbridgeSupplierMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(weighbridgeSupplierVOList)) {
            weighbridgeSupplierMap = weighbridgeSupplierVOList.stream().collect(Collectors.toMap(WeighbridgeSupplierVO::getSupplyId, e -> e));
        }
        return weighbridgeSupplierMap;
    }

    /**
     * 地磅系统编号查重
     *
     * @param form
     */
    private void checkWeighNoExist(MaterialWeighbridgeForm form) {
//        编辑和新增的时候 要判断这个编号是否重复，
        MaterialWeighbridge weighbridge = this.lambdaQuery()
                .eq(MaterialWeighbridge::getCompanyId, form.getCompanyId())
                .eq(MaterialWeighbridge::getWeighSupplier, form.getWeighSupplier())
                .eq(MaterialWeighbridge::getWeighSystemNo, form.getWeighSystemNo())
                .one();
        if (ObjectUtil.isNotEmpty(weighbridge)) {
            SimpleConstructionProjectDto simpleConstructionProjectDto = projectServiceProxy.findSimpleProject(weighbridge.getProjectId());
            throw new BOException("-15", "该地磅正在本企业" + simpleConstructionProjectDto.getProjectTitle() + "项目使用，如需更换地磅关联项目，请先从原项目删除此地磅设备。");
        }
    }

    /**
     * 地磅编辑回显
     *
     * @param user
     * @param id
     * @return
     */
    @Override
    public SimpleWeighBridgeVO showForUpdate(AuthUser user, String id) {
        SimpleWeighBridgeVO simpleWeighBridgeVO = new SimpleWeighBridgeVO();
        MaterialWeighbridge weighbridge = this.lambdaQuery()
                .eq(MaterialWeighbridge::getCompanyId, user.getCurrentCompanyId())
                .eq(MaterialWeighbridge::getProjectId, user.getCurrentProjectId())
                .eq(MaterialWeighbridge::getId, id)
                .one();
        Map<Integer, WeighbridgeSupplierVO> weighbridgeSupplierMap = getWeighbridgeSupplierMap();
        if (ObjectUtil.isNotEmpty(weighbridge)) {
            BeanUtils.copyProperties(weighbridge, simpleWeighBridgeVO);
            WeighbridgeSupplierVO weighbridgeSupplierVO = weighbridgeSupplierMap.get(weighbridge.getWeighSupplier());
            Optional.ofNullable(weighbridgeSupplierVO).map(WeighbridgeSupplierVO::getCompanyName).ifPresent(simpleWeighBridgeVO::setWeighSupplierName);
        } else {
            throw new BOException(BOExceptionEnum.WEIGH_IS_NOT_EXIST);
        }
        return simpleWeighBridgeVO;
    }

    /**
     * 根据项目状态统计地磅数量
     *
     * @param user
     * @return
     */
    @Override
    public SimpleWeighByStatusVO showByStatus(AuthUser user) {
        SimpleWeighByStatusVO vo = new SimpleWeighByStatusVO();
        List<Integer> projectIds = new ArrayList<>();
        if (user.getCurrentDepartmentId() != null) {
            projectIds = projectServiceProxy.getAllProjectIdByCompanyDeptId(user.getCurrentCompanyId(), user.getCurrentDepartmentId());
        } else {
            List<ProjectVO> vos = projectServiceProxy.getAllProjectsByCompanyId(user.getCurrentCompanyId());
            projectIds = vos.stream().map(ProjectVO::getProjectId).collect(Collectors.toList());
        }

        List<SimpleConstructionProjectDto> dto = projectServiceProxy.findProjectsByProjectIds(projectIds);
        Map<Byte, List<SimpleConstructionProjectDto>> map = dto.stream().collect(Collectors.groupingBy(SimpleConstructionProjectDto::getStatus));
        List<WeighAccountByStatusVO> list = new ArrayList<>();
        for (Byte k : map.keySet()) {
            Integer count = map.get(k).size();
            WeighAccountByStatusVO result = new WeighAccountByStatusVO();
            result.setStatus(k);
            result.setAccount(count);
            list.add(result);
        }
        vo.setList(list);
        vo.setProjectAccount(projectIds.size());
        return vo;
    }

    @Override
    public MaterialWeighbridge getWeighbridge(Integer companyId, Integer projectId, String deviceSn) {
        MaterialWeighbridge weighbridge = weighbridgeMapper.selectByDeviceSn(companyId, projectId, deviceSn);
        if (Objects.isNull(weighbridge)) {
            return null;
        }
        return weighbridge;
    }

//    @Override
//    public void updateBridgeStatus(Integer companyId, Integer projectId, String weighSystemNo, byte status) {
//        this.lambdaUpdate().eq(MaterialWeighbridge::getCompanyId, companyId).eq(MaterialWeighbridge::getProjectId, projectId)
//                .eq(MaterialWeighbridge::getWeighSystemNo, weighSystemNo)
//                .eq(MaterialWeighbridge::getStatus, ClientStatusEnum.DOWN.value())
//                .eq(MaterialWeighbridge::getIsDeleted, DeleteEnum.NORMAL.value())
//                .set(MaterialWeighbridge::getStatus, status).update();
//    }
//
//    /**
//     * 地磅客户端状态置为离线,以便心跳包过来刷新状态
//     */
//    @Override
//    public void updateAllBridgeStatus() {
//        this.lambdaUpdate().eq(MaterialWeighbridge::getStatus, ClientStatusEnum.UP.value())
//                .eq(MaterialWeighbridge::getIsDeleted, DeleteEnum.NORMAL.value())
//                .set(MaterialWeighbridge::getStatus, ClientStatusEnum.DOWN.value()).update();
//    }


    @Override
    public Map<Integer, Long> queryForClientOnlineStatus(Integer companyId) {
        List<ClientStatusResultDTO> list = queryForClientOnlineStatusList(companyId);
        if (CollUtil.isNotEmpty(list)) {
            return list.stream().collect(Collectors.groupingBy(ClientStatusResultDTO::getProjectId, Collectors.counting()));
        } else {
            return new HashMap<>();
        }
    }

    @Override
    public long queryForClientOnlineStatus(Integer companyId, List<Integer> projectList) {
        Map<Integer, Long> clientOnlineStatusMap = queryForClientOnlineStatus(companyId);
        long totalOnlineClientAmount = 0;
        for (Integer projectId : projectList) {
            if (clientOnlineStatusMap.containsKey(projectId)) {
                totalOnlineClientAmount += clientOnlineStatusMap.get(projectId);
            }
        }
        return totalOnlineClientAmount;
    }

    @Override
    public List<ClientStatusResultDTO> queryForClientOnlineStatusList(Integer companyId) {
        String result = HttpUtil.get(StrUtil.format("https://zhuang.pinming.cn/material-operation-center/api/test/getOnlineClient?companyId={}", companyId), 5 * 1000);
        if (!JSONUtil.isTypeJSON(result)) {
            throw new BOException(BOExceptionEnum.LOGIC_ERROR.errorCode(), "获取客户端详情接口异常");
        }
        log.error("result:{}", result);
        ResultDTO resultDTO = JSONUtil.toBean(result, ResultDTO.class);
        return JSONUtil.toList(resultDTO.getData(), ClientStatusResultDTO.class);
    }
}