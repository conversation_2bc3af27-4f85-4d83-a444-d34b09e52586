package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.MixingPlantOrderDetailItemDTO;
import cn.pinming.microservice.material.management.biz.entity.MixingPlantOrder;
import cn.pinming.microservice.material.management.biz.vo.*;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 拌合站订单 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-28 15:26:17
 */
@InterceptorIgnore(tenantLine = "true")
public interface MixingPlantOrderMapper extends BaseMapper<MixingPlantOrder> {

    Long plantOrderCount(@Param("status") Byte status, @Param("projectId") Integer projectId);

    List<MixingPlantOrderVO> plantOrderPage(@Param("status") Byte status,
                                            @Param("projectId") Integer projectId,
                                            @Param("idxStart") int idxStart,
                                            @Param("offset") int offset);

    MixingPlantOrderUpdateVO queryOrderUpdateVO(@Param("orderId") String orderId);

    List<MixingPlantOrderDetailUpdateVO> queryOrderDetailUpdateVO(@Param("orderId") String orderId);

    MixingPlantOrderDetailVO queryOrderDetailByOrderId(@Param("orderId") String orderId);

    MixingPlantOrderDetailVO queryOrderDetailByDetailId(@Param("orderDetailId") String orderDetailId);

    List<MixingPlantOrderDetailItemVO> queryOrderDetailItemsById(@Param("orderDetailId") String orderDetailId);

    List<MixingPlantOrderSendDetailVO> queryPlantOrderSendDetailsById(@Param("orderDetailId") String orderDetailId);

    List<MixingPlantOrderStatusCountVO> orderStatusCount(@Param("projectId") Integer projectId);

    List<String> customer(@Param("companyId") Integer companyId, @Param("projectId") Integer projectId);

}
