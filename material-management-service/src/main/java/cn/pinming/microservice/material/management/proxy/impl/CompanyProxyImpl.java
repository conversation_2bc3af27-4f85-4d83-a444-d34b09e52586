package cn.pinming.microservice.material.management.proxy.impl;

import cn.pinming.microservice.material.management.proxy.CompanyProxy;
import cn.pinming.v2.company.api.dto.CompanyDto;
import cn.pinming.v2.company.api.service.CompanyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2022/4/13 09:32
 */
@Slf4j
@Component
public class CompanyProxyImpl implements CompanyProxy {

    @Reference(parameters = {
            "cache.seconds", "3600"
    }, cache = "expiring", mock = "return null")
    private CompanyService companyService;

    @Override
    public CompanyDto findSimpleCompanyInfo(Integer companyId) {
        return companyService.findCompanyById(companyId);
    }

    @Override
    public String findSimpleCompanyName(Integer companyId) {
        CompanyDto companyInfo = findSimpleCompanyInfo(companyId);
        if (Objects.nonNull(companyInfo)) {
            return companyInfo.getCompanyName();
        }
        return null;
    }

    @Override
    public List<CompanyDto> findSimpleCompanyByCompanyList(List<Integer> companyList) {
        return companyService.findCompanyList(companyList);
    }
}
