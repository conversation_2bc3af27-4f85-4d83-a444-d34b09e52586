package cn.pinming.microservice.material.management.biz.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.entity.StatisticsMaterialConfig;
import cn.pinming.microservice.material.management.biz.entity.StatisticsMaterialGroup;
import cn.pinming.microservice.material.management.biz.enums.StatisticsDefaultGroupEnum;
import cn.pinming.microservice.material.management.biz.form.MaterialGroupUpdateForm;
import cn.pinming.microservice.material.management.biz.service.IStatisticsMaterialConfigService;
import cn.pinming.microservice.material.management.biz.service.IStatisticsMaterialGroupService;
import cn.pinming.microservice.material.management.common.SuccessResponse;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.IfBranchUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 统计配置表-项目&物料 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-04-11 13:32:11
 */
@Api(tags = "总览-统计设置-物料分类")
@RestController
@RequestMapping("/api/statistics/config")
public class StatisticsDefaultGroupController {

    @Resource
    private AuthUserHolder authUserHolder;

    @Resource
    private IStatisticsMaterialGroupService materialGroupService;

    @Resource
    private IStatisticsMaterialConfigService materialConfigService;

    @Transactional(rollbackFor = Exception.class)
    @ApiImplicitParam(name = "name", value = "分组名称", required = true, dataType = "String", paramType = "query")
    @PostMapping("/group")
    public ResponseEntity<SuccessResponse> addMaterialGroup(@RequestBody String params) {
        String id = "";
        JSONObject paramsJSONObject = JSONObject.parseObject(params);
        String name = paramsJSONObject.getString("name");
        if (StringUtils.isBlank(name)) {
            return ResponseEntity.ok(new SuccessResponse(false));
        }
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (ObjectUtil.isNotNull(currentUser)) {
            String userid = currentUser.getId();
            // 新增配置数据
            StatisticsMaterialConfig entity = new StatisticsMaterialConfig();
            entity.setName(name);
            materialConfigService.save(entity);
            id = entity.getId();
            // 是否为默认分组
            IfBranchUtil.isTrue(StatisticsDefaultGroupEnum.isDefaultGroup(name)).trueHandle(() -> {
                QueryWrapper<StatisticsMaterialGroup> deleteWrapper = new QueryWrapper<>();
                deleteWrapper.lambda().eq(StatisticsMaterialGroup::getCreateId, userid).eq(StatisticsMaterialGroup::getName, name);
                materialGroupService.remove(deleteWrapper);
            });
        }
        return ResponseEntity.ok(new SuccessResponse(id));
    }

    @Transactional(rollbackFor = Exception.class)
    @PutMapping("/group")
    public ResponseEntity<SuccessResponse> updateMaterialGroup(@RequestBody MaterialGroupUpdateForm form) {
        String oldName = form.getOldName();
        String name = form.getName();
        if (StringUtils.isBlank(name)) {
            return ResponseEntity.ok(new SuccessResponse(false));
        }
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (ObjectUtil.isNotNull(currentUser)) {
            String userid = currentUser.getId();
            // 新名称校验
            QueryWrapper<StatisticsMaterialConfig> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StatisticsMaterialConfig::getName, name).eq(StatisticsMaterialConfig::getCreateId, userid);
            StatisticsMaterialConfig one = materialConfigService.getOne(queryWrapper);
            if (!oldName.equals(name) && ObjectUtil.isNotNull(one)) {
                throw new BOException(BOExceptionEnum.REPETITION_NAME);
            }
            // 旧名称检测
            QueryWrapper<StatisticsMaterialConfig> oldWrapper = new QueryWrapper<>();
            oldWrapper.lambda().eq(StatisticsMaterialConfig::getName, oldName).eq(StatisticsMaterialConfig::getCreateId, userid);
            StatisticsMaterialConfig oldOne = materialConfigService.getOne(oldWrapper);
            if (ObjectUtil.isNotNull(oldOne)) {
                // 修改配置数据
                UpdateWrapper<StatisticsMaterialConfig> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(StatisticsMaterialConfig::getCreateId, userid).eq(StatisticsMaterialConfig::getName, oldName);
                StatisticsMaterialConfig entity = new StatisticsMaterialConfig();
                entity.setName(name);
                materialConfigService.update(entity, updateWrapper);
            } else {
                // 新增配置数据
                StatisticsMaterialConfig entity = new StatisticsMaterialConfig();
                entity.setName(name);
                materialConfigService.save(entity);
            }
            // 是否为默认分组
            IfBranchUtil.isTrue(StatisticsDefaultGroupEnum.isDefaultGroup(oldName)).trueHandle(() -> {
                StatisticsMaterialGroup entity = new StatisticsMaterialGroup();
                entity.setName(oldName);
                materialGroupService.save(entity);
            });
            // 是否为默认分组
            IfBranchUtil.isTrue(StatisticsDefaultGroupEnum.isDefaultGroup(name)).trueHandle(() -> {
                QueryWrapper<StatisticsMaterialGroup> deleteWrapper = new QueryWrapper<>();
                deleteWrapper.lambda().eq(StatisticsMaterialGroup::getCreateId, userid).eq(StatisticsMaterialGroup::getName, name);
                materialGroupService.remove(deleteWrapper);
            });
        }
        return ResponseEntity.ok(new SuccessResponse(true));
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiImplicitParam(name = "name", value = "分组名称", required = true, dataType = "String", paramType = "query")
    @DeleteMapping("/group")
    public ResponseEntity<SuccessResponse> delMaterialGroup(@RequestParam String name) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        IfBranchUtil.isTrue(ObjectUtil.isNotNull(currentUser)).trueHandle(() -> {
            String userid = currentUser.getId();
            // 删除配置数据
            QueryWrapper<StatisticsMaterialConfig> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.lambda().eq(StatisticsMaterialConfig::getCreateId, userid).eq(StatisticsMaterialConfig::getName, name);
            materialConfigService.remove(deleteWrapper);
            // 记录是否删除默认分组
            IfBranchUtil.isTrue(StatisticsDefaultGroupEnum.isDefaultGroup(name)).trueHandle(() -> {
                StatisticsMaterialGroup group = new StatisticsMaterialGroup();
                group.setName(name);
                materialGroupService.save(group);
            });
        });
        return ResponseEntity.ok(new SuccessResponse(true));
    }

}
