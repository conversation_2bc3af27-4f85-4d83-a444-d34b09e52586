package cn.pinming.microservice.material.management.proxy;

import cn.pinming.algModelApi.api.dto.RebarResultDto;
import cn.pinming.microservice.material.management.biz.enums.AiIdentifyEnum;

/**
 * <AUTHOR>
 * @date 2022/2/9
 * @description AI 智能识别业务
 */
public interface AiIdentifyProxy {

    /**
     * 获取业务token
     * @param bizId         业务ID
     * @param ticketId      业务唯一标识
     * @param userId        用户ID
     * @param projectId     项目ID
     * @param timeStamp     时间戳
     * @param companyId     企业ID
     * @return
     */
    String getToken(AiIdentifyEnum bizId, String ticketId, String userId, Integer projectId, Long timeStamp, Integer companyId);

    /**
     * AI 识别业务
     * @param bizId         业务ID
     * @param ticketId      业务唯一标识
     * @param timeStamp     时间戳
     * @return
     */
    RebarResultDto getRebarCalculateResult(AiIdentifyEnum bizId, String ticketId, Long timeStamp);

}
