package cn.pinming.microservice.material.management.common.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/24
 * @description
 */
public class DateUtil {

    private static String YYYY_MM_DD = "yyyy-MM-dd";
    public static DateTimeFormatter DD_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(YYYY_MM_DD);
    private static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static DateTimeFormatter SS_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS);

    public static String yesterdayStr() {
        String yesterday = LocalDateTime.now().minusDays(1).format(DD_DATE_TIME_FORMATTER);
        return yesterday;
    }

    public static String todayStr() {
        String today = LocalDateTime.now().format(DD_DATE_TIME_FORMATTER);
        return today;
    }

    public static String tomorrowStr() {
        String tomorrow = LocalDateTime.now().plusDays(1).format(DD_DATE_TIME_FORMATTER);
        return tomorrow;
    }

    public static LocalDate parseLocalDate(String time) {
        return LocalDate.parse(time, DD_DATE_TIME_FORMATTER);
    }

    public static LocalDateTime parseLocalDateTime(String time) {
        return LocalDateTime.parse(time, SS_DATE_TIME_FORMATTER);
    }

    public static String parseLocalDate(LocalDate time) {
        return time.format(DD_DATE_TIME_FORMATTER);
    }

    public static String parseLocalDateTime(LocalDateTime time, DateTimeFormatter formatter) {
        return time.format(formatter);
    }

    public static List<String> formatTimerShaft(String start, String end) {
        List<String> shafts = Lists.newArrayList();
        if (StringUtils.isBlank(start) || StringUtils.isBlank(end)) {
            return shafts;
        }

        shafts.add(start);
        LocalDate startTime = parseLocalDate(start);
        LocalDate endTime = parseLocalDate(end);

        long days = endTime.toEpochDay() - startTime.toEpochDay();
        for (int i = 1; i <= days; i++) {
            shafts.add(parseLocalDate(startTime.plusDays(i)));
        }
        return shafts;
    }

    @NotNull
    public static List<String> buildDateRange(LocalDate beginTime, LocalDate endTime, String pattern) {
        List<String> res = Lists.newArrayList();
        if (beginTime == null || endTime == null || StringUtils.isBlank(pattern) || beginTime.isAfter(endTime)) {
            return res;
        }
        Date begin = Date.from(beginTime.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        Date end = Date.from(endTime.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        List<DateTime> dateTimes = cn.hutool.core.date.DateUtil.rangeToList(begin, end, DateField.DAY_OF_YEAR);
        res = dateTimes.stream().map(dateTime -> cn.hutool.core.date.DateUtil.format(dateTime, pattern)).collect(Collectors.toList());
        return res;
    }


    public static String parseLocalDateTime(LocalDateTime time) {
        if (ObjectUtil.isNull(time)) {
            return null;
        }
        return time.format(DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
    }


}
