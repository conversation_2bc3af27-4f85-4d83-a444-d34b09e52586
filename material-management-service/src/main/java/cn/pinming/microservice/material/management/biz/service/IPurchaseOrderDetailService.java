package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.dto.PurchaseOrderDetailDTO;
import cn.pinming.microservice.material.management.biz.dto.PurchaseOrderDetailSimpleDTO;
import cn.pinming.microservice.material.management.biz.entity.PurchaseOrderDetail;
import cn.pinming.microservice.material.management.biz.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.biz.vo.GoodsForReviseVO;
import cn.pinming.microservice.material.management.biz.vo.MaterialReceiveVO;
import cn.pinming.microservice.material.management.biz.vo.SupplierAnalysisDetailVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface IPurchaseOrderDetailService extends IService<PurchaseOrderDetail> {

    List<PurchaseOrderDetailDTO> listOrderDetailById(String id);

    BigDecimal getPurchaseAmountByQuery(SupplierAnalysisQuery query);

    List<SupplierAnalysisDetailVO> selectSupplierAnalysisPageVO(SupplierAnalysisQuery query);

    IPage<MaterialReceiveVO> selectSupplierAnalysisDetailPageVO(SupplierAnalysisQuery query);

    IPage<SupplierAnalysisDetailVO> selectSupplierPageVO(SupplierAnalysisQuery query);

    List<GoodsForReviseVO> listForRevise(String purchaseOrderId);

    List<String> queryHistoryUsePartByProjectId(Integer projectId, String remake);

    PurchaseOrderDetailSimpleDTO getPurchaseOrderDetail(String purchaseOrderId, Integer materialId);
}
