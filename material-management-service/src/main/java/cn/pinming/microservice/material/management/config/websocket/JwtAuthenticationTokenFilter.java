package cn.pinming.microservice.material.management.config.websocket;


import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;

/**
 * @ClassName: com.lingdian.tool.filter.JwtAuthenticationTokenFilter
 * @Description: @TODO
 * @Author: Roc.hu
 * @Date: 2023/3/1
 * @Version: 1.0.0
 */
@Component
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Fi<PERSON><PERSON><PERSON><PERSON> filter<PERSON>hain) throws ServletException, IOException {

        filterChain.doFilter(request, response);
        System.out.println(request.getRequestURI());
    }
}
