package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 物料预报备
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-12-28 14:15:47
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_pre_material_report")
@ApiModel(value = "PreMaterialReport对象", description = "物料预报备")
public class PreMaterialReport implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "报备记录id")
    @TableField("pre_weigh_report_id")
    private String preWeighReportId;

    @ApiModelProperty(value = "采购单明细id")
    @TableField("purchase_order_detail_id")
    private String purchaseOrderDetailId;

    @ApiModelProperty(value = "采购合同物料明细id")
    @TableField("contract_detail_id")
    private String contractDetailId;

    @ApiModelProperty(value = "发货品牌")
    private String brand;

    @ApiModelProperty(value = "批次计划发货总量")
    private BigDecimal count;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "称重转换系数")
    @TableField("conversion_rate")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "公司id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
