package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.core.cookie.AuthUser;
import cn.pinming.microservice.material.management.biz.dto.ContractDecisionDTO;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContractDetail;
import cn.pinming.microservice.material.management.biz.entity.PurchaseOrder;
import cn.pinming.microservice.material.management.biz.enums.PurchaseOrderStatusEnum;
import cn.pinming.microservice.material.management.biz.form.PurchaseOrderForm;
import cn.pinming.microservice.material.management.biz.query.PurchaseOrderQuery;
import cn.pinming.microservice.material.management.biz.query.PurchaseQuery;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material_unit.api.material.dto.InfoItem;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 采购单 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface IPurchaseOrderService extends IService<PurchaseOrder> {

    IPage<?> pageListByQuery(PurchaseOrderQuery query);

    String savePurchaseOrder(PurchaseOrderForm form);

    void deletePurchaseOrder(String orderId);

    PurchaseOrderVO queryUpdateDetailById(String id, Integer companyId);

    String parseParameterRequirements(Integer categoryId, String parameterRequirements);

    void updatePurchaseOrder(PurchaseOrderForm form);

    PurchaseOrderVO queryDetailById(String id, Integer companyId);

    void cancelById(String id);

    List<String> listOrderById(Integer companyId, Integer projectId);

    /**
     * 获取采购信息
     *
     * @param orderId
     * @return
     */
    PurchaseSimpleVO selectOrderInfo(String orderId, Integer companyId);

    /**
     * 采购单编号校验
     *
     * @param orderNo,user
     */
    void checkOut(String orderNo, AuthUser user);

    String getPurchaseOrderUrl(String id);

    List<PrePurchaseVO> listPurchase(Integer supplierId);

    PurchaseOrderVO finalDetailById(String id, Integer companyId);


    /**
     * 更新采购单状态为收货中
     *
     * @param purchaseId
     * @param companyId
     * @param projectId
     */
    void updatePurchaseOrderReceiving(String purchaseId, Integer companyId, Integer projectId);

    /**
     * 根据采购单id（采购单编号）查询所属物料信息物料信息
     *
     * @param purchaseId
     * @param projectId
     * @return
     */
    List<PurchaseContractDetail> selectMaterialIdInfo(String purchaseId, Integer projectId);

    PurchaseOrder selectIdByPurchaseNo(String purchaseNo);

    List<ContractDecisionDTO> findDecisionFactor(String purchaseNo);

    List<PurchaseForReviseVO> listForRevise(String contractId);

    PurchaseSimpleVO findByPurNo(String pruNo, Boolean scan);

    PurchaseSimpleVO history();

    /**
     * 更新收货状态
     *
     * @param id     采购单id
     * @param finish PurchaseOrderStatusEnum
     */
    void updateReceiveStatus(String id, PurchaseOrderStatusEnum finish);

    /**
     * 根据采购单id获取收料明细
     *
     * @param id 采购单id
     * @return 收料明细
     */
    List<MaterialDetailVO> getMaterialDetailByOrderId(String id);

    /**
     * 根据采购单id查询是否关联收料记录
     *
     * @param id 采购单id
     * @return 是否关联收料记录
     */
    Boolean receiveDataRelation(String id);

    /**
     * 根据采购单id查询是否关联收料记录
     *
     * @param id 采购单id
     * @return 是否关联收料记录
     */
    Map<String, Boolean> receiveDataRelation(List<String> ids,Integer companyId);

    /**
     * 根据采购单id获取采购单名称
     *
     * @param id 采购单id
     * @return 采购单名称
     */
    String getPurchaseNameById(String id);

    /**
     * 获取打印采购单的url
     *
     * @param id 采购单id
     * @return url
     */
    String getPrintPurchaseOrderUrl(String id);

    /**
     * 当前人上次创建采购单的收货信息
     * @return
     */
    PurchaseOrderReceiveInfoVO lastReceiveInfo();

    /**
     * 根据二级分类ID查询参数要求
     * @param categoryId
     * @return
     */
    List<InfoItem> parameterRequirements(Integer categoryId);

    String qrCode(String purchaseId) throws IOException;

    void check(String code);

//    String qrCodePdf(String purchaseId) throws IOException;

    PurchasePdfVO selectPdf(PurchaseQuery query);

    List<PurchaseOrderDetailVO> getPurchaseOrderDetails(List<String> strings);
}
