package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.entity.ClientLog;
import cn.pinming.microservice.material.management.config.websocket.HeartBeatMessage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * ai对接记录信息 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-01 17:37:10
 */
public interface IClientLogService extends IService<ClientLog> {

    void saveClientStatus(HeartBeatMessage message);

}
