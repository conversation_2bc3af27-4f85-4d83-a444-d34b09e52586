package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.service.ITruckReportService;
import cn.pinming.microservice.material.management.biz.vo.TruckReportVO;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 车辆报备 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-09-06 14:04:33
 */
@Api(tags = "车辆管理-controller",value = "zh")
@RestController
@RequestMapping("api/biz/truck-report")
public class TruckReportController {

    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private ITruckReportService truckReportService;

    @ApiOperation(value = "车辆报备")
    @Log(title = "车辆报备", businessType = BusinessType.INSERT)
    @GetMapping("/add/{truckNo}")
    public ResponseEntity<Response> add(@PathVariable("truckNo")String truckNo){
        AuthUser user = authUserHolder.getCurrentUser();
        truckReportService.add(user,truckNo);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "车辆报备列表",response = TruckReportVO.class)
    @Log(title = "车辆报备列表", businessType = BusinessType.QUERY)
    @GetMapping("/show")
    public ResponseEntity<Response> list(@RequestParam(value = "truckNo",required = false)String truckNo){
        AuthUser user = authUserHolder.getCurrentUser();
        List<TruckReportVO> list = truckReportService.listTruckReport(user,truckNo);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "删除车辆")
    @Log(title = "删除车辆", businessType = BusinessType.DELETE)
    @GetMapping("/delete/{id}")
    public ResponseEntity<Response> del(@PathVariable("id")String id){
        AuthUser user = authUserHolder.getCurrentUser();
        truckReportService.del(user,id);
        return ResponseEntity.ok(new SuccessResponse());
    }




}
