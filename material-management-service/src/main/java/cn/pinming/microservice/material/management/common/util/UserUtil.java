package cn.pinming.microservice.material.management.common.util;

import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2025/5/21 09:25
 */
@Component
public class UserUtil {

    @Resource
    public AuthUserHolder authUserHolder;

    public AuthUser getUser() {
        return authUserHolder.getCurrentUser();
    }

    /**
     * 企业id
     *
     * @return 企业id
     */
    public Integer getCompanyId() {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (currentUser != null) {
            return currentUser.getCurrentCompanyId();
        } else {
            return null;
        }
    }

    /**
     * 项目id
     *
     * @return 项目id
     */
    public Integer getProjectId() {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (currentUser != null) {
            return currentUser.getCurrentProjectId();
        }
        return null;
    }

    /**
     * 用户id
     *
     * @return 用户id
     */
    public String getMid() {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (currentUser != null) {
            return currentUser.getId();
        }
        return null;
    }

    /**
     * 当前用户所属组织id
     *
     * @return 分公司id
     */
    public Integer getDepartmentId() {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (currentUser != null) {
            return currentUser.getCurrentDepartmentId();
        }
        return null;
    }

    public Integer checkInProject() {
        Integer projectId = getProjectId();
        if (projectId == null) {
            throw new BOException(BOExceptionEnum.LOGIC_ERROR.errorCode(), "请切换到项目层");
        }
        return projectId;
    }

    public String userName(){
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (currentUser != null) {
            return currentUser.getMemberName();
        }
        return null;
    }
}
