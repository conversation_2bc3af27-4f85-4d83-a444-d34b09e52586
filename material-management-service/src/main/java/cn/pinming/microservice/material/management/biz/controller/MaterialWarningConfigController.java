package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.web.response.Response;
import cn.pinming.microservice.material.management.biz.form.WarningTypeConfigForm;
import cn.pinming.microservice.material.management.biz.service.IMaterialWarningConfigService;
import cn.pinming.microservice.material.management.common.SuccessResponse;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 预警统计配置 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-08-24 10:27:10
 */
@Api(tags = "物料预警配置", value = "ly")
@RestController
@RequestMapping("/api/warning/config")
public class MaterialWarningConfigController {

    @Resource
    private IMaterialWarningConfigService warningConfigService;

    @ApiOperation(value = "预警配置列表")
    @Log(title = "预警配置列表", businessType = BusinessType.QUERY)
    @GetMapping("/list")
    public ResponseEntity<Response> list() {
        List<String> config = warningConfigService.listWarningConfig();
        return ResponseEntity.ok(new SuccessResponse(config));
    }


    @ApiOperation(value = "添加预警配置")
    @Log(title = "添加预警配置", businessType = BusinessType.QUERY)
    @PostMapping("/save")
    public ResponseEntity<Response> save(@RequestBody @Validated WarningTypeConfigForm warningTypeConfigForm) {
        warningConfigService.saveWarningTypeConfig(warningTypeConfigForm);
        return ResponseEntity.ok(new SuccessResponse());
    }

}
