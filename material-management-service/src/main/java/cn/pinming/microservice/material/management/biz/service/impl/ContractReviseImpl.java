package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.management.biz.entity.MaterialRevise;
import cn.pinming.microservice.material.management.biz.entity.MobileReceive;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContract;
import cn.pinming.microservice.material.management.biz.form.MobileReceiveUpdateForm;
import cn.pinming.microservice.material.management.biz.service.MobileReviseService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 合同修订实现类
 */
@Service(value = "contractRevise")
public class ContractReviseImpl implements MobileReviseService {
    @Resource
    private MobileReceiveServiceImpl mobileReceiveService;
    @Resource
    private PurchaseContractServiceImpl purchaseContractService;
    @Resource
    private MaterialReviseServiceImpl materialReviseService;

    @Override
    public void revise(MobileReceiveUpdateForm form) {
        MobileReceive mobileReceive = mobileReceiveService.lambdaQuery()
                .eq(MobileReceive::getReceiveId, form.getReceiveId())
                .one();
        PurchaseContract contract = purchaseContractService.lambdaQuery()
                .eq(PurchaseContract::getId, form.getContractId())
                .one();
        MaterialRevise materialRevise = materialReviseService.lambdaQuery()
                .eq(MaterialRevise::getMaterialDataId, form.getReceiveId())
                .one();
        // 合同变更
        mobileReceiveService.lambdaUpdate()
                .eq(MobileReceive::getReceiveId,form.getReceiveId())
                .set(MobileReceive::getContractId,form.getContractId())
                .set(MobileReceive::getReceiveType,1)
                .set(MobileReceive::getSupplierId,form.getSupplierId())
                .set(MobileReceive::getIsRevise,1)
                .update();
        // 无合同转变为有合同
        if (StrUtil.isBlank(mobileReceive.getContractId())) {
            MaterialRevise revise = new MaterialRevise();
            revise.setMaterialDataId(form.getReceiveId());
            revise.setType((byte)2);
            revise.setReviseRemark(form.getReviseRemark());
            revise.setId(ObjectUtil.isNotNull(materialRevise) ? materialRevise.getId() : null);
            String str = StrUtil.format("从无合同变更为{};",contract.getName());
            isJoin(revise,str);
            materialReviseService.saveOrUpdate(revise);
        }
    }
}
