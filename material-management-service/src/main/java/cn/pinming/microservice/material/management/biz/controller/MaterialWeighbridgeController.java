package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.form.MaterialWeighbridgeForm;
import cn.pinming.microservice.material.management.biz.query.MaterialWeighbridgeQuery;
import cn.pinming.microservice.material.management.biz.service.IMaterialWeighbridgeService;
import cn.pinming.microservice.material.management.biz.vo.MaterialWeighbridgeVO;
import cn.pinming.microservice.material.management.biz.vo.ProjectVO;
import cn.pinming.microservice.material.management.biz.vo.SimpleWeighBridgeVO;
import cn.pinming.microservice.material.management.biz.vo.SimpleWeighByStatusVO;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 地磅信息 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Api(tags = "地磅信息-controller", value = "zh")
@RestController
@RequestMapping("/api/weighbridge")
public class MaterialWeighbridgeController {

    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private IMaterialWeighbridgeService weighbridgeService;
    @Resource
    private ProjectServiceProxy projectServiceProxy;

    @ApiOperation("新增、编辑地磅")
    @PostMapping("/saveOrUpdate")
    @Log(title = "新增、编辑地磅", businessType = BusinessType.INSERTORUPDATE)
    public ResponseEntity<Response> saveOrUpdateWeighbridge(@Validated @Valid @RequestBody MaterialWeighbridgeForm form) {
        AuthUser user = authUserHolder.getCurrentUser();
        form.setCompanyId(user.getCurrentCompanyId());
        weighbridgeService.saveOrUpdateWeighbridge(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation("删除地磅")
    @GetMapping("/delete/{weighbridgeId}")
    @Log(title = "删除地磅", businessType = BusinessType.DELETE)
    public ResponseEntity<Response> deleteWeighbridge(@PathVariable("weighbridgeId") String weighbridgeId) {
        AuthUser user = authUserHolder.getCurrentUser();
        weighbridgeService.deleteWeighbridge(user, weighbridgeId);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "显示企业地磅信息", response = MaterialWeighbridgeVO.class)
    @Log(title = "显示企业地磅信息", businessType = BusinessType.QUERY)
    @PostMapping("/showInfo")
    public ResponseEntity<Response> showWeighbridgeInCompany(@RequestBody MaterialWeighbridgeQuery query) {
        AuthUser user = authUserHolder.getCurrentUser();
        query.setCompanyId(user.getCurrentCompanyId());
        List<Integer> projectIds = new ArrayList<>();
        if (user.getCurrentDepartmentId() != null) {
            projectIds = projectServiceProxy.getAllProjectIdByCompanyDeptId(user.getCurrentCompanyId(), user.getCurrentDepartmentId());
        } else {
            List<ProjectVO> vos = projectServiceProxy.getAllProjectsByCompanyId(user.getCurrentCompanyId());
            projectIds = vos.stream().map(ProjectVO::getProjectId).collect(Collectors.toList());
        }
        query.setProjectIds(projectIds);
        IPage<MaterialWeighbridgeVO> page = weighbridgeService.showWeighbridgeInCompany(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "显示项目地磅信息", response = MaterialWeighbridgeVO.class)
    @Log(title = "显示项目地磅信息", businessType = BusinessType.QUERY)
    @PostMapping("/show")
    public ResponseEntity<Response> showWeighbridgeInProject(@RequestBody MaterialWeighbridgeQuery query) {
        AuthUser user = authUserHolder.getCurrentUser();
        query.setCompanyId(user.getCurrentCompanyId());
        List<MaterialWeighbridgeVO> materialWeighbridgeVOList = weighbridgeService.showWeighbridgeInProject(query);
        return ResponseEntity.ok(new SuccessResponse(materialWeighbridgeVOList));
    }

    @ApiOperation(value = "编辑地磅数据回显", response = SimpleWeighBridgeVO.class)
    @GetMapping("/showForUpdate/{id}/{projectId}")
    @Log(title = "新增、编辑地磅数据回显", businessType = BusinessType.QUERY)
    public ResponseEntity<Response> showForUpdate(@PathVariable("id") String id, @PathVariable("projectId") Integer projectId) {
        AuthUser user = authUserHolder.getCurrentUser();
        user.setCurrentProjectId(projectId);
        SimpleWeighBridgeVO simpleWeighBridgeVO = weighbridgeService.showForUpdate(user, id);
        return ResponseEntity.ok(new SuccessResponse(simpleWeighBridgeVO));
    }

    @ApiOperation(value = "按项目状态统计项目数量", response = SimpleWeighByStatusVO.class)
    @GetMapping("/showByStatus")
    @Log(title = "按项目状态统计项目数量", businessType = BusinessType.QUERY)
    public ResponseEntity<Response> showByStatus() {
        AuthUser user = authUserHolder.getCurrentUser();
        SimpleWeighByStatusVO vo = weighbridgeService.showByStatus(user);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }


}
