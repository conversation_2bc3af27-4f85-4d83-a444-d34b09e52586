package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.dto.MaterialDataForFY;
import cn.pinming.microservice.material.management.biz.dto.PicDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialData;
import cn.pinming.microservice.material.management.biz.form.PackageForm;
import cn.pinming.microservice.material.management.biz.form.PicForm;
import cn.pinming.microservice.material.management.biz.form.PicUploadForm;
import cn.pinming.microservice.material.management.biz.query.*;
import cn.pinming.microservice.material.management.biz.vo.*;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 收货/发货明细 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:32
 */
public interface IMaterialDataService extends IService<MaterialData> {

    /**
     * 地磅收货明细
     *
     * @param query
     * @return
     */
    WeighReceiveVO showWeightDetail(MaterialWeighQuery query);

    SupplierAnalysisVO getSupplierAnalysisByQuery(SupplierAnalysisQuery query);

    List<SupplierAnalysisDetailVO> getSupplierAnalysisPageVO(SupplierAnalysisQuery query);

    List<String> listUnitByCompanyId(Integer currentCompanyId);

    /**
     * 地磅发货明细
     *
     * @param weighbridgeSendQuery
     * @return
     */
    WeighbridgeSendDetailVO showWeighBridgeSendDetail(WeighbridgeSendQuery weighbridgeSendQuery);

    List<MaterialDataForFY> getDataForFuYang();

    void saveDataForPhoto(PicDTO dto, PicUploadForm form);

    /**
     * 报备收料、临时收料
     *
     * @param query
     * @return
     */
    List<SupplierRankVO> negativeFrequencyRankListByQuery(SupplierRankQuery query);

    /**
     * 移动收料
     *
     * @param query
     * @return
     */
    List<SupplierRankVO> negativeFrequencyMobileRankListByQuery(SupplierRankQuery query);

    /**
     * 全部收料
     *
     * @param query
     * @return
     */
    List<SupplierRankVO> negativeFrequencyAllRankListByQuery(SupplierRankQuery query);

    /**
     * 报备收料、临时收料
     *
     * @param query
     * @return
     */
    List<SupplierRankVO> negativeFrequencyProportionByQuery(SupplierRankQuery query);

    /**
     * 移动收料
     *
     * @param query
     * @return
     */
    List<SupplierRankVO> negativeFrequencyMobileProportionByQuery(SupplierRankQuery query);

    /**
     * 全部收料
     *
     * @param query
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<SupplierRankVO> negativeFrequencyAllProportionByQuery(SupplierRankQuery query);

    /**
     * 扣量次数排行榜
     *
     * @param query
     * @return
     */
    List<SupplierRankVO> deductRankByQuery(SupplierRankQuery query);

    /**
     * 扣量次数占比排行榜
     *
     * @param query
     * @return
     */
    List<SupplierRankVO> deductProportionByQuery(SupplierRankQuery query);

    /**
     * 扣量总量排行榜
     *
     * @param query
     * @return
     */
    List<SupplierRankVO> deductTotalRankByQuery(SupplierRankQuery query);

    /**
     * 扣量总量占比排行榜
     *
     * @param query
     * @return
     */
    List<SupplierRankVO> deductTotalProportionByQuery(SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalRankByQuery(SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalMobileRankByQuery(SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalAllRankByQuery(SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalProportionByQuery(SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalMobileProportionByQuery(SupplierRankQuery query);

    List<SupplierRankDeviationVO> negativeTotalAllProportionByQuery(SupplierRankQuery query);

    Map<String, WeighCarVO> showWeighCar(WeighInfoQuery query);

    void solve();

    void pic(PicForm form);

    /**
     * 获取需要推送数据的id集合
     * @return ids
     */
    List<String> needPushData();

    /**
     * 更新推送成功数据状态
     * @param successIds ids
     * @param status 状态
     */
    void updatePushDataState(List<String> successIds, byte status);

    void clear(List<String> weighIds);

    /**
     * 称重单打印
     * @param query 查询条件
     * @param templateId 模板id
     * @return 生成的模板内容
     */
    String print(MaterialWeighQuery query, String templateId);

    List<MaterialData> algsNeedPushData(Integer companyId, Integer projectId);

    void saveSdk(PackageForm form);

    MaterialReviseDetailVO mobileHistory();

    void delete(String id);

    /**
     *
     * @param type 收发料类型 1：收货；2：发货
     * @param receiveType 1 按合同收料  2 按采购单收料
     * @return
     */
    MaterialReviseDetailVO mobileHistoryByType(Byte type,Byte receiveType);
}
