package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 预警处理人建议表
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 13:21:53
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_warning_handler_advice")
@ApiModel(value = "WarningHandlerAdvice对象", description = "预警处理人建议表")
public class WarningHandlerAdvice implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "处理人建议id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "预警id")
    @TableField("warning_id")
    private String warningId;

    @ApiModelProperty(value = "处理人id")
    @TableField("handler_id")
    private String handlerId;

    @ApiModelProperty(value = "处理人姓名")
    @TableField("handler_name")
    private String handlerName;

    @ApiModelProperty(value = "处理人建议")
    @TableField("handler_advice")
    private String handlerAdvice;

    @ApiModelProperty(value = "处理时间")
    @TableField("handler_time")
    private LocalDateTime handlerTime;

    @ApiModelProperty(value = "预警状态: 1 待处理,2 已处理")
    @TableField("warning_status")
    private Byte warningStatus;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
