package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * AI智能识别记录表
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-02-08 17:15:47
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("s_material_ai_identify_record")
@ApiModel(value = "MaterialAiIdentifyRecord对象", description = "AI智能识别记录表")
public class MaterialAiIdentifyRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "用户id")
    @TableField("user_id")
    private String userId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "业务id")
    @TableField("biz_id")
    private String bizId;

    @ApiModelProperty(value = "业务唯一编号")
    @TableField("ticket_id")
    private String ticketId;

    @ApiModelProperty(value = "token请求时间戳")
    @TableField("token_time_stamp")
    private Long tokenTimeStamp;

    @ApiModelProperty(value = "业务Token")
    private String token;

    @ApiModelProperty(value = "结果请求时间戳")
    @TableField("result_time_stamp")
    private Long resultTimeStamp;


}
