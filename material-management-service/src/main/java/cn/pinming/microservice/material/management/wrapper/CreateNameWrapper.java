package cn.pinming.microservice.material.management.wrapper;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.management.proxy.EmployeeServiceProxy;
import cn.pinming.v2.company.api.dto.EmployeeDetailDto;
import cn.pinming.v2.company.api.dto.EmployeeDetailQueryDto;
import cn.pinming.weaponx.wrapper.dto.MemberDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class CreateNameWrapper {
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;

    public void wrap(List<? extends MemberDTO> list, @NotNull Integer companyId) {
        if (CollUtil.isNotEmpty(list)) {
            EmployeeDetailQueryDto queryDto = new EmployeeDetailQueryDto();
            List<String> createIds = list.stream().map(MemberDTO::getCreateId).distinct().collect(Collectors.toList());
            queryDto.setCompanyId(companyId);
            queryDto.setMemberIdList(createIds);
            List<EmployeeDetailDto> employeeDetailDtos = employeeServiceProxy.employeeList(queryDto);
            if (CollUtil.isNotEmpty(employeeDetailDtos)) {
                Map<String, String> collect = employeeDetailDtos.stream().collect(Collectors.toMap(EmployeeDetailDto::getMemberId, EmployeeDetailDto::getMemberName));
                list.forEach(PurchaseOrderDTO -> PurchaseOrderDTO.setCreateName(collect.get(PurchaseOrderDTO.getCreateId())));
            }
        }
    }

    public void wrap(MemberDTO obj, @NotNull Integer companyId) {
        wrap(Collections.singletonList(obj), companyId);
    }

    public void wrap(Page<? extends MemberDTO> page, Integer companyId) {
        if (null != page && !CollectionUtils.isEmpty(page.getRecords())) {
            List list = page.getRecords();
            wrap(list,companyId);
            page.setRecords(list);
        }

    }
}
