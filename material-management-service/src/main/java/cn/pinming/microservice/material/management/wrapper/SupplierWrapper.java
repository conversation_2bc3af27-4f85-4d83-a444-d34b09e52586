package cn.pinming.microservice.material.management.wrapper;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.management.biz.dto.SimpleSupplierDTO;
import cn.pinming.microservice.material.management.biz.vo.CooperateVO;
import cn.pinming.microservice.material.management.proxy.CooperateServiceProxy;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2021/9/8 10:35 上午
 */
@Component
public class SupplierWrapper {

    @Resource
    private CooperateServiceProxy cooperateServiceProxy;

    public void wrap(List<? extends SimpleSupplierDTO> list, @NotNull Integer companyId) {
        if (CollUtil.isNotEmpty(list)) {
            String supplierIds = list.stream().map(SimpleSupplierDTO::getSupplierId).distinct().map(String::valueOf).collect(Collectors.joining(","));
            List<CooperateVO> cooperateList = cooperateServiceProxy.findCooperateByIds(companyId, supplierIds);
            if (CollUtil.isNotEmpty(cooperateList)) {
                Map<Integer, String> collect = cooperateList.stream().collect(Collectors.toMap(CooperateVO::getId, CooperateVO::getName));
                list.forEach(SimpleSupplierDTO -> SimpleSupplierDTO.setSupplierTitle(collect.get(SimpleSupplierDTO.getSupplierId())));
            }
        }
    }

    public void wrap(SimpleSupplierDTO obj, @NotNull Integer companyId) {
        wrap(Collections.singletonList(obj), companyId);
    }

}
