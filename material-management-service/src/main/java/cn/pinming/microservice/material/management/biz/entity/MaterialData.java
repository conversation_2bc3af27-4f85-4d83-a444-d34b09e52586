package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 收货/发货明细
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-04 10:19:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_material_data")
@ApiModel(value = "MaterialData对象", description = "收货/发货明细")
public class MaterialData implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "明细ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "收货单ID")
    @TableField("receive_id")
    private String receiveId;

    @ApiModelProperty(value = "1 临时收料，2 报备收料，3 无归属收料")
    @TableField("receive_mode")
    private Byte receiveMode;

    @ApiModelProperty(value = "1 有效，2 无效")
    @TableField("material_validity")
    private Byte materialValidity;

    @ApiModelProperty(value = "采购单id")
    @TableField("purchase_order_id")
    private String purchaseOrderId;

    @ApiModelProperty(value = "合同明细id")
    @TableField("contract_detail_id")
    private String contractDetailId;

    @ApiModelProperty(value = "供应商id")
    @TableField("supplier_id")
    private Integer supplierId;

    @ApiModelProperty(value = "供应商名称")
    @TableField("supplier_name")
    private String supplierName;

    @ApiModelProperty(value = "材料ID")
    @TableField("material_id")
    private Integer materialId;

    @ApiModelProperty(value = "材料名称")
    @TableField("material_name")
    private String materialName;

    @ApiModelProperty(value = "二级分类id")
    @TableField("category_id")
    private Integer categoryId;

    @ApiModelProperty(value = "二级分类名称")
    @TableField("category_name")
    private String categoryName;

    @ApiModelProperty(value = "结算单位")
    @TableField("weight_unit")
    private String weightUnit;

    @ApiModelProperty(value = "面单应收量：发货数量")
    @TableField("weight_send")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "毛重")
    @TableField("weight_gross")
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重")
    @TableField("weight_tare")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "扣重")
    @TableField("weight_deduct")
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "净重")
    @TableField("weight_net")
    private BigDecimal weightNet;

    @ApiModelProperty(value = "含水率")
    @TableField("moisture_content")
    private BigDecimal moistureContent;

    @ApiModelProperty(value = "实重")
    @TableField("weight_actual")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "换算系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "实际数量：实重 * 换算系数")
    @TableField("actual_count")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "实收数量")
    @TableField("actual_receive")
    private BigDecimal actualReceive;

    @ApiModelProperty(value = "单价")
    @TableField("unit_price")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "金额")
    @TableField("total_price")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "偏差率")
    @TableField("deviation_rate")
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "偏差状态 0 正常 1 负偏差 2 正偏差 ")
    @TableField("deviation_status")
    private Byte deviationStatus;

    @ApiModelProperty(value = "毛皮重完整性 1，完整 2，不完整")
    @TableField("is_weight_integrality")
    private Byte isWeightIntegrality;

    @ApiModelProperty(value = "是否使用合同转换系数 1，是 2，否")
    @TableField("is_contract_rate_used")
    private Byte isContractRateUsed;

    @ApiModelProperty(value = "是否使用合同结算单位 1，是 2，否")
    @TableField("is_contract_unit_used")
    private Byte isContractUnitUsed;

    @ApiModelProperty(value = "材料id存在采购单中的状态: 1 存在 2 不存在")
    @TableField("material_exist")
    private Byte materialExist;

    @ApiModelProperty(value = "对账id")
    @TableField("reconciliation_id")
    private String reconciliationId;

    @ApiModelProperty(value = "基石数据id1")
    @TableField("record_id_1")
    private String recordId1;

    @ApiModelProperty(value = "基石数据id2")
    @TableField("record_id_2")
    private String recordId2;

    @ApiModelProperty(value = "是否修订: 1 是 2 否")
    @TableField("is_revise")
    private Byte isRevise;

    @ApiModelProperty(value = "是否为自研终端数据 1 否 2 是")
    @TableField("is_device")
    private Byte isDevice;

    @ApiModelProperty(value = "终端是否推送过 1 是 仅用于幂等")
    @TableField("is_machine_pushed")
    private Byte isMachinePushed;

    @ApiModelProperty(value = "是否推送至仓库 0,否 1,是")
    @TableField("is_pushed")
    private Byte isPushed;

    @ApiModelProperty(value = "终端称重记录id")
    private String weighId;

    @ApiModelProperty(value = "高拍仪单据照片")
    @TableField("document_pic")
    private String documentPic;

    @ApiModelProperty(value = "进场时间")
    @TableField("enter_time")
    private LocalDateTime enterTime;

    @ApiModelProperty(value = "进场图片")
    @TableField("enter_pic")
    private String enterPic;

    @ApiModelProperty(value = "出场时间")
    @TableField("leave_time")
    private LocalDateTime leaveTime;

    @ApiModelProperty(value = "出场图片")
    @TableField("leave_pic")
    private String leavePic;

    @ApiModelProperty(value = "收货/发货时间")
    @TableField("receive_time")
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "计划使用部位")
    @TableField("position")
    private String position;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;

    @ApiModelProperty(value = "push state")
    private Byte pushState;

    @ApiModelProperty(value = "收料确认方式 1 扫码组装 2 司机自助确认 3 OCR结果落库")
    private Integer confirmType;

    /**
     * 运单货物备注 可为空
     */
    private String remark;

    private Integer mixingType;

    @ApiModelProperty(value = "签名人照片")
    private String signerPic;

    @ApiModelProperty(value = "签名照片")
    private String signaturePic;
}
