package cn.pinming.microservice.material.management.proxy.impl;

import cn.pinming.algModelApi.api.dto.CaculateResultRequestDto;
import cn.pinming.algModelApi.api.dto.RebarResultDto;
import cn.pinming.algModelApi.api.dto.TokenRequestDto;
import cn.pinming.algModelApi.api.service.CaculateService;
import cn.pinming.algModelApi.api.service.TokenService;
import cn.pinming.microservice.material.management.biz.enums.AiIdentifyEnum;
import cn.pinming.microservice.material.management.proxy.AiIdentifyProxy;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/2/9
 * @description
 */
@Service
public class AiIdentifyProxyImpl implements AiIdentifyProxy {

    @Reference()
    private TokenService tokenService;

    @Reference
    private CaculateService caculateService;

    @Override
    public String getToken(AiIdentifyEnum bizId, String ticketId, String userId, Integer projectId, Long timeStamp, Integer companyId) {
        TokenRequestDto tokenRequestDto = new TokenRequestDto();
        tokenRequestDto.setBizId(bizId.getCode());
        tokenRequestDto.setTicketId(ticketId);
        tokenRequestDto.setUserId(userId);
        tokenRequestDto.setProjectId(projectId);
        tokenRequestDto.setTimeStamp(timeStamp);
        tokenRequestDto.setCompanyId(companyId);
        String token = tokenService.getToken(tokenRequestDto);
        return token;
    }

    @Override
    public RebarResultDto getRebarCalculateResult(AiIdentifyEnum bizId, String ticketId, Long timeStamp) {
        CaculateResultRequestDto caculateResultRequestDto = new CaculateResultRequestDto();
        caculateResultRequestDto.setBizId(bizId.getCode());
        caculateResultRequestDto.setTicketId(ticketId);
        caculateResultRequestDto.setTimeStamp(timeStamp);
        RebarResultDto rebarCalculateResult = caculateService.getRebarCaculateResult(caculateResultRequestDto);
        return rebarCalculateResult;
    }
}
