package cn.pinming.microservice.material.management.biz.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@Data
public class MobileReceiveMaterialForm {

    @ApiModelProperty(value = "材料id")
    @NotNull(message = "材料id不能为空")
    private Integer materialId;

    @ApiModelProperty(value = "采购单材料备注")
    private String remark;

    @ApiModelProperty(value = "采购单材料品牌")
    private String purchaseBrand;

    @ApiModelProperty(value = "偏差率")
    @Digits(fraction = 2,message = "偏差率小数点上限为2位", integer = 999)
    private BigDecimal deviationRate;

    @ApiModelProperty(value = "偏差状态 0 正常 1 负偏差 2 正偏差 ")
    private Byte deviationStatus;

    @ApiModelProperty(value = "面单应收总件数")
    @Min(value = 0,message = "面单应收总件数必须为正整数")
    private Integer sendNumber;

    @ApiModelProperty(value = "面单应收总含量")
    @Digits(fraction = 6,message = "面单应收总含量小数点上限为6位", integer = 999)
    @DecimalMin(value = "0",message = "面单应收总含量必须为正数")
    private BigDecimal sendContent;

    @ApiModelProperty(value = "面单应收结算合计")
    @DecimalMin(value = "0",message = "面单应收结算合计必须为正数")
    @Digits(fraction = 6,message = "面单应收结算合计小数点上限为6位", integer = 999)
    private BigDecimal sendSettlementTotal;

    @ApiModelProperty(value = "实际收料总件数")
//    @NotNull(message = "实际收料总件数不能为空")
    private Integer actualNumber;

    @ApiModelProperty(value = "实际收料总含量")
    @Digits(fraction = 6,message = "实际收料总含量小数点上限为6位", integer = 999)
    @DecimalMin(value = "0",message = "实际收料总含量必须为正数")
    private BigDecimal actualContent;

    @ApiModelProperty(value = "实际收料结算合计")
    @Digits(fraction = 6,message = "实际收料结算合计小数点上限为6位", integer = 999)
    @DecimalMin(value = "0",message = "实际收料结算合计必须为正数")
    private BigDecimal actualSettlementTotal;

    @ApiModelProperty(value = "实重合计")
    @Digits(fraction = 3,message = "实重合计小数点上限为3位", integer = 999)
    @DecimalMin(value = "0",message = "实重合计必须为正数")
    private BigDecimal actualWeightTotal;

    @ApiModelProperty(value = "理重合计")
    @Digits(fraction = 3,message = "理重合计小数点上限为3位", integer = 999)
    @DecimalMin(value = "0",message = "理重合计必须为正数")
    private BigDecimal theoreticalWeightTotal;

    @ApiModelProperty(value = "实理偏差值")
    @Digits(fraction = 3,message = "实理偏差值小数点上限为3位", integer = 999)
    private BigDecimal deviationValueTotal;

    @ApiModelProperty(value = "实理偏差率")
    @Digits(fraction = 2,message = "实理偏差率小数点上限为2位", integer = 999)
    private BigDecimal deviationRateTotal;

    @ApiModelProperty(value = "含量单位")
//    @NotBlank(message = "含量单位不能为空")
    private String unit;

    @ApiModelProperty(value = "结算单位")
//    @NotBlank(message = "结算单位不能为空")
    private String settlementUnit;

    @ApiModelProperty(value = "到场品牌,多个以,分隔")
    private String brand;

    @ApiModelProperty(value = "状态快照 1,实重 2,理重 null,无理重展示")
    private Byte kind;

    @ApiModelProperty(value = "收货材料清单")
    @Valid
    private List<MobileReceiveMaterialDetailForm> detailList;

    @ApiModelProperty(value = "约定偏差阈值上限")
    private BigDecimal deviationCeiling;

    @ApiModelProperty(value = "约定偏差阈值下限")
    private BigDecimal deviationFloor;
}
