package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.entity.*;
import cn.pinming.microservice.material.management.biz.enums.*;
import cn.pinming.microservice.material.management.biz.form.AdditionalRecordForm;
import cn.pinming.microservice.material.management.biz.form.MaterialWarningForm;
import cn.pinming.microservice.material.management.biz.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.MaterialWeighCalculateUtil;
import cn.pinming.microservice.material.management.common.util.NoUtil;
import cn.pinming.microservice.material.management.common.util.WarningUtil;
import cn.pinming.microservice.material.management.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.material.management.proxy.FileServiceProxy;
import cn.pinming.v2.company.api.dto.EmployeeDto;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AdditionalRecordServiceImpl extends ServiceImpl<MaterialDataMapper, MaterialData> implements IAdditionalRecordSerivce {
    @Resource
    private MaterialWeighCalculateUtil materialWeighCalculateUtil;
    @Resource
    private IMaterialSendReceiveService materialSendReceiveService;
    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private WarningUtil warningUtil;
    @Resource
    private IPurchaseContractDetailService purchaseContractDetailService;
    @Resource
    private IMaterialVerifyService materialVerifyService;
    @Resource
    private IMaterialVerifyRelationService materialVerifyRelationService;
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private NoUtil noUtil;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void additionalRecord(AdditionalRecordForm form) {
        if (form.getReviceType() != null && form.getReviceType() == 3) {
            //无合同收料
            form.setPurchaseOrderId(null);
            form.setOrderNo(null);
            form.setContractDetailId(null);
        }
        // 校验
        check(form);
        // 合同信息
        PurchaseContractDetail contractDetail = null;
        if (StrUtil.isNotBlank(form.getContractDetailId())) {
            contractDetail = purchaseContractDetailService.lambdaQuery()
                    .eq(PurchaseContractDetail::getId, form.getContractDetailId())
                    .one();
        }
        // 计算
        materialWeighCalculateUtil.calculate(form,contractDetail);
        // 保存单据
        MaterialSendReceive materialSendReceive = getMaterialSendReceive(form);
        materialSendReceiveService.save(materialSendReceive);
        // 保存明细
        MaterialData materialData = getMaterialData(form,contractDetail);
        materialData.setReceiveId(materialSendReceive.getId());
        materialDataService.save(materialData);
        // 预警
        warning(form,materialData,materialSendReceive,contractDetail);
        // 收料且需对账
        if (form.getType().equals(WeighTypeEnum.RECEIVE.value()) && ObjectUtil.isNotNull(contractDetail) && form.getIsAutoVerify() == (byte)1) {
            MaterialVerifyRelation materialVerifyRelation = getMaterialVerify(materialData);
            materialVerifyRelationService.save(materialVerifyRelation);
        }
    }

    private void check(AdditionalRecordForm form) {
        boolean flag = form.getWeightGross().compareTo(form.getWeightTare()) <= 0;
        if (flag) {
            throw new BOException(BOExceptionEnum.TARE_OR_GROSS_IS_ERROR);
        }
    }

    private MaterialSendReceive getMaterialSendReceive(AdditionalRecordForm form) {
        AuthUser user = authUserHolder.getCurrentUser();
        MaterialSendReceive materialSendReceive = new MaterialSendReceive();
        BeanUtils.copyProperties(form,materialSendReceive);
        materialSendReceive.setId(IdUtil.fastSimpleUUID());
        materialSendReceive.setMaterialValidity(MaterialValidityEnum.VALID.value());
        materialSendReceive.setReceiveTime(LocalDateTime.now());
        materialSendReceive.setRemark(form.getReviseRemark());
        materialSendReceive.setIsAddition(AdditionalRecordEnum.YES.value());
        materialSendReceive.setReceiveNo(noUtil.getWeighNo(user.getCurrentProjectId()));
        EmployeeDto employee = employeeServiceProxy.findEmployee(user.getCurrentCompanyId(), user.getId());
        if (ObjectUtil.isNotNull(employee)) {
            materialSendReceive.setReceiver(employee.getMemberName());
        }

        // 收料
        if (form.getType().equals(WeighTypeEnum.RECEIVE.value())) {
            if (form.getReviceType() != null && form.getReviceType() == 3) {
                //无合同收料
                materialSendReceive.setReceiveMode(ReceiveModeEnum.UNBELOGN.value());
            } else {
                materialSendReceive.setReceiveMode(ReceiveModeEnum.TEMPORARY.value());
                materialSendReceive.setPurchaseId(form.getPurchaseOrderId());
            }
        }

        return materialSendReceive;
    }

    private MaterialData getMaterialData(AdditionalRecordForm form,PurchaseContractDetail contractDetail) {
        MaterialData materialData = new MaterialData();
        BeanUtils.copyProperties(form,materialData);
        materialData.setMaterialValidity(MaterialValidityEnum.VALID.value());
        materialData.setIsWeightIntegrality(IsWeightIntegralityEnum.COMPLETE.value());
        materialData.setWeighId(IdUtil.fastSimpleUUID());
        materialData.setReceiveTime(form.getEnterTime());
        materialData.setPosition(form.getPosition());
        if (StrUtil.isNotBlank(form.getDocumentPic())) {
            fileServiceProxy.confirmFiles(StrUtil.split(form.getDocumentPic(),","), "material-management");
        }
        if (StrUtil.isNotBlank(form.getEnterPic())) {
            fileServiceProxy.confirmFiles(StrUtil.split(form.getEnterPic(), ","), "material-management");
        }
        if (StrUtil.isNotBlank(form.getLeavePic())) {
            fileServiceProxy.confirmFiles(StrUtil.split(form.getLeavePic(),","),  "material-management");
        }


        // 收料
        if (form.getType().equals(WeighTypeEnum.RECEIVE.value())) {
            if (form.getReviceType() != null && form.getReviceType() == 3) {
                //无合同收料
                materialData.setReceiveMode(ReceiveModeEnum.UNBELOGN.value());
            } else {
                materialData.setReceiveMode(ReceiveModeEnum.TEMPORARY.value());
            }
            materialData.setIsContractRateUsed(IsContractRateUsedEnum.NO.value());
            materialData.setIsContractUnitUsed(IsContractUnitUsedEnum.NO.value());
            materialData.setMaterialExist(MaterialExistEnum.NO.value());

            // 是否使用合同换算系数/结算单位
            if (ObjectUtil.isNotNull(contractDetail)) {
                if (contractDetail.getConversionRate().compareTo(form.getRatio()) == 0) {
                    materialData.setIsContractRateUsed(IsContractRateUsedEnum.YES.value());
                }
                if (contractDetail.getUnit().equals(form.getWeightUnit())) {
                    materialData.setIsContractUnitUsed(IsContractUnitUsedEnum.YES.value());
                }
            }
            // 材料是否在合同/采购单中存在
            if (StrUtil.isNotBlank(form.getPurchaseOrderId()) && ObjectUtil.isNotNull(contractDetail)) {
                materialData.setMaterialExist(MaterialExistEnum.YES.value());
            }
            if (StrUtil.isBlank(form.getPurchaseOrderId()) && ObjectUtil.isNotNull(contractDetail)) {
                materialData.setMaterialExist(MaterialExistEnum.OTHER.value());
            }

            // 是否立即完成对账
            if (ObjectUtil.isNotNull(contractDetail) && form.getIsAutoVerify() == (byte)1) {
                MaterialVerify materialVerify = materialVerifyService.lambdaQuery()
                        .eq(MaterialVerify::getContractId, contractDetail.getContractId())
                        .eq(MaterialVerify::getVerifyProjectId,contractDetail.getProjectId())
                        .eq(MaterialVerify::getIsOrigin, (byte) 1)
                        .one();
                if (ObjectUtil.isNotNull(materialVerify)) {
                    materialData.setReconciliationId(materialVerify.getId());
                }
            }
        }
        return materialData;
    }

    private void warning(AdditionalRecordForm form,MaterialData materialData, MaterialSendReceive materialSendReceive,PurchaseContractDetail contractDetail) {
        // 收料
        if (form.getType().equals(WeighTypeEnum.RECEIVE.value()) && ObjectUtil.isNotNull(contractDetail)) {
            if (form.getDeviationStatus().equals(DeviationStatusEnum.NEGATIVEDIFFERENCE.value())) {
                MaterialWarningForm materialWarningForm = new MaterialWarningForm();
                materialWarningForm.setWarningType(WarningTypeEnum.NEGATIVE_DEVIATION.value());
                materialWarningForm.setWarningInfo(String.format(WarningSubTypeEnum.NEGATIVE_DEVIATION_ONE.desc(),contractDetail.getDeviationFloor(),contractDetail.getDeviationCeiling(),materialData.getDeviationRate()));
                materialWarningForm.setWarningSourceStr("手工补单-收料");
                materialWarningForm.setWarningSourceId(materialData.getId());
                materialWarningForm.setWarningSourceNo(materialSendReceive.getReceiveNo());
                materialWarningForm.setSourceProjectId(materialData.getProjectId());

                warningUtil.saveWarning(materialWarningForm);
            }
        }
    }

    private MaterialVerifyRelation getMaterialVerify(MaterialData materialData) {
        MaterialVerifyRelation materialVerifyRelation = new MaterialVerifyRelation();
        materialVerifyRelation.setVerifyId(materialData.getReconciliationId());
        materialVerifyRelation.setReceiveDataId(materialData.getId());
        return materialVerifyRelation;
    }
}
