package cn.pinming.microservice.material.management.biz.message;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.management.biz.dto.WeighbridgeAcceptDataDTO;
import cn.pinming.microservice.material.management.biz.iot.service.IWeighbridgeService;
import cn.pinming.microservice.material.management.biz.service.IOTMessageService;
import cn.pinming.microservice.material.management.common.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 地磅消息处理
 *
 * <AUTHOR>
 * @version 2021/9/9 3:00 下午
 */
@Slf4j
@Component
@Transactional(rollbackFor = Exception.class)
public class IOTMessageListener {

    @Resource
    private IOTMessageService iotMessageService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private IWeighbridgeService iWeighbridgeService;

    @KafkaListener(topics = "IOT_MATERIAL_SHARE_TOPIC",containerFactory = "batchFactory")
    public void listen(String message, Acknowledgment ack) {
        if (log.isErrorEnabled()) {
            log.error("地磅上报数据开始:{}", message);
        }
        WeighbridgeAcceptDataDTO acceptDataDTO = JSONUtil.toBean(message, WeighbridgeAcceptDataDTO.class);
        String weighId = acceptDataDTO.getWeighId();
        if (StringUtils.isBlank(weighId)) {
            log.warn("weighId为空, message:{}", message);
            return;
        }
        // weighId 幂等性检测
        boolean idempotentCheck = iWeighbridgeService.weighIdIdempotentCheck(weighId);
        if (idempotentCheck) {
            log.warn("重复的weighId:{}", weighId);
            return;
        }
        // 缓存及数据库均无数据：存入缓存
        redisUtil.set(weighId, weighId, 30);
        iotMessageService.processPushData(acceptDataDTO);
        ack.acknowledge();
        if (log.isErrorEnabled()) { log.error("地磅上报数据成功"); }
    }


}
