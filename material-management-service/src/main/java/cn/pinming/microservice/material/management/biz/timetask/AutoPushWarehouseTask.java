package cn.pinming.microservice.material.management.biz.timetask;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.ContractMapDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialData;
import cn.pinming.microservice.material.management.biz.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.biz.mapper.MaterialVerifyMapper;
import cn.pinming.microservice.material.management.biz.mapper.MaterialVerifyRelationMapper;
import cn.pinming.microservice.material.management.biz.mapper.PurchaseContractDetailMapper;
import cn.pinming.microservice.material.management.biz.service.IMaterialDataService;
import cn.pinming.microservice.material.management.biz.service.IMaterialVerifyService;
import cn.pinming.microservice.material.management.biz.vo.ProjectVO;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.warehouse.management.dto.EnterLeaveRecord;
import cn.pinming.microservice.warehouse.management.service.EnterLeaveRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 收料自动推送仓库定时任务
 * <AUTHOR>
 * @date 2022/9/8
 */
@Slf4j
@Component
public class AutoPushWarehouseTask {

    @Resource
    private MaterialVerifyMapper materialVerifyMapper;
    @Resource
    private MaterialVerifyRelationMapper materialVerifyRelationMapper;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Reference
    private EnterLeaveRecordService enterLeaveRecordService;
    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private MaterialDataMapper materialDataMapper;


//    @Scheduled(cron = "0 0/5 * * * ? ")
    public void autoPush() {
        List<String> verifyIdList = materialVerifyMapper.selectPushed();
        if (CollUtil.isNotEmpty(verifyIdList)) {List<MaterialData> materialDataList = materialVerifyRelationMapper.selectPushed(verifyIdList);

            if (CollUtil.isNotEmpty(materialDataList)) {
                List<String> successList = new ArrayList<>();
                Map<String,String> fail = new HashMap<>();
                Map<String, String> contractMap = new HashMap<>();

                List<Integer> projectIdList = materialDataList.stream().map(e -> e.getProjectId()).distinct().collect(Collectors.toList());
                List<ProjectVO> projectVOList = projectServiceProxy.getSimpleProjects(projectIdList);
                Map<Integer, String> map = new HashMap<>();
                if (CollUtil.isNotEmpty(projectVOList)) {
                    map = projectVOList.stream().collect(Collectors.toMap(ProjectVO::getProjectId, ProjectVO::getProjectTitle));
                }
                List<String> ids = materialDataList.stream().map(e -> e.getId()).collect(Collectors.toList());
                List<ContractMapDTO> contractIdList = materialDataMapper.selectContractByIds(ids);
                if (CollUtil.isNotEmpty(contractIdList)) {
                    contractMap = contractIdList.stream().collect(Collectors.toMap(ContractMapDTO::getId, ContractMapDTO::getContractId));
                }

                Map<Integer, String> finalMap = map;
                Map<String, String> finalContractMap = contractMap;
                materialDataList.stream().forEach(e -> {
                    EnterLeaveRecord enterLeaveRecord = new EnterLeaveRecord();
                    enterLeaveRecord.setType(1);
                    enterLeaveRecord.setCategoryId(e.getCategoryId());
                    enterLeaveRecord.setMaterialId(e.getMaterialId());
                    enterLeaveRecord.setSupplierId(e.getSupplierId());
                    enterLeaveRecord.setReceiveId(String.valueOf(e.getProjectId()));
                    if (CollUtil.isNotEmpty(finalMap) && StrUtil.isNotBlank(finalMap.get(e.getProjectId()))) {
                        enterLeaveRecord.setReceiveName(finalMap.get(e.getProjectId()));
                    }
                    if (CollUtil.isNotEmpty(finalContractMap) && StrUtil.isNotBlank(finalContractMap.get(e.getId()))) {
                        enterLeaveRecord.setContractId(finalContractMap.get(e.getId()));
                    }
                    enterLeaveRecord.setMaterialUnit(e.getWeightUnit());
                    enterLeaveRecord.setMaterialDataId(e.getId());
                    enterLeaveRecord.setMaterialNum(e.getActualCount());
                    enterLeaveRecord.setCompanyId(e.getCompanyId());
                    enterLeaveRecord.setProjectId(e.getProjectId());
                    try {
                        enterLeaveRecordService.createEnterLeaveRecord(enterLeaveRecord);
                        successList.add(enterLeaveRecord.getMaterialDataId());
                    } catch (Exception exception) {
                        exception.printStackTrace();
                        fail.put(e.getId(),exception.getMessage());
                    }
                });

                List<MaterialData> collect = successList.stream().map(e -> {
                    MaterialData materialData = new MaterialData();
                    materialData.setId(e);
                    materialData.setIsPushed((byte) 1);

                    return materialData;
                }).collect(Collectors.toList());

                materialDataService.updateBatchById(collect);

                log.info("本次成功推送{}条收料记录,推送数据id为:{}",successList.size(),String.join(",",successList));
                log.info("本次共有{}条收料记录推送失败,失败数据id为:{}",fail.size(),fail.toString());
            }else {
                log.info("本次推送0条收料记录");
            }
        }
    }
}
