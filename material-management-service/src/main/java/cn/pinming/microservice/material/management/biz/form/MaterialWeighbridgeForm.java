package cn.pinming.microservice.material.management.biz.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 地磅信息Form
 *
 * <AUTHOR>
 */
@Data
public class MaterialWeighbridgeForm extends BaseForm {
    @ApiModelProperty(value = "地磅系统id")
    private String weighSystemId;

    @ApiModelProperty(value = "地磅系统名称")
    @NotBlank
    private String weighSystemName;

    @ApiModelProperty(value = "地磅系统编码")
    private String weighSystemNo;

    @ApiModelProperty(value = "地磅供应商")
    @NotNull(message = "地磅供应商为空")
    private Integer weighSupplier;

//    @ApiModelProperty(value = "数据上传方式, 1 IOT上传 2 接口上传 ")
//    @NotNull
//    private Byte uploadType;
//
//    @ApiModelProperty(value = "磅点名称")
////    @NotBlank
//    private String weighPointName;

    @ApiModelProperty(value = "磅点名称列表")
//    @NotEmpty
    private List<String> weighPointList;

}
