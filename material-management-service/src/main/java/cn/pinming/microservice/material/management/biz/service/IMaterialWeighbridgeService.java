package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.core.cookie.AuthUser;
import cn.pinming.materialManagement.dto.WeighInfoDTO;
import cn.pinming.microservice.material.management.biz.dto.ClientStatusResultDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialWeighbridge;
import cn.pinming.microservice.material.management.biz.form.MaterialWeighbridgeForm;
import cn.pinming.microservice.material.management.biz.query.MaterialWeighbridgeQuery;
import cn.pinming.microservice.material.management.biz.vo.MaterialWeighbridgeVO;
import cn.pinming.microservice.material.management.biz.vo.SimpleWeighBridgeVO;
import cn.pinming.microservice.material.management.biz.vo.SimpleWeighByStatusVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 地磅信息 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface IMaterialWeighbridgeService extends IService<MaterialWeighbridge> {

    /**
     * 新增、编辑地磅信息
     *
     * @param form
     */
    void saveOrUpdateWeighbridge(MaterialWeighbridgeForm form);

    /**
     * 删除地磅信息
     *
     * @param user
     * @param weighbridgeId
     */
    void deleteWeighbridge(AuthUser user, String weighbridgeId);

    /**
     * 显示企业地磅信息
     *
     * @param query
     * @return
     */
    IPage<MaterialWeighbridgeVO> showWeighbridgeInCompany(MaterialWeighbridgeQuery query);

    /**
     * 显示项目地磅信息
     *
     * @param query
     * @return
     */
    List<MaterialWeighbridgeVO> showWeighbridgeInProject(MaterialWeighbridgeQuery query);

    /**
     * 显示项目级地磅信息
     *
     * @param companyId
     * @param projectId
     * @return
     */
    List<WeighInfoDTO> selectWeighInfo(Integer companyId, Integer projectId);

    /**
     * 地磅编辑回显
     *
     * @param user
     * @param id
     * @return
     */
    SimpleWeighBridgeVO showForUpdate(AuthUser user,String id);

    /**
     * 根据项目状态统计地磅数量
     *
     * @param user
     * @return
     */
    SimpleWeighByStatusVO showByStatus(AuthUser user);

    /**
     * 查询地磅信息
     *
     * @param deviceSn
     * @return
     */
    MaterialWeighbridge getWeighbridge(Integer companyId, Integer projectId, String deviceSn);

//    void updateBridgeStatus(Integer companyId, Integer projectId, String weighSystemNo, byte status);
//
//    void updateAllBridgeStatus();

    Map<Integer, Long> queryForClientOnlineStatus(Integer companyId);

    long queryForClientOnlineStatus(Integer companyId,List<Integer> projectList);

    List<ClientStatusResultDTO> queryForClientOnlineStatusList(Integer companyId);
}
