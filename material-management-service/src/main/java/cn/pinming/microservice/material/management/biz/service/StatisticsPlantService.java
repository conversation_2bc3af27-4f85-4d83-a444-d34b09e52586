package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.query.MachineCapacityQuery;
import cn.pinming.microservice.material.management.biz.query.MachineExitDetailQuery;
import cn.pinming.microservice.material.management.biz.vo.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/22
 */
public interface StatisticsPlantService {

    List<StatisticsPlantCategoryVO> queryStatisticsPlantCategory();

    StatisticsPlantVO queryHistoryPlantHistogram(String unit, List<Integer> materialIdList);

    List<StatisticsPlantLineVO> queryTimeRangePlantLine(String start, String end, String unit, List<Integer> materialIdList);

    List<StatisticsPlantHistoryVO> queryHistoryPlantHistogramSecond(String time, String unit, List<Integer> materialIdList);

    List<StatisticsPlantFutureVO> queryTomorrowPlantHistogramSecond(String unit, List<Integer> materialIdList);

    List<MachineCapacityVO> queryMachineCapacity(MachineCapacityQuery machineCapacityQuery, List<Integer> materialIdList);

    List<MachineExitDetailVO> queryMachineExitDetail(MachineExitDetailQuery machineExitDetailQuery, List<Integer> materialIdList);

    Boolean openWarehousePlugin();

    List<RawMaterialDemandStockVO> queryRawMaterialDemandStock(String type, String deadline, Boolean isOverview);

    List<RawMaterialLineVO> queryRawMaterialDailyConsumption(String start, String end);

    List<RawMaterialSendReceiveVO> queryRawMaterialSendReceive();

    List<RawMaterialWarehouseVO> queryRawMaterialWarehouseList(Boolean isOverview);

    List<RawMaterialLineVO> queryRawMaterialPaperInventoryRate(String periods, String warehouseId, Boolean isOverview);

    List<RawMaterialPaperInventoryAmountVO> queryRawMaterialPaperInventoryAmount(List<String> warehouseIds, Boolean isOverview);

    RawMaterialPaperInventoryTableVO queryRawMaterialPaperInventoryDetail(List<String> warehouseIds);

    Map<String, List<PlantOverviewOutboundVO>> queryOverviewOutbound();

    List<PlantOverviewCapacityMaterialVO> queryOverviewCapacityMaterialAndUnit();

    StatisticsPlantHistoryVO queryOverviewCapacity(Integer materialId, String unit);

    List<MixingPlantMachineVO> queryMachineList(List<Integer> materialIds, String unit);

}
