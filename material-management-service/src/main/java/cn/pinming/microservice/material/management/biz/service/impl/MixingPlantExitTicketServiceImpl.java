package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.ExistTicketDetailDTO;
import cn.pinming.microservice.material.management.biz.dto.ExistTicketDetailStatisticsDTO;
import cn.pinming.microservice.material.management.biz.dto.ExitTicketPageDTO;
import cn.pinming.microservice.material.management.biz.dto.PreInfoDTO;
import cn.pinming.microservice.material.management.biz.entity.*;
import cn.pinming.microservice.material.management.biz.enums.*;
import cn.pinming.microservice.material.management.biz.form.ExitTicketForm;
import cn.pinming.microservice.material.management.biz.mapper.*;
import cn.pinming.microservice.material.management.biz.query.BaseQuery;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.biz.vo.ExistTicketDetailVO;
import cn.pinming.microservice.material.management.biz.vo.ExistTicketHistoryVO;
import cn.pinming.microservice.material.management.biz.vo.ExitTicketPageVO;
import cn.pinming.microservice.material.management.biz.vo.MixingPlantOrderDetailItemVO;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.NoUtil;
import cn.pinming.microservice.material.management.proxy.DepartmentTreeServiceProxy;
import cn.pinming.microservice.material.management.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material.management.wrapper.CreateNameWrapper;
import cn.pinming.microservice.material.management.wrapper.ProjectNameWrapper;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.v2.company.api.dto.EmployeeDto;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.PropertyPlaceholderHelper;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 拌合站出场单 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-27 15:11:44
 */

@Service
public class MixingPlantExitTicketServiceImpl extends ServiceImpl<MixingPlantExitTicketMapper, MixingPlantExitTicket> implements IMixingPlantExitTicketService {

    @Resource
    private NoUtil noUtil;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private IPreWeighReportService preWeighReportService;
    @Resource
    private IPreMaterialReportService preMaterialReportService;
    @Resource
    private IPreTruckReportService preTruckReportService;
    @Resource
    private IPreTruckReportDetailService preTruckReportDetailService;
    @Resource
    private PreWeighReportMapper preWeighReportMapper;
    @Resource
    private PreMaterialReportMapper preMaterialReportMapper;
    @Resource
    private PreTruckReportMapper preTruckReportMapper;
    @Resource
    private PreTruckReportDetailMapper preTruckReportDetailMapper;
    @Resource
    private IMixingPlantOrderDetailService mixingPlantOrderDetailService;
    @Resource
    private ProjectNameWrapper projectNameWrapper;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private CreateNameWrapper createNameWrapper;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private ILeaveTemplateService leaveTemplateService;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;
    @Resource
    private IPurchaseOrderService purchaseOrderService;
    @Resource
    private MixingPlantExitTicketMapper mixingPlantExitTicketMapper;
    @Resource
    private DepartmentTreeServiceProxy departmentTreeServiceProxy;
    @Resource
    private MixingPlantOrderDetailMapper mixingPlantOrderDetailMapper;
    @Resource
    private IMixingPlantMachineService mixingPlantMachineService;
    @Resource
    private IPurchaseOrderDetailService purchaseOrderDetailService;


    /**
     * 新增出场单 + 预报备
     *
     * @param form
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String add(ExitTicketForm form) {
        // 新增
        AuthUser user = authUserHolder.getCurrentUser();
        Integer projectId = user.getCurrentProjectId();
        MixingPlantExitTicket mixingPlantExitTicket = new MixingPlantExitTicket();
        String exitTicketNo = noUtil.getExitTicketNo(projectId);

        BeanUtil.copyProperties(form, mixingPlantExitTicket);
        mixingPlantExitTicket.setTicketNo(exitTicketNo);

        this.save(mixingPlantExitTicket);

        // 出场单只能选择到非"发货完毕"、"已作废"订单明细，直接更新，不用考虑什么
        mixingPlantOrderDetailService.lambdaUpdate()
                .eq(MixingPlantOrderDetail::getId, mixingPlantExitTicket.getMixingPlantOrderDetailId())
                .set(MixingPlantOrderDetail::getStatus, PlantOrderDetailStatusEnum.FOUR.value())
                .update();

        MixingPlantOrderDetail mixingPlantOrderDetail = mixingPlantOrderDetailService.lambdaQuery()
                .select(MixingPlantOrderDetail::getPurchaseOrderDetailId)
                .eq(MixingPlantOrderDetail::getId, form.getMixingPlantOrderDetailId())
                .one();

        PurchaseOrderDetail purchaseOrderDetail = purchaseOrderDetailService.lambdaQuery()
                .select(PurchaseOrderDetail::getProjectId)
                .eq(PurchaseOrderDetail::getId, mixingPlantOrderDetail.getPurchaseOrderDetailId())
                .one();

        Boolean flag = ObjectUtil.isNotNull(mixingPlantOrderDetail) && StrUtil.isNotBlank(mixingPlantOrderDetail.getPurchaseOrderDetailId()) && ObjectUtil.isNotNull(purchaseOrderDetail);

        if (flag) {
            this.saveOrUpdatePreWeighReport(mixingPlantExitTicket, purchaseOrderDetail.getProjectId(), true);
        }

        return mixingPlantExitTicket.getId();
    }

    /**
     * 作废
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancel(String id, String mixingPlantOrderDetailId) {
        this.removeById(id);

        // 可操作判断
        this.judge(mixingPlantOrderDetailId);

        // 出场单关联的订单明细挂的所有出场单是否都被作废，是，回滚至"待配料"或者"生产中"
        List<MixingPlantExitTicket> list = this.getBaseMapper().selectIsCanceled(mixingPlantOrderDetailId);
        if (CollUtil.isEmpty(list)) {
            mixingPlantOrderDetailService.lambdaUpdate()
                    .eq(MixingPlantOrderDetail::getId, mixingPlantOrderDetailId)
                    .set(MixingPlantOrderDetail::getStatus, PlantOrderDetailStatusEnum.ONE.value())
                    .update();
        }
    }

    /**
     * 编辑出场单 + 更新预报备
     *
     * @param form
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fix(ExitTicketForm form) {
        Optional.ofNullable(form.getId()).orElseThrow(() -> new BOException(BOExceptionEnum.EXIT_TICKET_IS_EMPTY));

        // 可操作判断
        this.judge(form.getMixingPlantOrderDetailId());

        MixingPlantExitTicket mixingPlantExitTicket = new MixingPlantExitTicket();
        BeanUtil.copyProperties(form, mixingPlantExitTicket);

        this.updateById(mixingPlantExitTicket);

        MixingPlantOrderDetail mixingPlantOrderDetail = mixingPlantOrderDetailService.lambdaQuery()
                .select(MixingPlantOrderDetail::getPurchaseOrderDetailId)
                .eq(MixingPlantOrderDetail::getId, form.getMixingPlantOrderDetailId())
                .one();
        if (ObjectUtil.isNotNull(mixingPlantOrderDetail) && StrUtil.isNotBlank(mixingPlantOrderDetail.getPurchaseOrderDetailId())) {
            this.saveOrUpdatePreWeighReport(mixingPlantExitTicket, null, false);
        }
    }

    /**
     * 出场单列表
     *
     * @return
     */
    @Override
    public IPage<ExitTicketPageVO> selectPage(BaseQuery query) {
        IPage<ExitTicketPageVO> page = new Page<>();
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
        Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();
        query.setCompanyId(companyId);
        query.setProjectId(projectId);

        IPage<ExitTicketPageDTO> dtoPage = this.getBaseMapper().selectPages(query);
        if (CollUtil.isNotEmpty(dtoPage.getRecords())) {
            List<ExitTicketPageDTO> list = dtoPage.getRecords();
            Map<Integer, MaterialDto> materialDtoMap = new HashMap<>();
            Map<Integer, String> projectDtoMap = new HashMap<>();

            List<Integer> materialIdList = list.stream().map(ExitTicketPageDTO::getMaterialId).distinct().collect(Collectors.toList());
            List<MaterialDto> materialDtos = materialServiceProxy.listMaterialByIds(materialIdList);
            if (CollUtil.isNotEmpty(materialDtos)) {
                materialDtoMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, e -> e));
            }
            List<Integer> projectIdList = list.stream().map(ExitTicketPageDTO::getReceiverProject).distinct().collect(Collectors.toList());
            List<SimpleConstructionProjectDto> projectDtos = projectServiceProxy.findProjectsByProjectIds(projectIdList);
            if (CollUtil.isNotEmpty(projectDtos)) {
                projectDtoMap = projectDtos.stream().collect(Collectors.toMap(SimpleConstructionProjectDto::getProjectId, SimpleConstructionProjectDto::getProjectTitle));
            }
            projectNameWrapper.wrap(list);

            Map<Integer, MaterialDto> finalMaterialDtoMap = materialDtoMap;
            Map<Integer, String> finalProjectDtoMap = projectDtoMap;
            List<ExitTicketPageVO> result = list.stream().map(e -> {
                ExitTicketPageVO vo = new ExitTicketPageVO();

                if (CollUtil.isNotEmpty(finalMaterialDtoMap)) {
                    MaterialDto materialDto = finalMaterialDtoMap.get(e.getMaterialId());
                    if (ObjectUtil.isNotNull(materialDto)) {
                        e.setType(StrUtil.format("{}/{}", materialDto.getMaterialName(), materialDto.getMaterialSpec()));
                        e.setParameterRequirementsDesc(purchaseOrderService.parseParameterRequirements(materialDto.getMaterialCategoryId(), e.getParameterRequirements()));
                    }
                }

                BeanUtil.copyProperties(e, vo);

                if (CollUtil.isNotEmpty(finalProjectDtoMap)) {
                    vo.setReceiverProject(finalProjectDtoMap.get(e.getReceiverProject()));
                }

                return vo;
            }).collect(Collectors.toList());

            BeanUtil.copyProperties(dtoPage, page);
            page.setRecords(result);
        }

        return page;
    }

    @Override
    public String print(String id, String templateId, String mixingPlantOrderDetailId) {
        Properties properties = buildTemplateData(id, mixingPlantOrderDetailId);
        LeaveTemplate leaveTemplate = leaveTemplateService.getById(templateId);
        if (null == leaveTemplate || StrUtil.isEmpty(leaveTemplate.getTemplateContent())) {
            throw new BOException(BOExceptionEnum.TEMPLATE_CONTENT_EMPTY);
        }
        PropertyPlaceholderHelper helper = new PropertyPlaceholderHelper("${", "}");
        return helper.replacePlaceholders(leaveTemplate.getTemplateContent(), properties);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public ExistTicketDetailVO selectDetail(String id) {
        ExistTicketDetailVO vo = new ExistTicketDetailVO();
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();

        ExistTicketDetailDTO dto = this.getBaseMapper().selectDetail(id);

        if (ObjectUtil.isNotNull(dto)) {
            MixingPlantOrderDetailItemVO detailItemVO = new MixingPlantOrderDetailItemVO();

            createNameWrapper.wrap(dto, companyId);
            projectNameWrapper.wrap(dto);
            MaterialDto materialDto = materialServiceProxy.materialById(dto.getMaterialId());
            SimpleConstructionProjectDto simpleProject = projectServiceProxy.findSimpleProject(dto.getReceiverProject());

            BeanUtil.copyProperties(dto, vo);
            vo.setPlantName(dto.getProjectTitle());
            vo.setCustomerName(dto.getCreateName());
            BeanUtil.copyProperties(dto, detailItemVO);
            detailItemVO.setId(dto.getMixingPlantOrderDetailId());
            if (ObjectUtil.isNotNull(materialDto)) {
                detailItemVO.setType(StrUtil.format("{}/{}", materialDto.getMaterialName(), materialDto.getMaterialSpec()));
                detailItemVO.setParameterRequirementsDesc(purchaseOrderService.parseParameterRequirements(materialDto.getMaterialCategoryId(), detailItemVO.getParameterRequirements()));
            }
            if (ObjectUtil.isNotNull(simpleProject)) {
                vo.setReceiverProject(simpleProject.getProjectTitle());
                vo.setProjectAddress(simpleProject.getAddress());
                Integer relationDepartmentId = simpleProject.getRelationDepartmentId();
                if (ObjectUtil.isNotNull(relationDepartmentId)) {
                    String departmentParent = departmentTreeServiceProxy.getDepartmentParent(relationDepartmentId);
                    vo.setReceiverCorp(departmentParent);
                }
            }

            // 累计发货量和累计发货车次
            ExistTicketDetailStatisticsDTO statisticsDTO = this.getBaseMapper().statistics(dto.getMixingPlantOrderDetailId());
            if (ObjectUtil.isNotNull(statisticsDTO)) {
                detailItemVO.setSendCount(statisticsDTO.getSendCount());
                detailItemVO.setTruckCount(statisticsDTO.getTruckCount());
            }

            ExistTicketHistoryVO history = mixingPlantExitTicketMapper.history(dto.getMixingPlantOrderDetailId());
            detailItemVO.setHistoryVO(history);

            vo.setOrderDetailItemVOS(Arrays.asList(detailItemVO));
        }

        return vo;
    }

    private Properties buildTemplateData(String id, String mixingPlantOrderDetailId) {
        ExistTicketDetailVO existTicketDetailVO = selectDetail(id);
        Optional.ofNullable(existTicketDetailVO).orElseThrow(() -> new BOException(BOExceptionEnum.ILLEGAL_PARAM));

        Properties properties = new Properties();
        String preTruckNo = this.baseMapper.getPreTruckNoByTicketNo(existTicketDetailVO.getTicketNo(), existTicketDetailVO.getMixingPlantOrderId());
        if (StrUtil.isEmpty(preTruckNo)) {
            properties.put(LeaveParamEnum.PRE_TRUCK_NO.value(), "无");
        } else {
            properties.put(LeaveParamEnum.PRE_TRUCK_NO.value(), preTruckNo);
        }
        properties.put(LeaveParamEnum.TICKET_NO.value(), buildParam(existTicketDetailVO.getTicketNo()));
        String time = LocalDateTimeUtil.format(existTicketDetailVO.getOrderDetailItemVOS().get(0).getLeaveTime(), DatePattern.NORM_DATETIME_FORMATTER);
        properties.put(LeaveParamEnum.EXIT_DATE.value(), buildParam(time.split(String.valueOf(StrUtil.C_SPACE))[0]));
        properties.put(LeaveParamEnum.EXIT_TIME.value(), buildParam(time.split(String.valueOf(StrUtil.C_SPACE))[1]));
        properties.put(LeaveParamEnum.RECEIVER_PROJECT.value(), buildParam(existTicketDetailVO.getReceiverProject()));
        properties.put(LeaveParamEnum.PROJECT_ADDRESS.value(), buildParam(existTicketDetailVO.getProjectAddress()));
        properties.put(LeaveParamEnum.SUB_COMPANY_NAME.value(), buildParam(existTicketDetailVO.getReceiverCorp()));
        properties.put(LeaveParamEnum.RECEIVER.value(), buildParam(existTicketDetailVO.getReceiver()));
        properties.put(LeaveParamEnum.RECEIVER_TEL.value(), buildParam(existTicketDetailVO.getReceiverTel()));
        MixingPlantOrderDetailItemVO mixingPlantOrderDetailItemVO = existTicketDetailVO.getOrderDetailItemVOS().get(0);
        String position = mixingPlantOrderDetailItemVO.getPosition();
        if (StrUtil.isEmpty(position)) {
            properties.put(LeaveParamEnum.POSITION.value(), "/");
        } else {
            properties.put(LeaveParamEnum.POSITION.value(), position);
        }
        // 对应出场单
        MixingPlantExitTicket mixingPlantExitTicket = this.getById(id);
        String plantMachineId = mixingPlantExitTicket.getPlantMachineId();
        if (StrUtil.isEmpty(plantMachineId)) {
            properties.put(LeaveParamEnum.MACHINE_NAME.value(), "无");
        } else {
            MixingPlantMachine mixingPlantMachine = mixingPlantMachineService.getById(plantMachineId);
            Optional.ofNullable(mixingPlantMachine).ifPresent(machine -> properties.put(LeaveParamEnum.MACHINE_NAME.value(), machine.getMachineName()));
        };
        EmployeeDto employee = employeeServiceProxy.findEmployee(mixingPlantExitTicket.getCompanyId(), mixingPlantExitTicket.getCreateId());
        properties.put(LeaveParamEnum.TRUCK_NO.value(), buildParam(mixingPlantExitTicket.getTruckNo()));
        properties.put(LeaveParamEnum.COUNT.value(), buildParam(existTicketDetailVO.getOrderDetailItemVOS().get(0).getCount().toString()));
        properties.put(LeaveParamEnum.SEND_COUNT.value(), buildParam(existTicketDetailVO.getActualCount().toString()));
        properties.put(LeaveParamEnum.SEND_TOTAL_COUNT.value(), buildParam(existTicketDetailVO.getOrderDetailItemVOS().get(0).getSendCount().toString()));
        properties.put(LeaveParamEnum.PARAMETER.value(), buildParam(existTicketDetailVO.getOrderDetailItemVOS().get(0).getParameterRequirementsDesc()));
        properties.put(LeaveParamEnum.SPEC.value(), buildParam(existTicketDetailVO.getOrderDetailItemVOS().get(0).getType()));
        properties.put(LeaveParamEnum.SENDER.value(), buildParam(employee.getMemberName()));
        properties.put(LeaveParamEnum.BARCODE.value(), buildParam(mixingPlantExitTicket.getTicketNo()));
        return properties;
    }

    public String buildParam(Object param) {
        return param == null ? "/" : param.toString();
    }

    /**
     * 车牌选择
     *
     * @param truckNo
     * @return
     */
    @Override
    public List<String> truckNoHistory(String truckNo) {
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
        Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();

        List<String> list = this.getBaseMapper().selectTruckNoHistory(truckNo, companyId, projectId);

        return list;
    }

    /**
     * 创建预报备
     *
     * @param mixingPlantExitTicket
     * @param projectId
     */
    private void saveOrUpdatePreWeighReport(MixingPlantExitTicket mixingPlantExitTicket, Integer projectId, Boolean flag) {
        PreWeighReport preWeighReport = new PreWeighReport();
        PreMaterialReport preMaterialReport = new PreMaterialReport();
        PreTruckReport preTruckReport = new PreTruckReport();
        PreTruckReportDetail preTruckReportDetail = new PreTruckReportDetail();

        PreInfoDTO dto = mixingPlantOrderDetailMapper.selectPreInfo(mixingPlantExitTicket.getMixingPlantOrderDetailId());

        // 编辑
        if (flag == false) {
            String preWeighReportId = preWeighReportMapper.selectExitTicketId(mixingPlantExitTicket.getId());
            if (StrUtil.isBlank(preWeighReportId)) {
                return;
            }
            String preMaterialReportId = preMaterialReportMapper.selectId(preWeighReportId);
            String preTruckReportId = preTruckReportMapper.selectId(preWeighReportId);
            String preTruckReportDetailId = preTruckReportDetailMapper.selectId(preWeighReportId);
            preWeighReport.setId(preWeighReportId);
            preMaterialReport.setId(preMaterialReportId);
            preTruckReport.setId(preTruckReportId);
            preTruckReportDetail.setId(preTruckReportDetailId);
        }
        // 新增
        if (flag == true) {
            String preWeighReportNo = noUtil.getReportNo(projectId);
            preWeighReport.setNo(preWeighReportNo);
            String preTruckReportDetailNo = noUtil.getWeighNo(projectId);
            preTruckReportDetail.setNo(preTruckReportDetailNo);
            preWeighReport.setProjectId(projectId);
            preMaterialReport.setProjectId(projectId);
            preTruckReport.setProjectId(projectId);
            preTruckReportDetail.setProjectId(projectId);
        }

        preWeighReport.setOrderDetailId(mixingPlantExitTicket.getMixingPlantOrderDetailId());
        preWeighReport.setExitTicketId(mixingPlantExitTicket.getId());
        preWeighReport.setType(PreWeighReportTypeEnum.RECEIVE.value());
        preWeighReport.setWeighType(PreWeighTypeEnum.SINGLE.value());
        preWeighReport.setStatus(PreReportStatusEnum.COMMIT.value());
        preWeighReport.setPurchaseOrderId(dto.getPurchaseOrderId());
        preWeighReportService.saveOrUpdate(preWeighReport);

        preMaterialReport.setPreWeighReportId(preWeighReport.getId());
        preMaterialReport.setUnit(mixingPlantExitTicket.getUnit());
        preMaterialReport.setCount(mixingPlantExitTicket.getActualCount());
        preMaterialReport.setContractDetailId(dto.getContractDetailId());
        preMaterialReport.setPurchaseOrderDetailId(dto.getPurchaseOrderDetailId());
        preMaterialReportService.saveOrUpdate(preMaterialReport);

        preTruckReport.setPreWeighReportId(preWeighReport.getId());
        preTruckReport.setTruckNo(mixingPlantExitTicket.getTruckNo());
        preTruckReport.setCapacity(mixingPlantExitTicket.getActualCount());
        preTruckReport.setTimes(1);
        preTruckReport.setIntervalTime(0);
        preTruckReportService.saveOrUpdate(preTruckReport);

        preTruckReportDetail.setPreWeighReportId(preWeighReport.getId());
        preTruckReportDetail.setPreTruckReportId(preTruckReport.getId());
        preTruckReportDetailService.saveOrUpdate(preTruckReportDetail);
    }

    /**
     * 编辑、作废操作权限判断
     *
     * @param mixingPlantOrderDetailId
     */
    private void judge(String mixingPlantOrderDetailId) {
        // 已有出场单的情况下，订单明细不会为"作废"状态
        MixingPlantOrderDetail mixingPlantOrderDetail = mixingPlantOrderDetailService.lambdaQuery()
                .select(MixingPlantOrderDetail::getStatus)
                .eq(MixingPlantOrderDetail::getId, mixingPlantOrderDetailId)
                .one();
        // 发货完毕
        if (mixingPlantOrderDetail.getStatus() == PlantOrderDetailStatusEnum.FIVE.value()) {
            throw new BOException(BOExceptionEnum.ORDERDETAIL_STATUS_IS_FIVE);
        }
        // 已归档
//        if (mixingPlantOrderDetail.getStatus() == PlantOrderDetailStatusEnum.SIX.value()) {
//            throw new BOException(BOExceptionEnum.ORDERDETAIL_STATUS_IS_SIX);
//        }
    }
}
