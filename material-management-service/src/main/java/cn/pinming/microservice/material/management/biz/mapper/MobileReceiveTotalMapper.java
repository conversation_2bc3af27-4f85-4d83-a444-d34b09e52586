package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.entity.MobileReceiveTotal;
import cn.pinming.microservice.material.management.biz.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.biz.vo.CategoryReceiveVO;
import cn.pinming.microservice.material.management.biz.vo.MobileReceiveHistoryVO;
import cn.pinming.microservice.material.management.biz.vo.ReceiveDeviationSummaryVO;
import cn.pinming.microservice.material.management.biz.vo.ReceiveDeviationVO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 移动收料总计表 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:31:40
 */
public interface MobileReceiveTotalMapper extends BaseMapper<MobileReceiveTotal> {

    MobileReceiveHistoryVO selectHistory(@Param("totalId") String totalId);


    /**
     * 移动收料总量
     *
     * @param query
     * @return
     */
    List<CategoryReceiveVO> countMobileReceiveNum(SupplierAnalysisQuery query);

    /**
     * 移动收料 偏差
     *
     * @param query
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ReceiveDeviationVO> countDeviation(@Param("query") SupplierAnalysisQuery query, @Param("receiveModes") List<Byte> receiveMode);

    /**
     * 移动收料 偏差
     *
     * @param query
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ReceiveDeviationSummaryVO> countDeviationStatus(@Param("query") SupplierAnalysisQuery query, @Param("receiveModes") List<Byte> receiveMode);

    /**
     * 移动收料 收料计量单位
     *
     * @param companyId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<String> selectUnitListByCompanyId(@Param("companyId") Integer companyId);
}
