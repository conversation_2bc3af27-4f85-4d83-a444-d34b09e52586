package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 物料对账表
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-18 10:22:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_material_verify")
@ApiModel(value = "MaterialVerify对象", description = "物料对账表")
public class MaterialVerify implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "对账批次编号")
    @TableField("verify_no")
    private String verifyNo;

    @ApiModelProperty(value = "报账项目id")
    @TableField(value = "verify_project_id")
    private Integer verifyProjectId;

    @ApiModelProperty(value = "对账供应商id")
    @TableField("supplier_id")
    private String supplierId;

    @ApiModelProperty(value = "合同id")
    @TableField("contract_id")
    private String contractId;

    @ApiModelProperty(value = "对账状态 0 已归档 1 对账中")
    private Byte status;

    @ApiModelProperty(value = "是否为合同创建时自动生成 0,否 1,是")
    @TableField("is_origin")
    private Byte isOrigin;

    @ApiModelProperty(value = "归档时间")
    @TableField("file_time")
    private LocalDateTime fileTime;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
