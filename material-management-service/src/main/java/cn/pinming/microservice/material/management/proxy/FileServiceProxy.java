package cn.pinming.microservice.material.management.proxy;

import cn.pinming.core.upload.AliyunOssUploadComponent;
import cn.pinming.core.upload.UploadComponent;
import cn.pinming.model.dto.FilePreviewDto;
import cn.pinming.v2.common.api.dto.FileDto;

import java.util.List;

/**
 * Created by jin on 2020-03-17.
 */
public interface FileServiceProxy {

    void confirmFiles(List<String> fileUUIDs, String code);

    UploadComponent getAliyunOssUploadComponent();

    UploadComponent getDynamicUploadComponent();

    void confirmFile(String uuid, String fileCode);

    FilePreviewDto findFilePreview(String uuid);

    List<FileDto> findFilesByUUIDs(List<String> fileList);

    FileDto findFilesByUUID(String fileUUID);

    String fileDownloadUrlByUUID(String fileUUID);

    List<String> fileDownloadUrlByUUIDs(List<String> fileUUIDs);

    void deleteFileByUUID(String fileUUID);

    void deleteFilesByUUIDs(List<String> fileUUIDs);
}
