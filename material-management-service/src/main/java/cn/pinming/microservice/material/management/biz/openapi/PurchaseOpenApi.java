package cn.pinming.microservice.material.management.biz.openapi;

import cn.pinming.microservice.material.management.biz.form.PurchaseOrderForm;
import cn.pinming.microservice.material.management.biz.query.PurchaseOrderQuery;
import cn.pinming.microservice.material.management.biz.service.IPurchaseContractService;
import cn.pinming.microservice.material.management.biz.service.IPurchaseOrderService;
import cn.pinming.microservice.material.management.biz.vo.PurchaseOrderVO;
import cn.pinming.microservice.material.management.common.OpenApiAdd;
import cn.pinming.microservice.material.management.common.ValidCommon;
import cn.pinming.microservice.material.management.common.response.Result;
import cn.pinming.microservice.material.management.common.util.OpenApiUtil;
import cn.pinming.openapi.client.OpenIStrategy;
import cn.pinming.openapi.client.annotation.OpenApi;
import cn.pinming.openapi.client.annotation.OpenApiEndpoint;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Validator;
import java.util.Map;

@Component
@OpenApiEndpoint
public class PurchaseOpenApi extends OpenIStrategy {
    @Resource
    private IPurchaseOrderService orderService;
    @Resource
    private IPurchaseContractService contractService;
    @Resource
    private Validator validator;
    @Value("${openApi.switch:false}")
    private Boolean isEnabled;

    /**
     * 新增采购单
     */
    @OpenApi(itype = "/test/savePurchase")
    public String savePurchase(Map<String, String[]> map) {
        if (!isEnabled) {
            return null;
        }
        String data = getMapValue(map,"data");
        log.info("----保存元数据data ------>{}" + data);
        Integer companyId = OpenApiUtil.getCompanyId(map);
        PurchaseOrderForm form = JSONObject.parseObject(data, PurchaseOrderForm.class);
        ValidCommon.validRequestParams(validator.validate(form, OpenApiAdd.class));
        form.setCompanyId(companyId);
        String str = orderService.savePurchaseOrder(form);
        return OpenApiUtil.toJSONString(Result.success(str));
    }

    /**
     * 取消采购单
     */
    @OpenApi(itype = "/test/cancelPurchase")
    public String delPurchase(Map<String, String[]> map) {
        if (!isEnabled) {
            return null;
        }
        String data = getMapValue(map,"data");
        log.info("----取消元数据data ------>{}" + data);
        orderService.cancelById(data);
        return buildOk();
    }

    /**
     * 查看采购单列表
     */
    @OpenApi(itype = "/test/listPurchase")
    public String listPurchase(Map<String, String[]> map) {
        if (!isEnabled) {
            return null;
        }
        String data = getMapValue(map,"data");
        log.info("----根据条件data查看采购单列表 ------>{}" + data);
        Integer companyId = OpenApiUtil.getCompanyId(map);
        PurchaseOrderQuery query = JSONObject.parseObject(data, PurchaseOrderQuery.class);
        query.setCompanyId(companyId);
        IPage<?> result = orderService.pageListByQuery(query);
        return OpenApiUtil.toJSONString(Result.success(result));
    }

    /**
     * 查看采购单详情
     */
    @OpenApi(itype = "/test/detailPurchase")
    public String detailPurchase(Map<String, String[]> map) {
        if (!isEnabled) {
            return null;
        }
        String data = getMapValue(map,"data");
        Integer companyId = OpenApiUtil.getCompanyId(map);
        log.info("----根据条件data查看采购单详情 ------>{}" + data);
        PurchaseOrderVO vo = orderService.queryDetailById(data,companyId);
        return OpenApiUtil.toJSONString(Result.success(vo));
    }

    /**
     * 所属合同列表
     */
    @OpenApi(itype = "/test/belongPurchase")
    public String belongPurchase(Map<String, String[]> map) {
        if (!isEnabled) {
            return null;
        }
        String data = getMapValue(map,"data");
        log.info("----查看所属合同列表 ------>{}" + data);
        contractService.listContract(data);
        return buildOk();
    }

    @Override
    protected String toJson(Object o) {
        return JSONObject.toJSONString(o);
    }

}
