package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.entity.MixingPlantMachine;
import cn.pinming.microservice.material.management.biz.form.MixingPlantMachineForm;
import cn.pinming.microservice.material.management.biz.vo.MixingPlantMachineVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 拌合站机组管理 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-24 17:11:35
 */
public interface IMixingPlantMachineService extends IService<MixingPlantMachine> {

    /**
     * 获取该项目下的机组信息
     * @return 机组信息
     */
    List<MixingPlantMachineVO> getMixingPlantMachineInfo();

    /**
     * 更新机组信息
     * @param mixingPlantMachineFormList 信息表单
     */
    void updateMachineInfo(List<MixingPlantMachineForm> mixingPlantMachineFormList);

}
