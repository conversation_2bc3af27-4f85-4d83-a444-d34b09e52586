package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.entity.MixingPlantMachine;
import cn.pinming.microservice.material.management.biz.form.MixingPlantMachineForm;
import cn.pinming.microservice.material.management.biz.mapper.MixingPlantMachineMapper;
import cn.pinming.microservice.material.management.biz.service.IMixingPlantMachineService;
import cn.pinming.microservice.material.management.biz.vo.MixingPlantMachineVO;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 拌合站机组管理 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-24 17:11:35
 */
@Service
public class MixingPlantMachineServiceImpl extends ServiceImpl<MixingPlantMachineMapper, MixingPlantMachine> implements IMixingPlantMachineService {

    @Resource
    private AuthUserHolder authUserHolder;

    @Override
    public List<MixingPlantMachineVO> getMixingPlantMachineInfo() {
        List<MixingPlantMachine> mixingPlantMachineList = getMixingPlantMachineInfoByProject();
        if (CollUtil.isEmpty(mixingPlantMachineList)) {
            // 初始化机组信息
            initMixingPlantMachine();
        }

        return getMixingPlantMachineInfoByProject().stream().map(mixingPlantMachine -> {
            MixingPlantMachineVO mixingPlantMachineVO = new MixingPlantMachineVO();
            BeanUtils.copyProperties(mixingPlantMachine, mixingPlantMachineVO);
            return mixingPlantMachineVO;
        }).collect(Collectors.toList());
    }

    @Override
    public void updateMachineInfo(List<MixingPlantMachineForm> mixingPlantMachineFormList) {

        if (CollUtil.isEmpty(mixingPlantMachineFormList)) {
            throw new BOException(BOExceptionEnum.PARAM_CAN_NOT_EMPTY);
        }
        mixingPlantMachineFormList.forEach(mixingPlantMachineForm -> {
            Integer machineNumber = mixingPlantMachineForm.getMachineNumber();
            List<MixingPlantMachine> mixingPlantMachineList = getMixingPlantMachineByNumber(machineNumber);
            if (CollUtil.isEmpty(mixingPlantMachineList)) {
                throw new BOException(BOExceptionEnum.MACHINE_NOT_EXIST);
            }
            MixingPlantMachine mixingPlantMachine = mixingPlantMachineList.get(0);
            BeanUtils.copyProperties(mixingPlantMachineForm, mixingPlantMachine);
            updateById(mixingPlantMachine);
        });
    }


    private List<MixingPlantMachine> getMixingPlantMachineInfoByProject() {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        return this.lambdaQuery().eq(MixingPlantMachine::getProjectId, currentUser.getCurrentProjectId())
                .orderByAsc(MixingPlantMachine::getMachineNumber).list();
    }

    private List<MixingPlantMachine> getMixingPlantMachineByNumber(Integer machineNumber) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        return this.lambdaQuery().eq(MixingPlantMachine::getProjectId, currentUser.getCurrentProjectId())
                .eq(MixingPlantMachine::getMachineNumber, machineNumber).list();
    }

    private void initMixingPlantMachine() {
        List<MixingPlantMachine> mixingPlantMachineList = new ArrayList<>();
        MixingPlantMachine firstMachine = new MixingPlantMachine();
        MixingPlantMachine secondMachine = new MixingPlantMachine();
        MixingPlantMachine thirdMachine = new MixingPlantMachine();
        firstMachine.setMachineName("一号机组");
        firstMachine.setMachineNumber(1);
        firstMachine.setEnable((byte) 1);
        secondMachine.setMachineName("二号机组");
        secondMachine.setMachineNumber(2);
        secondMachine.setEnable((byte) 0);
        thirdMachine.setMachineName("三号机组");
        thirdMachine.setMachineNumber(3);
        thirdMachine.setEnable((byte) 0);
        mixingPlantMachineList.add(firstMachine);
        mixingPlantMachineList.add(secondMachine);
        mixingPlantMachineList.add(thirdMachine);
        saveBatch(mixingPlantMachineList);
    }

}
