package cn.pinming.microservice.material.management.proxy.impl;

import cn.pinming.core.upload.AliyunOssUploadComponent;
import cn.pinming.core.upload.DynamicUploadComponent;
import cn.pinming.core.upload.UploadComponent;
import cn.pinming.microservice.material.management.proxy.FileServiceProxy;
import cn.pinming.model.FileInfos;
import cn.pinming.model.dto.FilePreviewDto;
import cn.pinming.model.dto.OssFileDto;
import cn.pinming.v2.common.api.dto.FileDto;
import cn.pinming.v2.common.api.service.FileService;
import cn.pinming.zhuang.api.file.service.FileDtoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by jin on 2020-03-17.
 */
@Slf4j
@Component
public class FileServiceProxyImpl implements FileServiceProxy {

    @Reference(parameters = {
            "fileDownloadUrl.timeout","2000"
    })
    private FileInfos fileInfos;

    @Reference
    private FileService fileService;

    @Override
    public  UploadComponent getAliyunOssUploadComponent() {
        AliyunOssUploadComponent aliyunOssUploadComponent = new AliyunOssUploadComponent(fileInfos);
        return aliyunOssUploadComponent;
    }

    @Override
    public UploadComponent getDynamicUploadComponent() {
        DynamicUploadComponent dynamicUploadComponent = new DynamicUploadComponent(fileInfos);
        return dynamicUploadComponent;
    }

    @Override
    public void confirmFile(String uuid, String fileCode) {
        fileService.confirmFile(uuid,fileCode);
    }

    @Override
    public void confirmFiles(List<String> fileUuids, String code) {
        fileService.confirmFiles(fileUuids, code);
    }

    @Override
    public FilePreviewDto findFilePreview(String uuid) {
        FilePreviewDto filePreview = fileInfos.findFilePreview(uuid);
        return filePreview;
    }

    @Override
    public List<FileDto> findFilesByUUIDs(List<String> fileList) {
        return fileService.findFilesByUUIDs(fileList);
    }

    @Override
    public FileDto findFilesByUUID(@NotNull String fileUUID) {
        return fileService.findFileByUUID(fileUUID);
    }

    @Override
    public String fileDownloadUrlByUUID(String fileUUID) {
        return fileService.fileDownloadUrlByUUID(fileUUID);
    }

    @Override
    public List<String> fileDownloadUrlByUUIDs(List<String> fileUUIDs) {
        return fileService.fileDownloadUrlByUUIDs(fileUUIDs);
    }

    @Override
    public void deleteFileByUUID(String fileUUID) {
        OssFileDto ossFileDto = new OssFileDto();
        ossFileDto.setFileId(fileUUID);
        fileInfos.deleteFile(ossFileDto);
    }

    @Override
    public void deleteFilesByUUIDs(List<String> fileUUIDs) {
        List<OssFileDto> ossFileList= fileUUIDs.stream().map(obj -> {
            OssFileDto ossFileDto = new OssFileDto();
            ossFileDto.setFileId(obj);
            return ossFileDto;
        }).collect(Collectors.toList());
        fileInfos.deleteBatchFiles(ossFileList);
    }

}
