package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 项目配置表
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-09-05 15:43:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("s_project_config")
@ApiModel(value = "ProjectConfig对象", description = "项目配置表")
public class ProjectConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "类型 1.报备收料是否自动对账归档 2.是否有权限设置含水率 3.允许修正后立即完成对账核对 4.ocr相关配置 5.同步仓库配置")
    private Byte type;

    @ApiModelProperty(value = " 1 是 2 否")
    @TableField("is_enable")
    private Byte isEnable;

    @ApiModelProperty(value = "可选值")
    @TableField("scope")
    private String scope;

    @ApiModelProperty(value = "ocr模版id")
    @TableField("ocr_module_id")
    private String ocrModuleId;

    @ApiModelProperty(value = "ocr单据分类 1 一车一料 2 一车多料")
    @TableField("ocr_module_type")
    private Byte ocrModuleType;

    @ApiModelProperty(value = "物料分类")
    @TableField("ocr_material_type")
    private String ocrMaterialType;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;



}
