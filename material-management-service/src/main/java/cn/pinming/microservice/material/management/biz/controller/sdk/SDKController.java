package cn.pinming.microservice.material.management.biz.controller.sdk;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.material.v2.Material;
import cn.pinming.material.v2.MaterialClientBuilder;
import cn.pinming.material.v2.model.Page;
import cn.pinming.material.v2.model.PageList;
import cn.pinming.material.v2.model.QueryPage;
import cn.pinming.material.v2.model.WeighDataAssemble;
import cn.pinming.material.v2.model.dto.WeighDataDTO;
import cn.pinming.material.v2.model.form.MatchForm;
import cn.pinming.material.v2.model.form.WeighDataAssembleForm;
import cn.pinming.material.v2.model.query.WeighDataQuery;
import cn.pinming.microservice.material.management.biz.entity.MaterialData;
import cn.pinming.microservice.material.management.biz.entity.SdkConfig;
import cn.pinming.microservice.material.management.biz.form.MobileMaterialBatchForm;
import cn.pinming.microservice.material.management.biz.form.PackageForm;
import cn.pinming.microservice.material.management.biz.form.WeighDataForm;
import cn.pinming.microservice.material.management.biz.iot.service.IWeighbridgeService;
import cn.pinming.microservice.material.management.biz.service.IMaterialDataService;
import cn.pinming.microservice.material.management.biz.service.ISdkConfigService;
import cn.pinming.microservice.material.management.common.SuccessResponse;
import cn.pinming.microservice.material.management.common.util.DateUtils;
import cn.pinming.microservice.material.management.common.util.OCRConvertUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

@Slf4j
@Api(tags = "sdk-controller", value = "zh")
@RestController
@RequestMapping("/api/biz/sdk")
public class SDKController {
    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private ISdkConfigService sdkConfigService;
    @Resource
    private OCRConvertUtil ocrConvertUtil;
    @Value("${sdk.host}")
    private String host;

    @Value("${sdk.appKey}")
    private String appKey;

    @Value("${sdk.appSecretKey}")
    private String appSecretKey;
    @Autowired
    private IWeighbridgeService weighbridgeService;

    @ApiOperation(value = "数据重组")
    @PostMapping("/sdk")
    public ResponseEntity<Response> sdk(@RequestBody WeighDataAssembleForm form) {
        Material material = appJudge();
        WeighDataAssemble weighDataAssemble = material.weighDataAssemble(form);
        return ResponseEntity.ok(new SuccessResponse(weighDataAssemble));
    }

    @ApiOperation(value = "重组数据落库")
    @PostMapping("/packageSave")
    public ResponseEntity<Response> packageSave(@RequestBody @Validated PackageForm form) {
        // 模拟重组结果符合业务所需,进行客户端落库
        String weighId = IdUtil.simpleUUID();
        form.setWeighId(weighId);
        materialDataService.saveSdk(form);
        // 确认数据
        WeighDataAssembleForm assembleForm = new WeighDataAssembleForm();
        assembleForm.setFirst(form.getRecordId1());
        assembleForm.setSecond(form.getRecordId2());
        assembleForm.setAttributionCode(form.getCode());
        this.confirm(assembleForm);
        // 返回数据
        MaterialData data = materialDataService.lambdaQuery()
                .eq(MaterialData::getWeighId, weighId)
                .one();
        return ResponseEntity.ok(new SuccessResponse(data));
    }

    @ApiOperation(value = "查询称重数据")
    @PostMapping("/query")
    public ResponseEntity<Response> queryDataList(@RequestParam(value = "current", required = false, defaultValue = "1") int current,
                                                  @RequestParam(value = "size", required = false, defaultValue = "1000") int size,
                                                  @RequestBody WeighDataForm form) {
        Material materialService = appJudge();
        QueryPage<WeighDataQuery> queryPage = new QueryPage<>();
        queryPage.setPage(new Page(current, size));
        WeighDataQuery query = new WeighDataQuery();
        String startTime = form.getStartTime();
        String endTime = form.getEndTime();
        query.setIds(form.getIds());
        query.setTruckNos(form.getTruckNos());
        query.setDeviceSn(form.getDeviceSn());
        query.setAttributionCode(form.getAttributionCode());
        query.setStartTime(StringUtils.isBlank(startTime) ? null : DateUtils.parseLocalDateTime(startTime));
        query.setEndTime(StringUtils.isBlank(endTime) ? null : DateUtils.parseLocalDateTime(endTime));
        queryPage.setT(query);
        PageList<WeighDataDTO> pageList = materialService.queryWeighData(queryPage);
        return ResponseEntity.ok(new SuccessResponse(pageList));
    }

    @ApiOperation(value = "SDK测试")
    @PostMapping("/confirm")
    public ResponseEntity<Response> confirm(@RequestBody WeighDataAssembleForm form) {
        Material material = appJudge();
        Boolean flag = material.weighDataConfirm(form);
        return ResponseEntity.ok(new SuccessResponse(flag));
    }

    @ApiOperation(value = "OCR批量落库")
    @PostMapping("/ocr")
    public ResponseEntity<Response> batchOcr(@RequestBody @Validated MatchForm form) throws IOException {
        Material material = appJudge();
        String res = material.ocrMatch(form);
        MobileMaterialBatchForm result = ocrConvertUtil.convert(res, true);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "OCR单个落库")
    @PostMapping("/singleOcr")
    public ResponseEntity<Response> singleOcr(@RequestBody @Validated MatchForm form) throws IOException {
        Material material = appJudge();
        String res = material.ocrMatch(form);
        MobileMaterialBatchForm result = ocrConvertUtil.convert(res, false);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    private Material appJudge() {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (currentUser.getCurrentProjectId() != null) {
            SdkConfig projectConfig = sdkConfigService.lambdaQuery()
                    .eq(SdkConfig::getCompanyId, currentUser.getCurrentCompanyId())
                    .eq(SdkConfig::getProjectId, currentUser.getCurrentProjectId())
                    .one();
            if (ObjectUtil.isNotNull(projectConfig)) {
                return new MaterialClientBuilder().build(host, projectConfig.getAppKey(), projectConfig.getAppSecretKey());
            }
        } else {
            LambdaQueryChainWrapper<SdkConfig> wrapper = sdkConfigService.lambdaQuery()
                    .eq(SdkConfig::getCompanyId, currentUser.getCurrentCompanyId())
                    .isNull(SdkConfig::getProjectId);
            SdkConfig companyConfig = sdkConfigService.getOne(wrapper, false);
            if (ObjectUtil.isNotNull(companyConfig)) {
                return new MaterialClientBuilder().build(host, companyConfig.getAppKey(), companyConfig.getAppSecretKey());
            }
        }
        return new MaterialClientBuilder().build(host, appKey, appSecretKey);
    }
}
