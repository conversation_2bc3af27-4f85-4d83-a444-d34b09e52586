package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.MaterialNameOneDTO;
import cn.pinming.microservice.material.management.biz.dto.WarningDetailDTO;
import cn.pinming.microservice.material.management.biz.dto.WarningInfoDTO;
import cn.pinming.microservice.material.management.biz.dto.WarningOverviewSecondDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialWarning;
import cn.pinming.microservice.material.management.biz.query.SummaryDeliveryQuery;
import cn.pinming.microservice.material.management.biz.query.WarningDetailQuery;
import cn.pinming.microservice.material.management.biz.query.WarningInfoQuery;
import cn.pinming.microservice.material.management.biz.timetask.AutoProcessWarningType;
import cn.pinming.microservice.material.management.biz.timetask.WarningAutoProcessTask;
import cn.pinming.microservice.material.management.biz.vo.SummaryWarningSecondVO;
import cn.pinming.microservice.material.management.biz.vo.SummaryWarningVO;
import cn.pinming.microservice.material.management.biz.vo.WarningSummaryAnalysisVO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 预警信息 Mapper 接口
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 10:46:38
 */
public interface MaterialWarningMapper extends BaseMapper<MaterialWarning> {

    IPage<WarningInfoDTO> selectPageByQuery(@Param("query") WarningInfoQuery warningInfoQuery);

    /**
     * 预警总览数据
     * @param query query
     * @return 览数据
     */
    List<SummaryWarningVO> listSummaryWarningByQuery(@Param("query") SummaryDeliveryQuery query);

    /**
     * 预警总览下钻数据
     * @param query query
     * @return 总览下钻数据
     */
    List<SummaryWarningSecondVO> listSummaryWarningSecondByQuery(@Param("query") SummaryDeliveryQuery query);

    /**
     * 总览预警汇总统计
     * @param query query
     * @return 汇总数据
     */
    WarningSummaryAnalysisVO queryWarningSummary(@Param("query") SummaryDeliveryQuery query);

    /**
     * 标题总览预警下钻统计
     * @param query query
     * @return 下钻数据
     */
    List<WarningOverviewSecondDTO> queryOverviewWarningSummary(@Param("query") SummaryDeliveryQuery query);

    /**
     * 总览预警异常提示
     * @param companyId 企业id
     * @param projectIds 项目ids
     * @param warningTypeList 预警类型列表
     * @return 异常数量
     */
    Integer queryOverviewWarningTips(@Param("companyId") Integer companyId, @Param("projectIds") List<Integer> projectIds, @Param("warningTypeList") List<Byte> warningTypeList);

    @InterceptorIgnore(tenantLine = "true")
    List<MaterialWarning> queryAllMaterialWarning(@Param("types") List<AutoProcessWarningType> types);

    /**
     * 称重转换系数异常预警:1.1 检查相应data与对应合同明细的转换系数，是否相同，相同则自动置为"已处理〞。仍不相同，则不作处理
     * @param ids
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<String> weighConvertOne(@Param("ids") List<String> ids);

    /**
     * 称重转换系数异常预警:1.2 检查相应data当前是否已设置转换系数，如已设置，则自动置为“已处理〞。仍末设置，则不作处理
     * @param ids
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<String> weighConvertTwo(@Param("ids") List<String> ids);

    /**
     * 面单应收数量异常预警:3.1 检查相应data当前是否已录入面单应收量，如已设置，则自动置为“已处理”。仍末设置，则不作处理
     * @param ids
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<String> receiveNumberOne(@Param("ids") List<String> ids);

    /**
     * 结算单位异常预警:
     *   4.1 检查相应data与对应合同明细的结算单位是否相同，相同则自动置为 “已处理”•。仍不相同，则不作处理
     *   4.2 检查相应data与对应合同明细的结算单位是否相同，相同则自动置为 〝已处理”，仍不相同，则不作处理
     * @param ids
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<String> chargeUnitOne(@Param("ids") List<String> ids);

    /**
     * 无效称重预警:6.1 检查相应data的材料ID与对应合同明细材料ID是否相同，相同则自动置为〝已处理”仍不相同，则不作处理
     * @param ids
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<String> invalidWeightOne(@Param("ids") List<String> ids);

    /**
     * 物料名称异常:7.1 检查相应data的材料ID在企业材料库内是否存在存在则自动置为"已处理〞。仍不存在则不作处理
     * @param ids
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<MaterialNameOneDTO> materialNameOne(@Param("ids") List<String> ids);

    /**
     * 根据预警来源编号获取预警信息
     * @param warningDetailQuery
     * @return
     */
    List<WarningDetailDTO> listWarningDetail(@Param("query") WarningDetailQuery warningDetailQuery);
}
