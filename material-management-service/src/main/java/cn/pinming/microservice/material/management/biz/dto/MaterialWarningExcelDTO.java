package cn.pinming.microservice.material.management.biz.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 预警信息
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 10:46:38
 */
@Data
public class MaterialWarningExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警id")
    @ExcelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "预警编号")
    @ExcelProperty("warning_no")
    private String warningNo;

    @ApiModelProperty(value = "1、称重转换系数异常 2、毛/皮重异常 3、面单应收数量异常 4、结算单位异常 5、超负差 6、无效称重 7、物料名称异常 8、超时未出场 9、超期未进场 10、皮重偏差异常")
    @ExcelProperty("warning_type")
    private Byte warningType;

    @ApiModelProperty(value = "预警类型子分类")
    @ExcelProperty("warning_sub_type")
    private Byte warningSubType;

    @ApiModelProperty(value = "预警信息")
    @ExcelProperty("warning_info")
    private String warningInfo;

    @ApiModelProperty(value = "预警来源")
    @ExcelProperty("warning_source")
    private String warningSource;

    @ApiModelProperty(value = "预警来源记录id")
    @ExcelProperty("warning_source_id")
    private String warningSourceId;

    @ApiModelProperty(value = "预警来源记录编号")
    @ExcelProperty("warning_source_no")
    private String warningSourceNo;

    @ApiModelProperty(value = "发生项目")
    @ExcelProperty("source_project_id")
    private Integer sourceProjectId;

    @ApiModelProperty(value = "处理人id")
    @ExcelProperty("handler_id")
    private String handlerId;

    @ApiModelProperty(value = "处理人姓名")
    @ExcelProperty("handler_name")
    private String handlerName;

    @ApiModelProperty(value = "处理人建议id")
    @ExcelProperty("handler_advice_id")
    private String handlerAdviceId;

    @ApiModelProperty(value = "处理时间")
    @ExcelProperty("handler_time")
    private LocalDateTime handlerTime;

    @ApiModelProperty(value = "预警状态: 1 待处理,2 已处理")
    @ExcelProperty("warning_status")
    private Byte warningStatus;

    @ApiModelProperty(value = "企业id")
    @ExcelProperty(value = "company_id")
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @ExcelProperty(value = "project_id")
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @ExcelProperty(value = "department_id")
    private Integer departmentId;




}
