package cn.pinming.microservice.material.management.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.management.biz.entity.MaterialData;
import cn.pinming.microservice.material.management.biz.service.IMaterialDataService;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 基石称重记录是否重复落库判断
 */
@Component
public class WeighDuplicationUtil {
    @Resource
    private IMaterialDataService materialDataService;

    public void judge(List<String> recordIdList) {
        if (CollUtil.isEmpty(recordIdList)) {return;}
//        List<MaterialData> list = materialDataService.lambdaQuery()
//                .in(MaterialData::getRecordId1, recordIdList)
//                .list();
//        List<MaterialData> list1 = materialDataService.lambdaQuery()
//                .in(MaterialData::getRecordId2, recordIdList)
//                .list();
//        if (CollUtil.isNotEmpty(list) || CollUtil.isNotEmpty(list1)) {
//            throw new BOException(BOExceptionEnum.RECORD_HAS_USED);
//        }
    }
}
