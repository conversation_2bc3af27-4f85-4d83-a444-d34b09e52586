package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.dto.SimpleContractDetailDTO;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContract;
import cn.pinming.microservice.material.management.biz.form.PurchaseContractForm;
import cn.pinming.microservice.material.management.biz.form.PushConfigForm;
import cn.pinming.microservice.material.management.biz.query.ContractQuery;
import cn.pinming.microservice.material.management.biz.query.PurchaseContractQuery;
import cn.pinming.microservice.material.management.biz.vo.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 采购合同 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface IPurchaseContractService extends IService<PurchaseContract> {

    IPage<?> pageListByQuery(PurchaseContractQuery query);

    String  savePurchaseContract(PurchaseContractForm form);

    PurchaseContractVO queryDetailById(String id,Integer companyId);

    void deletePurchaseContractById(String id);

    List<ProjectVO> listBelongProjects(String id);

    List<PurchaseContractVO> listContract(String contractName);

    List<PurchaseOrderStatusVO> listOrderStatus();

    List<CooperateVO> getCooperateList();

    List<CooperateVO> listSupplier(String supplierName);

    List<PurchaseContractVO> listBySupplierId(ContractQuery query);

    List<ContractForReviseVO> getContractForRevise(Integer supplierId);

    Set<Integer> listUsedSupplierId();

    List<AppContractChooseVO> appchoose(String contractId, String categoryId);

    List<ProjectVO> projectList();

    PushConfigVO queryPushConfig(String contractId);

    void savePushConfig(PushConfigForm form);

    SimpleContractDetailDTO querySimpleContractDetail(String cargoId);
}
