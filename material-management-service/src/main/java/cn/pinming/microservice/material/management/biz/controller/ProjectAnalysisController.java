package cn.pinming.microservice.material.management.biz.controller;

import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.query.DeviationInfoQuery;
import cn.pinming.microservice.material.management.biz.query.MobileWeighInfoQuery;
import cn.pinming.microservice.material.management.biz.query.WeighInfoQuery;
import cn.pinming.microservice.material.management.biz.service.IFourCircleService;
import cn.pinming.microservice.material.management.biz.service.IMaterialDataService;
import cn.pinming.microservice.material.management.biz.service.IMaterialSendReceiveService;
import cn.pinming.microservice.material.management.biz.service.IMobileReceiveService;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目应用分析 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-01
 */
@Api(tags = "项目应用分析-controller",value = "zh")
@RestController
@RequestMapping("/api/materialData")
public class ProjectAnalysisController {

//  客户设置条件：项目范围、二级分类id   前端传条件：所选顶点、时间范围

    @Resource
    private IMaterialSendReceiveService sendReceiveService;
    @Resource
    private IMobileReceiveService mobileReceiveService;
    @Resource
    private IFourCircleService fourCircleService;
    @Resource
    private IMaterialDataService materialDataService;

    @ApiOperation(value = "过磅情况",response = WeighInfoVO.class)
    @Log(title = "过磅情况", businessType = BusinessType.QUERY)
    @PostMapping("/weighInfo")
    public ResponseEntity<Response> showWeighInfo(@RequestBody WeighInfoQuery query){
        WeighInfoVO weighInfoVO = sendReceiveService.selectWeighInfo(query);
        return ResponseEntity.ok(new SuccessResponse(weighInfoVO));
    }

    @ApiOperation(value = "过磅情况-下钻页",response = WeighInfoVO.class)
    @Log(title = "更多情况", businessType = BusinessType.QUERY)
    @PostMapping("/weighInfos")
    public ResponseEntity<Response> showWeighInfos(@RequestBody WeighInfoQuery query){
        Map<String,WeighInfoVO> map = sendReceiveService.selectWeighInfos(query);
        return ResponseEntity.ok(new SuccessResponse(map));
    }

    @ApiOperation(value = "移动收发料统计",response = WeighInfoVO.class)
    @Log(title = "移动收发料统计", businessType = BusinessType.QUERY)
    @PostMapping("/mobileWeighInfo")
    public ResponseEntity<Response> showMobileWeighInfo(@RequestBody MobileWeighInfoQuery query){
        WeighInfoVO vo = mobileReceiveService.select(query);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "收料偏差情况-统计", response = WeighDeviationVO.class)
    @Log(title = "过磅收料偏差情况统计", businessType = BusinessType.QUERY)
    @PostMapping("/weighDeviation")
    public ResponseEntity<Response> showWeighDeviation(@RequestBody DeviationInfoQuery query){
//      二级分类id范围，前端调接口
        WeighDeviationVO weighDeviationVO = sendReceiveService.selectWeighDeviation(query);
        return ResponseEntity.ok(new SuccessResponse(weighDeviationVO));
    }

    @ApiOperation(value = "收料偏差情况-列表",response = WeighDeviationDetailVO.class)
    @PostMapping("/deviationList")
    public ResponseEntity<Response> deviationList(@RequestBody DeviationInfoQuery query){
//      二级分类id范围，前端调接口
        IPage<WeighDeviationDetailVO> page = mobileReceiveService.selectDeviationList(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "四小圆",response = FourCircleVO.class)
    @GetMapping("/fourCircle/{point}")
    public ResponseEntity<Response> fourCircle(@PathVariable("point")Integer point){
        FourCircleVO vo = fourCircleService.selectRate(point);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "四小圆-下钻页",response = FourCircleVO.class)
    @GetMapping("/fourCircleList/{point}")
    public ResponseEntity<Response> fourCircleList(@PathVariable("point")Integer point){
        Map<String, FourCircleVO>  map = fourCircleService.selectList(point);
        return ResponseEntity.ok(new SuccessResponse(map));
    }

    @ApiOperation(value = "车辆过磅情况-下钻页",response = WeighCarVO.class)
    @Log(title = "更多情况", businessType = BusinessType.QUERY)
    @PostMapping("/weighCar")
    public ResponseEntity<Response> showWeighCar(@RequestBody WeighInfoQuery query){
        Map<String,WeighCarVO> map = materialDataService.showWeighCar(query);
        return ResponseEntity.ok(new SuccessResponse(map));
    }

    @ApiOperation(value = "移动车辆过磅情况-下钻页",response = WeighCarVO.class)
    @Log(title = "移动车辆-更多情况", businessType = BusinessType.QUERY)
    @PostMapping("/MobileCar")
    public ResponseEntity<Response> showMobileWeighCar(@RequestBody WeighInfoQuery query){
        Map<String,WeighCarVO> map = mobileReceiveService.showWeighCar(query);
        return ResponseEntity.ok(new SuccessResponse(map));
    }

//    @ApiOperation(value = "过磅情况 - 导出",response = WeighInfoVO.class)
//    @Log(title = "过磅收料情况列表 - 导出", businessType = BusinessType.QUERY)
//    @PostMapping("/local/detail/export")
//    public void detailExport(HttpServletResponse response, @RequestBody WeighInfoQuery query) {
//        query.setPages(0);
//        query.setSize(Integer.MAX_VALUE);
//        AuthUser user = authUserHolder.getCurrentUser();
//        query.setCompanyId(user.getCurrentCompanyId());
//        IPage<WeighInfoVO> weighInfoVOList  = sendReceiveService.selectWeighInfos(query);
//        ExcelUtils.export(response,weighInfoVOList.getRecords(),WeighInfoVO.class,"收料过磅详情");
//    }
}
