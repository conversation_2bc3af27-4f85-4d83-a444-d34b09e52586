package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.entity.PreTruckReport;
import cn.pinming.microservice.material.management.biz.vo.PreTruckReportDetailVO;
import cn.pinming.microservice.material.management.biz.vo.PreTruckReportVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 送货车辆预报备 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-11-16 15:07:43
 */
public interface PreTruckReportMapper extends BaseMapper<PreTruckReport> {

    List<PreTruckReportVO> selectDetail(@Param("id") String id);

    List<PreTruckReportDetailVO> selectData(@Param("id") String id);

    List<String> selectTruckAnomalStatus(@Param("list") List<String> truckNoList, @Param("projectId") Integer projectId);

    List<String> selectTruckDetailIdsByTruckNo(@Param("list") List<String> truckNos, @Param("projectId") Integer currentProjectId);

    String selectId(@Param("preWeighReportId") String preWeighReportId);
}
