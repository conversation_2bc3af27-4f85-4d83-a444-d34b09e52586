package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 采购单
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_purchase_order")
@ApiModel(value = "PurchaseOrder对象", description = "采购单")
public class PurchaseOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "采购单ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "名称")
    @TableField("order_name")
    private String orderName;

    @ApiModelProperty(value = "编号")
    @TableField("order_no")
    private String orderNo;

    @ApiModelProperty(value = "采购合同ID")
    @TableField("contract_id")
    private String contractId;

    @ApiModelProperty(value = "采购类目")
    private String category;

    @ApiModelProperty(value = "供应商ID")
    @TableField("supplier_id")
    private Integer supplierId;

    @ApiModelProperty(value = "计划使用部位")
    private String position;

    @ApiModelProperty(value = "下单日期")
    @TableField("order_time")
    private LocalDate orderTime;

    @ApiModelProperty(value = "收货地址")
    @TableField("receiver_address")
    private String receiverAddress;

    @ApiModelProperty(value = "收货项目")
    @TableField("receiver_project")
    private Integer receiverProject;

    @ApiModelProperty(value = "收货人")
    private String receiver;

    @ApiModelProperty(value = "收货电话")
    @TableField("receiver_tel")
    private String receiverTel;

    @ApiModelProperty(value = "要货日期")
    @TableField("receive_time")
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "采购单状态， 1：待审批；2：待复核；3：待收货；4：收货中；5：收货完毕")
    private Byte status;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


    @ApiModelProperty(value = "是否推送至基石“订单/发货”系统 1 是 0 否")
    private Byte isPush;

    @ApiModelProperty(value = "推送基石平台状态 1 是 0 否")
    private Byte pushStatus;

}
