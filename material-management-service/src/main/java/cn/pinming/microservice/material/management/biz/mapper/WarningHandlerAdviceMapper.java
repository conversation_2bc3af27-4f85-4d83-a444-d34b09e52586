package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.HandleAdviceDTO;
import cn.pinming.microservice.material.management.biz.entity.WarningHandlerAdvice;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 预警处理人建议表 Mapper 接口
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 13:21:53
 */
public interface WarningHandlerAdviceMapper extends BaseMapper<WarningHandlerAdvice> {


    /**
     * 获取最新处理建议
     * @param warningIds 预警ids
     * @return 建议
     */
    @InterceptorIgnore(tenantLine = "true")
    List<HandleAdviceDTO> selectLastHandlerAdvice(List<String> warningIds);
}
