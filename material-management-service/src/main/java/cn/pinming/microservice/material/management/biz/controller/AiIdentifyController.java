package cn.pinming.microservice.material.management.biz.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.pinming.algModelApi.api.dto.RebarResultDto;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.ErrorResponse;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.enums.AiIdentifyEnum;
import cn.pinming.microservice.material.management.biz.service.AiIdentifyService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/2/9
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/api/ai/identify")
public class AiIdentifyController {

    @Resource
    public AuthUserHolder authUserHolder;

    @Resource
    public AiIdentifyService aiIdentifyService;

    @ApiOperation(value = "获取业务Token")
    @GetMapping("/token")
    public ResponseEntity<Response> getAIBizToken(@RequestParam("bizId") String bizId,
                                                  @RequestParam("projectId") Integer projectId,
                                                  @RequestParam("companyId") Integer companyId) {
        // 校验业务ID
        if (AiIdentifyEnum.bizIdValidate(bizId)) {
            return ResponseEntity.ok(new ErrorResponse("invalid param", "业务ID错误"));
        }
        AuthUser user = authUserHolder.getCurrentUser();
        Integer currentProjectId = user.getCurrentProjectId();
        // 校验项目ID
        if (ObjectUtil.isNull(currentProjectId) || ObjectUtil.isNull(projectId) || currentProjectId.intValue() != projectId.intValue()) {
            return ResponseEntity.ok(new ErrorResponse("invalid param", "项目不匹配"));
        }
        String userId = user.getId();
        String token = aiIdentifyService.getToken(AiIdentifyEnum.valueOf(bizId), userId, projectId, companyId);
        return ResponseEntity.ok(new SuccessResponse(token));
    }

    @ApiOperation(value = "AI智能识别业务")
    @GetMapping("/result")
    public ResponseEntity<Response> getRebarCalculateResult(@RequestParam("bizId") String bizId,
                                                            @RequestParam("token") String token) {
        // 校验业务ID
        if (AiIdentifyEnum.bizIdValidate(bizId)) {
            return ResponseEntity.ok(new ErrorResponse("invalid param", "业务ID错误"));
        }
        // token校验
        if (StringUtils.isBlank(token)) {
            return ResponseEntity.ok(new ErrorResponse("invalid param", "token为空"));
        }
        RebarResultDto rebarCalculateResult = aiIdentifyService.getRebarCalculateResult(AiIdentifyEnum.valueOf(bizId), token);
        return ResponseEntity.ok(new SuccessResponse(rebarCalculateResult));
    }

}
