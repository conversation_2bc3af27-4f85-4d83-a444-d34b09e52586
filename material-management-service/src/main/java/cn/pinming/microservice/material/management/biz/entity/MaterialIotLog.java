package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * iot日志
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-11-27 22:01:11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("s_material_iot_log")
@ApiModel(value = "MaterialIotLog对象", description = "iot日志")
public class MaterialIotLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "日志ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "请求参数")
    @TableField("request_param")
    private String requestParam;

    @ApiModelProperty(value = "返回结果")
    @TableField("request_result")
    private String requestResult;

    @ApiModelProperty(value = "错误详情")
    @TableField("error_info")
    private String errorInfo;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
