package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.core.cookie.AuthUser;
import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContractDetail;
import cn.pinming.microservice.material.management.biz.entity.PurchaseOrder;
import cn.pinming.microservice.material.management.biz.query.DeviationInfoQuery;
import cn.pinming.microservice.material.management.biz.query.PurchaseOrderQuery;
import cn.pinming.microservice.material.management.biz.vo.*;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.math.BigDecimal;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 采购单 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface PurchaseOrderMapper extends BaseMapper<PurchaseOrder> {

    @InterceptorIgnore(tenantLine = "true")
    IPage<PurchaseOrderDTO> selectPageDTO(PurchaseOrderQuery query);

    @InterceptorIgnore(tenantLine = "true")
    PurchaseOrderDTO selectPurchaseOrderById(@Param("id") String id);

    @InterceptorIgnore(tenantLine = "true")
    List<PurchaseContractDetail> selectMaterialIdInfo(@Param("purchaseId")String purchaseId, @Param("projectId")Integer projectId);

    @InterceptorIgnore(tenantLine = "true")
    List<String> selectOrderById(@Param("companyId")Integer companyId,@Param("projectId") Integer projectId);

    @InterceptorIgnore(tenantLine = "true")
    void updateStatusById( @Param("purchaseId")String purchaseId, @Param("companyId")Integer companyId, @Param("projectId")Integer projectId,@Param("status") byte status);

    /**
     * 采购单编号动态模糊查询
     *
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<OrderNoVO> seek(@Param("orderNo")String orderNo, AuthUser user);

    /**
     * 获取采购单信息
     *
     * @param orderId
     * @return
     */
    PurchaseSimpleVO selectOrderInfo(@Param("orderId")String orderId);

    @InterceptorIgnore(tenantLine = "true")
    void remove(@Param("id")String id);

    @InterceptorIgnore(tenantLine = "true")
    Integer count(@Param("id")String id);

    List<PrePurchaseDTO> listPurchase(@Param("supplierId")Integer supplierId, @Param("projectId")Integer projectId,@Param("companyId")Integer companyId);

    MaterialSimpleDTO selectMaterial(@Param("purchaseId")String purchaseId,@Param("materialId")Integer materialId);


    @InterceptorIgnore(tenantLine = "true")
    PurchaseOrder selectIdByPurchaseNo(@Param("purchaseNo") String purchaseNo);

    List<ContractDecisionDTO> findDecisionFactor(@Param("purchaseNo")String purchaseNo);

    /**
     * 累计采购下单量
     *
     * @param query
     * @return
     */
    BigDecimal selectPurchaseAccount(DeviationInfoQuery query);

    List<PurchaseForReviseVO> listForRevise(@Param("contractId") String contractId,Integer projectId);

    PurchaseOrder selectOrderByPruNo(@Param("purNo")String completePruNo,@Param("projectId") Integer projectId);

    PurchaseSimpleVO getPurchaseHistory(@Param("companyId")Integer companyId,@Param("projectId") Integer projectId,@Param("updateId") String updateId);

    PurchaseOrderReceiveInfoVO lastReceiveInfo(@Param("userId") String userId);

    Integer queryPlantId(@Param("supplierId") Integer supplierId);

    List<PurchasePdfDetailVO> selectDetail(@Param("id") String id);

    List<PurchaseOrderDetailVO> getPurchaseOrderDetail(@Param("list") List<String> purchaseOrderDetailIdList);
}
