package cn.pinming.microservice.material.management.biz.enums;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/3/21 10:56
 */
public enum VerifyEnum {
    VERIFY((byte) 1, "已对账"),
    NO_VERIFY((byte) 2, "未对账");

    /**
     * 状态值
     */
    private final byte value;
    /**
     * 状态的描述
     */
    private final String description;

    VerifyEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte value() {
        return value;
    }

    public String description() {
        return description;
    }

    public static String desc(Byte value){
        if (value == null) {return null;}
        for (VerifyEnum statusEnum : VerifyEnum.values()) {
            if (statusEnum.value == value) {
                return statusEnum.description;
            }
        }
        return null;
    }
}
