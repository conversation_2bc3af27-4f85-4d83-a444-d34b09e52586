package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.entity.IngredientApplyDetail;
import cn.pinming.microservice.material.management.biz.vo.MaterialReportVO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 拌合站配料申请明细 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 09:54:01
 */
public interface IngredientApplyDetailMapper extends BaseMapper<IngredientApplyDetail> {

    @InterceptorIgnore(tenantLine = "true")
    List<IngredientApplyDetail> queryIngredientApplyDetailByReportIds(@Param("reportIds") List<String> reportIds);

    List<MaterialReportVO> selectApplyDetailVO(@Param("id") String id);
}
