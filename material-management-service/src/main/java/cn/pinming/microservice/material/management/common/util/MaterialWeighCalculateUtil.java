package cn.pinming.microservice.material.management.common.util;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContractDetail;
import cn.pinming.microservice.material.management.biz.enums.DeviationStatusEnum;
import cn.pinming.microservice.material.management.biz.enums.WeighTypeEnum;
import cn.pinming.microservice.material.management.biz.form.MaterialWeighBaseForm;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 */
@Component
public class MaterialWeighCalculateUtil {
    public void calculate(MaterialWeighBaseForm form,PurchaseContractDetail contractDetail) {
        // 净重
        BigDecimal weightNet = NumberUtil.sub(form.getWeightGross(), form.getWeightTare());
        // 实重
        BigDecimal weightActual = NumberUtil.sub(weightNet,form.getWeightDeduct());

        if (form.getType() == WeighTypeEnum.RECEIVE.value()) {
            weightActual = NumberUtil.mul(weightActual, NumberUtil.div(NumberUtil.sub(BigDecimal.valueOf(100), form.getMoistureContent()), BigDecimal.valueOf(100)));
        }
        // 实际数量
        BigDecimal actualCount = NumberUtil.mul(form.getRatio(),weightActual);
        form.setWeightNet(weightNet);
        form.setWeightActual(weightActual);
        form.setActualCount(actualCount);

        // 偏差量
        if (form.getType() == WeighTypeEnum.RECEIVE.value()) {
            BigDecimal deviation = NumberUtil.mul(NumberUtil.div(NumberUtil.sub(actualCount, form.getWeightSend()), form.getWeightSend(), 4), 100);
            form.setDeviationRate(deviation);

            // 偏差状态
            if (ObjectUtil.isNotNull(contractDetail)) {
                BigDecimal deviationCeiling = contractDetail.getDeviationCeiling();
                BigDecimal deviationFloor = contractDetail.getDeviationFloor();
                int flag;
                int flag1;
                flag = deviation.compareTo(deviationFloor);
                flag1 = deviation.compareTo(deviationCeiling);
                if (flag < 0) {
                    form.setDeviationStatus(DeviationStatusEnum.NEGATIVEDIFFERENCE.value());
                }
                if (flag1 > 0) {
                    form.setDeviationStatus(DeviationStatusEnum.POSITIVEDIFFERENCE.value());
                }
                if (flag >= 0 && flag1 <= 0) {
                    form.setDeviationStatus(DeviationStatusEnum.NORMAL.value());
                }
            }
        }
    }
}
