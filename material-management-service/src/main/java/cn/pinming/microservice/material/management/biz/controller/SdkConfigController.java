package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.entity.SdkConfig;
import cn.pinming.microservice.material.management.biz.form.SdkConfigForm;
import cn.pinming.microservice.material.management.biz.service.ISdkConfigService;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 基石平台配置表 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2023-07-06 13:52:11
 */
@RestController
@RequestMapping("/api/sdk-config")
public class SdkConfigController {

    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private ISdkConfigService sdkConfigService;

    @PostMapping("save")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<Response> save(@RequestBody SdkConfigForm form) {
        SdkConfig sdkConfig = new SdkConfig();
        sdkConfig.setId(form.getId());
        sdkConfig.setAppKey(form.getAppKey().trim());
        sdkConfig.setAppSecretKey(form.getAppSecretKey().trim());
        sdkConfigService.saveOrUpdate(sdkConfig);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @GetMapping("info")
    public ResponseEntity<Response> info() {
        AuthUser user = authUserHolder.getCurrentUser();
        Integer companyId = user.getCurrentCompanyId();
        Integer projectId = user.getCurrentProjectId();
        SdkConfig sdkConfig = sdkConfigService.lambdaQuery()
                .eq(SdkConfig::getCompanyId, companyId)
                .eq(Objects.nonNull(projectId), SdkConfig::getProjectId, projectId)
                .isNull(Objects.isNull(projectId), SdkConfig::getProjectId)
                .select(SdkConfig::getId, SdkConfig::getAppKey, SdkConfig::getAppSecretKey)
                .one();
        return ResponseEntity.ok(new SuccessResponse(sdkConfig));
    }

    @GetMapping("delete/{id}")
    public ResponseEntity<Response> delete(@PathVariable("id")Long id) {
        sdkConfigService.removeById(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

}
