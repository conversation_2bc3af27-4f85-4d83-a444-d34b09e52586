package cn.pinming.microservice.material.management.biz.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.form.PlantOrderForm;
import cn.pinming.microservice.material.management.biz.form.PlantOrderStatusForm;
import cn.pinming.microservice.material.management.biz.service.IMixingPlantOrderService;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 拌合站订单 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-28 15:26:17
 */
@Slf4j
@RestController
@Api(tags = "拌合站订单", value = "zh")
@RequestMapping("/api/plant/order")
public class MixingPlantOrderController {

    @Resource
    private IMixingPlantOrderService plantOrderService;

    @ApiOperation(value = "选择发货订单",response = MixingPlantOrderDetailVO.class)
    @Log(title = "选择发货订单", businessType = BusinessType.QUERY)
    @GetMapping("/choose")
    public ResponseEntity<Response> choose(@RequestParam(value = "name",required = false)String name,@RequestParam(value = "no",required = false) String no) {
        List<MixingPlantOrderDetailVO> list = plantOrderService.choose(name,no);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "选择客户")
    @Log(title = "选择客户", businessType = BusinessType.QUERY)
    @GetMapping("/customer")
    public ResponseEntity<Response> customer() {
        List<String> list = plantOrderService.customer();
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "历史订单明细", response = ExistTicketDetailVO.class)
    @Log(title = "历史订单明细", businessType = BusinessType.QUERY)
    @PostMapping("/orderDetail")
    public ResponseEntity<Response> orderDetail() {
        ExistTicketDetailVO vo = plantOrderService.orderDetail();
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "新增&修改拌合站订单(修改传ID，新增不传ID)")
    @PostMapping
    public ResponseEntity<Response> addOrUpdate(@RequestBody PlantOrderForm plantOrderForm) {
        String id = plantOrderForm.getId();
        if (StringUtils.isBlank(id)) {
            plantOrderService.addPlantOrder(plantOrderForm);
        } else {
            plantOrderService.updatePlantOrder(plantOrderForm);
        }
        return ResponseEntity.ok(new SuccessResponse());
    }


    @ApiOperation(value = "修改订单明细状态")
    @PostMapping("/status")
    public ResponseEntity<Response> updateStatus(@RequestBody PlantOrderStatusForm plantOrderStatusForm) {
        plantOrderService.updateStatus(plantOrderStatusForm.getId(), plantOrderStatusForm.getStatus());
        return ResponseEntity.ok(new SuccessResponse());
    }


    @ApiOperation(value = "订单状态数量统计", response = MixingPlantOrderStatusCountVO.class)
    @GetMapping("/count")
    public ResponseEntity<Response> orderStatusCount() {
        List<MixingPlantOrderStatusCountVO> list = plantOrderService.orderStatusCount();
        return ResponseEntity.ok(new SuccessResponse(list));
    }


    @ApiOperation(value = "订单分页", response = MixingPlantOrderVO.class)
    @GetMapping
    public ResponseEntity<Response> orderPage(@RequestParam(value = "status", required = false) Byte status,
                                              @RequestParam(value = "pageNum") Integer pageNum,
                                              @RequestParam(value = "pageSize") Integer pageSize) {
        IPage<MixingPlantOrderVO> orderPage = plantOrderService.plantOrderPage(status, pageNum, pageSize);
        return ResponseEntity.ok(new SuccessResponse(orderPage));
    }


    @ApiOperation(value = "修改-订单详情", response = MixingPlantOrderUpdateVO.class)
    @GetMapping("/update/{id}")
    public ResponseEntity<Response> orderDetailUpdate(@PathVariable("id") String id) {
        String orderId = plantOrderService.queryOrderIdByOrderDetailId(id);
        MixingPlantOrderUpdateVO mixingPlantOrderUpdateVO = plantOrderService.queryUpdateVO(orderId);
        return ResponseEntity.ok(new SuccessResponse(mixingPlantOrderUpdateVO));
    }


    @ApiOperation(value = "订单详情", response = MixingPlantOrderDetailVO.class)
    @GetMapping("/{id}")
    public ResponseEntity<Response> orderDetail(@PathVariable("id") String id) {
        // 展示订单下所有的话用订单ID查询即可
//        String orderId = plantOrderService.queryOrderIdByOrderDetailId(id);
        MixingPlantOrderDetailVO mixingPlantOrderDetailVO = plantOrderService.queryOrderDetailById(id);
        return ResponseEntity.ok(new SuccessResponse(mixingPlantOrderDetailVO));
    }


    @ApiOperation(value = "打印")
    @GetMapping("/preview/{id}")
    public ResponseEntity<SuccessResponse> preview(@PathVariable String id) {
//        String orderId = plantOrderService.queryOrderIdByOrderDetailId(id);
        String url = plantOrderService.getPrintPlantOrderUrl(id);
        return ResponseEntity.ok(new SuccessResponse(url));
    }


    @ApiOperation(value = "订单详情-发料明细", response = MixingPlantOrderSendDetailVO.class)
    @GetMapping("/send/{id}")
    public ResponseEntity<Response> orderSendDetail(@PathVariable("id") String id) {
        // 展示订单下所有的话用订单ID查询即可
//        String orderId = plantOrderService.queryOrderIdByOrderDetailId(id);
        List<MixingPlantOrderSendDetailVO> mixingPlantOrderSendDetailVOS = plantOrderService.queryPlantOrderSendDetailsById(id);
        return ResponseEntity.ok(new SuccessResponse(mixingPlantOrderSendDetailVOS));
    }

    @ApiOperation(value = "导出发料明细")
    @GetMapping("/export/{id}")
    public void export(@PathVariable("id") String id, HttpServletResponse response) {
        InputStream is = null;
        try {
            // 展示订单下所有的话用订单ID查询即可
//        String orderId = plantOrderService.queryOrderIdByOrderDetailId(id);
            List<MixingPlantOrderSendDetailVO> mixingPlantOrderSendDetailVOS = plantOrderService.queryPlantOrderSendDetailsById(id);
            is = this.getClass().getClassLoader().getResourceAsStream("templates/plant-order.xlsx");
            if (is != null) {
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("utf-8");
                String fileName = URLEncoder.encode("拌合站订单发料明细", "UTF-8");
                response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes("UTF-8"),"ISO-8859-1") + ".xlsx");
                ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build();
                WriteSheet sheet = EasyExcel.writerSheet("发料明细").build();
                excelWriter.fill(mixingPlantOrderSendDetailVOS, sheet);
                excelWriter.finish();
            }
        } catch (IOException e) {
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Map<String, String> map = new HashMap<>(4);
            map.put("status", "failure");
            map.put("message", "导出文件失败" + e.getMessage());
            try {
                response.getWriter().println(JSONUtil.toJsonStr(map));
            } catch (IOException ioException) {
                log.error("response write io error.", ioException.getMessage());
            }
            if (is != null) {
                try {
                    is.close();
                } catch (Exception ioCloseException) {
                    log.error("file read io close error.", ioCloseException.getMessage());
                }
            }
        }
    }

}
