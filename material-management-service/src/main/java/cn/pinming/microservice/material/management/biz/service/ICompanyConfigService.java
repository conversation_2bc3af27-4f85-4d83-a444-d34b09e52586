package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.core.cookie.AuthUser;
import cn.pinming.microservice.material.management.biz.entity.CompanyConfig;
import cn.pinming.microservice.material.management.biz.form.CompanyConfigForm;
import cn.pinming.microservice.material.management.biz.vo.CompanyConfigVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 项目配置表 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-13 10:28:23
 */
public interface ICompanyConfigService extends IService<CompanyConfig> {

    void saveOrUpdateConfig(CompanyConfigForm form);

    CompanyConfigVO show(Byte type, AuthUser user);

    Boolean enable(Integer companyId, Integer projectId, byte b);

}
