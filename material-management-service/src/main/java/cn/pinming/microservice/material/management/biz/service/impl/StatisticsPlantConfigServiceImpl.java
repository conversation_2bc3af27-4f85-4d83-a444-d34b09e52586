package cn.pinming.microservice.material.management.biz.service.impl;

import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.entity.StatisticsPlantConfig;
import cn.pinming.microservice.material.management.biz.mapper.StatisticsPlantConfigMapper;
import cn.pinming.microservice.material.management.biz.service.IStatisticsPlantConfigService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 统计配置表-拌合站 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-21 10:09:58
 */
@Service
public class StatisticsPlantConfigServiceImpl extends ServiceImpl<StatisticsPlantConfigMapper, StatisticsPlantConfig> implements IStatisticsPlantConfigService {

    @Resource
    private AuthUserHolder siteContextHolder;

    @Override
    public StatisticsPlantConfig queryOneConfig() {
        AuthUser currentUser = siteContextHolder.getCurrentUser();
        String id = currentUser.getId();
        Integer currentCompanyId = currentUser.getCurrentCompanyId();

        QueryWrapper<StatisticsPlantConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StatisticsPlantConfig::getCreateId, id)
                .eq(StatisticsPlantConfig::getCompanyId, currentCompanyId)
                .eq(StatisticsPlantConfig::getIsDeleted, 0);
        StatisticsPlantConfig one = this.getOne(queryWrapper);
        return one;
    }
}
