package cn.pinming.microservice.material.management.config;

import cn.pinming.core.cookie.AuthUserHelper;
import cn.pinming.core.cookie.CorsFilter;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.cookie.support.spring.AuthUserInterceptor;
import cn.pinming.core.web.exception.UnauthorizedException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.error.ErrorAttributes;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;


/**
 * Created by tcz on 2020/02/19.
 */
@Slf4j
@Configuration
public class WebConfiguration implements WebMvcConfigurer {

    @Resource
    private AuthUserInterceptor authUserInterceptor;
//    @Resource
//    private PermissionInterceptor permissionInterceptor;

    @Configuration
    static class WebComponentConfiguration {
        @Bean
        public AuthUserHelper authUserHelper(@Value("${cookie.domain}") String domain) {
            AuthUserHelper helper = new AuthUserHelper();
            helper.setDomain(domain);
            return helper;
        }

        @Bean
        public FilterRegistrationBean corsFilter() {
            FilterRegistrationBean<CorsFilter> bean =
                new FilterRegistrationBean<>(new CorsFilter());
            bean.setOrder(0);
            return bean;
        }

        @Bean
        public ErrorAttributes errorAttributes() {
            return new CommonErrorAttributesSpringBoot2();
        }

        @Bean
        public AuthUserInterceptor authUserInterceptor(
            @Qualifier("siteContextHolder") AuthUserHolder holder,
            AuthUserHelper authUserHelper) {
            return new AuthUserInterceptor(holder, authUserHelper, errorMessage -> {
                if (errorMessage == null) {
                    throw new UnauthorizedException();
                } else {
                    throw new UnauthorizedException(errorMessage);
                }
            });
        }
    }

    @Bean
    public MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter() {
        ObjectMapper objectMapper = Jackson2ObjectMapperBuilder.json().build();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer());
        simpleModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer());
        simpleModule.addDeserializer(LocalDate.class, new com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer(
            DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        simpleModule.addDeserializer(LocalDateTime.class, new com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer(
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        objectMapper.registerModule(simpleModule);
        return new MappingJackson2HttpMessageConverter(objectMapper);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        Objects.requireNonNull(authUserInterceptor, "AuthUserInterceptor can not be null");
        registry.addInterceptor(authUserInterceptor)
            .addPathPatterns("/api/**")
            .excludePathPatterns("/api/common/**")
            .excludePathPatterns("/api/callback/**");

//        registry.addInterceptor(permissionInterceptor)
//                .addPathPatterns("/api/**")
//                .excludePathPatterns("/api/common/**")
//                .excludePathPatterns("/api/callback/**");
    }

}
