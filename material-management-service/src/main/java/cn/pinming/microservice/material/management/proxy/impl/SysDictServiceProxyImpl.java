package cn.pinming.microservice.material.management.proxy.impl;

import cn.pinming.microservice.material.management.proxy.SysDictServiceProxy;
import cn.pinming.zhuang.api.system.dto.SysDictDto;
import cn.pinming.zhuang.api.system.dto.SysDictQueryDto;
import cn.pinming.zhuang.api.system.service.SysDictService;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

@Component
public class SysDictServiceProxyImpl implements SysDictServiceProxy {

    @Reference
    private SysDictService sysDictService;

    @Override
    public SysDictDto selectSysDict(SysDictQueryDto sysDictQueryDto) {
        return sysDictService.selectSysDict(sysDictQueryDto);
    }
}
