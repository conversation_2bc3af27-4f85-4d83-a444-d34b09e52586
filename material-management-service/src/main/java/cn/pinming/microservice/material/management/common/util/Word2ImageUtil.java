//package cn.pinming.microservice.material.management.common.util;
//
//import com.spire.doc.Document;
//import com.spire.doc.FileFormat;
//import com.spire.doc.documents.ImageType;
//import lombok.Data;
//
//import javax.imageio.ImageIO;
//import java.awt.image.BufferedImage;
//import java.io.File;
//import java.io.FileInputStream;
//import java.io.IOException;
//
//
//@Data
//public class Word2ImageUtil {
//    public static void changeDocToImg(File file, String imgPath) {
//        try {
//            Document doc = new Document();
//            //加载文件 第二个参数 FileFormat.Auto 会自动去分别上传文件的 docx、doc类型
//            doc.loadFromStream(new FileInputStream(file), FileFormat.Auto);
//            //上传文档页数，也是最后要生成的图片数
//            Integer pageCount = doc.getPageCount();
//            // 参数第一个和第三个都写死 第二个参数就是生成图片数
//            BufferedImage[] image = doc.saveToImages(0, pageCount, ImageType.Bitmap);
//            // 循环，输出图片保存到本地
//            for (int i = 0; i < image.length; i++) {
//                File f = new File(imgPath);
//                ImageIO.write(image[i], "PNG", f);
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }
//}
//
