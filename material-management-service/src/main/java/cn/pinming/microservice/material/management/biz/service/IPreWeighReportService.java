package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.core.cookie.AuthUser;
import cn.pinming.microservice.material.management.biz.entity.PreWeighReport;
import cn.pinming.microservice.material.management.biz.form.PreReportForm;
import cn.pinming.microservice.material.management.biz.form.PreReportNewForm;
import cn.pinming.microservice.material.management.biz.query.PreReportQuery;
import cn.pinming.microservice.material.management.biz.vo.ExpirePageVO;
import cn.pinming.microservice.material.management.biz.vo.PreReportDetailVO;
import cn.pinming.microservice.material.management.biz.vo.PreReportVO;
import cn.pinming.microservice.material.management.biz.vo.PreTruckReportDetailVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 进出场预报备 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-11-16 15:07:43
 */
public interface IPreWeighReportService extends IService<PreWeighReport> {

    String add(PreReportForm form,String code);

    void del(String id);

    IPage<PreReportVO> pageByQuery(PreReportQuery query, AuthUser user);

    PreReportDetailVO selectDetail(String id,Integer companyId);

    /**
     * 生成外部链接code
     * @return
     */
    ExpirePageVO generateExternalPageInfo(String id);

    /**
     * 校验code过期
     * @param code 生成外部链接code
     * @return  过期时间
     */
    ExpirePageVO checkExternalCodeExpire(String code);

    /**
     * 校验code是否已提交过  只能提交一次
     * @param code 生成外部链接code
     */
    void checkExternalCodeCommit(String code, boolean flag);

    List<PreReportVO> listByPurchaseId(String purchaseId);

    List<PreTruckReportDetailVO> listTruckByPurchaseId(String purchaseId);

    void submit(PreReportNewForm form);
}
