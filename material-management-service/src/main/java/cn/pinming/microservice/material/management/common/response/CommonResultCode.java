package cn.pinming.microservice.material.management.common.response;

import cn.pinming.microservice.material_unit.api.exceptions.ResultCode;

/**
 * Create by WangSJ on 2019/03/11
 * 基础通用的错误代码
 */
public enum CommonResultCode implements ResultCode {

    //成功  通用
    SUCCESS(200, "success"),
    ERR(500, "服务器繁忙"),

    /**
     * 登录失败状态码
     */
    UN_AUTH(401,"未登录"),
    LOGIN_FAIL(402,"用户名或密码错误"),
    LOGIN_DISABLED(403,"用户已被禁用，请联系管理员"),
    LOGIN_FAIL_UNKNOW(404,"用户登录失败，原因未知"),

    // shiro角色校验失败
    UN_HAS_ROLES(405,"无此权限"),
    // shiro权限校验失败
    UN_HAS_PERMS(406,"无此权限"),

    CAPTCHA_CHECK_ERR(410,"验证码校验失败"),
    LOGIN_LOCK(411,"错误次数超出限制，已被锁定"),
    CHECK_PASSWORD_ERR(412,"密码过于简单，请修改密码！"),
    FREQUENT(429, "请求过于频繁"),

    // 加密失败
    ENCRYPTION_ERR(511,"加密失败"),
    // 解密失败
    DECRYPTION_ERR(512,"解密失败"),


    /**
     * 携带Exception消息的错误码
     * 101开头的错误码，在响应到客户端时，message将拼接异常的exceptionMessage
     */
    QUERY_METHOD_ERR(10100,"请求方式错误"),


    // 分布式锁加锁失败 将抛出此异常
    LOCK_ERROR(20100, "服务器繁忙，请稍后再试"),
    // 分布式锁解锁失败 将抛出此异常
    UNLOCK_ERROR(20101, "服务器繁忙，请稍后再试"),

    /**
     * 常用校验异常
     */
    NO_PARAM(20102, "必填参数未填写"),
    ILLEGAL_PARAM(20103, "参数无效"),
    SERVER_ERROR(20104, "系统错误"),
    LOGIC_ERROR(20105, "逻辑错误"),

    /**
     * 系统异常
     */
    OUTPUTSTREAM_IO_EXCEPTION(20201, "您的主机中的软件中止了一个已建立的连接。"),
    UPLOAD_MAX_SIZE_ERROR(20202, "上传文件大小超过限制"),

    /**
     * sql异常
     */
    SQL_BASE_ERROR(20302, "数据异常，操作失败"),
    // sql语法异常
    SQL_GRAMMAR_ERROR(20303, "服务器繁忙"),

    /**
     * Http请求异常
     */
    HTTP_BASE_ERROR(20400, "http请求失败"),
    HTTP_CLIENT_ACCEPT_ERROR(20401, "客户端Accept错误"),

    /**
     * 导入Excel发生错误,数据格式异常
     */
    IMPORT_EXCEL_ERROR(20500, "导入文件发生错误,数据格式异常"),
    IMPORT_EXCEL_ERROR_DOWNLOAD(20501, "导入文件发生错误,有错误文件返回")

    ;


    private final int code;
    private final String msg;

    CommonResultCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }



    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }}