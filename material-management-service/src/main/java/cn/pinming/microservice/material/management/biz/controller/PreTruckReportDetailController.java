package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.query.PreTruckDetailReportQuery;
import cn.pinming.microservice.material.management.biz.service.IPreTruckReportDetailService;
import cn.pinming.microservice.material.management.biz.vo.PreTruckReportDetailVO;
import cn.pinming.microservice.material.management.biz.vo.TruckReportInfoVO;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 送货车辆 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-11-16 15:07:43
 */
@Api(tags = "车辆预报备-controller", value = "lh")
@RestController
@RequestMapping("/api/pre/truck/detail")
@AllArgsConstructor
public class PreTruckReportDetailController {

    public final IPreTruckReportDetailService truckReportDetailService;

    @ApiOperation(value = "列表", response = PreTruckReportDetailVO.class)
    @Log(title = "列表", businessType = BusinessType.QUERY)
    @PostMapping("/list")
    public ResponseEntity<Response> list(@RequestBody PreTruckDetailReportQuery query) {
        IPage<?> page = truckReportDetailService.pageListByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "详情", response = TruckReportInfoVO.class)
    @Log(title = "详情", businessType = BusinessType.QUERY)
    @PostMapping("/{id}")
    public ResponseEntity<Response> info(@PathVariable String id) {
        TruckReportInfoVO result = truckReportDetailService.findPreTruckReportInfo(id);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "取消")
    @Log(title = "取消", businessType = BusinessType.DELETE)
    @GetMapping("/{id}")
    public ResponseEntity<Response> cancel(@PathVariable String id) {
        truckReportDetailService.cancelById(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

}
