package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.service.IPurchaseContractDetailService;
import cn.pinming.microservice.material.management.biz.vo.PurchaseContractDetailVO;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 采购合同物料明细 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Api(tags = "采购合同物料明细", value = "lh")
@RestController
@RequestMapping("/api/contract/detail")
@AllArgsConstructor
public class PurchaseContractDetailController {

    public final IPurchaseContractDetailService contractDetailService;

    @ApiOperation(value = "列表", response = PurchaseContractDetailVO.class)
    @Log(title = "列表", businessType = BusinessType.QUERY)
    @GetMapping("/{contractId}/list")
    public ResponseEntity<Response> list(@PathVariable String contractId) {
        List<PurchaseContractDetailVO> list = contractDetailService.listContractDetailById(contractId);
        return ResponseEntity.ok(new SuccessResponse(list));
    }
}
