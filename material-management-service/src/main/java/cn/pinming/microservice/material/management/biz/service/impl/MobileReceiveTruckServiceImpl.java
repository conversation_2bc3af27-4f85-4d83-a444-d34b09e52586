package cn.pinming.microservice.material.management.biz.service.impl;

import cn.pinming.microservice.material.management.biz.entity.MobileReceiveTruck;
import cn.pinming.microservice.material.management.biz.mapper.MobileReceiveTruckMapper;
import cn.pinming.microservice.material.management.biz.service.IMobileReceiveTruckService;
import cn.pinming.microservice.material.management.biz.vo.MobileReceiveVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 移动收料货车表 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:31:56
 */
@Service
public class MobileReceiveTruckServiceImpl extends ServiceImpl<MobileReceiveTruckMapper, MobileReceiveTruck> implements IMobileReceiveTruckService {

    @Override
    public List<MobileReceiveVO> listByPurchaseId(String purchaseId){
        List<MobileReceiveVO> vos = this.getBaseMapper().listByPurchaseId(purchaseId);
        return vos;
    }

}
