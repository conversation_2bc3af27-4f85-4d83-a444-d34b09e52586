package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.web.response.Response;
import cn.pinming.microservice.material.management.biz.entity.MixingPlantMachine;
import cn.pinming.microservice.material.management.biz.form.MixingPlantMachineForm;
import cn.pinming.microservice.material.management.biz.service.IMixingPlantMachineService;
import cn.pinming.microservice.material.management.biz.vo.MixingPlantMachineVO;
import cn.pinming.microservice.material.management.common.SuccessResponse;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 拌合站机组管理 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-24 17:11:35
 */
@Api(tags = "拌合站机组管理")
@RestController
@RequestMapping("/api/biz")
public class MixingPlantMachineController {

    @Resource
    private IMixingPlantMachineService plantMachineService;

    @ApiOperation(value = "机组信息")
    @Log(title = "机组信息", businessType = BusinessType.QUERY)
    @GetMapping("/mixing-plant-machine")
    public ResponseEntity<Response> getMachineInfo() {
        List<MixingPlantMachineVO> mixingPlantMachine = plantMachineService.getMixingPlantMachineInfo();
        return ResponseEntity.ok(new SuccessResponse(mixingPlantMachine));
    }

    @ApiOperation(value = "修改机组信息")
    @Log(title = "修改机组信息", businessType = BusinessType.UPDATE)
    @PutMapping("/mixing-plant-machine")
    public ResponseEntity<Response> updateMachineInfo(@RequestBody @Validated List<MixingPlantMachineForm> mixingPlantMachineForm) {
        plantMachineService.updateMachineInfo(mixingPlantMachineForm);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "获取开启的机组信息")
    @Log(title = "机组信息", businessType = BusinessType.QUERY)
    @GetMapping("/mixing-plant-machine/enable")
    public ResponseEntity<Response> getEnableMachineInfo() {
        List<MixingPlantMachineVO> mixingPlantMachine = plantMachineService.getMixingPlantMachineInfo();
        List<MixingPlantMachineVO> mixingPlantMachineVOList = mixingPlantMachine.stream().filter(mixingPlantMachineVO -> mixingPlantMachineVO.getEnable() == 1).collect(Collectors.toList());
        return ResponseEntity.ok(new SuccessResponse(mixingPlantMachineVOList));
    }


}
