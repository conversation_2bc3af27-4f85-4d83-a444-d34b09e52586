package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 项目配置表
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-13 10:28:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("s_company_config")
@ApiModel(value = "CompanyConfig对象", description = "企业配置表")
public class CompanyConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "类型 1.是否允许在终端未推送之前扫码过磅 2.是否开启gboss角色权限  3.理重收料项目 4.移动收料是否允许相册上传 5.jar包设置")
    private Byte type;

    @ApiModelProperty(value = "1 不允许（不开启） 2 允许（开启）/  1 默认配置 2 其他配置")
    @TableField("is_enable")
    private Byte isEnable;

    @ApiModelProperty(value = "部门配置合集")
    @TableField("department_ids")
    private String departmentIds;

    @ApiModelProperty(value = "项目配置合集")
    @TableField("project_ids")
    private String projectIds;

    @ApiModelProperty(value = "jar包配置id")
    @TableField("jar_config_id")
    private String jarConfigId;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
