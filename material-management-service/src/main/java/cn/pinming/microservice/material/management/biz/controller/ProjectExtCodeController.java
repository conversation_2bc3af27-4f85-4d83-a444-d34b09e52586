package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.service.IProjectExtCodeService;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 外部编码 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-05-06 15:08:46
 */
@Api(tags = "外部代码-controller",value = "ly")
@RestController
@RequestMapping("/api/extCode")
public class ProjectExtCodeController {

    @Resource
    private AuthUserHolder authUserHolder;

    @Resource
    private IProjectExtCodeService extCodeService;

    @ApiOperation(value = "编辑项目外部代码回显")
    @GetMapping("/editExt/{projectId}")
    @Log(title = "编辑项目外部代码回显", businessType = BusinessType.QUERY)
    public ResponseEntity<Response> editExtInfo(@PathVariable("projectId") Integer projectId) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer companyId = currentUser.getCurrentCompanyId();
        String extCode = extCodeService.getExtCode(companyId, projectId);
        return ResponseEntity.ok(new SuccessResponse(extCode));
    }

    @ApiOperation(value = "编辑项目外部代码")
    @PutMapping(value = {"/editExt/{projectId}/{extCode}", "/editExt/{projectId}"})
    @Log(title = "编辑项目外部代码回显", businessType = BusinessType.UPDATE)
    public ResponseEntity<Response> editExt(@PathVariable("projectId") Integer projectId, @PathVariable(value = "extCode", required = false) String extCode) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer companyId = currentUser.getCurrentCompanyId();
        extCodeService.editExtCode(companyId, projectId, extCode);
        return ResponseEntity.ok(new SuccessResponse());
    }

}
