package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.PreTruckReportDetailDTO;
import cn.pinming.microservice.material.management.biz.dto.PreTruckReportInfoDTO;
import cn.pinming.microservice.material.management.biz.dto.TruckReportDetailInfoForWarnDTO;
import cn.pinming.microservice.material.management.biz.dto.VehicleInfoDTO;
import cn.pinming.microservice.material.management.biz.entity.PreTruckReportDetail;
import cn.pinming.microservice.material.management.biz.query.PreTruckDetailReportQuery;
import cn.pinming.microservice.material.management.biz.vo.PreReportHistoryVO;
import cn.pinming.microservice.material.management.biz.vo.PreTruckReportDetailVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 送货车辆 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-11-16 15:07:43
 */
public interface PreTruckReportDetailMapper extends BaseMapper<PreTruckReportDetail> {

    IPage<PreTruckReportDetailDTO> selectPageDTO(PreTruckDetailReportQuery query);

    PreTruckReportInfoDTO selectPreTruckInfoById(@Param("id") String id);

    List<String> selectRemoveVehicleList(@Param("ids") List<String> preTruckList);

    List<String> selectPreTruckIds(@Param("id")String id);

    List<PreTruckReportDetailVO> listTruckByPurchaseId(@Param("purchaseId")String purchaseId);

    TruckReportDetailInfoForWarnDTO selectInfoForWarn(@Param("preTruckReportId") String preTruckReportId);

    String selectDataById(@Param("id") String id);

    String selectId(@Param("preWeighReportId") String preWeighReportId);

    PreReportHistoryVO reportHistory(@Param("preWeighId") String preWeighId,@Param("weChatId") String weChatId);
}
