package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.entity.MixingPlantRawMaterialTr;
import cn.pinming.microservice.material.management.biz.vo.MixingPlantRawMaterialTrVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 拌合站—原料实验报告 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 16:30:16
 */
public interface MixingPlantRawMaterialTrMapper extends BaseMapper<MixingPlantRawMaterialTr> {

    List<MixingPlantRawMaterialTrVO> seek(@Param("companyId") Integer companyId,@Param("projectId") Integer projectId,@Param("materialId") Integer materialId);
}
