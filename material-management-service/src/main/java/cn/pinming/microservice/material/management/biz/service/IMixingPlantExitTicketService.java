package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.entity.MixingPlantExitTicket;
import cn.pinming.microservice.material.management.biz.form.ExitTicketForm;
import cn.pinming.microservice.material.management.biz.query.BaseQuery;
import cn.pinming.microservice.material.management.biz.vo.ExistTicketDetailVO;
import cn.pinming.microservice.material.management.biz.vo.ExitTicketPageVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 拌合站出场单 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-27 15:11:44
 */
public interface IMixingPlantExitTicketService extends IService<MixingPlantExitTicket> {

    String add(ExitTicketForm form);

    void cancel(String id,String mixingPlantOrderDetailId);

    void fix(ExitTicketForm form);

    IPage<ExitTicketPageVO> selectPage(BaseQuery query);

    ExistTicketDetailVO selectDetail(String id);

    List<String> truckNoHistory(String truckNo);

    /**
     * 打印
     * @param id
     * @param templateId
     * @param mixingPlantOrderDetailId
     * @return
     */
    String print(String id, String templateId, String mixingPlantOrderDetailId);
}
