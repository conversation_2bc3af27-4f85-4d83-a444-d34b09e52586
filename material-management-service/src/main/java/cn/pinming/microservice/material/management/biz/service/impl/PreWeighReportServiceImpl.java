package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.PreReportDTO;
import cn.pinming.microservice.material.management.biz.dto.QrcodeRedisDTO;
import cn.pinming.microservice.material.management.biz.entity.*;
import cn.pinming.microservice.material.management.biz.enums.*;
import cn.pinming.microservice.material.management.biz.form.PreReportForm;
import cn.pinming.microservice.material.management.biz.form.PreReportNewForm;
import cn.pinming.microservice.material.management.biz.form.PreTruckReportForm;
import cn.pinming.microservice.material.management.biz.form.PreTruckReportNewForm;
import cn.pinming.microservice.material.management.biz.mapper.*;
import cn.pinming.microservice.material.management.biz.query.PreReportQuery;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.AppConstant;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.NoUtil;
import cn.pinming.microservice.material.management.common.util.RedisUtil;
import cn.pinming.microservice.material.management.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material.management.wrapper.CreateNameWrapper;
import cn.pinming.microservice.material.management.wrapper.ProjectNameWrapper;
import cn.pinming.microservice.material.management.wrapper.SupplierWrapper;
import cn.pinming.v2.company.api.dto.EmployeeDto;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <p>
 * 进出场预报备 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-11-16 15:07:43
 */
@Service
public class PreWeighReportServiceImpl extends ServiceImpl<PreWeighReportMapper, PreWeighReport> implements IPreWeighReportService {

    @Value("${pre.weigh.externalPage.expire:259200}")
    private long expire;

    @Resource
    private NoUtil noUtil;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private IPreMaterialReportService preMaterialReportService;
    @Resource
    private IPreTruckReportService preTruckReportService;
    @Resource
    private PreMaterialReportMapper preMaterialReportMapper;
    @Resource
    private SupplierWrapper supplierWrapper;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;
    @Resource
    private PreTruckReportMapper preTruckReportMapper;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private PreTruckReportDetailMapper detailMapper;
    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;
    @Resource
    private CreateNameWrapper createNameWrapper;
    @Resource
    private ProjectNameWrapper projectTitleWrapper;
    @Resource
    private IPurchaseOrderService purchaseOrderService;
    @Resource
    private IPurchaseOrderDetailService purchaseOrderDetailService;
    @Resource
    private IPreTruckReportDetailService preTruckReportDetailService;
    @Resource
    private IPurchaseContractDetailService purchaseContractDetailService;

    private AuthUser getAuthUser() {
        return authUserHolder.getCurrentUser();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String add(PreReportForm form, String code) {
        AuthUser user = authUserHolder.getCurrentUser();
        Integer projectId = null;
        Integer companyId = null;
        if (ObjectUtil.isNotEmpty(user)) {
            projectId = user.getCurrentProjectId();
            companyId = user.getCurrentCompanyId();
        }

        if (StrUtil.isNotBlank(code)) {
            if (form.getStatus() == PreReportStatusEnum.COMMIT.value()) {
                throw new BOException(BOExceptionEnum.SUPPLIER_CAN_NOT_COMMIT);
            }
            ExpirePageVO expirePageVO = this.checkExternalCodeExpire(code);
            this.checkExternalCodeCommit(code, false);
            projectId = expirePageVO.getProjectId();
            companyId = expirePageVO.getCompanyId();
        }

        if (form.getStatus() == PreReportStatusEnum.COMMIT.value() && CollUtil.isEmpty(form.getList())) {
            throw new BOException(BOExceptionEnum.PRETRUCK_CAN_NOT_SUBMIT_IN_COMMIT);
        }
//      允许编辑的条件判断
        if (StrUtil.isNotBlank(form.getId())) {
            PreWeighReport dto = this.lambdaQuery()
                    .eq(PreWeighReport::getId, form.getId())
                    .one();
            if (ObjectUtil.isEmpty(dto)) {
                throw new BOException(BOExceptionEnum.PREREPORT_IS_NOT_EXIST);
            }
            if (dto.getStatus() == PreReportStatusEnum.COMMIT.value()) {
                throw new BOException(BOExceptionEnum.PREREPORT_CAN_NOT_EDIT);
            }
        }

        PreWeighReport preWeighReport = new PreWeighReport();
        BeanUtils.copyProperties(form, preWeighReport);
        if (StrUtil.isBlank(form.getId())) {
            String no = noUtil.getReportNo(projectId);
            preWeighReport.setNo(no);
            preWeighReport.setCompanyId(companyId);
            preWeighReport.setProjectId(projectId);
        }
        this.saveOrUpdate(preWeighReport);

//      新增
        if (StrUtil.isBlank(form.getId())) {
            PreMaterialReport preMaterialReport = new PreMaterialReport();
            BeanUtils.copyProperties(form, preMaterialReport);
            preMaterialReport.setPreWeighReportId(preWeighReport.getId());
            preMaterialReport.setBrand(form.getSendBrand());
            saveContractDetailId(preMaterialReport);

            preMaterialReportService.save(preMaterialReport);
//          车辆预报备
            this.saveTruck(form, preWeighReport.getId(), companyId, projectId);
        }
//      编辑
        else {
            PreMaterialReport dto = preMaterialReportService.lambdaQuery()
                    .eq(PreMaterialReport::getPurchaseOrderDetailId, form.getPurchaseOrderDetailId())
                    .eq(PreMaterialReport::getPreWeighReportId, form.getId())
                    .one();
            if (ObjectUtil.isNotEmpty(dto)) {
//              编辑的就是原来的材料
                PreMaterialReport preMaterialReport = new PreMaterialReport();
                BeanUtils.copyProperties(form, preMaterialReport);
                preMaterialReport.setBrand(form.getSendBrand());
                preMaterialReportMapper.updateEntity(preMaterialReport, form.getId());
                this.saveTruck(form, preWeighReport.getId(), companyId, projectId);
            } else {
//              编辑时换了材料，将原先的删除
                preMaterialReportService.lambdaUpdate()
                        .eq(PreMaterialReport::getPreWeighReportId, form.getId())
                        .set(PreMaterialReport::getIsDeleted, DeleteEnum.DELETE.value())
                        .update();
                PreMaterialReport preMaterialReport = new PreMaterialReport();
                BeanUtils.copyProperties(form, preMaterialReport);
                preMaterialReport.setBrand(form.getSendBrand());
                preMaterialReport.setPreWeighReportId(form.getId());
                preMaterialReport.setCompanyId(companyId);
                preMaterialReport.setProjectId(projectId);
                saveContractDetailId(preMaterialReport);
                preMaterialReportService.save(preMaterialReport);

                this.saveTruck(form, preWeighReport.getId(), companyId, projectId);
            }
        }
        if (StrUtil.isNotBlank(code)) {
            this.checkExternalCodeCommit(code, true);
        }
        return preWeighReport.getId();
    }

    private void saveContractDetailId(PreMaterialReport preMaterialReport){
        String contractDetailId = purchaseOrderDetailMapper.selectContractDetailId(preMaterialReport.getPurchaseOrderDetailId());
        preMaterialReport.setContractDetailId(contractDetailId);
    }

    private void saveTruck(PreReportForm form, String id, Integer companyId, Integer projectId) {
        if (CollUtil.isNotEmpty(form.getList())) {
            boolean isCommit = form.getStatus() == PreReportStatusEnum.COMMIT.value();
            preTruckReportService.saveOrUpdateTruck(id, form.getList(), form.getCount(), isCommit, form.getProcessType(), companyId, projectId);
        } else {
            preTruckReportService.lambdaUpdate().eq(PreTruckReport::getPreWeighReportId, id).set(PreTruckReport::getIsDeleted, DeleteEnum.DELETE.value()).update();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(String id) {
        PreWeighReport dto = this.lambdaQuery()
                .eq(PreWeighReport::getId, id)
                .one();
        if (ObjectUtil.isNotEmpty(dto)) {
            if (dto.getStatus() == PreReportStatusEnum.COMMIT.value()) {
                throw new BOException(BOExceptionEnum.PREREPORT_CAN_NOT_DELETE);
            }
            this.lambdaUpdate()
                    .set(PreWeighReport::getIsDeleted, DeleteEnum.DELETE.value())
                    .eq(PreWeighReport::getId, id)
                    .update();
            preMaterialReportService.lambdaUpdate()
                    .eq(PreMaterialReport::getPreWeighReportId, id)
                    .set(PreMaterialReport::getIsDeleted, DeleteEnum.DELETE.value())
                    .update();
            preTruckReportService.lambdaUpdate()
                    .eq(PreTruckReport::getPreWeighReportId, id)
                    .set(PreTruckReport::getIsDeleted, DeleteEnum.DELETE.value())
                    .update();
        }
    }

    @Override
    public IPage<PreReportVO> pageByQuery(PreReportQuery query, AuthUser user) {
        IPage<PreReportVO> page = new Page<>();
        IPage<PreReportDTO> preReportDTOIPage = this.getBaseMapper().selectPreReport(query, user);
        List<PreReportDTO> list = preReportDTOIPage.getRecords();
        if (CollUtil.isNotEmpty(list)) {
            List<PreReportVO> result = new ArrayList<>();

            projectTitleWrapper.wrap(list);
            createNameWrapper.wrap(list,user.getCurrentCompanyId());
            supplierWrapper.wrap(list, user.getCurrentCompanyId());

            list.forEach(e -> {
                PreReportVO vo = new PreReportVO();
                BeanUtils.copyProperties(e, vo);

                vo.setMaterial(StrUtil.format("{}/{}/{}", e.getCategoryName(), e.getMaterialName(), e.getMaterialSpec()));
                vo.setSupplier(e.getSupplierTitle());
                vo.setReceiverProject(e.getProjectTitle());

                result.add(vo);
            });

            BeanUtils.copyProperties(preReportDTOIPage, page);
            page.setRecords(result);
        }
        return page;
    }

    @Override
    public PreReportDetailVO selectDetail(String id, Integer companyId) {
        AuthUser user = authUserHolder.getCurrentUser();
        Integer coId = null;
        if (ObjectUtil.isNotEmpty(user)) {
            coId = user.getCurrentCompanyId();
        }
        if (companyId != null && companyId != 0) {
            coId = companyId;
        }
        PreReportDetailVO vo = this.getBaseMapper().selectDetail(id);
        if (ObjectUtil.isNotEmpty(vo)) {
            EmployeeDto dto = employeeServiceProxy.findEmployee(coId, vo.getCreateId());
            if (ObjectUtil.isNotEmpty(dto)) {
                vo.setCreateName(dto.getMemberName());
            }

            List<PreTruckReportVO> list = preTruckReportMapper.selectDetail(id);
            if (CollUtil.isNotEmpty(list)) {
                vo.setList(list);
            }

            if (vo.getStatus() == PreReportStatusEnum.COMMIT.value()) {
                List<PreTruckReportDetailVO> dataVOS = preTruckReportMapper.selectData(id);
                if (CollUtil.isNotEmpty(dataVOS)) {
                    vo.setDataList(dataVOS);
                }
            }
        }
        return vo;
    }

    @Override
    public ExpirePageVO generateExternalPageInfo(String id) {
        String code = IdUtil.fastSimpleUUID();
        ExpirePageVO result = new ExpirePageVO(id, code, expire, getAuthUser().getCurrentProjectId(), 0, getAuthUser().getCurrentCompanyId());

        String viewKey = StrUtil.format(AppConstant.PAGE_VIEW_PREFIX, code);
        redisUtil.hmset(viewKey, BeanUtil.beanToMap(result));
        redisUtil.set(StrUtil.format(AppConstant.PAGE_EXP_PREFIX, code), id, expire);
        return result;
    }

    @Override
    public ExpirePageVO checkExternalCodeExpire(String code) {
        String hashKey = StrUtil.format(AppConstant.PAGE_EXP_PREFIX, code);
        long expire = redisUtil.getExpire(hashKey);
        String viewKey = StrUtil.format(AppConstant.PAGE_VIEW_PREFIX, code);
        if (expire < 0) {
            redisUtil.del(viewKey);
            throw new BOException(BOExceptionEnum.EXT_PAGE_EXPIRE);
        }
        Map<Object, Object> map = redisUtil.hmget(viewKey);
        if (Objects.isNull(map)) {
            throw new BOException(BOExceptionEnum.EXT_PAGE_VIEW);
        }
        ExpirePageVO vo = BeanUtil.fillBeanWithMap(map, new ExpirePageVO(), true);

        if (vo.getCommit() > 1) {
            throw new BOException(BOExceptionEnum.EXT_PAGE_VIEW);
        }

        int count = this.lambdaQuery().eq(PreWeighReport::getId, vo.getId()).eq(PreWeighReport::getIsDeleted, DeleteEnum.DELETE.value()).count();
        if (count > 0) {
            throw new BOException(BOExceptionEnum.EXT_PAGE_DELETED);
        }

        vo.setExpireTime(expire);
        return vo;
    }

    @Override
    public void checkExternalCodeCommit(String code, boolean flag) {
        //保存判断页面请求次数
        String viewKey = StrUtil.format(AppConstant.PAGE_VIEW_PREFIX, code);
        synchronized (this) {
            Map<Object, Object> map = redisUtil.hmget(viewKey);
            if (Objects.isNull(map)) {
                throw new BOException(BOExceptionEnum.EXT_PAGE_VIEW);
            }
            ExpirePageVO vo = BeanUtil.fillBeanWithMap(map, new ExpirePageVO(), true);
            if (vo.getCommit() > 0) {
                throw new BOException(BOExceptionEnum.EXT_PAGE_VIEW);
            }
            if (flag) {
                redisUtil.hincr(viewKey, "commit", 1);
                //String hashKey = StrUtil.format(AppConstant.PAGE_EXP_PREFIX, code);
                //redisUtil.del(viewKey);
            }
        }
    }

    @Override
    public List<PreReportVO> listByPurchaseId(String purchaseId) {
        AuthUser user = authUserHolder.getCurrentUser();
        List<PreReportVO> vos = this.getBaseMapper().selectPreReportByPurchaseId(purchaseId, user.getId());
        return vos;
    }

    @Override
    public List<PreTruckReportDetailVO> listTruckByPurchaseId(String purchaseId) {
        List<PreTruckReportDetailVO> vos = detailMapper.listTruckByPurchaseId(purchaseId);
        return vos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submit(PreReportNewForm form) {
        // 判断采购单是否收货完毕或者已删除
        PurchaseOrder purchaseOrder = purchaseOrderService.lambdaQuery()
                .eq(PurchaseOrder::getId, form.getPurchaseOrderId())
                .one();
        PurchaseOrderDetail purchaseOrderDetail = purchaseOrderDetailService.lambdaQuery()
                .eq(PurchaseOrderDetail::getId, form.getPurchaseOrderDetailId())
                .one();
        if (ObjectUtil.isNotNull(purchaseOrder) && purchaseOrder.getStatus() == PurchaseOrderStatusEnum.FINISH.value()) {
            throw new BOException(BOExceptionEnum.PURCHASE_IS_FINISH);
        }
        if (ObjectUtil.isNull(purchaseOrderDetail)) {
            throw new BOException(BOExceptionEnum.PURCHASE_DETAIL_IS_DELETED);
        }

        // 判断二维码是否过期
        if (StrUtil.isNotBlank(form.getCode())) {
            long expire = redisUtil.getExpire(StrUtil.format(AppConstant.QRCODE_VIEW_PREFIX,form.getCode()));
            if (expire < 0) {
                throw new BOException(BOExceptionEnum.QRCODE_EXPIRE);
            }
        }

        Object redisEntity = redisUtil.get(StrUtil.format(AppConstant.QRCODE_VIEW_PREFIX, form.getCode()));
        QrcodeRedisDTO redisDTO = JSONUtil.toBean(redisEntity.toString(), QrcodeRedisDTO.class);
        String preWeighReportId = redisDTO.getPreWeighReportId();
        String creatId = redisDTO.getCreateId();

        PreTruckReportNewForm preTruckReportForm = form.getList().get(0);

        if (StrUtil.isNotBlank(preWeighReportId)) {
            // 追加
            PreTruckReport preTruckReport = new PreTruckReport();
            preTruckReport.setPreWeighReportId(preWeighReportId);
            preTruckReport.setTruckNo(preTruckReportForm.getTruckNo());
            preTruckReport.setCapacity(preTruckReportForm.getCapacity());
            preTruckReport.setTimes(1);
            preTruckReport.setIntervalTime(null);
            preTruckReport.setCompanyId(purchaseOrder.getCompanyId());
            preTruckReport.setProjectId(purchaseOrder.getProjectId());
            preTruckReport.setWeChatId(form.getWeChatId());
            preTruckReport.setArriveTime(LocalDateTime.now().plusMinutes(30));
            preTruckReportService.save(preTruckReport);

            PreTruckReportDetail preTruckReportDetail = new PreTruckReportDetail();

            preTruckReportDetail.setPreWeighReportId(preWeighReportId);
            preTruckReportDetail.setPreTruckReportId(preTruckReport.getId());
            String preTruckReportDetailNo = noUtil.getWeighNo(purchaseOrder.getProjectId());
            preTruckReportDetail.setNo(preTruckReportDetailNo);
            preTruckReportDetail.setCompanyId(purchaseOrder.getCompanyId());
            preTruckReportDetail.setProjectId(purchaseOrder.getProjectId());
            preTruckReportDetail.setArriveTime(LocalDateTime.now().plusMinutes(30));
            preTruckReportDetailService.save(preTruckReportDetail);
        }else {
            // 新增

            PreWeighReport preWeighReport = new PreWeighReport();
            PreMaterialReport preMaterialReport = new PreMaterialReport();
            PreTruckReport preTruckReport = new PreTruckReport();
            PreTruckReportDetail preTruckReportDetail = new PreTruckReportDetail();

            String no = noUtil.getReportNo(purchaseOrder.getProjectId());
            preWeighReport.setNo(no);
            preWeighReport.setPurchaseOrderId(form.getPurchaseOrderId());
            preWeighReport.setType(form.getType());
            preWeighReport.setWeighType(form.getWeighType());
            preWeighReport.setStatus(form.getStatus());
            preWeighReport.setCompanyId(purchaseOrder.getCompanyId());
            preWeighReport.setProjectId(purchaseOrder.getProjectId());
            preWeighReport.setCreateId(creatId);
            this.save(preWeighReport);

            preMaterialReport.setPreWeighReportId(preWeighReport.getId());
            preMaterialReport.setPurchaseOrderDetailId(form.getPurchaseOrderDetailId());
            preMaterialReport.setContractDetailId(purchaseOrderDetail.getContractDetailId());
            preMaterialReport.setCount(preTruckReportForm.getCapacity());
            preMaterialReport.setCompanyId(purchaseOrder.getCompanyId());
            preMaterialReport.setProjectId(purchaseOrder.getProjectId());
            PurchaseContractDetail one = purchaseContractDetailService.lambdaQuery()
                    .select(PurchaseContractDetail::getConversionRate)
                    .eq(PurchaseContractDetail::getId, purchaseOrderDetail.getContractDetailId())
                    .one();
            if (ObjectUtil.isNotNull(one)) {
                preMaterialReport.setConversionRate(one.getConversionRate());
            }
            preMaterialReportService.save(preMaterialReport);

            preTruckReport.setPreWeighReportId(preWeighReport.getId());
            preTruckReport.setTruckNo(preTruckReportForm.getTruckNo());
            preTruckReport.setCapacity(preTruckReportForm.getCapacity());
            preTruckReport.setTimes(1);
            preTruckReport.setIntervalTime(null);
            preTruckReport.setArriveTime(LocalDateTime.now().plusMinutes(30));
            preTruckReport.setCompanyId(purchaseOrder.getCompanyId());
            preTruckReport.setProjectId(purchaseOrder.getProjectId());
            preTruckReport.setWeChatId(form.getWeChatId());
            preTruckReportService.save(preTruckReport);

            preTruckReportDetail.setPreWeighReportId(preWeighReport.getId());
            preTruckReportDetail.setPreTruckReportId(preTruckReport.getId());
            String preTruckReportDetailNo = noUtil.getWeighNo(purchaseOrder.getProjectId());
            preTruckReportDetail.setNo(preTruckReportDetailNo);
            preTruckReportDetail.setCompanyId(purchaseOrder.getCompanyId());
            preTruckReportDetail.setProjectId(purchaseOrder.getProjectId());
            preTruckReportDetail.setArriveTime(LocalDateTime.now().plusMinutes(30));
            preTruckReportDetailService.save(preTruckReportDetail);

            // 把报备id放到redis中
            String key = StrUtil.format(AppConstant.QRCODE_VIEW_PREFIX, form.getCode());
            Object obj = redisUtil.get(key);
            QrcodeRedisDTO dto = JSONUtil.toBean(obj.toString(), QrcodeRedisDTO.class);
            dto.setPreWeighReportId(preWeighReport.getId());
            long expire = redisUtil.getExpire(StrUtil.format(AppConstant.QRCODE_VIEW_PREFIX, form.getCode()));
            redisUtil.set(StrUtil.format(AppConstant.QRCODE_VIEW_PREFIX, form.getCode()), JSON.toJSONString(dto),expire);
        }
    }
}


