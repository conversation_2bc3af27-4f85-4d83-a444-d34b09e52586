package cn.pinming.microservice.material.management.biz.timetask.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.management.biz.entity.MaterialWarning;
import cn.pinming.microservice.material.management.biz.enums.WarningSubTypeEnum;
import cn.pinming.microservice.material.management.biz.enums.WarningTypeEnum;
import cn.pinming.microservice.material.management.biz.mapper.MaterialWarningMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 面单应收数量异常预警自动处理方案
 * <AUTHOR>
 * @date 2022/6/14
 */
@Slf4j
@Component
public class WarningThreeStrategy extends WarningAutoProcessStrategy implements CommandLineRunner {

    @Resource
    private MaterialWarningMapper warningMapper;

    /**
     * 3.1 检查相应data当前是否已录入面单应收量，如已设置，则自动置为“已处理”。仍末设置，则不作处理
     * @param warnings
     * @return
     */
    @Override
    public void autoProcessWay(List<MaterialWarning> warnings) {
        List<String> warningIds = Lists.newArrayList();
        log.info("面单应收数量异常预警自动处理 start...");
        if (CollectionUtil.isEmpty(warnings)) {
            log.info("待处理的预警为空");
            log.info("面单应收数量异常预警自动处理 end...");
            return;
        }
        // 预警子分类分组
        Map<Byte, List<MaterialWarning>> subTypeMap = warnings.stream().filter(item -> ObjectUtil.isNotNull(item.getWarningSubType()))
                .collect(Collectors.groupingBy(MaterialWarning::getWarningSubType));
        List<MaterialWarning> materialWarningsOne = subTypeMap.get(WarningSubTypeEnum.RECEIVE_NUMBER_ONE.subType());
        if (CollectionUtil.isEmpty(materialWarningsOne)) {
            log.info("面单应收数量异常subType=1预警为空");
        } else {
            List<String> ids = materialWarningsOne.stream().map(MaterialWarning::getId).collect(Collectors.toList());
            warningIds.addAll(Optional.ofNullable(warningMapper.receiveNumberOne(ids)).orElse(Lists.newArrayList()));
        }
        log.info("面单应收数量异常预警自动处理 end...");
        super.autoCloseWarning(warningIds);
    }

    @Override
    public void run(String... args) throws Exception {
        super.addChildClass(WarningTypeEnum.RECEIVE_NUMBER.value(), this);
    }
}
