package cn.pinming.microservice.material.management.biz.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class WeighSendFixForm {
    @ApiModelProperty("称重记录UUID")
    @JsonProperty(value = "tUuid")
    private String tUuid;

    @ApiModelProperty(value = "单据照片")
    private List<String> documentPics;

    @ApiModelProperty(value = "材料id")
    @NotNull(message = "请重新选择材料")
    private Integer materialId;

    @ApiModelProperty("接收方")
    private String supplierName;

    @ApiModelProperty(value = "换算系数")
    @DecimalMin(value = "0",message = "换算系数必须为正数")
    @Digits(fraction = 4,message = "换算系数小数点上限为4位", integer = 999)
    private BigDecimal ratio;

    @ApiModelProperty(value = "发货数量")
    @NotNull(message = "发货数量不能为空")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "结算单位")
    @NotBlank(message = "结算单位不能为空")
    private String weightUnit;

    @ApiModelProperty(value = "发货单id")
    @NotBlank(message = "发货单id不能为空")
    private String sendId;

    @ApiModelProperty(value = "修订说明")
    @Length(max = 500, message = "修订说明过长")
    private String reviseRemark;

    @ApiModelProperty(value = "实际数量：实重 * 换算系数")
    @NotNull(message = "实际数量不能为空")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "收料:是否立即完成对账 1 是 2 否;  发料:是否需要立即推送 1 是 2 否")
    private Byte isAutoVerify;

    @ApiModelProperty(value = "收发料子类型 收料:1 采购  2 调入  3 甲供         发料:4 发料  5 废旧物料  6 调出  7 售出")
    private Byte typeDetail;

    @ApiModelProperty(value = "外部系统单号")
    private String extNo;

    @ApiModelProperty(value = "计划使用部位")
    private String position;
}
