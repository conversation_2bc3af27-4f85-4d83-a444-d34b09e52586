package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.form.MaterialBrandForm;
import cn.pinming.microservice.material.management.biz.query.PurchaseBrandQuery;
import cn.pinming.microservice.material.management.biz.service.IMaterialBrandService;
import cn.pinming.microservice.material.management.biz.vo.MaterialBrandVO;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * 材料品牌 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
@Api(tags = "材料品牌", value = "lh")
@RestController
@RequestMapping("/api/brand")
@AllArgsConstructor
public class MaterialBrandController {

    private final IMaterialBrandService brandService;

    @ApiOperation(value = "列表",response = MaterialBrandVO.class)
    @Log(title = "列表", businessType = BusinessType.QUERY)
    @GetMapping("/list")
    public ResponseEntity<Response> list(@Validated @Valid PurchaseBrandQuery query) {
        List<MaterialBrandVO> list = brandService.queryListByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation("保存")
    @Log(title = "保存", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseEntity<Response> save(@Validated @Valid @RequestBody MaterialBrandForm form) {
        String name = brandService.saveMaterialBrand(form);
        return ResponseEntity.ok(new SuccessResponse(name));
    }

}
