package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.dto.SupplierDTO;
import cn.pinming.microservice.material.management.biz.entity.Supplier;
import cn.pinming.microservice.material.management.biz.form.SupplierForm;
import cn.pinming.microservice.material.management.biz.query.SupplierQuery;
import cn.pinming.microservice.material.management.biz.vo.ImportResultVO;
import cn.pinming.microservice.material.management.biz.vo.MixPlantInfoVO;
import cn.pinming.microservice.material.management.biz.vo.ProjectVO;
import cn.pinming.microservice.material.management.biz.vo.SupplierVO;
import cn.pinming.v2.common.QueryPagination;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 供应商表 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-04-13 13:17:24
 */
public interface ISupplierService extends IService<Supplier> {

    Page<SupplierVO> listByQuery(SupplierQuery query);

    void saveSupplier(SupplierForm form);

    void updateSupplierStatus(String id);

    void deleteSupplierById(Integer id);

    ImportResultVO importSupplier(List<SupplierForm> list);

    /**
     * 获取拌合站点列表
     * @return 站点信息
     */
    List<MixPlantInfoVO> listMixPlantInfo();

    void initDataList();

    void updateSupplierList();

    List<SupplierDTO> findListByCompanyIdAndSupplierIds(Integer companyId, List<Integer> supplierIdList);
}
