package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.MaterialVerify;
import cn.pinming.microservice.material.management.biz.query.MaterialVerifyQuery;
import cn.pinming.microservice.material.management.biz.vo.MaterialVerifyReceiveVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 物料对账表 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-18 10:22:28
 */
public interface MaterialVerifyMapper extends BaseMapper<MaterialVerify> {

    /**
     * 对账列表
     * @param materialVerifyQuery query
     * @return 对账列表
     */
    IPage<MaterialDataVerifyDTO> selectMaterialVerify(MaterialVerifyQuery materialVerifyQuery);

    /**
     * 根据id更新为已归档
     * @param id 对账id
     */
    void updateFileStatusById(@Param("id") String id);

    /**
     * 对账明细（临时收料&报备收料）
     * @param start         开始时间
     * @param end           结束时间
     * @param receiveMode   收料模式（1：临时收料，2：报备收料）
     * @param projectId     项目ID
     * @param supplierId    供应商ID
     * @param contractId    合同ID
     * @param verifyId      对账ID
     * @return
     */
    List<MaterialVerifyReceiveDetailDTO> tempAndReportVerifyReceiveDetail(@Param("start") String start,
                                                                          @Param("end") String end,
                                                                          @Param("receiveMode") byte receiveMode,
                                                                          @Param("projectId") Integer projectId,
                                                                          @Param("supplierId") Integer supplierId,
                                                                          @Param("contractId") String contractId,
                                                                          @Param("verifyId") String verifyId);

    /**
     *  对账明细（无归属收料&无效称重）
     * @param start         开始时间
     * @param end           结束时间
     * @param receiveMode   收料模式（3：无归属收料，0：无效称重）
     * @param projectId     项目ID
     * @param verifyId      对账ID
     * @return
     */
    List<MaterialVerifyReceiveDetailDTO> unbelognAndInvalidVerifyReceiveDetail(@Param("start") String start,
                                                                               @Param("end") String end,
                                                                               @Param("receiveMode") byte receiveMode,
                                                                               @Param("projectId") Integer projectId,
                                                                               @Param("verifyId") String verifyId);

    /**
     * 对账-收料列表
     * @param verifyId  对账ID
     * @return
     */
    List<MaterialVerifyReceiveDTO> verifyReceiveList(@Param("verifyId") String verifyId);

    /**
     * 对账-材料分类统计
     * @param dataIds  data表ID
     * @return
     */
    List<MaterialVerifyMaterialDTO> verifyReceiveMaterialList(@Param("dataIds") List<String> dataIds);

    /**
     * 根据终端称重记录ID/收料单号查询数据
     * @param weighId   终端称重记录ID
     * @param receiveNo 收料单号
     * @return
     */
    List<MaterialVerifyReceiveDetailScanDTO> receiveDetail(@Param("weighId") String weighId, @Param("receiveNo") String receiveNo);

    /**
     * 扫码枪提交收料记录检查
     * @param receiveIds    收料ID
     * @return
     */
    List<MaterialVerifyReceiveDetailScanCheckDTO> scanSubmitCheck(@Param("receiveIds") List<String> receiveIds);

    List<String> selectPushed();

    List<String> selectVerifyPerson(@Param("companyId") Integer companyId, @Param("projectId") Integer projectId);
}
