package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.IngredientList;
import cn.pinming.microservice.material.management.biz.entity.MixingPlantMachine;
import cn.pinming.microservice.material.management.biz.entity.PlantProjectConfig;
import cn.pinming.microservice.material.management.biz.entity.StatisticsPlantConfig;
import cn.pinming.microservice.material.management.biz.enums.IngredientListEnum;
import cn.pinming.microservice.material.management.biz.mapper.IngredientListMapper;
import cn.pinming.microservice.material.management.biz.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.biz.mapper.StatisticsPlantMapper;
import cn.pinming.microservice.material.management.biz.query.MachineCapacityQuery;
import cn.pinming.microservice.material.management.biz.query.MachineExitDetailQuery;
import cn.pinming.microservice.material.management.biz.service.IMixingPlantMachineService;
import cn.pinming.microservice.material.management.biz.service.IPlantProjectConfigService;
import cn.pinming.microservice.material.management.biz.service.IStatisticsPlantConfigService;
import cn.pinming.microservice.material.management.biz.service.StatisticsPlantService;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.util.DateUtil;
import cn.pinming.microservice.material.management.common.util.IfBranchUtil;
import cn.pinming.microservice.material.management.common.util.MathUtil;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialCategoryDto;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.warehouse.management.dto.*;
import cn.pinming.microservice.warehouse.management.service.WarehousePositionService;
import cn.pinming.microservice.warehouse.management.service.WarehouseStockInfoService;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.pinming.microservice.material.management.biz.dto.PaperInventoryPeriodsDTO.atPeriods;
import static java.util.stream.Collectors.*;
/**
 * <AUTHOR>
 * @date 2022/7/22
 */
@Service
public class StatisticsPlantServiceImpl implements StatisticsPlantService {

    private static String UTC_TIME = "1970-01-01 12:00:00";

    private static LocalDateTime LOCAL_UTC_TIME = DateUtil.parseLocalDateTime(UTC_TIME);

    @Resource
    private AuthUserHolder siteContextHolder;

    @Resource
    private StatisticsPlantMapper plantMapper;

    @Resource
    private MaterialDataMapper materialDataMapper;

    @Resource
    private IStatisticsPlantConfigService plantConfigService;

    @Resource
    private IPlantProjectConfigService plantProjectConfigService;

    @Resource
    private MaterialServiceProxy materialServiceProxy;

    @Resource
    private ProjectServiceProxy projectServiceProxy;

    @Resource
    private IngredientListMapper ingredientListMapper;

    @Resource
    private IMixingPlantMachineService plantMachineService;

    @Reference
    private WarehouseStockInfoService stockInfoService;

    @Reference
    private WarehousePositionService warehousePositionService;
    
    @Resource
    private AuthUserHolder authUserHolder;

    @Override
    public List<StatisticsPlantCategoryVO> queryStatisticsPlantCategory() {
        AuthUser currentUser = siteContextHolder.getCurrentUser();
        Integer currentProjectId = currentUser.getCurrentProjectId();
        StatisticsPlantConfig statisticsPlantConfig = plantConfigService.queryOneConfig();
        if (ObjectUtil.isNull(statisticsPlantConfig)) {
            return Lists.newArrayList();
        }
        String categoryId = statisticsPlantConfig.getCategoryId();
        // 未配置二级分类 --> 返回空
        if (StringUtils.isBlank(categoryId)) {
            return Lists.newArrayList();
        }
        // 二级分类列表
        List<Integer> categoryIds = Arrays.stream(categoryId.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        // 二级分类名称
        List<MaterialCategoryDto> materialCategoryDtos = materialServiceProxy.listCategoryByIds(currentUser.getCurrentCompanyId(), categoryIds);
        Map<Integer, String> categoryMap = materialCategoryDtos.stream().collect(Collectors.toMap(MaterialCategoryDto::getMaterialCategoryId,
                MaterialCategoryDto::getMaterialCategoryName));

        PlantProjectConfig plantProjectConfig = plantProjectConfigService.queryOneConfig();
        String belongProjectId = ObjectUtil.isNull(plantProjectConfig) ? null : plantProjectConfig.getBelongProjectId();
        // 企业层且未配置项目 --> 返回已配置的二级分类
        if (ObjectUtil.isNull(currentProjectId) && StringUtils.isBlank(belongProjectId)) {
            return plantCategoryVO(categoryMap);
        }
        List<Integer> projectIds = ObjectUtil.isNull(currentProjectId) ?
                                    Arrays.stream(belongProjectId.split(",")).map(Integer::parseInt).collect(Collectors.toList()) :
                                    Arrays.asList(currentProjectId);
        List<StatisticsPlantCategoryDTO> categoryDTOS = plantMapper.queryStatisticsPlantCategory(projectIds, categoryIds);
        // 无数据 --> 返回已配置的二级分类
        if (CollectionUtil.isEmpty(categoryDTOS)) {
            return plantCategoryVO(categoryMap);
        }
        // 材料名称
        List<Integer> materialIds = categoryDTOS.stream().map(StatisticsPlantCategoryDTO::getMaterialId).collect(Collectors.toList());
        List<MaterialDto> materialDtos = materialServiceProxy.listMaterialByIds(materialIds);
        Map<Integer, String> materialMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, MaterialDto::getMaterialName));
        // 有数据 --> 根据已配置的二级分类构造数据
        Map<Integer, Map<String, List<StatisticsPlantCategoryDTO>>> categoryUnitMap = categoryDTOS.stream().collect(
                Collectors.groupingBy(StatisticsPlantCategoryDTO::getCategoryId,
                        Collectors.groupingBy(StatisticsPlantCategoryDTO::getUnit)));

        // 返回集合
        List<StatisticsPlantCategoryVO> result = Lists.newArrayList();

        categoryMap.forEach((cgId, cgName) -> {
            // 二级分类
            StatisticsPlantCategoryVO categoryVO = new StatisticsPlantCategoryVO();
            // 二级分类ID
            categoryVO.setCategoryId(cgId);
            // 二级分类名称
            categoryVO.setCategoryName(cgName);
            // 单位-材料map
            Map<String, List<StatisticsPlantCategoryDTO>> unitMap = categoryUnitMap.get(cgId);
            IfBranchUtil.isTrue(CollectionUtil.isNotEmpty(unitMap)).trueHandle(() -> {
                // 单位-材料集合
                List<StatisticsPlantUnitVO> materials = Lists.newArrayList();
                unitMap.forEach((unit, materialVOS) -> {
                    StatisticsPlantUnitVO unitVO = new StatisticsPlantUnitVO();
                    // 单位
                    unitVO.setUnit(unit);
                    // 材料集合
                    List<StatisticsPlantMaterialVO> materialList = materialVOS.stream().map(item -> {
                        Integer materialId = item.getMaterialId();
                        StatisticsPlantMaterialVO materialVO = new StatisticsPlantMaterialVO();
                        materialVO.setMaterialId(materialId);
                        materialVO.setMaterialName(materialMap.get(materialId));
                        return materialVO;
                    }).collect(Collectors.toList());
                    // 去重
                    materialList = materialList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                            new TreeSet<>(Comparator.comparing(StatisticsPlantMaterialVO :: getMaterialId))), ArrayList::new));
                    unitVO.setMaterials(materialList);
                    materials.add(unitVO);
                });
                categoryVO.setMaterials(materials);
            });
            result.add(categoryVO);
        });
        return result;
    }

    @Override
    public StatisticsPlantVO queryHistoryPlantHistogram(String unit, List<Integer> materialIdList) {

        StatisticsPlantVO result = new StatisticsPlantVO();

        if (StringUtils.isBlank(unit) || CollectionUtil.isEmpty(materialIdList)) {
            return result;
        }

        List<Integer>  projectIds = getProjectIdConfig();
        if (CollectionUtil.isEmpty(projectIds)) {
            return result;
        }

        // 所有时间的计划需求量
        List<StatisticsPlantDTO> plantPlanList = Optional.ofNullable(plantMapper.queryPlanCount(projectIds, unit, materialIdList)).orElse(Lists.newArrayList());
        // 所有时间的出站量
        List<StatisticsPlantDTO> plantExitList = Optional.ofNullable(plantMapper.queryExitCount(projectIds, unit, materialIdList)).orElse(Lists.newArrayList());

        // 昨日计划需求量及出站量
        Map<Integer, Double> yesterdayPlanCountProjectMap = planCountProjectMap(DateUtil.yesterdayStr(), plantPlanList);
        Map<Integer, Double> yesterdayExitCountProjectMap = exitCountProjectMap(DateUtil.yesterdayStr(), plantExitList);
        // 昨日计划需求量 - 出站量（合并后的项目列表）
        Map<Integer, Double> yesterdayResidueProjectMap = residueProjectMap(yesterdayPlanCountProjectMap, yesterdayExitCountProjectMap);
        // 昨日配单量
        Map<Integer, Double> yesterdayProductionCountProjectMap = productionCountProjectMap(DateUtil.yesterdayStr(), plantPlanList);

        // 今日计划需求量及出站量
        Map<Integer, Double> todayPlanCountProjectMap = planCountProjectMap(DateUtil.todayStr(), plantPlanList);
        Map<Integer, Double> todayExitCountProjectMap = exitCountProjectMap(DateUtil.todayStr(), plantExitList);
        // 今日计划需求量 - 出站量（合并后的项目列表）
        Map<Integer, Double> todayResidueProjectMap = residueProjectMap(todayPlanCountProjectMap, todayExitCountProjectMap);
        // 今日配单量
        Map<Integer, Double> todayProductionCountProjectMap = productionCountProjectMap(DateUtil.todayStr(), plantPlanList);

        // 明日计划需求量
        Map<Integer, Double> tomorrowPlanCountProjectMap = planCountProjectMap(DateUtil.tomorrowStr(), plantPlanList);
        // 明日计划需求量 - 出站量（合并后的项目列表）
        Set<Integer> tomorrowProjectIds = tomorrowPlanCountProjectMap.keySet();

        // 截止昨日的计划需求量及出站量
        Map<String, Double> cutoffYesterdayPlanCountProjectMap = cutoffSomedayPlanCountProjectMap(DateUtil.yesterdayStr(), plantPlanList);
        Map<String, Double> cutoffYesterdayExitCountProjectMap = cutoffSomedayExitCountProjectMap(DateUtil.yesterdayStr(), plantExitList);
        // 昨日的昨日剩余量（累加截止昨日的每天【计划需求量 - 出站量】）
        Map<Integer, Double> cutoffYesterdayResidueProjectMap = cutoffSomedayResidueProjectMap(cutoffYesterdayPlanCountProjectMap, cutoffYesterdayExitCountProjectMap);

        // 项目ID获取项目名称
        List<Integer> projectIdList = Lists.newArrayList();
        projectIdList.addAll(tomorrowProjectIds);
        projectIdList.addAll(yesterdayResidueProjectMap.keySet());
        projectIdList.addAll(todayResidueProjectMap.keySet());
        projectIdList.addAll(cutoffYesterdayResidueProjectMap.keySet());
        projectIdList = projectIdList.stream().distinct().collect(Collectors.toList());
        List<SimpleConstructionProjectDto> projectDtoList = Optional.ofNullable(projectServiceProxy.findProjectsByProjectIds(projectIdList)).orElse(Lists.newArrayList());
        Map<Integer, String> projectMap = projectDtoList.stream().collect(Collectors.toMap(SimpleConstructionProjectDto::getProjectId,
                SimpleConstructionProjectDto::getProjectTitle, (k1, k2) -> k1));

        // 昨日总产能需求完成情况表格
        List<StatisticsPlantHistoryVO> yesterdayTable = Lists.newArrayList();
        projectIdList.forEach(projectId -> {
            StatisticsPlantHistoryVO statisticsPlantHistoryVO = new StatisticsPlantHistoryVO();
            statisticsPlantHistoryVO.setProjectId(projectId);
            statisticsPlantHistoryVO.setProjectName(projectMap.get(projectId));
            // 计划需求量
            BigDecimal plantPlanCount = BigDecimal.valueOf(yesterdayPlanCountProjectMap.getOrDefault(projectId, 0D));
            statisticsPlantHistoryVO.setPlanDemand(plantPlanCount);
            // 配单量
            BigDecimal plantProductionCount = BigDecimal.valueOf(yesterdayProductionCountProjectMap.getOrDefault(projectId, 0D));
            statisticsPlantHistoryVO.setProduction(plantProductionCount);
            // 出站量
            BigDecimal exitCount = BigDecimal.valueOf(yesterdayExitCountProjectMap.getOrDefault(projectId, 0D));
            statisticsPlantHistoryVO.setExit(exitCount);
            // 昨日剩余量
            BigDecimal yesterdayResidueCount = BigDecimal.valueOf(cutoffYesterdayResidueProjectMap.getOrDefault(projectId, 0D));
            statisticsPlantHistoryVO.setYesterdayResidue(yesterdayResidueCount);
            yesterdayTable.add(statisticsPlantHistoryVO);
        });

        // 今日总产能需求完成情况表格
        List<StatisticsPlantHistoryVO> todayTable = Lists.newArrayList();
        projectIdList.forEach(projectId -> {
            StatisticsPlantHistoryVO statisticsPlantHistoryVO = new StatisticsPlantHistoryVO();
            statisticsPlantHistoryVO.setProjectId(projectId);
            statisticsPlantHistoryVO.setProjectName(projectMap.get(projectId));
            // 计划需求量
            BigDecimal plantPlanCount = BigDecimal.valueOf(todayPlanCountProjectMap.getOrDefault(projectId, 0D));
            statisticsPlantHistoryVO.setPlanDemand(plantPlanCount);
            // 配单量
            BigDecimal plantProductionCount = BigDecimal.valueOf(todayProductionCountProjectMap.getOrDefault(projectId, 0D));
            statisticsPlantHistoryVO.setProduction(plantProductionCount);
            // 出站量
            BigDecimal exitCount = BigDecimal.valueOf(todayExitCountProjectMap.getOrDefault(projectId, 0D));
            statisticsPlantHistoryVO.setExit(exitCount);
            // 昨日计划需求量 - 昨日出站量
            BigDecimal yesterdayResidue = BigDecimal.valueOf(yesterdayResidueProjectMap.getOrDefault(projectId, 0D));
            // 昨日的昨日剩余量
            BigDecimal theDayBeforeYesterdayResidueCount = BigDecimal.valueOf(cutoffYesterdayResidueProjectMap.getOrDefault(projectId, 0D));
            // 昨日剩余量 = 昨日的昨日剩余量 + (昨日计划需求量 - 昨日出站量)
            BigDecimal yesterdayResidueCount = theDayBeforeYesterdayResidueCount.add(yesterdayResidue);
            statisticsPlantHistoryVO.setYesterdayResidue(yesterdayResidueCount);
            todayTable.add(statisticsPlantHistoryVO);
        });

        // 明日总产能需求完成情况表格
        List<StatisticsPlantFutureVO> tomorrowTable = Lists.newArrayList();
        projectIdList.forEach(projectId -> {
            StatisticsPlantFutureVO statisticsPlantFutureVO = new StatisticsPlantFutureVO();
            statisticsPlantFutureVO.setProjectId(projectId);
            statisticsPlantFutureVO.setProjectName(projectMap.get(projectId));
            // 计划需求量
            BigDecimal plantPlanCount = BigDecimal.valueOf(tomorrowPlanCountProjectMap.getOrDefault(projectId, 0D));
            statisticsPlantFutureVO.setPlanDemand(plantPlanCount);
            // 今日出站量
            BigDecimal exitCount = BigDecimal.valueOf(todayExitCountProjectMap.getOrDefault(projectId, 0D));
            // 今日需求量 = 今日计划需求量 + 昨日剩余量
            BigDecimal todayPlantPlanCount = BigDecimal.valueOf(todayPlanCountProjectMap.getOrDefault(projectId, 0D));
            BigDecimal yesterdayResidue = BigDecimal.valueOf(yesterdayResidueProjectMap.getOrDefault(projectId, 0D));
            BigDecimal theDayBeforeYesterdayResidueCount = BigDecimal.valueOf(cutoffYesterdayResidueProjectMap.getOrDefault(projectId, 0D));
            BigDecimal yesterdayResidueCount = theDayBeforeYesterdayResidueCount.add(yesterdayResidue);
            // 昨日剩余需求量 = 今日需求量 - 今日出站量
            statisticsPlantFutureVO.setYesterdayResidue(BigDecimal.valueOf(MathUtil.parsePoint(todayPlantPlanCount.add(yesterdayResidueCount).subtract(exitCount).doubleValue(), 2)));
            tomorrowTable.add(statisticsPlantFutureVO);
        });

        result.setYesterdayTable(yesterdayTable);
        result.setTodayTable(todayTable);
        result.setTomorrowTable(tomorrowTable);

        return result;
    }

    @Override
    public List<StatisticsPlantLineVO> queryTimeRangePlantLine(String start, String end, String unit, List<Integer> materialIdList) {

        List<StatisticsPlantLineVO> result = Lists.newArrayList();

        if (StringUtils.isBlank(start) || StringUtils.isBlank(end) || StringUtils.isBlank(unit) || CollectionUtil.isEmpty(materialIdList)) {
            return result;
        }

        List<Integer> projectIds = getProjectIdConfig();
        if (CollectionUtil.isEmpty(projectIds)) {
            return result;
        }

        BigDecimal planSum = Optional.ofNullable(plantMapper.queryCutoffSomedayPlanCount(start, projectIds, unit, materialIdList)).orElse(BigDecimal.ZERO);
        BigDecimal exitSum = Optional.ofNullable(plantMapper.queryCutoffSomedayExitCount(start, projectIds, unit, materialIdList)).orElse(BigDecimal.ZERO);
        // 开始时间的昨日剩余量
        BigDecimal residueSum = planSum.subtract(exitSum);

        List<StatisticsPlantDTO> rangePlanCount = Optional.ofNullable(plantMapper.queryTimeRangePlanCount(start, end, projectIds, unit, materialIdList)).orElse(Lists.newArrayList());
        List<StatisticsPlantDTO> rangeExitCount = Optional.ofNullable(plantMapper.queryTimeRangeExitCount(start, end, projectIds, unit, materialIdList)).orElse(Lists.newArrayList());
        Map<String, BigDecimal> planMap = rangePlanCount.stream().collect(Collectors.toMap(StatisticsPlantDTO::getReceiveTime, StatisticsPlantDTO::getPlanCount));
        Map<String, BigDecimal> productionMap = rangePlanCount.stream().collect(Collectors.toMap(StatisticsPlantDTO::getReceiveTime, StatisticsPlantDTO::getProductionCount));
        Map<String, BigDecimal> exitMap = rangeExitCount.stream().collect(Collectors.toMap(StatisticsPlantDTO::getExitTime, StatisticsPlantDTO::getExitCount));

        // 时间轴
        List<String> timerShaft = DateUtil.formatTimerShaft(start, end);

        for (int i = 0; i < timerShaft.size(); i++) {
            String time = timerShaft.get(i);
            StatisticsPlantLineVO vo = new StatisticsPlantLineVO();
            vo.setTime(time);
            // 计划需求量
            BigDecimal planCount = planMap.getOrDefault(time, BigDecimal.ZERO);
            // 配单量
            BigDecimal productionCount = productionMap.getOrDefault(time, BigDecimal.ZERO);
            vo.setProduction(productionCount);
            // 出站量
            BigDecimal exitCount = exitMap.getOrDefault(time, BigDecimal.ZERO);
            vo.setExit(exitCount);
            // 需求量 = 计划需求量 + 昨日剩余量 - 出站量
            vo.setDemand(i == 0 ? planCount.add(residueSum) : planCount.add(result.get(i - 1).getDemand().subtract(result.get(i - 1).getExit())));
            result.add(vo);
        }

        return result;
    }

    @Override
    public List<StatisticsPlantHistoryVO> queryHistoryPlantHistogramSecond(String time, String unit, List<Integer> materialIdList) {
        List<StatisticsPlantHistoryVO> result = Lists.newArrayList();

        if (StringUtils.isBlank(time) || StringUtils.isBlank(unit) || CollectionUtil.isEmpty(materialIdList)) {
            return result;
        }

        List<Integer> projectIds = getProjectIdConfig();
        if (CollectionUtil.isEmpty(projectIds)) {
            return result;
        }
        // 计划需求量
        List<StatisticsPlantDTO> planList = Optional.ofNullable(plantMapper.querySomedayPlanCount(time, projectIds, unit, materialIdList)).orElse(Lists.newArrayList());
        Map<Integer, Double> planCountProjectMap = planList.stream().collect(Collectors.toMap(StatisticsPlantDTO::getProjectId, item -> MathUtil.parsePoint(item.getPlanCount().doubleValue(), 2)));
        // 出站量
        List<StatisticsPlantDTO> exitList = Optional.ofNullable(plantMapper.querySomedayExitCount(time, projectIds, unit, materialIdList)).orElse(Lists.newArrayList());
        Map<Integer, Double> exitCountProjectMap = exitList.stream().collect(Collectors.toMap(StatisticsPlantDTO::getProjectId, item -> MathUtil.parsePoint(item.getExitCount().doubleValue(), 2)));
        // 截止指定日期的计划需求量之和
        List<StatisticsPlantDTO> cutoffSomedayPlanList = Optional.ofNullable(plantMapper.queryCutoffSomedayPlanCountGroupByPlant(time, projectIds, unit, materialIdList)).orElse(Lists.newArrayList());
        Map<Integer, Double> cutoffSomedayPlanCountProjectMap = cutoffSomedayPlanList.stream().collect(Collectors.toMap(StatisticsPlantDTO::getProjectId, item -> MathUtil.parsePoint(item.getPlanCount().doubleValue(), 2)));
        // 截止指定日期的出站量之和
        List<StatisticsPlantDTO> cutoffSomedayExitList = Optional.ofNullable(plantMapper.queryCutoffSomedayExitCountGroupByPlant(time, projectIds, unit, materialIdList)).orElse(Lists.newArrayList());
        Map<Integer, Double> cutoffSomedayExitCountProjectMap = cutoffSomedayExitList.stream().collect(Collectors.toMap(StatisticsPlantDTO::getProjectId, item -> MathUtil.parsePoint(item.getExitCount().doubleValue(), 2)));
        // 截止指定日期：昨日剩余量
        Map<Integer, Double> cutoffSomedayResidueMap = residueProjectMap(cutoffSomedayPlanCountProjectMap, cutoffSomedayExitCountProjectMap);

        // 拌合站名称
        List<Integer> projectIdList = Lists.newArrayList();
        projectIdList.addAll(planCountProjectMap.keySet());
        projectIdList.addAll(exitCountProjectMap.keySet());
        projectIdList.addAll(cutoffSomedayResidueMap.keySet());
        projectIdList = projectIdList.stream().distinct().collect(Collectors.toList());
        List<SimpleConstructionProjectDto> projectDtoList = projectServiceProxy.findProjectsByProjectIds(projectIdList);
        Map<Integer, String> projectMap = projectDtoList.stream().collect(Collectors.toMap(SimpleConstructionProjectDto::getProjectId,
                SimpleConstructionProjectDto::getProjectTitle, (k1, k2) -> k1));

        for (Integer projectId : projectIdList) {
            StatisticsPlantHistoryVO vo = new StatisticsPlantHistoryVO();
            vo.setProjectId(projectId);
            vo.setProjectName(projectMap.get(projectId));
            vo.setPlanDemand(BigDecimal.valueOf(planCountProjectMap.getOrDefault(projectId, 0D)));
            vo.setExit(BigDecimal.valueOf(exitCountProjectMap.getOrDefault(projectId, 0D)));
            vo.setYesterdayResidue(BigDecimal.valueOf(cutoffSomedayResidueMap.getOrDefault(projectId, 0D)));
            result.add(vo);
        }

        return result;
    }

    @Override
    public List<StatisticsPlantFutureVO> queryTomorrowPlantHistogramSecond(String unit, List<Integer> materialIdList) {
        List<StatisticsPlantFutureVO> result = Lists.newArrayList();

        if (StringUtils.isBlank(unit) || CollectionUtil.isEmpty(materialIdList)) {
            return result;
        }

        List<Integer> projectIds = getProjectIdConfig();
        if (CollectionUtil.isEmpty(projectIds)) {
            return result;
        }
        // 计划需求量
        List<StatisticsPlantDTO> planList = Optional.ofNullable(plantMapper.querySomedayPlanCount(DateUtil.tomorrowStr(), projectIds, unit, materialIdList)).orElse(Lists.newArrayList());
        Map<Integer, Double> planCountProjectMap = planList.stream().collect(Collectors.toMap(StatisticsPlantDTO::getProjectId, item -> MathUtil.parsePoint(item.getPlanCount().doubleValue(), 2)));
        // 今日计划需求量
        List<StatisticsPlantDTO> todayPlanList = Optional.ofNullable(plantMapper.querySomedayPlanCount(DateUtil.todayStr(), projectIds, unit, materialIdList)).orElse(Lists.newArrayList());
        Map<Integer, Double> todayPlanCountProjectMap = todayPlanList.stream().collect(Collectors.toMap(StatisticsPlantDTO::getProjectId, item -> MathUtil.parsePoint(item.getPlanCount().doubleValue(), 2)));
        // 今日出站量
        List<StatisticsPlantDTO> todayExitList = Optional.ofNullable(plantMapper.querySomedayExitCount(DateUtil.todayStr(), projectIds, unit, materialIdList)).orElse(Lists.newArrayList());
        Map<Integer, Double> todayExitCountProjectMap = todayExitList.stream().collect(Collectors.toMap(StatisticsPlantDTO::getProjectId, item -> MathUtil.parsePoint(item.getExitCount().doubleValue(), 2)));
        // 截止今日的计划需求量之和
        List<StatisticsPlantDTO> cutoffSomedayPlanList = Optional.ofNullable(plantMapper.queryCutoffSomedayPlanCountGroupByPlant(DateUtil.todayStr(), projectIds, unit, materialIdList)).orElse(Lists.newArrayList());
        Map<Integer, Double> cutoffSomedayPlanCountProjectMap = cutoffSomedayPlanList.stream().collect(Collectors.toMap(StatisticsPlantDTO::getProjectId, item -> MathUtil.parsePoint(item.getPlanCount().doubleValue(), 2)));
        // 截止今日的出站量之和
        List<StatisticsPlantDTO> cutoffSomedayExitList = Optional.ofNullable(plantMapper.queryCutoffSomedayExitCountGroupByPlant(DateUtil.todayStr(), projectIds, unit, materialIdList)).orElse(Lists.newArrayList());
        Map<Integer, Double> cutoffSomedayExitCountProjectMap = cutoffSomedayExitList.stream().collect(Collectors.toMap(StatisticsPlantDTO::getProjectId, item -> MathUtil.parsePoint(item.getExitCount().doubleValue(), 2)));
        // 今日：昨日剩余量
        Map<Integer, Double> cutoffSomedayResidueMap = residueProjectMap(cutoffSomedayPlanCountProjectMap, cutoffSomedayExitCountProjectMap);

        // 拌合站名称
        List<Integer> projectIdList = Lists.newArrayList();
        projectIdList.addAll(planCountProjectMap.keySet());
        projectIdList.addAll(todayPlanCountProjectMap.keySet());
        projectIdList.addAll(todayExitCountProjectMap.keySet());
        projectIdList.addAll(cutoffSomedayResidueMap.keySet());
        projectIdList = projectIdList.stream().distinct().collect(Collectors.toList());
        List<SimpleConstructionProjectDto> projectDtoList = projectServiceProxy.findProjectsByProjectIds(projectIdList);
        Map<Integer, String> projectMap = projectDtoList.stream().collect(Collectors.toMap(SimpleConstructionProjectDto::getProjectId,
                SimpleConstructionProjectDto::getProjectTitle, (k1, k2) -> k1));

        for (Integer projectId : projectIdList) {
            StatisticsPlantFutureVO vo = new StatisticsPlantFutureVO();
            vo.setProjectId(projectId);
            vo.setProjectName(projectMap.get(projectId));
            vo.setPlanDemand(BigDecimal.valueOf(planCountProjectMap.getOrDefault(projectId, 0D)));
            vo.setYesterdayResidue(BigDecimal.valueOf(MathUtil.parsePoint(todayPlanCountProjectMap.getOrDefault(projectId, 0D) +
                    cutoffSomedayResidueMap.getOrDefault(projectId, 0D) - todayExitCountProjectMap.getOrDefault(projectId, 0D), 2)));
            result.add(vo);
        }

        return result;
    }

    @Override
    public List<MachineCapacityVO> queryMachineCapacity(MachineCapacityQuery machineCapacityQuery, List<Integer> materialIdList) {
        List<MachineCapacityVO> result = Lists.newArrayList();
        List<Integer> projectIds = getProjectIdConfig();
        if (CollectionUtil.isEmpty(projectIds)) {
            return result;
        }
        List<MachineCapacityDTO> machineCapacityDTOList =  plantMapper.queryMachineCapacity(machineCapacityQuery, projectIds, materialIdList);
        if (CollectionUtil.isEmpty(machineCapacityDTOList)) {
            return result;
        }
        // 构建日期序列
        List<String> dateRange = DateUtil.buildDateRange(machineCapacityQuery.getBeginTime(), machineCapacityQuery.getEndTime(), "MM/dd");

        Map<String, List<MachineCapacityDetailVO>> machineCapacityMap = machineCapacityDTOList.stream()
                .collect(groupingBy(MachineCapacityDTO::getMachineId, mapping(MachineCapacityDetailVO::new, toList())));
        // 机组ids
        List<String> machineIds = machineCapacityDTOList.stream().map(MachineCapacityDTO::getMachineId).collect(toList());
        List<MixingPlantMachine> mixingPlantMachineList = plantMachineService.getBaseMapper().selectBatchIds(machineIds);
        Map<String, MixingPlantMachine> mixingPlantMachineMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(mixingPlantMachineList)) {
            mixingPlantMachineMap = mixingPlantMachineList.stream().collect(toMap(MixingPlantMachine::getId, e -> e));
        }

        Map<String, MixingPlantMachine> finalMixingPlantMachineMap = mixingPlantMachineMap;
        machineCapacityMap.forEach((k, v) -> {
            Map<String, MachineCapacityDetailVO> machineCapacityDetailVOMap = v.stream().collect(toMap(MachineCapacityDetailVO::getDate, e -> e));
            List<MachineCapacityDetailVO> finalMachineCapacityDetailVOList = Lists.newArrayList();
            // 封装日期
            dateRange.forEach(date -> {
                MachineCapacityDetailVO machineCapacityDetailVO = new MachineCapacityDetailVO();
                if (machineCapacityDetailVOMap.containsKey(date)) {
                    machineCapacityDetailVO = machineCapacityDetailVOMap.get(date);
                } else {
                    machineCapacityDetailVO.setDate(date);
                    machineCapacityDetailVO.setNum(BigDecimal.ZERO);
                }
                finalMachineCapacityDetailVOList.add(machineCapacityDetailVO);
            });
            MachineCapacityVO machineCapacityVO = new MachineCapacityVO();
            machineCapacityVO.setMachineId(k);
            MixingPlantMachine mixingPlantMachine = finalMixingPlantMachineMap.get(k);
            Optional.ofNullable(mixingPlantMachine).ifPresent(machine -> machineCapacityVO.setMachineName(machine.getMachineName()));
            machineCapacityVO.setMachineCapacityDetailVOList(finalMachineCapacityDetailVOList);
            result.add(machineCapacityVO);
        });

        return result;
    }

    @Override
    public List<MachineExitDetailVO> queryMachineExitDetail(MachineExitDetailQuery machineExitDetailQuery, List<Integer> materialIdList) {
        List<MachineExitDetailVO> result = Lists.newArrayList();
        List<Integer> projectIds = getProjectIdConfig();
        if (CollectionUtil.isEmpty(projectIds)) {
            return result;
        }
        List<MachineExitDetailVO> machineExitDetailVOList = plantMapper.queryMachineExitDetail(machineExitDetailQuery, projectIds, materialIdList);
        if (CollectionUtil.isEmpty(machineExitDetailVOList)) {
            return result;
        }
        // 材料ids
        Set<Integer> materialIds = machineExitDetailVOList.stream().map(MachineExitDetailVO::getMaterialId).filter(Objects::nonNull).collect(toSet());
        List<MaterialDto> materialDtoList = materialServiceProxy.listMaterialByIds(materialIds);
        Map<Integer, MaterialDto> materialMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(materialDtoList)) {
            materialMap = materialDtoList.stream().collect(toMap(MaterialDto::getMaterialId, e -> e));
        }
        Map<Integer, MaterialDto> finalMaterialMap = materialMap;
        // 封装材料信息
        machineExitDetailVOList.forEach(machineExitDetailVO -> {
            MaterialDto materialDto = finalMaterialMap.get(machineExitDetailVO.getMaterialId());
            Optional.ofNullable(materialDto).ifPresent(material -> {
                machineExitDetailVO.setMaterialSpec(material.getMaterialSpec());
                machineExitDetailVO.setMaterialName(material.getMaterialName());
            });
        });

        return machineExitDetailVOList;
    }


    @Override
    public Boolean openWarehousePlugin() {
        AuthUser currentUser = siteContextHolder.getCurrentUser();
        Integer currentCompanyId = currentUser.getCurrentCompanyId();
        Integer currentProjectId = currentUser.getCurrentProjectId();
        List<Integer> projectIds = Lists.newArrayList();
        if (ObjectUtil.isNotNull(currentProjectId)) {
            projectIds.add(currentProjectId);
        } else {
            List<ProjectVO> allProjectsByCompanyId = projectServiceProxy.getAllProjectsByCompanyId(currentCompanyId);
            if (CollectionUtil.isNotEmpty(allProjectsByCompanyId)) {
                projectIds = allProjectsByCompanyId.stream().map(ProjectVO::getProjectId).collect(Collectors.toList());
            }
        }
        List<Integer> warehouseProjectIds = projectServiceProxy.getPluginProjectIds(ProjectServiceProxy.WAREHOUSE_PLUGIN_NO);
        return projectIds.stream().anyMatch(item -> warehouseProjectIds.contains(item));
    }

    @Override
    public List<RawMaterialDemandStockVO> queryRawMaterialDemandStock(String type, String deadline, Boolean isOverview) {
        List<RawMaterialDemandStockVO> list = Lists.newArrayList();
        // 项目集合
        List<Integer> projectIds = (isOverview != null && isOverview) ? getOverviewProjectId() : getProjectIdConfig();
        if (CollectionUtil.isEmpty(projectIds)) {
            return list;
        }
        // 需求量
        list = plantMapper.queryRawMaterialDemand(projectIds, deadline);
        if (CollectionUtil.isNotEmpty(list)) {
            // 材料ID
            List<Integer> materialIds = list.stream().map(RawMaterialDemandStockVO::getMaterialId).collect(toList());
            Map<Integer, String> materialSpecMap = materialServiceProxy.materialSpec(new HashSet<>(materialIds));
            // 账面库存 & 盘点库存
            Map<Integer, WarehouseStock> stockMap = new HashMap<>();
            List<WarehouseStock> warehouseStockInfo = "1".equals(type) ?
                    stockInfoService.getWarehousePaperStockInfo(projectIds, materialIds) :
                    stockInfoService.getWarehouseInventoryStockInfo(projectIds, materialIds);
            if (CollectionUtil.isNotEmpty(warehouseStockInfo)) {
                stockMap = warehouseStockInfo.stream().collect(toMap(WarehouseStock::getMaterialId, Function.identity()));
            }
            for (RawMaterialDemandStockVO vo : list) {
                Integer materialId = vo.getMaterialId();
                vo.setMaterialSpec(materialSpecMap.getOrDefault(materialId, ""));
                WarehouseStock warehouseStock = stockMap.get(materialId);
                Optional.ofNullable(warehouseStock).ifPresent(item ->
                        vo.setStock("2".equals(type) ?
                                item.getStock() :
                                BigDecimal.valueOf(MathUtil.parsePoint((item.getStock()).subtract(vo.getConsumption()).doubleValue(), 2))));
            }
            // 排序
            list.sort(Comparator.comparing(item -> item.getStock().doubleValue() - item.getDemand().doubleValue()));
        }
        return list;
    }

    @Override
    public List<RawMaterialLineVO> queryRawMaterialDailyConsumption(String start, String end) {

        List<RawMaterialLineVO> result = Lists.newArrayList();
        // 项目集合
        List<Integer> projectIds = getProjectIdConfig();
        if (CollectionUtil.isEmpty(projectIds)) {
            return result;
        }

        List<PlantRawMaterialDailyConsumptionDTO> dtos = plantMapper.queryRawMaterialDailyConsumption(projectIds, start, end);
        if (CollectionUtil.isNotEmpty(dtos)) {
            Map<Integer, List<PlantRawMaterialDailyConsumptionDTO>> materialDataMap = dtos.stream().collect(groupingBy(PlantRawMaterialDailyConsumptionDTO::getMaterialId));
            // 时间轴
            List<String> timerShaft = DateUtil.formatTimerShaft(start, end);
            // 材料ID
            List<Integer> materialIds = dtos.stream().map(PlantRawMaterialDailyConsumptionDTO::getMaterialId).collect(toList());
            Map<Integer, String> materialSpecMap = materialServiceProxy.materialSpec(new HashSet<>(materialIds));

            materialDataMap.forEach((materialId, values) -> {
                Map<String, BigDecimal> timeDataMap = values.stream().collect(
                        Collectors.toMap(PlantRawMaterialDailyConsumptionDTO::getTime, PlantRawMaterialDailyConsumptionDTO::getDailyConsumption));
                RawMaterialLineVO vo = new RawMaterialLineVO();
                vo.setMaterialId(materialId);
                vo.setMaterialSpec(materialSpecMap.getOrDefault(materialId, ""));
                List<RawMaterialLineItemVO> list = Lists.newArrayList();
                for (String time : timerShaft) {
                    RawMaterialLineItemVO item = new RawMaterialLineItemVO();
                    item.setTime(time);
                    item.setValue(timeDataMap.getOrDefault(time, BigDecimal.ZERO));
                    list.add(item);
                }
                vo.setList(list);
                result.add(vo);
            });
        }
        return result;
    }

    @Override
    public List<RawMaterialSendReceiveVO> queryRawMaterialSendReceive() {
        List<RawMaterialSendReceiveVO> list = Lists.newArrayList();
        // 项目集合
        List<Integer> projectIds = getProjectIdConfig();
        if (CollectionUtil.isEmpty(projectIds)) {
            return list;
        }
        // 耗用量统计
        list = plantMapper.queryRawMaterialSendReceive(projectIds);
        if (CollectionUtil.isNotEmpty(list)) {
            // 材料ID
            List<Integer> materialIds = list.stream().map(RawMaterialSendReceiveVO::getMaterialId).collect(toList());
            Map<Integer, String> materialSpecMap = materialServiceProxy.materialSpec(new HashSet<>(materialIds));
            // 入库量统计
            List<WarehouseHistoryStock> historyStockList = stockInfoService.getWarehouseHistoryPaperStockInfo(projectIds, materialIds);
            if (CollUtil.isNotEmpty(historyStockList)) {
                Map<Integer, WarehouseHistoryStock> historyStockMap = historyStockList.stream().collect(Collectors.toMap(WarehouseHistoryStock::getMaterialId,e -> e));
                list.forEach(item -> {
                    Integer materialId = item.getMaterialId();
                    WarehouseHistoryStock warehouseHistoryStock = historyStockMap.get(materialId);
                    String materialSpec = materialSpecMap.get(materialId);
                    item.setVariety(materialSpec.replaceFirst("/.*$", ""));
                    item.setSpecification(materialSpec.replaceFirst(".*?/", ""));
                    if (ObjectUtil.isNotNull(warehouseHistoryStock)) {
                        item.setAllReceive(warehouseHistoryStock.getStock());
                        item.setYesterdayReceive(warehouseHistoryStock.getYesterdayReceive());
                        item.setCurrentMonthReceive(warehouseHistoryStock.getCurrentMonthReceive());
                        item.setCurrentYearReceive(warehouseHistoryStock.getCurrentYearReceive());
                    }
                });
            }
        }
        return list;
    }

    @Override
    public List<RawMaterialWarehouseVO> queryRawMaterialWarehouseList(Boolean isOverview) {
        List<RawMaterialWarehouseVO> list = Lists.newArrayList();
        // 项目集合
        List<Integer> projectIds = (isOverview != null && isOverview) ? getOverviewProjectId() : getProjectIdConfig();
        if (CollectionUtil.isEmpty(projectIds)) {
            return list;
        }
        // 仓库列表
        List<WarehousePosition> infoList = warehousePositionService.warehouseInfoList(projectIds);
        if (CollectionUtil.isNotEmpty(infoList)) {
            Map<Integer, String> projectNameMap = new HashMap<>();
            // 项目名称--项目层不需要带项目名称
            Integer currentProjectId = siteContextHolder.getCurrentUser().getCurrentProjectId();
            if (ObjectUtil.isNull(currentProjectId)) {
                List<ProjectVO> simpleProjects = projectServiceProxy.getSimpleProjects(projectIds);
                projectNameMap = simpleProjects.stream().collect(Collectors.toMap(ProjectVO::getProjectId, ProjectVO::getProjectTitle));
            }
            for (WarehousePosition position : infoList) {
                String projectName = projectNameMap.get(position.getProjectId());
                RawMaterialWarehouseVO vo = new RawMaterialWarehouseVO();
                vo.setWarehouseId(position.getWarehouseId());
                vo.setWarehouseName((StringUtils.isNotBlank(projectName) ? projectName + "-" : "") + position.getWarehouseName());
                list.add(vo);
            }
        }
        return list;
    }

    @Override
    public List<RawMaterialLineVO> queryRawMaterialPaperInventoryRate(String periods, String warehouseId, Boolean isOverview) {
        List<RawMaterialLineVO> list = Lists.newArrayList();
        if (StringUtils.isBlank(periods) || StringUtils.isBlank(warehouseId)) {
            return list;
        }
        // 项目集合
        List<Integer> projectIds = (isOverview != null && isOverview) ? getOverviewProjectId() : getProjectIdConfig();
        if (CollectionUtil.isEmpty(projectIds)) {
            return list;
        }
        if ("ALL".equals(periods)) {
            periods = String.valueOf(Integer.MAX_VALUE - 1);
        }
        // 查询指定盘点期次，由于计算需要上期盘点数据，所以查询需加一次盘点期次
        Integer intPeriods = Integer.parseInt(periods);
        List<PaperInventoryAmount> paperInventoryAmount = stockInfoService.getPaperInventoryAmount(projectIds, Arrays.asList(warehouseId), intPeriods + 1);
        if (CollectionUtil.isNotEmpty(paperInventoryAmount)) {
            // 盘存
            Map<Integer, List<PaperInventoryAmount>> rankingMap = paperInventoryAmount.stream().collect(groupingBy(PaperInventoryAmount::getRanking));
            // 材料ID
            List<Integer> materialIds = paperInventoryAmount.stream().map(PaperInventoryAmount::getMaterialId).distinct().collect(toList());
            // 品种/规格
            Map<Integer, String> materialSpecMap = materialServiceProxy.materialSpec(new HashSet<>(materialIds));
            // 盘点排名(时间倒序)
            List<Integer> rankingList = paperInventoryAmount.stream().map(PaperInventoryAmount::getRanking).distinct().sorted().collect(toList());

            Integer maxRank = rankingList.get(rankingList.size() - 1);
            // true: 表示查询期次最早的盘点期次有上期盘点
            boolean b = maxRank.intValue() == (intPeriods + 1);
            String startTime = b ? DateUtil.parseLocalDateTime(rankingMap.get(maxRank).get(0).getInventoryTime(), DateUtil.SS_DATE_TIME_FORMATTER) : UTC_TIME;
            String endTime = DateUtil.parseLocalDateTime(rankingMap.get(1).get(0).getInventoryTime(), DateUtil.SS_DATE_TIME_FORMATTER);
            // 账面实耗
            List<PaperInventoryPeriodsDTO> paperInventoryDTOS = plantMapper.queryRawMaterialPaperInventoryPeriods(startTime, endTime, projectIds, materialIds);
            // 收料累计/站外支出查询参数
            EnterLeaveWarehouseParam param = new EnterLeaveWarehouseParam();
            param.setWarehouseId(warehouseId);
            param.setStartTime(DateUtil.parseLocalDateTime(startTime));
            param.setEndTime(DateUtil.parseLocalDateTime(endTime));
            // 收料累计/站外支出
            List<EnterLeaveAmount> enterLeaveAmount = stockInfoService.getWarehouseEnterLeaveAmount(Arrays.asList(param));
            // 汇总
            materialIds.forEach(materialId -> {
                List<RawMaterialLineItemVO> itemVoList = Lists.newArrayList();
                RawMaterialLineVO vo = new RawMaterialLineVO();
                vo.setMaterialId(materialId);
                vo.setMaterialSpec(materialSpecMap.get(materialId));
                // 盘点排名反向遍历
                for (int i = rankingList.size() - (b ? 1 : 0); i >= 1; i--) {
                    RawMaterialLineItemVO itemVO = new RawMaterialLineItemVO();
                    // 本期盘存(必有值)
                    List<PaperInventoryAmount> currentPaperInventoryList = rankingMap.get(i);
                    // 当前材料及盘点期次的本期盘存
                    Double currentPaperInventorySum = currentPaperInventoryList.stream()
                            .filter(item -> item.getMaterialId().intValue() == materialId.intValue())
                            .collect(summingDouble(item -> Optional.ofNullable(item.getInventoryAmount()).orElse(BigDecimal.ZERO).doubleValue()));
                    // 上期盘存(b=true 必有值，b=false & i=rankingList.size() 没值)
                    List<PaperInventoryAmount> lastPaperInventoryList = rankingMap.get(i + 1);
                    // 当前材料及盘点期次的上期盘存
                    Double lastPaperInventorySum = Optional.ofNullable(lastPaperInventoryList).orElse(Lists.newArrayList()).stream()
                            .filter(item -> item.getMaterialId().intValue() == materialId.intValue())
                            .collect(summingDouble(item -> Optional.ofNullable(item.getInventoryAmount()).orElse(BigDecimal.ZERO).doubleValue()));
                    // 本期盘存时间
                    LocalDateTime currentPaperInventoryTime = currentPaperInventoryList.get(0).getInventoryTime();
                    itemVO.setTime(DateUtil.parseLocalDateTime(currentPaperInventoryTime, DateUtil.SS_DATE_TIME_FORMATTER));
                    // 上期盘存时间
                    LocalDateTime lastPaperInventoryTime = CollectionUtil.isEmpty(lastPaperInventoryList) ?
                            DateUtil.parseLocalDateTime(UTC_TIME) : lastPaperInventoryList.get(0).getInventoryTime();
                    // 当前材料及盘点期次的账面实耗
                    Double paperInventoryPeriodsSum = Optional.ofNullable(paperInventoryDTOS).orElse(Lists.newArrayList()).stream()
                            .filter(item -> item.getMaterialId().intValue() == materialId.intValue() &&
                                    atPeriods(lastPaperInventoryTime, currentPaperInventoryTime, item.getIngredientTime()))
                            .collect(summingDouble(item -> Optional.ofNullable(item.getAllConsumption()).orElse(BigDecimal.ZERO).doubleValue()));
                    // 当前材料及盘点期次的收料数量
                    Double enterMaterialPeriodsSum = Optional.ofNullable(enterLeaveAmount).orElse(Lists.newArrayList()).stream()
                            .filter(item -> ObjectUtil.isNotNull(item.getEnterMaterialId()) &&
                                    item.getEnterMaterialId().intValue() == materialId.intValue() &&
                                    atPeriods(lastPaperInventoryTime, currentPaperInventoryTime, item.getEnterAuditTime()))
                            .collect(summingDouble(item -> Optional.ofNullable(item.getEnterMaterialNum()).orElse(BigDecimal.ZERO).doubleValue()));
                    // 当前材料及盘点期次的站外支出
                    Double outMaterialPeriodsSum = Optional.ofNullable(enterLeaveAmount).orElse(Lists.newArrayList()).stream()
                            .filter(item -> ObjectUtil.isNotNull(item.getOutMaterialId()) &&
                                    item.getOutMaterialId().intValue() == materialId.intValue() &&
                                    atPeriods(lastPaperInventoryTime, currentPaperInventoryTime, item.getOutAuditTime()))
                            .collect(summingDouble(item -> Optional.ofNullable(item.getOutMaterialNum()).orElse(BigDecimal.ZERO).doubleValue()));
                    // 节超率 = (节超 / 账面实耗) * 100%
                    IfBranchUtil.isTureOrFalse(ObjectUtil.isNull(paperInventoryPeriodsSum) || paperInventoryPeriodsSum == 0)
                            .trueOrFalseHandle(
                                    () -> itemVO.setValue(BigDecimal.ZERO),
                                    () -> itemVO.setValue(BigDecimal.valueOf(
                                            // 节超 = 账面实耗 - 盘点实耗
                                            MathUtil.parsePoint((paperInventoryPeriodsSum -
                                                    // 盘点实耗 = 上期盘存 + 收料数量 - 站外支出 - 本期盘存
                                                    (Optional.ofNullable(lastPaperInventorySum).orElse(0D) +
                                                            Optional.ofNullable(enterMaterialPeriodsSum).orElse(0D) -
                                                            Optional.ofNullable(outMaterialPeriodsSum).orElse(0D) -
                                                            Optional.ofNullable(currentPaperInventorySum).orElse(0D))) /
                                                    paperInventoryPeriodsSum * 100, 2)))
                            );
                    itemVoList.add(itemVO);
                }
                vo.setList(itemVoList);
                list.add(vo);
            });
        }
        return list;
    }

    /**
     *  节超量 = 账面实耗 - 盘点实耗； 盘点实耗 = 上期盘存 + 收料数量 - 站外支出 - 本期盘存。
     *
     *  例：
     * 【仓库1】有三种物料【物料①】【物料②】【物料③】
     *   第一次盘点       第二次盘点          第三次盘点             第四次盘点
     *  物料①、物料②      物料②           物料①、物料②      物料①、物料②、物料③
     *
     *  1. 每次盘点的物料可能不同
     *  2. 存在多个仓库
     *  3. 不同仓库的物料可能有相同的，也可能有不同的
     *  4. 不同仓库的盘点期次和盘点时间不同
     *  5. 计算每个仓库的每次盘点下的所有物料的对应期次的节超量，第一次盘点的上期盘存取0计算，上期盘存时间取UTC起始时间。最终按物料累加
     *
     *  ➡️ 常规计算：
     *  1. 遍历盘点期次，遍历盘点的物料，计算物料对应期次的【账面实耗】和【盘点实耗】。需要注意【账面实耗】、【收料数量】和【站外支出】需是对应盘点周期和仓库的物料数量
     *  2. 多个仓库遍历即可
     *
     *  ➡️ 抽象计算：
     *  物料①第一次盘点节超量：账面实耗① - (0 + 收料数量① - 站外支出① - 盘存①)
     *  物料①第三次盘点节超量：账面实耗③ - (0 + 收料数量③ - 站外支出③ - 盘存③)
     *  物料①第四次盘点节超量：账面实耗④ - (盘存③ + 收料数量④ - 站外支出④ - 盘存④)
     *
     *  物料①节超量 = 账面实耗① - (0 + 收料数量① - 站外支出① - 盘存①) + 账面实耗③ - (0 + 收料数量③ - 站外支出③ - 盘存③) +
     *               账面实耗④ - (盘存③ + 收料数量④ - 站外支出④ - 盘存④)
     *
     *  ➡️ 将盘点期次抽象成 n  ==>
     *  如果每次盘点都有物料①，则 = ∑(账面实耗①..ⓝ) - ∑(收料数量①..ⓝ) + ∑(站外支出①..ⓝ) + 盘存ⓝ
     *  但存在某次(m)盘点可能不包含物料①，则约不掉上期盘存，即盘存m-1  ==>  ∑(账面实耗①..ⓝ) - ∑(收料数量①..ⓝ) + ∑(站外支出①..ⓝ) + ∑(盘存m-1) + 盘存ⓝ
     *
     *  ➡️ 合并多个仓库：
     *  取出所有盘点的物料作为统计范围，按仓库盘点期次形成本期盘点、上期盘点、上上期盘点...
     *  本期盘点(最近一次盘点)物料合并，剔除已统计的物料递归上期盘点，break条件为：统计完所有盘点的物料
     *
     *  ➡️ 完整公式：n为盘点期次，m为当前物料没有盘点的期次排名
     *  f(n) {
     *      if(统计完所有盘点的物料) {
     *          break;
     *      }
     *      节超量 = ∑(账面实耗) - ∑(收料数量) + ∑(站外支出) + ∑(盘存m-1) + 盘存ⓝ;
     *      剔除已统计的物料;
     *      f(n-1);
     *  }
     *
     * @param warehouseIds
     * @return
     */
    @Override
    public List<RawMaterialPaperInventoryAmountVO> queryRawMaterialPaperInventoryAmount(List<String> warehouseIds, Boolean isOverview) {
        List<RawMaterialPaperInventoryAmountVO> list = Lists.newArrayList();
        if (CollectionUtil.isEmpty(warehouseIds)) {
            return list;
        }
        // 项目集合
        List<Integer> projectIds = (isOverview != null && isOverview) ? getOverviewProjectId() : getProjectIdConfig();
        if (CollectionUtil.isEmpty(projectIds)) {
            return list;
        }

        // 查询所有盘点
        List<PaperInventoryAmount> allPaperInventoryAmount = stockInfoService.getPaperInventoryAmount(projectIds, warehouseIds, Integer.MAX_VALUE);
        if (CollectionUtil.isNotEmpty(allPaperInventoryAmount)) {
            // 盘存：按rank-materialId分组
            Map<Integer, Map<Integer, Double>> rankMaterialIdMap = allPaperInventoryAmount.stream().collect(
                    groupingBy(PaperInventoryAmount::getRanking,
                            groupingBy(PaperInventoryAmount::getMaterialId,
                                    collectingAndThen(toList(), l ->
                                            l.stream().collect(
                                                    summingDouble(item ->
                                                            Optional.ofNullable(item.getInventoryAmount()).orElse(BigDecimal.ZERO).doubleValue()))))));
            // 品种/规格
            Set<Integer> materialIds = allPaperInventoryAmount.stream().map(PaperInventoryAmount::getMaterialId).collect(toSet());
            Map<Integer, String> materialSpecMap = materialServiceProxy.materialSpec(materialIds);

            // 递归获取物料及最后盘点时间
            List<PaperInventoryMaterialTime> materialTimes = Lists.newArrayList();
            getAllMaterialTime(1, materialIds.size(), allPaperInventoryAmount, materialTimes);

            // 账面实耗
            List<RawMaterialPaperInventoryDTO> rawMaterialPaperInventoryDTOS = plantMapper.queryAllIngredientRawMaterialPaperInventory(projectIds, materialTimes);
            Map<Integer, BigDecimal> paperCostMap = Optional.ofNullable(rawMaterialPaperInventoryDTOS).orElse(Lists.newArrayList()).stream()
                    .collect(toMap(RawMaterialPaperInventoryDTO::getMaterialId, RawMaterialPaperInventoryDTO::getAllConsumption));
            // 收料累计、站外支出查询参数
            List<EnterLeaveWarehouseParam> params = buildAllEnterLeaveAmountParams(allPaperInventoryAmount);
            // 收料累计/站外支出
            Map<Integer, Double> enterDataMap = new HashMap<>();
            Map<Integer, Double> outDataMap = new HashMap<>();
            getEnterLeaveAmount(params, Lists.newArrayList(), enterDataMap, outDataMap);
            // 递归计算
            recursionCalculatePaperInventory(1, materialIds.size(), allPaperInventoryAmount, rankMaterialIdMap, materialSpecMap, paperCostMap, enterDataMap, outDataMap, list);

            // 倒序
            list = CollectionUtil.sort(list, Comparator.comparing(RawMaterialPaperInventoryAmountVO::getPaperInventoryAmount).reversed());
        }
        return list;
    }

    /**
     *
     * @param rank  排名
     * @param allMaterialNum    所有物料数量
     * @param allPaperInventoryAmount   原始数据
     * @param rankMaterialIdMap   原始数据盘存：按rank-materialId分组
     * @param materialSpecMap  品种规格
     * @param paperCostMap  账面实耗
     * @param enterDataMap  收料数量
     * @param outDataMap  站外支出
     * @param list  返回数据
     */
    private void recursionCalculatePaperInventory(int rank, int allMaterialNum,
                                                  List<PaperInventoryAmount> allPaperInventoryAmount,
                                                  Map<Integer, Map<Integer, Double>> rankMaterialIdMap,
                                                  Map<Integer, String> materialSpecMap,
                                                  Map<Integer, BigDecimal> paperCostMap,
                                                  Map<Integer, Double> enterDataMap,
                                                  Map<Integer, Double> outDataMap,
                                                  List<RawMaterialPaperInventoryAmountVO> list) {
        List<Integer> materialIds = list.stream().map(RawMaterialPaperInventoryAmountVO::getMaterialId).collect(toList());
        int finalRank = rank;
        List<PaperInventoryAmount> rankList = allPaperInventoryAmount.stream().filter(item ->
                item.getRanking().intValue() == finalRank &&
                        !materialIds.contains(item.getMaterialId())).collect(toList());
        if (CollectionUtil.isNotEmpty(rankList)) {
            // 物料去重
            Set<Integer> rankMaterialIdSet = rankList.stream().map(PaperInventoryAmount::getMaterialId).collect(toSet());
            // 计算节超量
            rankMaterialIdSet.stream().forEach(rankMaterialId -> {
                // 计算当前物料在以往的盘点中没有被盘点的期次
                List<Integer> notInventoryRank = getNotInventoryRank(rankMaterialId, finalRank, allPaperInventoryAmount);
                // 计算 ∑(盘存m-1)
                Double notInventoryRankSum = 0D;
                if (CollectionUtil.isNotEmpty(notInventoryRank)) {
                    for (Integer r : notInventoryRank) {
                        // 排名是按盘点时间倒序排列，所以这里 rank - 1
                        notInventoryRankSum += rankMaterialIdMap.getOrDefault(r - 1, new HashMap<>()).getOrDefault(rankMaterialId, 0D);
                    }
                }
                RawMaterialPaperInventoryAmountVO vo = new RawMaterialPaperInventoryAmountVO();
                vo.setMaterialId(rankMaterialId);
                vo.setMaterialSpec(materialSpecMap.get(rankMaterialId));
                // ∑(账面实耗)
                BigDecimal paperCost = paperCostMap.getOrDefault(rankMaterialId, BigDecimal.ZERO);
                // ∑(收料数量)
                Double enterData = enterDataMap.getOrDefault(rankMaterialId, 0D);
                // ∑(站外支出)
                Double outData = outDataMap.getOrDefault(rankMaterialId, 0D);
                // 盘存ⓝ
                Double inventory = rankMaterialIdMap.getOrDefault(finalRank, new HashMap<>()).getOrDefault(rankMaterialId, 0D);
                // 节超量
                BigDecimal paperInventory = paperCost.subtract(BigDecimal.valueOf(enterData)).add(BigDecimal.valueOf(outData)).add(BigDecimal.valueOf(notInventoryRankSum)).add(BigDecimal.valueOf(inventory));
                vo.setPaperInventoryAmount(BigDecimal.valueOf(MathUtil.parsePoint(paperInventory.doubleValue(), 2)));
                list.add(vo);
            });
        }
        if (allMaterialNum != list.size()) {
            rank++;
            recursionCalculatePaperInventory(rank, allMaterialNum, allPaperInventoryAmount, rankMaterialIdMap, materialSpecMap, paperCostMap, enterDataMap, outDataMap, list);
        }
    }

    /**
     * 获取当前物料及指定盘点期次之前未盘点的期次
     * @param materialId
     * @param rank
     * @param allPaperInventoryAmount
     * @return
     */
    private List<Integer> getNotInventoryRank(Integer materialId, Integer rank, List<PaperInventoryAmount> allPaperInventoryAmount) {
        List<Integer> list = Lists.newArrayList();
        List<Integer> rankList = allPaperInventoryAmount.stream().map(PaperInventoryAmount::getRanking).distinct().sorted().collect(toList());
        // 从当前盘点期次往前推，索引从0开始，rank从1开始，所以遍历不需要再加一，但真正的rank需要加一
        for (int i = rank; i < rankList.size(); i++) {
            int finalRank = i;
            boolean match = allPaperInventoryAmount.stream().anyMatch(item -> item.getRanking().intValue() == finalRank &&
                    item.getMaterialId().intValue() == materialId.intValue());
            if (!match) {
                list.add(i + 1);
            }
        }
        return list;
    }


    /**
     *
     * @param rank  排名
     * @param allMaterialNum    所有物料数量
     * @param allPaperInventoryAmount   原始数据
     * @param materialTimes 返回数据
     */
    private void getAllMaterialTime(int rank, int allMaterialNum,
                                    List<PaperInventoryAmount> allPaperInventoryAmount,
                                    List<PaperInventoryMaterialTime> materialTimes) {
        List<Integer> materialIds = materialTimes.stream().map(PaperInventoryMaterialTime::getMaterialId).collect(toList());
        int finalRank = rank;
        List<PaperInventoryAmount> rankList = allPaperInventoryAmount.stream().filter(item ->
                item.getRanking().intValue() == finalRank &&
                        !materialIds.contains(item.getMaterialId())).collect(toList());
        if (CollectionUtil.isNotEmpty(rankList)) {
            // 最后时间
            String maxTime = DateUtil.parseLocalDateTime(rankList.stream()
                            .max(Comparator.comparing(PaperInventoryAmount::getInventoryTime)).get().getInventoryTime(),
                    DateUtil.SS_DATE_TIME_FORMATTER);
            // 物料去重
            Set<Integer> rankMaterialId = rankList.stream().map(PaperInventoryAmount::getMaterialId).collect(toSet());
            rankMaterialId.stream().forEach(item -> {
                PaperInventoryMaterialTime materialTime = new PaperInventoryMaterialTime();
                materialTime.setMaterialId(item);
                materialTime.setStart(UTC_TIME);
                materialTime.setEnd(maxTime);
                materialTimes.add(materialTime);
            });
        }
        if (allMaterialNum != materialTimes.size()) {
            rank++;
            getAllMaterialTime(rank, allMaterialNum, allPaperInventoryAmount, materialTimes);
        }
    }

//    public static void main(String[] args) {
//        List<PaperInventoryAmount> allPaperInventoryAmount = Lists.newArrayList();
//        PaperInventoryAmount a1 = new PaperInventoryAmount();
//        a1.setWarehouseId("1");
//        a1.setMaterialId(1);
//        a1.setRanking(1);
//        a1.setInventoryTime(LocalDateTime.of(2022, 9, 16, 12, 10, 2));
//        allPaperInventoryAmount.add(a1);
//        PaperInventoryAmount a2 = new PaperInventoryAmount();
//        a2.setWarehouseId("1");
//        a2.setMaterialId(2);
//        a2.setRanking(1);
//        a2.setInventoryTime(LocalDateTime.of(2022, 9, 16, 12, 10, 2));
//        allPaperInventoryAmount.add(a2);
//        PaperInventoryAmount a3 = new PaperInventoryAmount();
//        a3.setWarehouseId("1");
//        a3.setMaterialId(3);
//        a3.setRanking(1);
//        a3.setInventoryTime(LocalDateTime.of(2022, 9, 16, 12, 10, 2));
//        allPaperInventoryAmount.add(a3);
//        PaperInventoryAmount a12 = new PaperInventoryAmount();
//        a12.setWarehouseId("1");
//        a12.setMaterialId(1);
//        a12.setRanking(2);
//        a12.setInventoryTime(LocalDateTime.of(2022, 9, 16, 3, 10, 2));
//        allPaperInventoryAmount.add(a12);
//        PaperInventoryAmount a22 = new PaperInventoryAmount();
//        a22.setWarehouseId("1");
//        a22.setMaterialId(2);
//        a22.setRanking(2);
//        a22.setInventoryTime(LocalDateTime.of(2022, 9, 16, 3, 10, 2));
//        allPaperInventoryAmount.add(a22);
//        PaperInventoryAmount a32 = new PaperInventoryAmount();
//        a32.setWarehouseId("1");
//        a32.setMaterialId(3);
//        a32.setRanking(2);
//        a32.setInventoryTime(LocalDateTime.of(2022, 9, 16, 3, 10, 2));
//        allPaperInventoryAmount.add(a32);
//
//        PaperInventoryAmount a13 = new PaperInventoryAmount();
//        a13.setWarehouseId("2");
//        a13.setMaterialId(1);
//        a13.setRanking(1);
//        a13.setInventoryTime(LocalDateTime.of(2022, 9, 16, 15, 10, 2));
//        allPaperInventoryAmount.add(a13);
//        PaperInventoryAmount a23 = new PaperInventoryAmount();
//        a23.setWarehouseId("2");
//        a23.setMaterialId(2);
//        a23.setRanking(1);
//        a23.setInventoryTime(LocalDateTime.of(2022, 9, 16, 15, 10, 2));
//        allPaperInventoryAmount.add(a23);
//        PaperInventoryAmount a33 = new PaperInventoryAmount();
//        a33.setWarehouseId("2");
//        a33.setMaterialId(3);
//        a33.setRanking(1);
//        a33.setInventoryTime(LocalDateTime.of(2022, 9, 16, 15, 10, 2));
//        allPaperInventoryAmount.add(a33);
//        PaperInventoryAmount a123 = new PaperInventoryAmount();
//        a123.setWarehouseId("2");
//        a123.setMaterialId(1);
//        a123.setRanking(2);
//        a123.setInventoryTime(LocalDateTime.of(2022, 9, 16, 7, 10, 2));
//        allPaperInventoryAmount.add(a123);
//        PaperInventoryAmount a223 = new PaperInventoryAmount();
//        a223.setWarehouseId("2");
//        a223.setMaterialId(2);
//        a223.setRanking(2);
//        a223.setInventoryTime(LocalDateTime.of(2022, 9, 16, 7, 10, 2));
//        allPaperInventoryAmount.add(a223);
//        PaperInventoryAmount a323 = new PaperInventoryAmount();
//        a323.setWarehouseId("2");
//        a323.setMaterialId(4);
//        a323.setRanking(2);
//        a323.setInventoryTime(LocalDateTime.of(2022, 9, 16, 7, 10, 2));
//        allPaperInventoryAmount.add(a323);
//
//
//        PaperInventoryAmount a1234 = new PaperInventoryAmount();
//        a1234.setWarehouseId("2");
//        a1234.setMaterialId(1);
//        a1234.setRanking(3);
//        a1234.setInventoryTime(LocalDateTime.of(2022, 9, 16, 4, 10, 2));
//        allPaperInventoryAmount.add(a1234);
//        PaperInventoryAmount a2234 = new PaperInventoryAmount();
//        a2234.setWarehouseId("2");
//        a2234.setMaterialId(2);
//        a2234.setRanking(3);
//        a2234.setInventoryTime(LocalDateTime.of(2022, 9, 16, 4, 10, 2));
//        allPaperInventoryAmount.add(a2234);
//        PaperInventoryAmount a3234 = new PaperInventoryAmount();
//        a3234.setWarehouseId("2");
//        a3234.setMaterialId(5);
//        a3234.setRanking(3);
//        a3234.setInventoryTime(LocalDateTime.of(2022, 9, 16, 4, 10, 2));
//        allPaperInventoryAmount.add(a3234);
//
//        List<PaperInventoryMaterialTime> materialTimes = Lists.newArrayList();
//        Set<Integer> collect = allPaperInventoryAmount.stream().map(PaperInventoryAmount::getMaterialId).collect(toSet());
//        getAllMaterialTime(1, collect.size(), allPaperInventoryAmount, materialTimes);
//        System.out.println();
//
//    }


    @Override
    public RawMaterialPaperInventoryTableVO queryRawMaterialPaperInventoryDetail(List<String> warehouseIds) {
        RawMaterialPaperInventoryTableVO tableVO = new RawMaterialPaperInventoryTableVO();
        if (CollectionUtil.isEmpty(warehouseIds)) {
            return tableVO;
        }
        // 项目集合
        List<Integer> projectIds = getProjectIdConfig();
        if (CollectionUtil.isEmpty(projectIds)) {
            return tableVO;
        }
        // 最近2期盘存
        List<PaperInventoryAmount> paperInventoryAmount = stockInfoService.getPaperInventoryAmount(projectIds, warehouseIds, 2);
        // 有数据则表示有本期盘点
        if (CollectionUtil.isNotEmpty(paperInventoryAmount)) {
            List<RawMaterialPaperInventoryDetailVO> list = Lists.newArrayList();
            // 材料ID
            List<Integer> materialIds = paperInventoryAmount.stream().map(PaperInventoryAmount::getMaterialId).distinct().collect(toList());
            // 入库单关联收料data表
            List<String> refDataIds = Lists.newArrayList();
            // 1. 本期盘点最晚时间(ranking=1)
            Optional<PaperInventoryAmount> currentAny = paperInventoryAmount.stream().filter(item -> item.getRanking() == 1).max(Comparator.comparing(PaperInventoryAmount::getInventoryTime));
            LocalDateTime lastInventoryTime = currentAny.get().getInventoryTime();
            tableVO.setEndTime(DateUtil.parseLocalDateTime(lastInventoryTime, DateUtil.DD_DATE_TIME_FORMATTER));
            // 2. 本期盘存
            Map<Integer, Double> currentInventoryMap = paperInventoryAmount.stream().filter(item -> item.getRanking() == 1).collect(
                    groupingBy(PaperInventoryAmount::getMaterialId, collectingAndThen(toList(),
                            l -> l.stream().collect(summingDouble(amount ->
                                    Optional.ofNullable(amount.getInventoryAmount()).orElse(BigDecimal.ZERO).doubleValue())))));
            // 3. 上期盘点最早时间(ranking=2)
            LocalDateTime firstInventoryTime = null;
            Map<Integer, Double> lastInventoryMap = new HashMap<>();
            Optional<PaperInventoryAmount> firstAny = paperInventoryAmount.stream().filter(item -> item.getRanking() == 2).min(Comparator.comparing(PaperInventoryAmount::getInventoryTime));
            if (firstAny.isPresent()) {
                firstInventoryTime = firstAny.get().getInventoryTime();
                tableVO.setStartTime(DateUtil.parseLocalDateTime(firstInventoryTime, DateUtil.DD_DATE_TIME_FORMATTER));
                // 4. 上期盘存
                lastInventoryMap = paperInventoryAmount.stream().filter(item -> item.getRanking() == 2).collect(
                        groupingBy(PaperInventoryAmount::getMaterialId, collectingAndThen(toList(),
                                l -> l.stream().collect(summingDouble(amount ->
                                        Optional.ofNullable(amount.getInventoryAmount()).orElse(BigDecimal.ZERO).doubleValue())))));
            }
            // 收料累计、站外支出查询参数
            List<EnterLeaveWarehouseParam> params = buildEnterLeaveAmountParams(paperInventoryAmount);
            // 4. 收料累计/站外支出数据
            Map<Integer, Double> enterDataMap = new HashMap<>();
            Map<Integer, Double> outDataMap = new HashMap<>();
            getEnterLeaveAmount(params, refDataIds, enterDataMap, outDataMap);
            // 5. 账面实耗
            Map<Integer, BigDecimal> paperCostMap = getPaperCost(
                    ObjectUtil.isNull(firstInventoryTime) ? UTC_TIME : DateUtil.parseLocalDateTime(firstInventoryTime, DateUtil.SS_DATE_TIME_FORMATTER),
                    DateUtil.parseLocalDateTime(lastInventoryTime, DateUtil.SS_DATE_TIME_FORMATTER), projectIds, materialIds);
            // 6. 扣量
            Map<Integer, BigDecimal> plantDeductMap = getPlantDeduct(refDataIds);
            // 品种/规格
            List<Integer> materialIdList = new ArrayList<>(currentInventoryMap.keySet());
            Map<Integer, String> materialSpecMap = materialServiceProxy.materialSpec(new HashSet<>(materialIdList));
            // 汇总
            for(Map.Entry<Integer, Double> entry : currentInventoryMap.entrySet()) {
                RawMaterialPaperInventoryDetailVO vo = new RawMaterialPaperInventoryDetailVO();
                Integer materialId = entry.getKey();
                // 材料
                vo.setMaterialId(materialId);
                String materialSpec = materialSpecMap.get(materialId);
                // 品种
                vo.setVariety(materialSpec.replaceFirst("/.*$", ""));
                // 规格
                vo.setSpecification(materialSpec.replaceFirst(".*?/", ""));
                // 收料累计
                Optional.ofNullable(enterDataMap.get(materialId)).ifPresent(v -> vo.setAllReceive(BigDecimal.valueOf(v)));
                // 站外支出
                Optional.ofNullable(outDataMap.get(materialId)).ifPresent(v -> vo.setExpenditure(BigDecimal.valueOf(v)));
                // 上期盘存
                Optional.ofNullable(lastInventoryMap.get(materialId)).ifPresent(v -> vo.setPriorInventory(BigDecimal.valueOf(v)));
                // 本期盘存
                Optional.ofNullable(currentInventoryMap.get(materialId)).ifPresent(v -> vo.setCurrentInventory(BigDecimal.valueOf(v)));
                // 账面实耗
                Optional.ofNullable(paperCostMap.get(materialId)).ifPresent(v -> vo.setPaperConsumption(v));
                // 扣量
                Optional.ofNullable(plantDeductMap.get(materialId)).ifPresent(v -> vo.setTakeOut(BigDecimal.valueOf(MathUtil.parsePoint(v.doubleValue(), 2))));
                list.add(vo);
            }
            tableVO.setList(list);
        }
        return tableVO;
    }

    @Override
    public Map<String, List<PlantOverviewOutboundVO>> queryOverviewOutbound() {
        Map<String, List<PlantOverviewOutboundVO>> map = new HashMap<>();
        // 项目集合
        List<Integer> projectIds = getOverviewProjectId();
        if (CollectionUtil.isEmpty(projectIds)) {
            return map;
        }

        List<StatisticsOverviewPlantExitDTO> exitDTOList = Optional.ofNullable(plantMapper.queryOverviewExitCount(projectIds)).orElse(Lists.newArrayList());
        exitDTOList = exitDTOList.stream().filter(item -> ObjectUtil.isNotNull(item.getMaterialId()) && StringUtils.isNotBlank(item.getUnit())).collect(toList());
        if (CollectionUtil.isEmpty(exitDTOList)) {
            return map;
        }
        // 品种/规格
        Set<Integer> materialIds = exitDTOList.stream().map(StatisticsOverviewPlantExitDTO::getMaterialId).collect(toSet());
        Map<Integer, String> materialSpecMap = materialServiceProxy.materialSpec(materialIds);

        map = exitDTOList.stream().collect(groupingBy(StatisticsOverviewPlantExitDTO::getUnit, collectingAndThen(toList(), l -> l.stream().map(item -> {
            Integer materialId = item.getMaterialId();
            PlantOverviewOutboundVO vo = new PlantOverviewOutboundVO();
            vo.setMaterialId(materialId);
            vo.setMaterialSpec(materialSpecMap.get(materialId));
            vo.setYesterdayOutbound(item.getYesterdayCount());
            vo.setCurrentMonthOutbound(item.getCurrentMonthCount());
            vo.setCurrentYearOutbound(item.getCurrentYearCount());
            return vo;
        }).collect(toList()))));

        return map;
    }

    @Override
    public List<PlantOverviewCapacityMaterialVO> queryOverviewCapacityMaterialAndUnit() {
        List<PlantOverviewCapacityMaterialVO> list = Lists.newArrayList();
        // 项目集合
        List<Integer> projectIds = getOverviewProjectId();
        if (CollectionUtil.isEmpty(projectIds)) {
            return list;
        }
        List<StatisticsPlantDTO> plantDTOS = Lists.newArrayList();
        // 所有时间的计划需求量
        List<StatisticsPlantDTO> plantPlanList = Optional.ofNullable(plantMapper.queryPlanCount(projectIds, null, null)).orElse(Lists.newArrayList());
        // 所有时间的出站量
        List<StatisticsPlantDTO> plantExitList = Optional.ofNullable(plantMapper.queryExitCount(projectIds, null, null)).orElse(Lists.newArrayList());

        Optional.ofNullable(plantPlanList).ifPresent(l -> plantDTOS.addAll(l.stream().filter(item ->
                ObjectUtil.isNotNull(item.getMaterialId()) && StringUtils.isNotBlank(item.getUnit())).collect(toList())));
        Optional.ofNullable(plantExitList).ifPresent(l -> plantDTOS.addAll(l.stream().filter(item ->
                ObjectUtil.isNotNull(item.getMaterialId()) && StringUtils.isNotBlank(item.getUnit())).collect(toList())));

        if (CollectionUtil.isEmpty(plantDTOS)) {
            return list;
        }
        // 材料
        Set<Integer> materialIds = plantDTOS.stream().map(StatisticsPlantDTO::getMaterialId).collect(toSet());
        List<MaterialDto> materialDtos = materialServiceProxy.listMaterialByIds(materialIds);
        Map<Integer, String> materialMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, MaterialDto::getMaterialName));
        // 单位
        Map<Integer, Set<String>> unitMap = plantDTOS.stream().collect(groupingBy(StatisticsPlantDTO::getMaterialId,
                collectingAndThen(toList(), l -> l.stream().map(StatisticsPlantDTO::getUnit).collect(toSet()))));

        materialIds.stream().forEach(materialId -> {
            PlantOverviewCapacityMaterialVO vo = new PlantOverviewCapacityMaterialVO();
            vo.setMaterialId(materialId);
            vo.setMaterialName(materialMap.get(materialId));
            vo.setUnits(unitMap.get(materialId));
            list.add(vo);
        });
        return list;
    }

    @Override
    public StatisticsPlantHistoryVO queryOverviewCapacity(Integer materialId, String unit) {
        StatisticsPlantHistoryVO vo = new StatisticsPlantHistoryVO();
        // 项目集合
        List<Integer> projectIds = getOverviewProjectId();
        if (CollectionUtil.isEmpty(projectIds)) {
            return vo;
        }
        List<StatisticsPlantDTO> plantDTOS = Lists.newArrayList();
        // 所有时间的计划需求量
        List<StatisticsPlantDTO> plantPlanList = Optional.ofNullable(plantMapper.queryPlanCount(projectIds, unit, Arrays.asList(materialId))).orElse(Lists.newArrayList());
        // 所有时间的出站量
        List<StatisticsPlantDTO> plantExitList = Optional.ofNullable(plantMapper.queryExitCount(projectIds, unit, Arrays.asList(materialId))).orElse(Lists.newArrayList());

        Optional.ofNullable(plantPlanList).ifPresent(l -> plantDTOS.addAll(l));
        Optional.ofNullable(plantExitList).ifPresent(l -> plantDTOS.addAll(l));

        if (CollectionUtil.isEmpty(plantDTOS)) {
            return vo;
        }
        // 昨日计划需求量及出站量
        Map<Integer, Double> yesterdayPlanCountProjectMap = planCountProjectMap(DateUtil.yesterdayStr(), plantPlanList);
        Map<Integer, Double> yesterdayExitCountProjectMap = exitCountProjectMap(DateUtil.yesterdayStr(), plantExitList);
        // 昨日计划需求量 - 出站量（合并后的项目列表）
        Map<Integer, Double> yesterdayResidueProjectMap = residueProjectMap(yesterdayPlanCountProjectMap, yesterdayExitCountProjectMap);

        // 今日计划需求量及出站量
        Map<Integer, Double> todayPlanCountProjectMap = planCountProjectMap(DateUtil.todayStr(), plantPlanList);
        Map<Integer, Double> todayExitCountProjectMap = exitCountProjectMap(DateUtil.todayStr(), plantExitList);
        // 今日配单量
        Map<Integer, Double> todayProductionCountProjectMap = productionCountProjectMap(DateUtil.todayStr(), plantPlanList);

        // 截止昨日的计划需求量及出站量
        Map<String, Double> cutoffYesterdayPlanCountProjectMap = cutoffSomedayPlanCountProjectMap(DateUtil.yesterdayStr(), plantPlanList);
        Map<String, Double> cutoffYesterdayExitCountProjectMap = cutoffSomedayExitCountProjectMap(DateUtil.yesterdayStr(), plantExitList);
        // 昨日的昨日剩余量（累加截止昨日的每天【计划需求量 - 出站量】）
        Map<Integer, Double> cutoffYesterdayResidueProjectMap = cutoffSomedayResidueProjectMap(cutoffYesterdayPlanCountProjectMap, cutoffYesterdayExitCountProjectMap);

        // 今日总产能需求完成情况表格
        List<StatisticsPlantHistoryVO> todayTable = Lists.newArrayList();
        projectIds.forEach(projectId -> {
            StatisticsPlantHistoryVO statisticsPlantHistoryVO = new StatisticsPlantHistoryVO();
            // 计划需求量
            BigDecimal plantPlanCount = BigDecimal.valueOf(todayPlanCountProjectMap.getOrDefault(projectId, 0D));
            statisticsPlantHistoryVO.setPlanDemand(plantPlanCount);
            // 配单量
            BigDecimal plantProductionCount = BigDecimal.valueOf(todayProductionCountProjectMap.getOrDefault(projectId, 0D));
            statisticsPlantHistoryVO.setProduction(plantProductionCount);
            // 出站量
            BigDecimal exitCount = BigDecimal.valueOf(todayExitCountProjectMap.getOrDefault(projectId, 0D));
            statisticsPlantHistoryVO.setExit(exitCount);
            // 昨日计划需求量 - 昨日出站量
            BigDecimal yesterdayResidue = BigDecimal.valueOf(yesterdayResidueProjectMap.getOrDefault(projectId, 0D));
            // 昨日的昨日剩余量
            BigDecimal theDayBeforeYesterdayResidueCount = BigDecimal.valueOf(cutoffYesterdayResidueProjectMap.getOrDefault(projectId, 0D));
            // 昨日剩余量 = 昨日的昨日剩余量 + (昨日计划需求量 - 昨日出站量)
            BigDecimal yesterdayResidueCount = theDayBeforeYesterdayResidueCount.add(yesterdayResidue);
            statisticsPlantHistoryVO.setYesterdayResidue(yesterdayResidueCount);
            todayTable.add(statisticsPlantHistoryVO);
        });
        vo = StatisticsPlantVO.getStatisticsPlantHistoryVO(todayTable);

        return vo;
    }

    @Override
    public List<MixingPlantMachineVO> queryMachineList(List<Integer> materialIds, String unit) {
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
        Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();
        List<Integer> projectIds = getProjectIdConfig();
        List<String> machineIds = Lists.newArrayList();
        if (projectId == null) {
            machineIds = plantMapper.queryMachineList(materialIds, projectIds, unit);
        } else {
            machineIds = plantMapper.queryMachineList(materialIds, Collections.singletonList(projectId), unit);
        }
        List<MixingPlantMachine> mixingPlantMachineList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(machineIds)) {
             mixingPlantMachineList = plantMachineService.listByIds(machineIds);
        }
        List<MixingPlantMachineVO> res = mixingPlantMachineList.stream().map(mixingPlantMachine -> {
            MixingPlantMachineVO mixingPlantMachineVO = new MixingPlantMachineVO();
            BeanUtils.copyProperties(mixingPlantMachine, mixingPlantMachineVO);
            return mixingPlantMachineVO;
        }).collect(toList());
        return res;
    }

    /**
     * 收料累计、站外支出查询参数
     * @param paperInventoryAmount
     * @return
     */
    private List<EnterLeaveWarehouseParam> buildEnterLeaveAmountParams(List<PaperInventoryAmount> paperInventoryAmount) {
        Map<String, Pair<LocalDateTime, LocalDateTime>> warehouseRangeTimeMap = paperInventoryAmount.stream().collect(groupingBy(PaperInventoryAmount::getWarehouseId, collectingAndThen(toList(), l -> {
            Optional<PaperInventoryAmount> min = l.stream().min(Comparator.comparing(PaperInventoryAmount::getInventoryTime));
            Optional<PaperInventoryAmount> max = l.stream().max(Comparator.comparing(PaperInventoryAmount::getInventoryTime));
            // 当前仓库上期盘点时间～本期盘点时间
            return Pair.of(min.get().getInventoryTime(), max.get().getInventoryTime());
        })));
        List<EnterLeaveWarehouseParam> params = Lists.newArrayList();
        warehouseRangeTimeMap.forEach((warehouseId, rangTime) -> {
            LocalDateTime left = rangTime.getLeft();
            LocalDateTime right = rangTime.getRight();
            EnterLeaveWarehouseParam param = new EnterLeaveWarehouseParam();
            param.setWarehouseId(warehouseId);
            // 相等表示当前仓库只有一次盘点，则周期起始时间取UTC起始时间
            param.setStartTime(left.compareTo(right) == 0 ? DateUtil.parseLocalDateTime(UTC_TIME) : left);
            param.setEndTime(right);
            params.add(param);
        });
        return params;
    }

    /**
     * 所有收料累计、站外支出查询参数
     * @param paperInventoryAmount
     * @return
     */
    private List<EnterLeaveWarehouseParam> buildAllEnterLeaveAmountParams(List<PaperInventoryAmount> paperInventoryAmount) {
        Map<String, LocalDateTime> warehouseRangeTimeMap = paperInventoryAmount.stream().collect(groupingBy(PaperInventoryAmount::getWarehouseId, collectingAndThen(toList(), l -> {
            Optional<PaperInventoryAmount> max = l.stream().max(Comparator.comparing(PaperInventoryAmount::getInventoryTime));
            // 当前仓库本期盘点时间
            return max.get().getInventoryTime();
        })));
        List<EnterLeaveWarehouseParam> params = Lists.newArrayList();
        warehouseRangeTimeMap.forEach((warehouseId, maxTime) -> {
            EnterLeaveWarehouseParam param = new EnterLeaveWarehouseParam();
            param.setWarehouseId(warehouseId);
            param.setStartTime(LOCAL_UTC_TIME);
            param.setEndTime(maxTime);
            params.add(param);
        });
        return params;
    }

    /**
     * 收料累计、站外支出
     * @param params
     * @param refDataIds
     * @param enterDataMap
     * @param outDataMap
     */
    private void getEnterLeaveAmount(List<EnterLeaveWarehouseParam> params, List<String> refDataIds, Map<Integer, Double> enterDataMap, Map<Integer, Double> outDataMap) {
        List<EnterLeaveAmount> enterLeaveAmount = stockInfoService.getWarehouseEnterLeaveAmount(params);
        if (CollectionUtil.isNotEmpty(enterLeaveAmount)) {
            List<String> dataIds = enterLeaveAmount.stream().filter(item -> StringUtils.isNotBlank(item.getEnterWarehouseId()))
                    .map(EnterLeaveAmount::getEnterMaterialDataId).collect(toList());
            if (CollectionUtil.isNotEmpty(dataIds)) {
                dataIds.forEach(item -> IfBranchUtil.isTrue(ObjectUtil.isNotNull(item) && !refDataIds.contains(item)).trueHandle(() -> refDataIds.add(item)));
            }
            // 收料累计
            Map<Integer, Double> enterMap = enterLeaveAmount.stream().filter(item -> StringUtils.isNotBlank(item.getEnterWarehouseId())).collect(
                    groupingBy(EnterLeaveAmount::getEnterMaterialId, collectingAndThen(toList(), l ->
                            l.stream().collect(summingDouble(item ->
                                    Optional.ofNullable(item.getEnterMaterialNum()).orElse(BigDecimal.ZERO).doubleValue())))));
            if (CollectionUtil.isNotEmpty(enterMap)) {
                enterMap.forEach((k, v) -> {
                    enterDataMap.put(k, v);
                });
            }
            // 站外支出
            Map<Integer, Double> outMap = enterLeaveAmount.stream().filter(item -> StringUtils.isNotBlank(item.getOutWarehouseId())).collect(
                    groupingBy(EnterLeaveAmount::getOutMaterialId, collectingAndThen(toList(), l ->
                            l.stream().collect(summingDouble(item ->
                                    Optional.ofNullable(item.getOutMaterialNum()).orElse(BigDecimal.ZERO).doubleValue())))));
            if (CollectionUtil.isNotEmpty(outMap)) {
                outMap.forEach((k, v) -> {
                    outDataMap.put(k, v);
                });
            }
        }
    }

    /**
     * 账面实耗
     * @param startTime
     * @param endTime
     * @param projectIds
     * @param materialIds
     * @return
     */
    private Map<Integer, BigDecimal> getPaperCost(String startTime, String endTime, List<Integer> projectIds, List<Integer> materialIds) {
        Map<Integer, BigDecimal> paperCostMap = new HashMap<>();
        List<RawMaterialPaperInventoryDTO> paperInventoryDTOS = plantMapper.queryRawMaterialPaperInventory(startTime, endTime, projectIds, materialIds);
        if (CollectionUtil.isNotEmpty(paperInventoryDTOS)) {
            paperCostMap = paperInventoryDTOS.stream().collect(toMap(RawMaterialPaperInventoryDTO::getMaterialId, RawMaterialPaperInventoryDTO::getAllConsumption));
        }
        return paperCostMap;
    }

    /**
     * 扣量
     * @param refDataIds
     * @return
     */
    private Map<Integer, BigDecimal> getPlantDeduct(List<String> refDataIds) {
        Map<Integer, BigDecimal> plantDeductMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(refDataIds)) {
            List<PlantDeductDTO> plantDeductDTOS = materialDataMapper.queryPlantDeduct(refDataIds);
            if (CollectionUtil.isNotEmpty(plantDeductDTOS)) {
                plantDeductMap = plantDeductDTOS.stream().collect(toMap(PlantDeductDTO::getMaterialId, PlantDeductDTO::getWeightDeduct));
            }
        }
        return plantDeductMap;
    }

    /**
     * 获取总览统计项目范围
     * @return
     */
    private List<Integer> getOverviewProjectId() {
        AuthUser currentUser = siteContextHolder.getCurrentUser();
        Integer currentCompanyId = currentUser.getCurrentCompanyId();
        Integer currentDepartmentId = currentUser.getCurrentDepartmentId();
        Integer currentProjectId = currentUser.getCurrentProjectId();
        List<Integer> projectIds = Lists.newArrayList();
        // 项目层
        if (ObjectUtil.isNotNull(currentProjectId)) {
            projectIds = Arrays.asList(currentProjectId);
        } else {
            // 当前企业下所有项目
            List<ProjectVO> allProjectsByCompanyId = projectServiceProxy.getAllProjectsByCompanyDeptId(currentCompanyId, currentDepartmentId);
            // 开通拌合站的项目
            List<Integer> plateIds = projectServiceProxy.getPluginProjectIds(ProjectServiceProxy.PLATE_PLUGIN_NO);
            // 过滤出拌合站项目
            allProjectsByCompanyId = allProjectsByCompanyId.stream().filter(item -> plateIds.contains(item.getProjectId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(allProjectsByCompanyId)) {
                projectIds = allProjectsByCompanyId.stream().map(ProjectVO::getProjectId).collect(toList());
            }
        }
        return projectIds;
    }

    /**
     * 获取项目范围配置
     * @return
     */
    private List<Integer> getProjectIdConfig() {
        AuthUser currentUser = siteContextHolder.getCurrentUser();
        Integer currentProjectId = currentUser.getCurrentProjectId();
        List<Integer> projectIds = Lists.newArrayList();
        // 项目层
        if (ObjectUtil.isNotNull(currentProjectId)) {
            projectIds = Arrays.asList(currentProjectId);
        } else {
            // 企业层
            // 配置的项目范围
            PlantProjectConfig plantProjectConfig = plantProjectConfigService.queryOneConfig();
            if (ObjectUtil.isNotNull(plantProjectConfig)) {
                String belongProjectId = plantProjectConfig.getBelongProjectId();
                if (StringUtils.isNotBlank(belongProjectId)) {
                    projectIds = Arrays.stream(belongProjectId.split(",")).map(Integer::parseInt).collect(Collectors.toList());
                }
            }
        }
        return projectIds;
    }

    /**
     * 截止日期【计划需求量 - 出站量】累加
     *  同一个项目同一天在两个Map中都得存在，不存在需要补全
     * @param planProjectMap：key: projectId-yyyy-MM-dd
     * @param exitProjectMap：key: projectId-yyyy-MM-dd
     * @return
     */
    private Map<Integer, Double> cutoffSomedayResidueProjectMap(Map<String, Double> planProjectMap, Map<String, Double> exitProjectMap) {
        Map<Integer, Double> residueMap = new HashMap<>();
        if (CollectionUtil.isEmpty(planProjectMap) && CollectionUtil.isEmpty(exitProjectMap)) {
            return residueMap;
        }
        List<String> keys = Lists.newArrayList();
        Set<String> planProjectAndTimeSet = planProjectMap.keySet();
        Set<String> exitProjectAndTimeSet = exitProjectMap.keySet();
        keys.addAll(planProjectAndTimeSet);
        keys.addAll(exitProjectAndTimeSet);
        keys = keys.stream().distinct().collect(Collectors.toList());;

        for (String projectAndTime : keys) {
            double projectAndTimeResidue = MathUtil.parsePoint(planProjectMap.getOrDefault(projectAndTime, 0D) -
                    exitProjectMap.getOrDefault(projectAndTime, 0D), 2);
            // 累加相同项目ID的值
            Integer projectId = Integer.parseInt(projectAndTime.replaceFirst("-\\d{4}-\\d{2}-\\d{2}$", ""));
            residueMap.put(projectId, residueMap.getOrDefault(projectId, 0D) + projectAndTimeResidue);
        }
        return residueMap;
    }

    /**
     * 截止日期计划需求量
     * @param time
     * @param plantPlanList
     * @return
     */
    private Map<String, Double> cutoffSomedayPlanCountProjectMap(String time, List<StatisticsPlantDTO> plantPlanList) {
        if (CollectionUtil.isEmpty(plantPlanList)) {
            return new HashMap<>();
        }
        Map<String, Double> planCountProjectMap = plantPlanList.stream().filter(item ->
                        DateUtil.parseLocalDate(item.getReceiveTime()).isBefore(DateUtil.parseLocalDate(time)))
                .collect(Collectors.groupingBy(plantPlan -> plantPlan.getProjectId() + "-" + plantPlan.getReceiveTime(),
                        Collectors.collectingAndThen(Collectors.toList(), list ->
                                list.stream().collect(Collectors.summingDouble(l ->
                                        Optional.ofNullable(l.getPlanCount()).orElse(BigDecimal.ZERO).doubleValue())))));
        return planCountProjectMap;
    }

    /**
     * 截止日期出站量
     * @param time
     * @param plantExitList
     * @return
     */
    private Map<String, Double> cutoffSomedayExitCountProjectMap(String time, List<StatisticsPlantDTO> plantExitList) {
        if (CollectionUtil.isEmpty(plantExitList)) {
            return new HashMap<>();
        }
        Map<String, Double> planCountProjectMap = plantExitList.stream().filter(item ->
                        DateUtil.parseLocalDate(item.getExitTime()).isBefore(DateUtil.parseLocalDate(time)))
                .collect(Collectors.groupingBy(plantExit -> plantExit.getProjectId() + "-" + plantExit.getExitTime(),
                        Collectors.collectingAndThen(Collectors.toList(), list ->
                                list.stream().collect(Collectors.summingDouble(l ->
                                        Optional.ofNullable(l.getExitCount()).orElse(BigDecimal.ZERO).doubleValue())))));
        return planCountProjectMap;
    }

    /**
     * 指定日期计划需求量 - 出站量
     *  同一个项目在两个Map中都得存在，不存在需要补全
     * @param planProjectMap
     * @param exitProjectMap
     * @return
     */
    private Map<Integer, Double> residueProjectMap(Map<Integer, Double> planProjectMap, Map<Integer, Double> exitProjectMap) {
        Map<Integer, Double> residueMap = new HashMap<>();
        Set<Integer> planProjectSet = Optional.ofNullable(planProjectMap).orElse(new HashMap<>()).keySet();
        Set<Integer> exitProjectSet = Optional.ofNullable(exitProjectMap).orElse(new HashMap<>()).keySet();
        List<Integer> projectIdList = Lists.newArrayList();
        projectIdList.addAll(planProjectSet);
        projectIdList.addAll(exitProjectSet);
        projectIdList = projectIdList.stream().distinct().collect(Collectors.toList());
        for (Integer projectId : projectIdList) {
            residueMap.put(projectId, MathUtil.parsePoint(planProjectMap.getOrDefault(projectId, 0D) -
                    exitProjectMap.getOrDefault(projectId, 0D), 2));
        }
        return residueMap;
    }

    /**
     * 指定日期计划需求量
     * @param time
     * @param plantPlanList
     * @return
     */
    private Map<Integer, Double> planCountProjectMap(String time, List<StatisticsPlantDTO> plantPlanList) {
        if (CollectionUtil.isEmpty(plantPlanList)) {
            return new HashMap<>();
        }
        Map<Integer, Double> planCountProjectMap = plantPlanList.stream().filter(item -> time.equals(item.getReceiveTime()))
                .collect(Collectors.groupingBy(StatisticsPlantDTO::getProjectId, Collectors.collectingAndThen(Collectors.toList(),
                        list -> list.stream().collect(Collectors.summingDouble(l ->
                                Optional.ofNullable(l.getPlanCount()).orElse(BigDecimal.ZERO).doubleValue())))));
        return planCountProjectMap;
    }

    /**
     * 指定日期配单量
     * @param time
     * @param plantPlanList
     * @return
     */
    private Map<Integer, Double> productionCountProjectMap(String time, List<StatisticsPlantDTO> plantPlanList) {
        if (CollectionUtil.isEmpty(plantPlanList)) {
            return new HashMap<>();
        }
        Map<Integer, Double> productionCountProjectMap = new HashMap<>();
        List<StatisticsPlantDTO> planList = plantPlanList.stream().filter(item -> time.equals(item.getReceiveTime())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(planList)) {
            List<String> detailIds = planList.stream().map(StatisticsPlantDTO::getDetailId).collect(Collectors.toList());
            QueryWrapper<IngredientList> wrapper = new QueryWrapper<>();
            wrapper.lambda().in(IngredientList::getStatus, Arrays.asList(IngredientListEnum.TWO.value(), IngredientListEnum.THREE.value(), IngredientListEnum.FOUR.value()))
                    .in(IngredientList::getMixingPlantOrderDetailId, detailIds);
            // 获取配料单
            List<IngredientList> ingredientList = Optional.ofNullable(ingredientListMapper.selectList(wrapper)).orElse(Lists.newArrayList());
            productionCountProjectMap = planList.stream()
                    .filter(item -> ingredientList.stream().anyMatch(ingredient -> ingredient.getMixingPlantOrderDetailId().equals(item.getDetailId())))
                    .collect(Collectors.groupingBy(StatisticsPlantDTO::getProjectId, Collectors.collectingAndThen(Collectors.toList(),
                            list -> list.stream().collect(Collectors.summingDouble(l ->
                                    Optional.ofNullable(l.getPlanCount()).orElse(BigDecimal.ZERO).doubleValue())))));
        }
        return productionCountProjectMap;
    }

    /**
     * 指定日期出站量
     * @param time
     * @param plantExitList
     * @return
     */
    private Map<Integer, Double> exitCountProjectMap(String time, List<StatisticsPlantDTO> plantExitList) {
        if (CollectionUtil.isEmpty(plantExitList)) {
            return new HashMap<>();
        }
        Map<Integer, Double> planCountProjectMap = plantExitList.stream().filter(item -> time.equals(item.getExitTime()))
                .collect(Collectors.groupingBy(StatisticsPlantDTO::getProjectId, Collectors.collectingAndThen(Collectors.toList(),
                        list -> list.stream().collect(Collectors.summingDouble(l ->
                                Optional.ofNullable(l.getExitCount()).orElse(BigDecimal.ZERO).doubleValue())))));
        return planCountProjectMap;
    }

    private List<StatisticsPlantCategoryVO> plantCategoryVO(Map<Integer, String> categoryMap) {
        List<StatisticsPlantCategoryVO> list = Lists.newArrayList();
        if (CollectionUtil.isEmpty(categoryMap)) {
            return list;
        }
        categoryMap.forEach((categoryId, categoryName) -> {
            StatisticsPlantCategoryVO categoryVO = new StatisticsPlantCategoryVO();
            categoryVO.setCategoryId(categoryId);
            categoryVO.setCategoryName(categoryName);
            list.add(categoryVO);
        });
        return list;
    }

}
