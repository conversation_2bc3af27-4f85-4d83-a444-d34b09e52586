package cn.pinming.microservice.material.management;

import cn.pinming.core.actuator.probe.ProbeServlet;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.net.UnknownHostException;

/**
 * 总启动类.
 */
@SpringBootApplication(scanBasePackages = {"cn.pinming.microservice.material.management", "cn.pinming.material.v2"})
@ServletComponentScan(basePackageClasses = ProbeServlet.class)
@EnableScheduling
@Slf4j
@EnableDubbo(scanBasePackages = {"cn.pinming.microservice.material.management.biz.dubbo"})
public class Application {

    public static void main(String[] args) throws UnknownHostException {
//        System.setProperty("dubbo.application.logger", "slf4j");
        ConfigurableApplicationContext context = SpringApplication.run(Application.class, args);

//        Environment environment = context.getBean(Environment.class);
//        String path = environment.getProperty("server.servlet.context-path");
//        if (StrUtil.isEmpty(path)) {
//            path = "";
//        }
//        log.info("====== 访问地址：http://{}:{}{} ======", InetAddress.getLocalHost().getHostAddress(), environment.getProperty("server.port"), path);
//        log.info("====== swagger访问地址：http://{}:{}{}/doc.html ======", InetAddress.getLocalHost().getHostAddress(), environment.getProperty("server.port"), path);
    }

}
