package cn.pinming.microservice.material.management.config.websocket;

/**
 * 推送信息指令类型，目前有：onOpen（连接建立）、heartBeat（心跳包）、send（消息发送）、update（系统升级）、
 * 
 * view（参数查看）、edit（参数修改）、logView（日志查看）、
 * 
 * 
 * <AUTHOR>
 */
public enum ActionType {

	onOpen("连接建立", (byte) 1), heartBeat("心跳包", (byte) 2), send("消息发送", (byte) 3), update("系统升级", (byte) 4),
	view("参数查看", (byte) 5), edit("参数修改", (byte) 6), logView("日志查看", (byte) 7),;

	// 成员变量
	private String name;
	private byte value;

	// 构造方法
	private ActionType(String name, byte value) {
		this.name = name;
		this.value = value;
	}

	// get set 方法
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public byte getValue() {
		return value;
	}

	public void setValue(byte index) {
		this.value = index;
	}
}
