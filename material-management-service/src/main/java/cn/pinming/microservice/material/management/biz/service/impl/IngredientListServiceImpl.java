package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.*;
import cn.pinming.microservice.material.management.biz.enums.IngredientListEnum;
import cn.pinming.microservice.material.management.biz.enums.NoticeStatusEnum;
import cn.pinming.microservice.material.management.biz.form.*;
import cn.pinming.microservice.material.management.biz.mapper.*;
import cn.pinming.microservice.material.management.biz.query.IngredientQuery;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.IfBranchUtil;
import cn.pinming.microservice.material.management.common.util.NoUtil;
import cn.pinming.microservice.material.management.proxy.FileServiceProxy;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material_unit.api.material.dto.InfoItem;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialCategoryDto;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.material_unit.api.material.dto.SelectItem;
import cn.pinming.microservice.material_unit.api.material.service.MaterialService;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.mysql.cj.protocol.x.Notice;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.xpath.operations.Bool;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Struct;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 拌合站配料单 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 09:54:01
 */
@Service
public class IngredientListServiceImpl extends ServiceImpl<IngredientListMapper, IngredientList> implements IIngredientListService {

    private static final String WATER = "水";

    @Resource
    private NoUtil noUtil;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private IIngredientApplyService iIngredientApplyService;
    @Resource
    private IngredientApplyMapper ingredientApplyMapper;
    @Resource
    private IIngredientApplyDetailService iIngredientApplyDetailService;
    @Resource
    private IngredientApplyDetailMapper ingredientApplyDetailMapper;
    @Resource
    private IngredientNoticeMapper ingredientNoticeMapper;
    @Resource
    private IIngredientNoticeService iIngredientNoticeService;
    @Resource
    private IIngredientNoticeDetailService iIngredientNoticeDetailService;
    @Resource
    private IngredientNoticeDetailMapper ingredientNoticeDetailMapper;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private MaterialFinishedBomMapper materialFinishedBomMapper;
    @Resource
    private IMaterialFinishedBomService materialFinishedBomService;
    @Resource
    private MaterialFinishedBomServiceImpl materialFinishedBomServiceImpl;
    @Reference
    private MaterialService materialService;
    @Resource
    private IPurchaseOrderService purchaseOrderService;
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private IMixingPlantMachineService mixingPlantMachineService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addOrUpdateFirst(IngredientForm ingredientForm) {
        List<InfoItemVO> infoItemList = ingredientForm.getApplyForm().getInfoItemList();
        List<MaterialReportForm> materialReportFormList = ingredientForm.getApplyForm().getMaterialReportFormList();

        if (StrUtil.isBlank(ingredientForm.getId()) || StrUtil.isNotBlank(ingredientForm.getId()) && StrUtil.isBlank(ingredientForm.getApplyForm().getId())) {
            // 新增
            Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();

            // 判断材料是否有BOM包
            Boolean hasBOM = this.check(ingredientForm.getCategoryId(),null);
            // 判断订单明细是否生成过配料单
            if (StrUtil.isBlank(ingredientForm.getId())) {
                Boolean judge = this.judge(ingredientForm.getMixingPlantOrderDetailId());
                if (judge) {
                    throw new BOException(BOExceptionEnum.INGREDIENT_HAS_CREATED);
                }
            }
            if (!hasBOM) {
                throw new BOException(BOExceptionEnum.BOM_HAS_NOT_CREATED);
            }
            // 时间判断
            Boolean timeJudge = ingredientForm.getApplyForm().getApplyDate().isAfter(ingredientForm.getApplyForm().getUseDate());
            if (timeJudge) {
                throw new BOException(BOExceptionEnum.APPLYDATE_CAN_NOT_AFTER_USEDATE);
            }

            // 保存配料单
            IngredientList ingredientList = new IngredientList();
            BeanUtil.copyProperties(ingredientForm,ingredientList);
            if (StrUtil.isBlank(ingredientForm.getId())) {ingredientList.setNo(noUtil.getIngredientListKeyPrefix(projectId));}
            ingredientList.setStatus(IngredientListEnum.TWO.value());

            this.saveOrUpdate(ingredientList);

            // 保存申请单
            IngredientApply ingredientApply = new IngredientApply();
            BeanUtil.copyProperties(ingredientForm.getApplyForm(),ingredientApply);
            ingredientApply.setIngredientListId(ingredientList.getId());
            if (CollUtil.isNotEmpty(infoItemList)) {
                String str = JSON.toJSON(infoItemList).toString();
                ingredientApply.setParameterRequirements(str);
            }

            iIngredientApplyService.save(ingredientApply);

            // 保存申请单明细
            this.saveApplyDetail(ingredientForm,ingredientApply);

            return ingredientList.getId();
        }else {
            // 编辑
            // 判断是否已归档
            Boolean isFile = this.isFile(ingredientForm.getId());
            if (isFile) {throw new BOException(BOExceptionEnum.INGREDIENT_IS_Filed);}

            int size = ingredientApplyMapper.size(ingredientForm.getId());

            // 编辑申请单
            IngredientApply ingredientApply = new IngredientApply();
            ingredientApply.setId(ingredientForm.getApplyForm().getId());
            ingredientApply.setNo(ingredientForm.getApplyForm().getNo());
            ingredientApply.setPosition(ingredientForm.getApplyForm().getPosition());
            ingredientApply.setApplyDate(ingredientForm.getApplyForm().getApplyDate());
            ingredientApply.setUseDate(ingredientForm.getApplyForm().getUseDate());
            if (CollUtil.isNotEmpty(infoItemList)) {
                String str = JSON.toJSON(infoItemList).toString();
                ingredientApply.setParameterRequirements(str);
            }

            iIngredientApplyService.updateById(ingredientApply);

            // 编辑申请单明细
            // 已经关联申请单的BOM包只能新增，没有删除，size只会更大
            if (materialReportFormList.size() == size) {
                // BOM包没有新增二级材料，原有数据上更新
                // 且不影响后面的通知单和确认单
                List<IngredientApplyDetail> ingredientApplyDetailList = ingredientForm.getApplyForm().getMaterialReportFormList().stream().map(e -> {
                    IngredientApplyDetail ingredientApplyDetail = new IngredientApplyDetail();
                    BeanUtil.copyProperties(e,ingredientApplyDetail);

                    return ingredientApplyDetail;
                }).collect(Collectors.toList());

                iIngredientApplyDetailService.updateBatchById(ingredientApplyDetailList);
            } else {
                // BOM包新增二级材料，先删后增
                iIngredientApplyDetailService.lambdaUpdate()
                        .eq(IngredientApplyDetail::getIngredientApplyId,ingredientApply.getId())
                        .set(IngredientApplyDetail::getIsDeleted,1)
                        .update();

                ingredientForm.getApplyForm().getMaterialReportFormList().stream().forEach(e -> {
                    e.setId(null);
                });
                // 保存申请单明细
                this.saveApplyDetail(ingredientForm,ingredientApply);

                // 判断是否有通知单
                Boolean flag = this.noticeFinished(ingredientForm.getId());
                if (flag) {
                    // 有,联动更新通知单明细表
                    IngredientNotice ingredientNotice = ingredientNoticeMapper.confirmFinished(ingredientForm.getId(),1);

                    // 根据申请单明细里的材料id匹配通知单明细表里的材料id，id存在的不动，id不存在的，新增，赋值0
                    List<NoticeMaterialSimpleDTO> dbMaterialDtoList = ingredientNoticeDetailMapper.selectMaterialIdList(ingredientNotice.getId());
                    List<Integer> dbMaterialIdList = dbMaterialDtoList.stream().map(NoticeMaterialSimpleDTO::getMaterialId).collect(Collectors.toList());
                    List<Integer> formMaterialIdList = ingredientForm.getApplyForm().getMaterialReportFormList().stream().map(MaterialReportForm::getMaterialId).collect(Collectors.toList());

                    List<Integer> addMaterialIdList = formMaterialIdList.stream().filter(e -> !dbMaterialIdList.contains(e)).collect(Collectors.toList());
                    List<IngredientNoticeDetail> addNoticeDetailList = addMaterialIdList.stream().map(e -> {
                        IngredientNoticeDetail ingredientNoticeDetail = new IngredientNoticeDetail();
                        ingredientNoticeDetail.setIngredientNoticeId(ingredientNotice.getId());
                        ingredientNoticeDetail.setMaterialId(e);
                        ingredientNoticeDetail.setUnit(dbMaterialDtoList.get(0).getUnit());
                        ingredientNoticeDetail.setUnitUsage(BigDecimal.ZERO);
                        ingredientNoticeDetail.setPerUsage(BigDecimal.ZERO);

                        return ingredientNoticeDetail;
                    }).collect(Collectors.toList());

                    iIngredientNoticeDetailService.saveBatch(addNoticeDetailList);

                    // 判断是否有确认单
                    Boolean sign = this.confirmFinished(ingredientForm.getId());
                    if (sign) {
                        // 有，联动更新确认单明细表
                        IngredientNotice ingredientConfirm = ingredientNoticeMapper.confirmFinished(ingredientForm.getId(),2);

                        List<IngredientNoticeDetail> addConfirmDetailList = addMaterialIdList.stream().map(e -> {
                            IngredientNoticeDetail ingredientNoticeDetail = new IngredientNoticeDetail();
                            ingredientNoticeDetail.setIngredientNoticeId(ingredientConfirm.getId());
                            ingredientNoticeDetail.setMaterialId(e);
                            ingredientNoticeDetail.setUnit(dbMaterialDtoList.get(0).getUnit());
                            ingredientNoticeDetail.setUnitUsage(BigDecimal.ZERO);
                            ingredientNoticeDetail.setPerUsage(BigDecimal.ZERO);
                            ingredientNoticeDetail.setPerUsageResult(BigDecimal.ZERO);
                            ingredientNoticeDetail.setUnitUsageResult(BigDecimal.ZERO);

                            return ingredientNoticeDetail;
                        }).collect(Collectors.toList());

                        iIngredientNoticeDetailService.saveBatch(addConfirmDetailList);
                    }
                }
            }
            return ingredientForm.getId();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addOrUpdateSecond(NoticeForm form) {
        if (StrUtil.isBlank(form.getId())) {
            // 新增
            // 判断申请单是否完成
            Boolean flag1 = this.applyFinished(form.getIngredientListId());
            if (!flag1) {throw new BOException(BOExceptionEnum.APPLY_IS_FIRST);}
            // 判断通知单是否已创建
            Boolean flag2 = this.noticeFinished(form.getIngredientListId());
            if (flag2) {throw new BOException(BOExceptionEnum.NOTICE_HAS_CREATED);}

            // 保存通知单
            IngredientNotice ingredientNotice = new IngredientNotice();
            BeanUtil.copyProperties(form,ingredientNotice);
            if (CollUtil.isNotEmpty(form.getFileList())) {
                List<String> fileList = form.getFileList().stream().map(FileListForm::getUuid).distinct().collect(Collectors.toList());
                fileServiceProxy.confirmFiles(fileList, "material-management");

                String fileObj = JSON.toJSON(form.getFileList()).toString();
                ingredientNotice.setEnclosure(fileObj);
            }

            iIngredientNoticeService.save(ingredientNotice);

            // 保存通知单明细
            List<IngredientNoticeDetail> collect = form.getList().stream().map(e -> {
                IngredientNoticeDetail ingredientNoticeDetail = new IngredientNoticeDetail();
                BeanUtil.copyProperties(e, ingredientNoticeDetail);
                ingredientNoticeDetail.setIngredientNoticeId(ingredientNotice.getId());

                return ingredientNoticeDetail;
            }).collect(Collectors.toList());

            iIngredientNoticeDetailService.saveBatch(collect);

            // 变更配料单状态 "目标配合比待录入" -> "生产配合比待调整"
            this.changeStatus(form.getIngredientListId(),IngredientListEnum.THREE.value());
        }else {
            // 编辑
            // 判断是否已归档
            Boolean isFile = this.isFile(form.getIngredientListId());
            if (isFile) {throw new BOException(BOExceptionEnum.INGREDIENT_IS_Filed);}

            IngredientNotice ingredientNotice = new IngredientNotice();
            BeanUtil.copyProperties(form,ingredientNotice);
            if (CollUtil.isNotEmpty(form.getFileList())) {
                List<String> fileList = form.getFileList().stream().map(FileListForm::getUuid).distinct().collect(Collectors.toList());
                fileServiceProxy.confirmFiles(fileList, "material-management");

                String fileObj = JSON.toJSON(form.getFileList()).toString();
                ingredientNotice.setEnclosure(fileObj);
            }

            iIngredientNoticeService.updateById(ingredientNotice);

            // 更新通知单明细
            List<IngredientNoticeDetail> addNoticeDetailList = form.getList().stream().map(e -> {
                IngredientNoticeDetail ingredientNoticeDetail = new IngredientNoticeDetail();
                BeanUtil.copyProperties(e, ingredientNoticeDetail);

                return ingredientNoticeDetail;
            }).collect(Collectors.toList());

            iIngredientNoticeDetailService.updateBatchById(addNoticeDetailList);

            // 判断是否有确认单
            Boolean flag = this.confirmFinished(form.getIngredientListId());
            if (flag) {
                // 有 根据材料id更新冗余值和确认值 含水率不变
                Map<Integer, IngredientNoticeDetail> map = addNoticeDetailList.stream().collect(Collectors.toMap(IngredientNoticeDetail::getMaterialId, e -> e));
                Map<String, IngredientNoticeDetail> waterMap = addNoticeDetailList.stream().filter(item -> StrUtil.isNotBlank(item.getMaterialName())).collect(Collectors.toMap(IngredientNoticeDetail::getMaterialName, e -> e));

                IngredientNotice ingredientConfirm = ingredientNoticeMapper.confirmFinished(form.getIngredientListId(), 2);
                List<IngredientNoticeDetail> dbConfirmDetailList = ingredientNoticeDetailMapper.selectConfirmDetail(ingredientConfirm.getId());
                dbConfirmDetailList.stream().forEach(e -> {
                    if (StrUtil.isBlank(e.getMaterialName())) {
                        // 更新"用量"冗余值
                        e.setUnitUsage(map.get(e.getMaterialId()).getUnitUsage());
                        e.setPerUsage(map.get(e.getMaterialId()).getPerUsage());

                        // 重新计算"用量"确认值
                        BigDecimal unitUsageResult = this.unitUsageResult(e);
                        e.setUnitUsageResult(unitUsageResult);
                    }
                });

                List<BigDecimal> unitUsageList = dbConfirmDetailList.stream().filter(item -> StrUtil.isBlank(item.getMaterialName())).map(e -> {
                    BigDecimal item = NumberUtil.mul(e.getUnitUsageResult(), NumberUtil.div(e.getMoistureContent(), 100));
                    return item;
                }).collect(Collectors.toList());
                BigDecimal unitUsage = unitUsageList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);

                dbConfirmDetailList.stream().forEach(e -> {
                    if (StrUtil.isNotBlank(e.getMaterialName())) {
                        // 更新 水
                        e.setUnitUsage(waterMap.get(e.getMaterialName()).getUnitUsage());
                        e.setPerUsage(waterMap.get(e.getMaterialName()).getPerUsage());

                        BigDecimal unitUsageResult = this.waterUnitUsageResult(e,unitUsage);
                        e.setUnitUsageResult(unitUsageResult);
                    }
                });

                BigDecimal unitTotal = dbConfirmDetailList.stream().map(e -> e.getUnitUsageResult()).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal requirement = this.getBaseMapper().selectRequirement(form.getIngredientListId());
                MixingPlantMachine mixingPlantMachine = mixingPlantMachineService.lambdaQuery()
                        .select(MixingPlantMachine::getPerMaxCapacity)
                        .eq(MixingPlantMachine::getId, form.getMachineId())
                        .one();
                if (ObjectUtil.isNull(mixingPlantMachine)) {
                    throw new BOException(BOExceptionEnum.MACHINE_IS_DELETED);
                }
                if (ObjectUtil.isNull(mixingPlantMachine.getPerMaxCapacity()) || mixingPlantMachine.getPerMaxCapacity().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new BOException(BOExceptionEnum.MACHINE_CAPACITY_IS_NULL);
                }
                BigDecimal perOfStandard = NumberUtil.mul(requirement, unitTotal).divide(mixingPlantMachine.getPerMaxCapacity(),0,BigDecimal.ROUND_DOWN);
                // 到这没问题
                if (perOfStandard.compareTo(BigDecimal.ZERO) != 0) {
                    // 标准盘数 >= 1
                    dbConfirmDetailList.stream().forEach(e -> {
                        BigDecimal result = NumberUtil.mul(mixingPlantMachine.getPerMaxCapacity(),NumberUtil.div(e.getUnitUsageResult(),unitTotal)).setScale(3,BigDecimal.ROUND_HALF_UP);
                        e.setPerUsageResult(result);
                    });
                }else {
                    // 标准盘数 = 0
                    // 各材料尾盘每盘用量
                    dbConfirmDetailList.stream().forEach(e -> {
                        e.setPerUsageResult(BigDecimal.ZERO);
                        BigDecimal result = NumberUtil.mul(requirement,e.getUnitUsage()).setScale(3,BigDecimal.ROUND_HALF_UP);
                        e.setTailPerUsage(result);
                    });
                }

                // 标准盘数 标准盘数总量 尾盘总量
                if (perOfStandard.compareTo(BigDecimal.ZERO) != 0) {
                    // 标准盘数 >= 1
                    ingredientConfirm.setStandardCoils(perOfStandard.intValue());
                    BigDecimal perUsageResultTotal = dbConfirmDetailList.stream().map(e -> e.getPerUsageResult()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    ingredientConfirm.setStandardCoilsTotal(NumberUtil.mul(perUsageResultTotal,perOfStandard).setScale(3,BigDecimal.ROUND_HALF_UP));

                    // 尾盘量 = 下单量 * ∑(各材料每结算单位用量) - 标准盘数 * ∑(各材料每盘用量)
                    ingredientConfirm.setTailCoilsTotal(NumberUtil.sub(NumberUtil.mul(requirement,unitTotal),ingredientConfirm.getStandardCoilsTotal()));

                    // 各材料尾盘每盘用量 = 下单量 * 对应材料每结算单位用量 - 标准盘数 * 对应材料每盘用量
                    dbConfirmDetailList.stream().forEach(e -> {
                        e.setTailPerUsage(NumberUtil.sub(NumberUtil.mul(requirement,e.getUnitUsageResult()),NumberUtil.mul(perOfStandard,e.getPerUsageResult())));
                    });
                } else {
                    // 标准盘数 = 0
                    ingredientConfirm.setStandardCoils(BigDecimal.ZERO.intValue());
                    ingredientConfirm.setStandardCoilsTotal(BigDecimal.ZERO);

                    // 尾盘量 = ∑(各材料每盘用量)
                    BigDecimal perUsageResultTotal = dbConfirmDetailList.stream().map(e -> e.getTailPerUsage()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    ingredientConfirm.setTailCoilsTotal(perUsageResultTotal);
                }
                iIngredientNoticeService.updateById(ingredientConfirm);

                iIngredientNoticeDetailService.updateBatchById(dbConfirmDetailList);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdateThird(NoticeConfirmForm form) {
        if (form.getStandardCoils() != 0) {
            int size = form.getList().stream().filter(e -> ObjectUtil.isNotNull(e.getPerUsageResult())).collect(Collectors.toList()).size();
            if (form.getList().size() != size) {
                throw new BOException(BOExceptionEnum.PERUSAGERESULT_CAN_NOT_BE_NULL);
            }
        }

        if (StrUtil.isBlank(form.getId())) {
            // 新增
            // 判断通知单是否完成
            Boolean flag1 = this.noticeFinished(form.getIngredientListId());
            if (!flag1) {throw new BOException(BOExceptionEnum.NOTICE_IS_FIRST);}
            Boolean flag2 = this.confirmFinished(form.getIngredientListId());
            if (flag2) {throw new BOException(BOExceptionEnum.CONFIRM_HAS_CREATED);}

            IngredientNotice ingredientNotice = new IngredientNotice();
            BeanUtil.copyProperties(form,ingredientNotice);
            ingredientNotice.setStatus(NoticeStatusEnum.YES.value());

            iIngredientNoticeService.save(ingredientNotice);

            List<IngredientNoticeDetail> collect = form.getList().stream().map(e -> {
                IngredientNoticeDetail ingredientNoticeDetail = new IngredientNoticeDetail();
                BeanUtil.copyProperties(e, ingredientNoticeDetail);
                ingredientNoticeDetail.setIngredientNoticeId(ingredientNotice.getId());

                return ingredientNoticeDetail;
            }).collect(Collectors.toList());

            iIngredientNoticeDetailService.saveBatch(collect);
        }else {
            // 编辑
            // 判断是否已归档
            Boolean isFile = this.isFile(form.getIngredientListId());
            if (isFile) {throw new BOException(BOExceptionEnum.INGREDIENT_IS_Filed);}

            IngredientNotice ingredientNotice = new IngredientNotice();
            BeanUtil.copyProperties(form,ingredientNotice);
            iIngredientNoticeService.updateById(ingredientNotice);

            // 这一步只会更改含水率,同步更新确认量
            // 前端只要传确认单明细id，含水率，每单位用量，每盘用量
            List<IngredientNoticeDetail> collect = form.getList().stream().map(e -> {
                IngredientNoticeDetail ingredientNoticeDetail = new IngredientNoticeDetail();
                BeanUtil.copyProperties(e, ingredientNoticeDetail);

                return ingredientNoticeDetail;
            }).collect(Collectors.toList());

            iIngredientNoticeDetailService.updateBatchById(collect);
        }
    }

    @Override
    public void file(String id) {
        Boolean flag1 = this.applyFinished(id);
        Boolean flag2 = this.noticeFinished(id);
        Boolean flag3 = this.confirmFinished(id);
        Boolean flag4 = this.isFile(id);
        if (!flag1) {throw new BOException(BOExceptionEnum.APPLY_HAS_NOT_CREATED);}
        if (!flag2) {throw new BOException(BOExceptionEnum.NOTICE_HAS_NOT_CREATED);}
        if (!flag3) {throw new BOException(BOExceptionEnum.CONFIRM_HAS_NOT_CREATED);}
        if (flag4) {throw new BOException(BOExceptionEnum.INGREDIENT_IS_Filed);}

        this.changeStatus(id,IngredientListEnum.FOUR.value());
    }

    @Override
    public IngredientListVO detail(String id) {
        IngredientListVO vo = new IngredientListVO();
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
        Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();
        Map<Integer, MaterialDto> materialDtoMap = new HashMap<>();
        List<InfoItemVO> infoItemVOS = new ArrayList<>();
        List<InfoItemVO> infoItemResult = new ArrayList<>();

        // 配料单vo
        vo = this.getBaseMapper().selectIngredientListVO(id);
        SimpleConstructionProjectDto simpleProject = projectServiceProxy.findSimpleProject(vo.getProjectId());
        MaterialDto materialDto = materialServiceProxy.materialById(vo.getMaterialId());

        // 申请单vo
        ApplyVO applyVO = new ApplyVO();
        if (ObjectUtil.isNotNull(simpleProject)) {
            applyVO.setCompanyTitle(simpleProject.getSubCompanyName());
            applyVO.setProjectTitle(simpleProject.getProjectTitle());
        }
        if (ObjectUtil.isNotNull(materialDto)) {
            applyVO.setMaterial(StrUtil.format("{}/{}",materialDto.getMaterialName(),materialDto.getMaterialSpec()));
        }
        
        // 判断有无申请单
        Boolean applyFinished = this.applyFinished(id);
        if (applyFinished) {
            // 渲染库中数据
            ApplyVO dbApplyVO = ingredientApplyMapper.selectApplyVO(id);
            applyVO.setId(dbApplyVO.getId());
            applyVO.setNo(dbApplyVO.getNo());
            applyVO.setApplyDate(dbApplyVO.getApplyDate());
            applyVO.setUseDate(dbApplyVO.getUseDate());

            // 返回"参数要求"中是材料字典的"可选框"
            infoItemVOS = JSONObject.parseArray(dbApplyVO.getParameterRequirements(), InfoItemVO.class);

            if (ObjectUtil.isNotNull(infoItemVOS)) {
                List<InfoItemVO> dictionaryVOS = infoItemVOS.stream().filter(e -> e.getKind() == 2 && e.getParamType().equals("2")).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(dictionaryVOS)) {
                    Map<String, InfoItem> map = this.dictionary(vo.getCategoryId());

                    Map<String, InfoItem> finalMap = map;
                    dictionaryVOS.stream().forEach(e -> {
                        if (CollUtil.isNotEmpty(finalMap)) {
                            e.setSelectItem(finalMap.get(e.getParamCode()).getSelectItem());
                        }
                    });
                }
            }

            if (StrUtil.isNotBlank(dbApplyVO.getPosition())) {vo.setPosition(dbApplyVO.getPosition());}

            // 库中材料 (有水的话包括水)
            List<MaterialReportVO> applyDetailVOList = ingredientApplyDetailMapper.selectApplyDetailVO(dbApplyVO.getId());
            List<Integer> applyDetailMaterialList = applyDetailVOList.stream().filter(item -> item.getMaterialId() != null).map(MaterialReportVO::getMaterialId).collect(Collectors.toList());
            List<MaterialDto> materialDtos = materialServiceProxy.listMaterialByIds(applyDetailMaterialList);
            if (CollUtil.isNotEmpty(materialDtos)) {
                materialDtoMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, e -> e));
            }

            if (vo.getStatus() != IngredientListEnum.FOUR.value()) {
                // 有无"水"不会变化
                // 新BOM包"材料"与老数据兼容
                List<String> applyDetailMaterial = new ArrayList<>();
                if (CollUtil.isNotEmpty(materialDtos)) {
                    applyDetailMaterial = materialDtos.stream().map(e -> {
                        String str = StrUtil.format("{}/{}", e.getMaterialCategoryId(), e.getMaterialName());

                        return str;
                    }).collect(Collectors.toList());
                }

                // BOM材料
                MaterialFinishedBom bom = materialFinishedBomMapper.selectMaterialList(vo.getCategoryId(),companyId,projectId);
                List<String> bomMaterial = StrUtil.split(bom.getCategoryMaterials(), ",");

                if (CollUtil.isEmpty(applyDetailMaterial) || bomMaterial.size() > applyDetailMaterial.size()) {
                    Map<Integer, MaterialCategoryDto> materialCategoryMap = new HashMap<>();
                    List<String> finalApplyDetailMaterial = applyDetailMaterial;
                    List<String> collect = bomMaterial;

                    if (CollUtil.isNotEmpty(finalApplyDetailMaterial)){
                        collect = bomMaterial.stream().filter(item -> !finalApplyDetailMaterial.contains(item)).collect(Collectors.toList());
                    }

                    List<Integer> categoryIdList = collect.stream().map(item -> Integer.valueOf(Arrays.stream(item.split("/")).reduce((first, second) -> first).orElse(null))).distinct().collect(Collectors.toList());
                    List<MaterialCategoryDto> materialCategoryDtos = materialService.listCategoryByIds(companyId, categoryIdList);
                    if (CollUtil.isNotEmpty(materialCategoryDtos)) {
                        materialCategoryMap = materialCategoryDtos.stream().filter(e -> e.getEnable() == true).collect(Collectors.toMap(MaterialCategoryDto::getMaterialCategoryId, e -> e));
                    }

                    Map<Integer, MaterialCategoryDto> finalMaterialCategoryMap = materialCategoryMap;
                    collect.stream().forEach(item -> {
                        MaterialReportVO materialReportVO = new MaterialReportVO();

                        String a = Arrays.stream(item.split("/")).reduce((first, second) -> first).orElse(null);
                        String b = Arrays.stream(item.split("/")).reduce((first, second) -> second).orElse(null);
                        if (CollUtil.isNotEmpty(finalMaterialCategoryMap)) {
                            MaterialCategoryDto materialCategoryDto = finalMaterialCategoryMap.get(Integer.valueOf(a));
                            if (ObjectUtil.isNotNull(materialCategoryDto)) {
                                materialReportVO.setMaterialCategoryId(Integer.valueOf(a));
                                materialReportVO.setMaterialCategoryName(materialCategoryDto.getMaterialCategoryName());
                                materialReportVO.setMaterialName(b);

                                applyDetailVOList.add(materialReportVO);
                            }
                        }
                    });
                }

                // 新BOM包"技术要求"与老数据兼容    ps：材料字典的参数要求如果变化我就不管了
                if (StrUtil.isNotBlank(bom.getOtherParameter())) {
                    List<String> bomRequirement = StrUtil.split(bom.getOtherParameter(), ",");

                    // 库里的参数要求不为空
                    if (StrUtil.isNotBlank(dbApplyVO.getParameterRequirements())){
                        Map<String, String> dbRequirementMap = infoItemVOS.stream().filter(e -> e.getKind() == 1 && e.getParamValue() != null).collect(Collectors.toMap(InfoItem::getParamName, InfoItem::getParamValue));
                        infoItemVOS.removeIf(e -> e.getKind() == 1);

                        List<InfoItemVO> collect = bomRequirement.stream().map(e -> {
                            InfoItemVO infoItemVO = new InfoItemVO();

                            infoItemVO.setParamName(e);
                            if (CollUtil.isNotEmpty(dbRequirementMap)){
                                infoItemVO.setParamValue(dbRequirementMap.get(e));
                            }
                            infoItemVO.setKind((byte) 1);
                            infoItemVO.setParamType("1");

                            return infoItemVO;
                        }).collect(Collectors.toList());

                        infoItemVOS.addAll(collect);
                    }else {
                        // 库里的技术要求为空
                        List<InfoItemVO> collect = bomRequirement.stream().map(e -> {
                            InfoItemVO infoItemVO = new InfoItemVO();

                            infoItemVO.setParamName(e);
                            infoItemVO.setKind((byte) 1);
                            infoItemVO.setParamType("1");

                            return infoItemVO;
                        }).collect(Collectors.toList());

                        infoItemVOS = collect;
                    }
                }
                // 如果BOM包的技术要求后来变为了空，还是按库里的显示
            }

            Map<Integer, MaterialDto> finalMaterialDtoMap = materialDtoMap;
            applyDetailVOList.stream().filter(item -> item.getMaterialId() != null ).forEach(item -> {
                String materialSpec = finalMaterialDtoMap.get(item.getMaterialId()).getMaterialSpec();
                Integer materialCategoryId = finalMaterialDtoMap.get(item.getMaterialId()).getMaterialCategoryId();
                String materialCategoryName = finalMaterialDtoMap.get(item.getMaterialId()).getMaterialCategoryName();
                String materialName = finalMaterialDtoMap.get(item.getMaterialId()).getMaterialName();
                
                item.setMaterialSpec(materialSpec);
                item.setMaterialCategoryId(materialCategoryId);
                item.setMaterialCategoryName(materialCategoryName);
                item.setMaterialName(materialName);
            });

            applyVO.setInfoItems(infoItemVOS);
            applyVO.setMaterialReportVOList(applyDetailVOList);
        }else {
            // 根据材料找"技术要求"和"材料列表"
            List<MaterialReportVO> applyDetailVOList = new ArrayList<>();

            OrderDetailDTO bom = materialFinishedBomMapper.selectOrderDetail(vo.getMixingPlantOrderDetailId(),companyId,projectId);
            if (ObjectUtil.isNotNull(bom)) {
                // BOM包材料渲染
                Map<Integer, MaterialCategoryDto> materialCategoryMap = new HashMap<>();
                List<String> bomMaterial = StrUtil.split(bom.getCategoryMaterials(), ",");

                List<Integer> categoryIdList = bomMaterial.stream().map(e -> {
                    String a = Arrays.stream(e.split("/")).reduce((first, second) -> first).orElse(null);

                    return Integer.valueOf(a);
                }).collect(Collectors.toList());

                List<MaterialCategoryDto> materialCategoryDtos = materialService.listCategoryByIds(companyId, categoryIdList);
                if (CollUtil.isNotEmpty(materialCategoryDtos)) {
                    materialCategoryMap = materialCategoryDtos.stream().filter(e -> e.getEnable() == true).collect(Collectors.toMap(MaterialCategoryDto::getMaterialCategoryId, e -> e));
                }

                if (CollUtil.isNotEmpty(materialCategoryMap)) {
                    Map<Integer, MaterialCategoryDto> finalMaterialCategoryMap = materialCategoryMap;
                    bomMaterial.stream().forEach(item -> {
                        MaterialReportVO materialReportVO = new MaterialReportVO();

                        String a = Arrays.stream(item.split("/")).reduce((first, second) -> first).orElse(null);
                        String b = Arrays.stream(item.split("/")).reduce((first, second) -> second).orElse(null);
                        if (CollUtil.isNotEmpty(finalMaterialCategoryMap)) {
                            materialReportVO.setMaterialCategoryId(finalMaterialCategoryMap.get(Integer.valueOf(a)).getMaterialCategoryId());
                            materialReportVO.setMaterialCategoryName(finalMaterialCategoryMap.get(Integer.valueOf(a)).getMaterialCategoryName());
                            materialReportVO.setMaterialName(b);
                        }

                        applyDetailVOList.add(materialReportVO);
                    });
                }

                // BOM包有水
                if (bom.getExistWater() == 1) {
                    MaterialReportVO materialReportVO = new MaterialReportVO();
                    materialReportVO.setMaterialName(WATER);
                    applyDetailVOList.add(materialReportVO);
                }

                // BOM包技术要求渲染
                infoItemVOS = materialFinishedBomServiceImpl.parameterRequirements(bom.getCategoryId());
                if (CollUtil.isNotEmpty(infoItemVOS)) {infoItemResult.addAll(infoItemVOS);}

                // 订单明细参数要求回显
                if (StrUtil.isNotBlank(bom.getParameterRequirements())) {
                    Map<String, InfoItem> map = this.dictionary(bom.getCategoryId());
                    List<InfoItemVO> itemVOList = JSONObject.parseArray(bom.getParameterRequirements(), InfoItemVO.class);

                    Map<String, InfoItem> finalMap = map;
                    itemVOList.stream().forEach(e -> {
                        e.setKind((byte)2);
                        e.setParamName(finalMap.get(e.getParamCode()).getParamName());
                        if (e.getParamType().equals("2") && CollUtil.isNotEmpty(finalMap)) {
                            e.setSelectItem(finalMap.get(e.getParamCode()).getSelectItem());
                        }
                    });

                    infoItemResult.addAll(itemVOList);
                }
            }

            applyVO.setInfoItems(infoItemResult);
            applyVO.setMaterialReportVOList(applyDetailVOList);
        }

        // 通知单vo
        // 如果没有创建过通知单，渲染就用申请单的材料
        NoticeVO noticeVO = new NoticeVO();
        Boolean noticeFinished = this.noticeFinished(id);
        if (noticeFinished) {
            IngredientNotice ingredientNotice = ingredientNoticeMapper.confirmFinished(id, 1);
            BeanUtil.copyProperties(ingredientNotice,noticeVO);
            if (StrUtil.isNotBlank(ingredientNotice.getEnclosure())) {
                List<FileListForm> fileListForms = JSONObject.parseArray(ingredientNotice.getEnclosure(), FileListForm.class);
                noticeVO.setFileList(fileListForms);
            }
            if (StrUtil.isNotBlank(ingredientNotice.getMachineId())) {
                MixingPlantMachine mixingPlantMachine = mixingPlantMachineService.lambdaQuery()
                        .select(MixingPlantMachine::getMachineName)
                        .eq(MixingPlantMachine::getId, ingredientNotice.getMachineId())
                        .one();
                if (ObjectUtil.isNotNull(mixingPlantMachine)) {
                    noticeVO.setMachineName(mixingPlantMachine.getMachineName());
                    noticeVO.setMachineId(ingredientNotice.getMachineId());
                }
            }

            List<IngredientNoticeDetail> ingredientNoticeDetails = ingredientNoticeDetailMapper.selectConfirmDetail(ingredientNotice.getId());

            Map<Integer, MaterialDto> finalMaterialDtoMap = materialDtoMap;
            List<MaterialProportioningVO> collect = ingredientNoticeDetails.stream().map(e -> {
                MaterialProportioningVO materialProportioningVO = new MaterialProportioningVO();

                BeanUtil.copyProperties(e, materialProportioningVO);

                if (CollUtil.isNotEmpty(finalMaterialDtoMap) && e.getMaterialId() != null) {
                    materialProportioningVO.setMaterialCategoryId(finalMaterialDtoMap.get(e.getMaterialId()).getMaterialCategoryId());
                    materialProportioningVO.setMaterialCategoryName(finalMaterialDtoMap.get(e.getMaterialId()).getMaterialCategoryName());
                    materialProportioningVO.setMaterialName(finalMaterialDtoMap.get(e.getMaterialId()).getMaterialName());
                    materialProportioningVO.setMaterialSpec(finalMaterialDtoMap.get(e.getMaterialId()).getMaterialSpec());
                }

                return materialProportioningVO;
            }).collect(Collectors.toList());

            noticeVO.setMaterialNoticeVOList(collect);
        }

        // 确认单vo
        // 如果没有创建过确认单，渲染就用申请单的材料
        NoticeConfirmVO noticeConfirmVO = new NoticeConfirmVO();
        Boolean confirmFinished = this.confirmFinished(id);
        if (StrUtil.isNotBlank(noticeVO.getMachineId())) {
            MixingPlantMachine mixingPlantMachine = mixingPlantMachineService.lambdaQuery()
                    .select(MixingPlantMachine::getPerMaxCapacity,MixingPlantMachine::getMachineName)
                    .eq(MixingPlantMachine::getId, noticeVO.getMachineId())
                    .one();
            if (ObjectUtil.isNotNull(mixingPlantMachine)) {
                noticeConfirmVO.setPerMaxCapacity(mixingPlantMachine.getPerMaxCapacity());
                noticeConfirmVO.setMachineName(mixingPlantMachine.getMachineName());
            }
        }
        if (confirmFinished) {
            IngredientNotice ingredientNotice = ingredientNoticeMapper.confirmFinished(id, 2);

            noticeConfirmVO.setId(ingredientNotice.getId());
            noticeConfirmVO.setStandardCoils(ingredientNotice.getStandardCoils());
            noticeConfirmVO.setStandardCoilsTotal(ingredientNotice.getStandardCoilsTotal());
            noticeConfirmVO.setTailCoilsTotal(ingredientNotice.getTailCoilsTotal());

            List<IngredientNoticeDetail> ingredientNoticeDetails = ingredientNoticeDetailMapper.selectConfirmDetail(ingredientNotice.getId());

            Map<Integer, MaterialDto> finalMaterialDtoMap = materialDtoMap;
            List<MaterialProportioningVO> collect = ingredientNoticeDetails.stream().map(e -> {
                MaterialProportioningVO materialProportioningVO = new MaterialProportioningVO();

                BeanUtil.copyProperties(e, materialProportioningVO);

                if (CollUtil.isNotEmpty(finalMaterialDtoMap) && e.getMaterialId() != null) {
                    materialProportioningVO.setMaterialCategoryId(finalMaterialDtoMap.get(e.getMaterialId()).getMaterialCategoryId());
                    materialProportioningVO.setMaterialCategoryName(finalMaterialDtoMap.get(e.getMaterialId()).getMaterialCategoryName());
                    materialProportioningVO.setMaterialName(finalMaterialDtoMap.get(e.getMaterialId()).getMaterialName());
                    materialProportioningVO.setMaterialSpec(finalMaterialDtoMap.get(e.getMaterialId()).getMaterialSpec());
                }

                return materialProportioningVO;
            }).collect(Collectors.toList());

            noticeConfirmVO.setMaterialConfirmVOList(collect);
        }

        vo.setApplyVO(applyVO);
        vo.setNoticeVO(noticeVO);
        vo.setNoticeConfirmVO(noticeConfirmVO);
        return vo;
    }

    @Override
    public IPage<IngredientPageVO> selectPage(IngredientQuery query) {
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
        Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();

        query.setCompanyId(companyId);
        query.setProjectId(projectId);
        IPage<IngredientPageVO> dtoPage = this.getBaseMapper().selectPages(query);
        if (CollUtil.isNotEmpty(dtoPage.getRecords())) {
            Map<Integer, MaterialDto> materialDtoMap = new HashMap<>();
            List<IngredientPageVO> records = dtoPage.getRecords();

            List<Integer> materialIdList = records.stream().map(IngredientPageVO::getMaterialId).distinct().collect(Collectors.toList());
            List<MaterialDto> materialDtos = materialServiceProxy.listMaterialByIds(materialIdList);
            if (CollUtil.isNotEmpty(materialDtos)) {
                materialDtoMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, e -> e));
            }

            Map<Integer, MaterialDto> finalMaterialDtoMap = materialDtoMap;
            records.stream().forEach(e -> {
                if (CollUtil.isNotEmpty(finalMaterialDtoMap) && ObjectUtil.isNotNull(finalMaterialDtoMap.get(e.getMaterialId()))) {
                    String materialName = finalMaterialDtoMap.get(e.getMaterialId()).getMaterialName();
                    String materialSpec = finalMaterialDtoMap.get(e.getMaterialId()).getMaterialSpec();

                    e.setMaterialNameSpec(StrUtil.format("{}/{}",materialName,materialSpec));
                }

                e.setParameterRequirementsDesc(purchaseOrderService.parseParameterRequirements(e.getCategoryId(), e.getParameterRequirements()));
            });
        }

        return dtoPage;
    }

    @Override
    public Map<Byte, Integer> statistics() {
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
        Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();
        Map<Byte, Integer> map = new HashMap<>();

        List<IngredientStatisticsDTO> list = this.getBaseMapper().statistics(companyId,projectId);

        if (CollUtil.isNotEmpty(list)) {
            map = list.stream().collect(Collectors.toMap(IngredientStatisticsDTO::getStatus, IngredientStatisticsDTO::getCount));
            int sum = list.stream().mapToInt(IngredientStatisticsDTO::getCount).sum();
            map.put((byte)9,sum);
        }

        return map;
    }

    @Override
    public Map<String,List<String>> requirement() {
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
        Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();
        List<String> list = this.getBaseMapper().requirement(companyId,projectId);
        Map<String,List<String>> result = new HashMap<>();

        if (CollUtil.isNotEmpty(list)) {
            List<InfoItemVO> requirementList = new ArrayList<>();

            list.stream().forEach(e -> {
                List<InfoItemVO> infoItemVOS = JSONObject.parseArray(e, InfoItemVO.class);

                requirementList.addAll(infoItemVOS);
            });

            Map<String, List<InfoItemVO>> map = requirementList.stream().collect(Collectors.groupingBy(InfoItem::getParamName));
            map.entrySet().stream().forEach(e -> {
                List<String> paramValueList = e.getValue().stream().filter(m -> StrUtil.isNotBlank(m.getParamValue())).map(m -> m.getParamValue()).distinct().collect(Collectors.toList());
                result.put(e.getKey(),paramValueList);
            });
        }

        return result;
    }

    private LoadingCache<Integer, Map<String, InfoItem>> cache1 = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .refreshAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<Integer, Map<String, InfoItem>>() {
                public Map<String,InfoItem> load(Integer categoryId) throws Exception {
                    Map<String, InfoItem> map = new HashMap<>();

                    MaterialCategoryDto dto = materialService.getCategoryDetail(categoryId);
                    if (ObjectUtil.isNotNull(dto) && CollUtil.isNotEmpty(dto.getExtList())) {
                       map = dto.getExtList().stream().filter(e -> !e.getParamType().equals("3")).collect(Collectors.toMap(InfoItem::getParamCode, e -> e));
                    }

                    return map;
                }
            });

    public Map<String, InfoItem> dictionary(Integer categoryId) {
        Map<String, InfoItem> map = new HashMap<>();
        try {
            map = cache1.get(categoryId);
        } catch (Exception e) {
            log.error("parameterRequirements cache load error.", e);
        }
        return map;
    }

    /**
     * 计算每单位确认用量(kg)
     * @return
     */
    private BigDecimal unitUsageResult(IngredientNoticeDetail e) {
        return NumberUtil.div(e.getUnitUsage(),NumberUtil.sub(1,NumberUtil.div(e.getMoistureContent(),100)));
    }

    /**
     * 计算每盘确认用量(kg)
     * @return
     */
    private BigDecimal perUsageResult(IngredientNoticeDetail e) {
        return NumberUtil.div(e.getPerUsage(),NumberUtil.sub(1,NumberUtil.div(e.getMoistureContent(),100)));
    }

    /**
     * 计算每单位确认用量(kg)-水
     * @return
     */
    private BigDecimal waterUnitUsageResult(IngredientNoticeDetail e,BigDecimal unitUsage) {
        return NumberUtil.sub(e.getUnitUsage(),unitUsage);
    }

    /**
     * 计算每盘确认用量(kg)-水
     * @return
     */
    private BigDecimal waterPerUsageResult(IngredientNoticeDetail e,BigDecimal perUsage) {
        return NumberUtil.sub(e.getUnitUsage(),perUsage);
    }

    /**
     * 保存申请单明细
     */
    private void saveApplyDetail(IngredientForm ingredientForm,IngredientApply ingredientApply){
        List<IngredientApplyDetail> ingredientApplyDetailList = ingredientForm.getApplyForm().getMaterialReportFormList().stream().map(e -> {
            IngredientApplyDetail ingredientApplyDetail = new IngredientApplyDetail();
            BeanUtil.copyProperties(e, ingredientApplyDetail);
            ingredientApplyDetail.setIngredientApplyId(ingredientApply.getId());

            return ingredientApplyDetail;
        }).collect(Collectors.toList());

        iIngredientApplyDetailService.saveBatch(ingredientApplyDetailList);
    }

    /**
     * 采购单订单/拌合站订单，自动生成配料单
     */
    public void autoAdd(List<IngredientAutoAddForm> autoAddFormList,Integer plantId) {
        List<IngredientAutoAddForm> list = new ArrayList<>();
        Integer projectId = null;
        if (plantId == null) {
            projectId = authUserHolder.getCurrentUser().getCurrentProjectId();
        }else {projectId = plantId;}

        Integer finalProjectId = projectId;
        autoAddFormList.stream().forEach(e -> {
            // 判断订单明细材料是否有BOM包
            Boolean flag1 = check(e.getCategoryId(), finalProjectId);
            // 判断订单明细是否生成过配料单
            Boolean flag2 = judge(e.getMixingPlantOrderDetailId());
            if(flag1 && !flag2){
                list.add(e);
            }
        });

        if (CollUtil.isNotEmpty(list)) {
            // 自动生成配料单
            List<IngredientList> collect = list.stream().map(e -> {
                IngredientList ingredientList = new IngredientList();
                BeanUtil.copyProperties(e, ingredientList);
                ingredientList.setNo(noUtil.getIngredientListKeyPrefix(finalProjectId));
                ingredientList.setProjectId(finalProjectId);

                return ingredientList;
            }).collect(Collectors.toList());

            this.saveBatch(collect);
        }
    }

    /**
     * 判断材料有无BOM包
     * @return true 存在
     */
    private Boolean check(Integer categoryId,Integer plantId) {
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
        Integer projectId = null;
        if (plantId == null) {
            projectId = authUserHolder.getCurrentUser().getCurrentProjectId();
        }else {projectId = plantId;}

        MaterialFinishedBom materialFinishedBom = materialFinishedBomService.lambdaQuery()
                .eq(MaterialFinishedBom::getCompanyId, companyId)
                .eq(MaterialFinishedBom::getProjectId, projectId)
                .eq(MaterialFinishedBom::getCategoryId, categoryId)
                .one();

        Boolean flag = ObjectUtil.isNotNull(materialFinishedBom) ? true : false;

        return flag;
    }

    /**
     * 判断订单明细有无生成过配料单
     * @return true 生成过
     */
    private Boolean judge(String id) {
        IngredientList ingredientList = this.getBaseMapper().judge(id);

        Boolean flag = ObjectUtil.isNotNull(ingredientList) ? true : false;

        return flag;
    }

    /**
     * 新建通知单-判断申请单是否完成
     * @return true 完成
     */
    private Boolean applyFinished(String ingredientListId) {
        IngredientApply ingredientApply = ingredientApplyMapper.applyFinished(ingredientListId);
        Boolean flag = ObjectUtil.isNotNull(ingredientApply) ? true : false;
        return flag;
    }

    /**
     * 新建确认单-判断通知单是否完成
     * @return true 完成
     */
    private Boolean noticeFinished(String ingredientListId) {
        IngredientNotice ingredientNotice = ingredientNoticeMapper.confirmFinished(ingredientListId,1);
        Boolean flag = ObjectUtil.isNotNull(ingredientNotice) ? true : false;
        return flag;
    }

    /**
     * 判断确认单是否已创建
     * @return true 完成
     */
    private Boolean confirmFinished(String ingredientListId) {
        IngredientNotice ingredientNotice = ingredientNoticeMapper.confirmFinished(ingredientListId,2);
        Boolean flag = ObjectUtil.isNotNull(ingredientNotice) ? true : false;
        return flag;
    }

    /**
     * 是否已归档
     * @return true 已归档
     */
    private Boolean isFile(String id) {
        IngredientList ingredientList = this.getBaseMapper().isFile(id);
        if (ObjectUtil.isNull(ingredientList)) {throw new BOException(BOExceptionEnum.INGREDIENT_IS_DELETED);}
        Boolean flag = ingredientList.getStatus() == IngredientListEnum.FOUR.value() ? true : false;

        return flag;
    }

    /**
     * 配料单状态变更
     */
    private void changeStatus(String id,Byte status) {
        this.lambdaUpdate()
                .eq(IngredientList::getId,id)
                .set(IngredientList::getStatus,status)
                .update();
    }
}
