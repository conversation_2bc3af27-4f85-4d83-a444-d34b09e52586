package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.dto.SimpleTransformDTO;
import cn.pinming.microservice.material.management.biz.entity.MobileReceiveTotal;
import cn.pinming.microservice.material.management.biz.form.MobileReceiveMaterialForm;
import cn.pinming.microservice.material.management.biz.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.biz.vo.CategoryReceiveVO;
import cn.pinming.microservice.material.management.biz.vo.MobileReceiveHistoryVO;
import cn.pinming.microservice.material.management.biz.vo.ReceiveDeviationSummaryVO;
import cn.pinming.microservice.material.management.biz.vo.ReceiveDeviationVO;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 移动收料总计表 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:31:40
 */
public interface IMobileReceiveTotalService extends IService<MobileReceiveTotal> {

    /**
     * 各材料收货记录统计录入
     *
     * @param form,receiveId,totalUuid
     */
    void add(MobileReceiveMaterialForm form, SimpleTransformDTO simpleTransformDTO);

    MobileReceiveHistoryVO history(String totalId);

    /**
     * 移动收料总量
     *
     * @param query
     * @return
     */
    List<CategoryReceiveVO> countMobileReceiveNum(SupplierAnalysisQuery query);

    /**
     * 移动收料 偏差
     *
     * @param query
     * @return
     */
    List<ReceiveDeviationVO> countDeviation(SupplierAnalysisQuery query, @Param("receiveModes") List<Byte> receiveMode);

    /**
     * 移动收料 占比
     *
     * @param query
     * @return
     */
    List<ReceiveDeviationSummaryVO> countDeviationStatus(SupplierAnalysisQuery query, @Param("receiveModes") List<Byte> receiveMode);

    /**
     * 查询收料计量单位
     *
     * @param currentCompanyId
     * @return
     */
    List<String> listUnitByCompanyId(Integer currentCompanyId);
}
