package cn.pinming.microservice.material.management.common.util;

import cn.hutool.core.io.FileUtil;
import org.apache.poi.util.IOUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;

/**
 * This class is for xxxx.
 *
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2022/9/9 16:01
 */
public class DownloadUtil {

    public static void download(File file, String name, HttpServletResponse response) throws IOException {
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(name, "utf-8"));
        ServletOutputStream outputStream = response.getOutputStream();
        IOUtils.copy(FileUtil.getInputStream(file), outputStream);
        response.flushBuffer();
    }

}
