package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.entity.PreTruckReport;
import cn.pinming.microservice.material.management.biz.entity.PreTruckReportDetail;
import cn.pinming.microservice.material.management.biz.entity.TruckReport;
import cn.pinming.microservice.material.management.biz.enums.DeleteEnum;
import cn.pinming.microservice.material.management.biz.enums.PreTruckStatusEnum;
import cn.pinming.microservice.material.management.biz.form.PreReportForm;
import cn.pinming.microservice.material.management.biz.form.PreTruckReportForm;
import cn.pinming.microservice.material.management.biz.mapper.PreTruckReportMapper;
import cn.pinming.microservice.material.management.biz.service.IPreTruckReportDetailService;
import cn.pinming.microservice.material.management.biz.service.IPreTruckReportService;
import cn.pinming.microservice.material.management.biz.service.ITruckReportService;
import cn.pinming.microservice.material.management.common.AppConstant;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 送货车辆预报备 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-11-16 15:07:43
 */
@Service
public class PreTruckReportServiceImpl extends ServiceImpl<PreTruckReportMapper, PreTruckReport> implements IPreTruckReportService {

    @Resource
    private IPreTruckReportDetailService truckReportDetailService;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private ITruckReportService truckReportService;

    private AuthUser getAuthUser() {
        return authUserHolder.getCurrentUser();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateTruck(String preWeighReportId, List<PreTruckReportForm> list, BigDecimal goodTotal, boolean isCommit, Byte processType, Integer companyId, Integer projectId) {
        AuthUser user = authUserHolder.getCurrentUser();
        //车牌去空格,判断车牌是否重复
        List<String> truckNoList = list.parallelStream().map(e -> StrUtil.trim(e.getTruckNo())).distinct().collect(Collectors.toList());
        if (truckNoList.size() != list.size()) {
            throw new BOException(BOExceptionEnum.TRUCK_NO_REPEAT);
        }

        BigDecimal totalCapacity = list.parallelStream().filter(obj -> obj.getCapacity() != null)
                .map(e -> NumberUtil.mul(e.getTimes(), e.getCapacity())).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (NumberUtil.isGreater(totalCapacity, goodTotal)) {//总装货量大于发货总量!
            throw new BOException(BOExceptionEnum.GOOD_TOTAL_GREATER_TOTAL_CAPACITY);
        }

//      判断车辆是否报备，若无则新增车辆报备
        if (isCommit) {
            List<TruckReport> dtos = truckReportService.lambdaQuery()
                    .select(TruckReport::getTruckNo)
                    .eq(TruckReport::getCompanyId, user.getCurrentCompanyId())
                    .eq(TruckReport::getProjectId, user.getCurrentProjectId())
                    .list();
            if (CollUtil.isNotEmpty(dtos)) {
                List<String> truckNos = dtos.stream().map(TruckReport::getTruckNo).collect(Collectors.toList());
                list.forEach(m -> {
                    if (!truckNos.contains(m.getTruckNo())) {
                        TruckReport truckReport = new TruckReport();
                        truckReport.setTruckNo(m.getTruckNo());
                        truckReportService.save(truckReport);
                    }
                });
            }
        }

        //保存数据库
        this.lambdaUpdate()
                .eq(PreTruckReport::getPreWeighReportId, preWeighReportId)
                .set(PreTruckReport::getIsDeleted, DeleteEnum.DELETE.value())
                .update();
        List<PreTruckReport> truckReportList = list.parallelStream().map(obj -> {
            String truckNo = obj.getTruckNo().toUpperCase();
//            if (!Validator.isPlateNumber(truckNo)) {
//                throw new BOException(BOExceptionEnum.TRUCK_NO_ILLEGAL);
//            }

            PreTruckReport truckReport = new PreTruckReport();
            truckReport.setCompanyId(companyId);
            truckReport.setProjectId(projectId);
            truckReport.setPreWeighReportId(preWeighReportId);
            BeanUtils.copyProperties(obj, truckReport);
            truckReport.setTruckNo(truckNo);
            return truckReport;
        }).collect(Collectors.toList());
        this.saveOrUpdateBatch(truckReportList);

        //提交才生成车辆过磅记录
        if (isCommit) {
            if (processType == null) {
                this.checkTruckAnomalyStatus(truckReportList);
            } else {
                //作废现存报备记录，使用本次报备，
                if (processType == 1) {
                    this.cancelPreTruck(truckReportList);
                }
            }
            truckReportDetailService.savePreReportDetailList(preWeighReportId, truckReportList);
        }
    }

    public void cancelPreTruck(List<PreTruckReport> truckReportList) {
        List<String> truckNos = truckReportList.stream().map(PreTruckReport::getTruckNo).collect(Collectors.toList());
        List<String> ids = this.getBaseMapper().selectTruckDetailIdsByTruckNo(truckNos, getAuthUser().getCurrentProjectId());
        if (CollUtil.isNotEmpty(ids)) {
            truckReportDetailService.lambdaUpdate().set(PreTruckReportDetail::getStatus, PreTruckStatusEnum.CANCEL.value()).in(PreTruckReportDetail::getId, ids).update();
        }
    }

    /**
     * 逐一检查车辆在本项目中是否存在“等待已下发到终端”、“等待车辆过磅”状态的车辆过磅记录
     *
     * @param list 车辆信息
     */
    private String checkTruckAnomalyStatus(List<PreTruckReport> list) {
        List<String> truckNoList = list.parallelStream().map(PreTruckReport::getTruckNo).collect(Collectors.toList());
        truckNoList = this.getBaseMapper().selectTruckAnomalStatus(truckNoList, getAuthUser().getCurrentProjectId());
        if (CollUtil.isNotEmpty(truckNoList)) {
            //throw new BOException(AppConstant.ERROR_CODE, message);
            return StrUtil.format(AppConstant.MESSAGE_TEMPLATE, StrUtil.join(",", truckNoList));
        }
        return "";
    }

    @Override
    public void deletePreTruckReport(String id) {
        this.removeById(id);
    }

    @Override
    public String checkTruckNo(PreReportForm form) {
        List<PreTruckReportForm> list = form.getList();
        List<PreTruckReport> truckList = list.stream().filter(e -> StrUtil.isNotBlank(e.getTruckNo())).map(e -> {
            PreTruckReport truckReport = new PreTruckReport();
            truckReport.setTruckNo(StrUtil.trim(e.getTruckNo()).toUpperCase());
            return truckReport;
        }).collect(Collectors.toList());
        return checkTruckAnomalyStatus(truckList);
    }


}
