package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.microservice.material.management.biz.form.MaterialHandlerForm;
import cn.pinming.microservice.material.management.biz.service.IMaterialHandlerService;
import cn.pinming.microservice.material.management.biz.vo.HandlerVO;
import cn.pinming.microservice.material.management.common.SuccessResponse;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 处理人信息 前端控制器
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 16:38:04
 */
@Api(tags = "处理人员信息",  value = "ly")
@RestController
@RequestMapping("/api/handler")
public class MaterialHandlerController {

    @Resource
    private IMaterialHandlerService materialHandlerService;

    @Resource
    private AuthUserHolder authUserHolder;

    @ApiOperation(value = "处理人员列表", response = HandlerVO.class)
    @Log(title = "处理人员列表", businessType = BusinessType.QUERY)
    @PostMapping("/list/{handleType}")
    public ResponseEntity<Response> list(@PathVariable Byte handleType) {
        List<HandlerVO> data = materialHandlerService.listHandler(handleType);
        return ResponseEntity.ok(new SuccessResponse(data));
    }

    @ApiOperation(value = "处理人员列表-处理人下拉框列表", response = HandlerVO.class)
    @Log(title = "企业处理人员列表", businessType = BusinessType.QUERY)
    @PostMapping("/company/list/{handleType}")
    public ResponseEntity<Response> listByCompany(@PathVariable Byte handleType) {
        List<HandlerVO> data = materialHandlerService.listProjectsHandler(handleType);
        return ResponseEntity.ok(new SuccessResponse(data));
    }

    @ApiOperation(value = "添加处理人员")
    @Log(title = "添加处理人员", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseEntity<Response> save(@RequestBody @Validated @Valid MaterialHandlerForm materialHandlerForm) {
        materialHandlerService.saveHandler(materialHandlerForm);
        return ResponseEntity.ok(new SuccessResponse());
    }


    @ApiOperation(value = "是否有权限处理")
    @Log(title = "是否有权限处理", businessType = BusinessType.QUERY)
    @PostMapping("/enable/{handleType}")
    public ResponseEntity<Response> enableHandle(@PathVariable Byte handleType) {
        String id = authUserHolder.getCurrentUser().getId();
        Boolean enable = materialHandlerService.enableHandle(id, handleType);
        return ResponseEntity.ok(new SuccessResponse(enable));
    }
}
