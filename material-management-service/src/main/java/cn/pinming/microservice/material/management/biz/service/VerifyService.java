package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.query.MaterialVerifyQuery;
import cn.pinming.microservice.material.management.biz.vo.*;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2022/3/21
 * @description 对账
 */
public interface VerifyService {

    /**
     * 终端称重记录ID
     */
    Pattern SCAN_CONTENT_PATTERN1 = Pattern.compile("tUuid:(.*?),");

    /**
     * 收料单号
     */
    Pattern SCAN_CONTENT_PATTERN2 = Pattern.compile("sNo:(.*?),");

    /**
     * 企业ID
     */
    Pattern SCAN_CONTENT_PATTERN3 = Pattern.compile("coId:(.*?),");

    /**
     * 项目ID
     */
    Pattern SCAN_CONTENT_PATTERN4 = Pattern.compile("pjId:(.*?),");

    /**
     * 对账批次编号
     * @param projectId 报账项目ID
     * @return
     */
    String verifyNoSequence(Integer projectId);

    /**
     * 新增对账记录
     * @param projectId 项目ID
     * @return
     */
    MaterialVerifyAddVO addVerifyRecord(Integer projectId);

    /**
     * 对账明细
     * @param start         开始时间
     * @param end           结束时间
     * @param supplierId    供应商ID
     * @param contractId    合同ID
     * @param verifyId      对账ID
     * @return
     */
    MaterialVerifyDetailVO verifyDetail(String start, String end, Integer supplierId, String contractId, String verifyId);

    /**
     * 关联对账明细
     * @param supplierId  供应商ID
     * @param contractId  合同ID
     * @param verifyId  对账ID
     * @param reviseIds 收料ID集合
     * @return
     */
    Boolean relationVerifyDetail(String supplierId, String contractId, String verifyId, List<String> reviseIds);

    /**
     * 根据对账ID查询对账收料列表
     * @param verifyId      对账ID
     * @param receiveId     收料ID
     * @return
     */
    MaterialVerifyPageVO verifyReceiveRecords(String verifyId, String receiveId);

    /**
     * 移除对账收料记录
     * @param verifyId  对账ID
     * @param reviseId  收料ID
     * @return
     */
    boolean removeVerifyReceiveRecord(String verifyId, String reviseId);

    /**
     * 查询对账列表
     * @param materialVerifyQuery query
     * @return 对账列表
     */
    IPage<MaterialVerifyVO> listMaterialVerify(MaterialVerifyQuery materialVerifyQuery);

    /**
     * 删除对账
     * @param id 对账id
     */
    void removeMaterialVerifyById(String id);

    /**
     * 归档
     * @param id 对账id
     */
    void materialFile(String id);


    /**
     * 获取excel vo
     * @param id 对账 id
     * @param verifyExcelVO 导入excel的基本信息
     * @return VerifyExcelVO
     */
    List<VerifyExcelDataVO> getVerifyExcelVO(String id, VerifyExcelVO verifyExcelVO);

    /**
     * 扫码枪对账范围判断
     * @param verifyId 对账ID
     * @param contractId 合同ID
     * @param inputVal   输入框内容
     * @param scan       是否为扫码枪内容
     * @return
     */
    List<MaterialVerifyScanVO> scanRangeCheck(String verifyId, String contractId, String inputVal, boolean scan);


    /**
     * 扫码枪对账提交
     * @param supplierId    供应商ID
     * @param contractId    合同ID
     * @param verifyId      对账ID
     * @param reviseIds     收料ID
     * @return
     */
    List<MaterialVerifyScanSubmitVO> scanSubmit(String supplierId, String contractId, String verifyId, List<String> reviseIds);



    /**
     * 对账人列表
     * @return 列表
     */
    List<VerifyPersonVO> listVerifyPerson();


}
