package cn.pinming.microservice.material.management.proxy;

import cn.pinming.microservice.material.management.biz.vo.MaterialCategoryVO;
import cn.pinming.microservice.material.management.biz.vo.MaterialUnitVO;
import cn.pinming.microservice.material_unit.api.beans.MyPage;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialCategoryDto;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import lombok.NonNull;
import org.jetbrains.annotations.NotNull;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2021/9/3 2:15 下午
 */
public interface MaterialServiceProxy {

    List<MaterialUnitVO> unitListByCategoryId(Integer companyId, Integer categoryId);

    List<MaterialUnitVO> unitListByCategoryIds(Integer companyId, List<Integer> categoryId);

    Map<Integer, List<String>> unitMapByCategoryIds(Integer companyId, List<Integer> categoryIds);

    List<MaterialDto> listMaterialByIds(Collection<Integer> materialIds);

    List<MaterialCategoryDto> listCategoryByIds(Integer companyId, Collection<Integer> categoryIds);

    MaterialDto materialById(Integer materialId);

    MaterialDto materialByExtCode(@NonNull String extCode);

    List<MaterialDto> materialByExtCodes(@NonNull Collection<String> extCodes);

    List<MaterialCategoryDto> materialCategoryByExtCodes(@NonNull Collection<String> extCodes);

    /**
     * 通过材料获取品种/规格型号
     * @param materialIds
     * @return
     */
    Map<Integer, String> materialSpec(Set<Integer> materialIds);

    /**
     * 通过材料获取二级分类/品种/规格型号
     * @param materialIds
     * @return
     */
    Map<Integer, String> materialCategorySpec(Set<Integer> materialIds);

    List<MaterialCategoryVO> listCategoryByCompanyId(Integer currentCompanyId);

    List<Integer> getMaterialIdsByCategoryId(Integer companyId, Integer categoryId);

    List<Integer> getMaterialIdsByCategoryIds(Integer companyId, List<Integer> categoryIds);

    MyPage<MaterialDto> listMaterial(@NotNull Integer companyId, Long timestamp, @NotNull Integer pageNum, @NotNull Integer pageSize);

}
