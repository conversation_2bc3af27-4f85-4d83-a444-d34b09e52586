package cn.pinming.microservice.material.management.biz.form;

import cn.pinming.microservice.material.management.config.LocalDateTimeDeserializer;
import cn.pinming.microservice.material.management.config.LocalDateTimeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;


@ApiModel("物料收货数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StandardMaterialDataItemFrom {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "本地称重记录id，MaterialDataItem的主键")
    private String weighId;

    @ApiModelProperty(value = "企业材料id")
    private Integer materialId;

    @ApiModelProperty(value = "外部系统材料id")
    private String clientMaterialId;

    @ApiModelProperty(value = "企业材料编码")
    private String materialCode;

    @ApiModelProperty(value = "材料名称")
    private String materialName;

    @ApiModelProperty(value = "品种/规格型号")
    private String materialSpec;

    @ApiModelProperty(value = "计量单位")
    private String weightUnit;

    @ApiModelProperty(value = "发货数量")
    private BigDecimal weightSend;

    @ApiModelProperty(value = "毛重(吨)")
    private BigDecimal weightGross;

    @ApiModelProperty(value = "皮重(吨)")
    private BigDecimal weightTare;

    @ApiModelProperty(value = "扣重(吨)")
    private BigDecimal weightDeduct;

    @ApiModelProperty(value = "净重(吨)")
    private BigDecimal weightNet;

    @ApiModelProperty(value = "实重(吨)")
    private BigDecimal weightActual;

    @ApiModelProperty(value = "实际数量：实重 * 换算系数")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "换算系数")
    private BigDecimal ratio;

    @ApiModelProperty(value = "实收数量")
    private BigDecimal actualReceiveCount;

    @ApiModelProperty(value = "单价：元/计量单位")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "金额：单价 * 实收数量")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "毛重时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime grossTime;

    @ApiModelProperty(value = "进场图片")
    private List<String> grossPicList;

    @ApiModelProperty(value = "毛重地磅id")
    private String grossPoundId;

    @ApiModelProperty(value = "毛重地磅名称")
    private String grossPoundName;

    @ApiModelProperty(value = "皮重时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime tareTime;

    @ApiModelProperty(value = "出场图片")
    private List<String> tarePicList;

    @ApiModelProperty(value = "皮重地磅id")
    private String tarePoundId;

    @ApiModelProperty(value = "皮重地磅名称")
    private String tarePoundName;

    /**
     * 标志位，true 不符合入库条件 false 符合入库条件
     */
    @ApiModelProperty(value = "标志位")
    private Boolean flag;
}
