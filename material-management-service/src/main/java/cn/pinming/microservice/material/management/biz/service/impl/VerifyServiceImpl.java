package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.MaterialVerify;
import cn.pinming.microservice.material.management.biz.entity.MaterialVerifyRelation;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContract;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContractDetail;
import cn.pinming.microservice.material.management.biz.enums.*;
import cn.pinming.microservice.material.management.biz.mapper.MaterialVerifyMapper;
import cn.pinming.microservice.material.management.biz.query.MaterialVerifyQuery;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.IfBranchUtil;
import cn.pinming.microservice.material.management.common.util.NoUtil;
import cn.pinming.microservice.material.management.config.SiteContextHolder;
import cn.pinming.microservice.material.management.proxy.CooperateServiceProxy;
import cn.pinming.microservice.material.management.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialCategoryDto;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.v2.company.api.dto.EmployeeDetailDto;
import cn.pinming.v2.company.api.dto.EmployeeDetailQueryDto;
import cn.pinming.v2.company.api.dto.EmployeeDto;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/21
 * @description
 */
@Service
public class VerifyServiceImpl implements VerifyService {

    @Resource
    private MaterialVerifyMapper materialVerifyMapper;
    @Resource
    private IMaterialVerifyRelationService relationService;
    @Resource
    private IMaterialHandlerService materialHandlerService;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;
    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private CooperateServiceProxy cooperateServiceProxy;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private NoUtil noUtil;
    @Resource
    private IPurchaseContractService contractService;
    @Resource
    private IPurchaseContractDetailService contractDetailService;
    @Resource
    private SiteContextHolder siteContextHolder;

    @Override
    public String verifyNoSequence(Integer projectId) {
        String verifyNo = noUtil.getBizNo(NoUtil.DZ_KEY_PREFIX, projectId);
        return verifyNo;
    }

    @Override
    public MaterialVerifyAddVO addVerifyRecord(Integer projectId) {
        String verifyNo = this.verifyNoSequence(projectId);
        MaterialVerify entity = new MaterialVerify();
        entity.setVerifyNo(verifyNo);
        entity.setVerifyProjectId(projectId);
        materialVerifyMapper.insert(entity);
        MaterialVerifyAddVO vo = new MaterialVerifyAddVO();
        vo.setId(entity.getId());
        vo.setVerifyNo(verifyNo);
        AuthUser currentUser = authUserHolder.getCurrentUser();
        // 对账人
        String verifyPersonId = currentUser.getId();
        Optional.ofNullable(verifyPersonId).ifPresent(personId -> {
            EmployeeDto employee = employeeServiceProxy.findEmployee(currentUser.getCurrentCompanyId(), personId);
            Optional.ofNullable(employee).ifPresent(employeeDto -> vo.setVerifyPerson(employeeDto.getMemberName()));
        });
        // 项目
        ProjectVO project = projectServiceProxy.getProjectById(projectId);
        Optional.ofNullable(project).ifPresent(proj -> vo.setProjectName(proj.getProjectTitle()));
        return vo;
    }

    @Override
    public MaterialVerifyDetailVO verifyDetail(String start, String end, Integer supplierId, String contractId, String verifyId) {
        String startTime = null;
        String endTime = null;
        if (StringUtils.isNotBlank(start) && StringUtils.isNotBlank(end)) {
            startTime = start + " 00:00:00";
            endTime = end + " 23:59:59";
        }
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer currentProjectId = currentUser.getCurrentProjectId();
        // 临时收料
        List<MaterialVerifyReceiveDetailDTO> temporaryReceive = materialVerifyMapper.tempAndReportVerifyReceiveDetail(startTime, endTime,
                ReceiveModeEnum.TEMPORARY.value(), currentProjectId, supplierId, contractId, verifyId);
        // 报备收料
        List<MaterialVerifyReceiveDetailDTO> reportReceive = materialVerifyMapper.tempAndReportVerifyReceiveDetail(startTime, endTime,
                ReceiveModeEnum.REPORT.value(), currentProjectId, supplierId, contractId, verifyId);
//        // 无归属收料
//        List<MaterialVerifyReceiveDetailDTO> noAffiliationReceive = materialVerifyMapper.unbelognAndInvalidVerifyReceiveDetail(
//                startTime, endTime, ReceiveModeEnum.UNBELOGN.value(), currentProjectId, verifyId);
//        // 过滤掉其中采购单存在，但不属于本合同的记录
//        noAffiliationReceive = filterHaveOrderNoNotInContract(noAffiliationReceive, contractId);
//        // 无效称重
//        List<MaterialVerifyReceiveDetailDTO> invalidWeigh = materialVerifyMapper.unbelognAndInvalidVerifyReceiveDetail(
//                startTime, endTime, ReceiveModeEnum.INVALID.value(), currentProjectId, verifyId);
        // 过滤掉其中采购单存在，但不属于本合同的记录
//        invalidWeigh = filterHaveOrderNoNotInContract(invalidWeigh, contractId);
        MaterialVerifyDetailVO result = MaterialVerifyDetailVO.builder()
                .temporaryReceive(verifyDetailDTOSConvert2VOS(temporaryReceive))
                .reportReceive(verifyDetailDTOSConvert2VOS(reportReceive))
//                .noAffiliationReceive(verifyDetailDTOSConvert2VOS(noAffiliationReceive))
//                .invalidWeigh(verifyDetailDTOSConvert2VOS(invalidWeigh))
                .build();
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean relationVerifyDetail(String supplierId, String contractId, String verifyId, List<String> reviseIds) {
        // 先删除
        QueryWrapper<MaterialVerifyRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MaterialVerifyRelation::getVerifyId, verifyId);
        relationService.remove(queryWrapper);
        // 后新增
        IfBranchUtil.isTrue(CollectionUtil.isNotEmpty(reviseIds)).trueHandle(() -> {
            List<MaterialVerifyRelation> relations = reviseIds.stream().map(reviseId -> {
                MaterialVerifyRelation relation = new MaterialVerifyRelation();
                relation.setVerifyId(verifyId);
                relation.setReceiveDataId(reviseId);
                return relation;
            }).collect(Collectors.toList());
            relationService.saveBatch(relations);
        });
        // 更新对账记录的供应商和合同
        MaterialVerify verify = new MaterialVerify();
        verify.setId(verifyId);
        verify.setContractId(contractId);
        verify.setSupplierId(supplierId);
        materialVerifyMapper.updateById(verify);
        return true;
    }

    @Override
    public MaterialVerifyPageVO verifyReceiveRecords(String verifyId, String receiveId) {
        MaterialVerifyPageVO vo = new MaterialVerifyPageVO();
        AuthUser currentUser = authUserHolder.getCurrentUser();
        MaterialVerify verify = materialVerifyMapper.selectById(verifyId);
        vo.setStatus(verify.getStatus());
        vo.setSupplierId(verify.getSupplierId());
        vo.setContractId(verify.getContractId());
        vo.setVerifyNo(verify.getVerifyNo());
        // 对账材料明细
        List<MaterialVerifyReceiveDTO> materialVerifyReceiveDTOS = materialVerifyMapper.verifyReceiveList(verifyId);
        // 对账材料分类统计
        if (CollectionUtil.isNotEmpty(materialVerifyReceiveDTOS)) {
            if (StringUtils.isNotBlank(receiveId)) {
                materialVerifyReceiveDTOS = materialVerifyReceiveDTOS.stream().filter(item -> receiveId.equals(item.getWeighRecordId())).collect(Collectors.toList());
            }
            // 对账材料明细
            List<MaterialVerifyReceiveVO> materialVerifyReceiveVOS = verifyDTOSConvert2VOS(materialVerifyReceiveDTOS);
            vo.setList(materialVerifyReceiveVOS);
            if (CollectionUtil.isNotEmpty(materialVerifyReceiveVOS)) {
                // 对账材料分类统计
                List<MaterialVerifyMaterialVO> verifyMaterialVOS = Lists.newArrayList();
                // 材料ID与品牌规格对应
                Map<String, String> materialSpecMap = materialVerifyReceiveVOS.stream()
                        .filter(item -> ObjectUtil.isNotNull(item.getMaterialId()) && ObjectUtil.isNotNull(item.getMaterialSpec()))
                        .collect(Collectors.toMap(item -> String.valueOf(item.getMaterialId()), MaterialVerifyReceiveVO::getMaterialSpec, (k1, k2) -> k1));
                List<String> dataIds = materialVerifyReceiveVOS.stream().map(MaterialVerifyReceiveVO::getReceiveDataId).collect(Collectors.toList());
                List<MaterialVerifyMaterialDTO> materialList = materialVerifyMapper.verifyReceiveMaterialList(dataIds);
                for (int i = 0; i < materialList.size(); i++) {
                    MaterialVerifyMaterialVO materialVO = new MaterialVerifyMaterialVO();
                    MaterialVerifyMaterialDTO item = materialList.get(i);
                    BeanUtils.copyProperties(item, materialVO);
                    materialVO.setIndex(i + 1);
                    materialVO.setMaterialSpec(materialSpecMap.getOrDefault(item.getMaterialId(), null));
                    // 没有关联采购单
                    if (!item.getIsPurchase()) {
                        // 没有材料ID
                        if (StringUtils.isBlank(item.getMaterialId())) {
                            materialVO.setMaterialSpec(null);
                        }
                        materialVO.setPurchaseTotal(null);
                        materialVO.setActualCountTotal(null);
                        materialVO.setActualReceiveTotal(null);
                        materialVO.setDeviationTotal(null);
                        materialVO.setDeviationRateTotal(null);
                        materialVO.setUnit(null);
                    }
                    verifyMaterialVOS.add(materialVO);
                }
                vo.setMaterialList(verifyMaterialVOS);
            }
        }
        // 对账人
        String verifyPersonId = verify.getCreateId();
        Optional.ofNullable(verifyPersonId).ifPresent(personId -> {
            EmployeeDto employee = employeeServiceProxy.findEmployee(verify.getCompanyId(), personId);
            Optional.ofNullable(employee).ifPresent(employeeDto -> vo.setVerifyPerson(employeeDto.getMemberName()));
        });
        // 项目
        Integer verifyProjectId = verify.getVerifyProjectId();
        Optional.ofNullable(verifyProjectId).ifPresent(projectId -> {
            vo.setProjectId(projectId);
            ProjectVO project = projectServiceProxy.getProjectById(projectId);
            Optional.ofNullable(project).ifPresent(proj -> vo.setProjectName(proj.getProjectTitle()));
        });
        // 对账中且为创建者
        IfBranchUtil.isTureOrFalse(verify.getStatus() == 1 && currentUser != null && currentUser.getId().equals(verify.getCreateId())).trueOrFalseHandle(
                () -> {
                    vo.setAddReceive(true);
                    vo.setScanAddReceive(true);
                    vo.setVerifyFile(true);
                },
                () -> {
                    vo.setAddReceive(false);
                    vo.setScanAddReceive(false);
                    vo.setVerifyFile(false);
                });
        return vo;
    }

    @Override
    public boolean removeVerifyReceiveRecord(String verifyId, String reviseId) {
        MaterialVerifyRelation entity = new MaterialVerifyRelation();
        entity.setIsDeleted((byte) 1);
        QueryWrapper<MaterialVerifyRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MaterialVerifyRelation::getVerifyId, verifyId).eq(MaterialVerifyRelation::getReceiveDataId, reviseId);
        relationService.remove(queryWrapper);
        return true;
    }

    @Override
    public IPage<MaterialVerifyVO> listMaterialVerify(MaterialVerifyQuery materialVerifyQuery) {

        // 查询对账列表
        IPage<MaterialDataVerifyDTO> page = materialVerifyMapper.selectMaterialVerify(materialVerifyQuery);
        List<MaterialDataVerifyDTO> materialDataVerifyDTOList = page.getRecords();
        // 对账人map
        Set<String> personIdList = materialDataVerifyDTOList.stream().map(MaterialDataVerifyDTO::getVerifyPersonId).filter(Objects::nonNull).collect(Collectors.toSet());
        EmployeeDetailQueryDto employeeDetailQueryDto = new EmployeeDetailQueryDto();
        employeeDetailQueryDto.setCompanyId(authUserHolder.getCurrentUser().getCurrentCompanyId());
        employeeDetailQueryDto.setMemberIdList(new ArrayList<>(personIdList));
        List<EmployeeDetailDto> employeeDetailDtoList = employeeServiceProxy.employeeList(employeeDetailQueryDto);
        Map<String, EmployeeDetailDto> personMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(employeeDetailDtoList)) {
            personMap = employeeDetailDtoList.stream().collect(Collectors.toMap(EmployeeDetailDto::getMemberId, e -> e));
        }

        // 供应商map
        String suppliers = materialDataVerifyDTOList.stream().map(MaterialDataVerifyDTO::getVerifySupplierId).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.joining(StrUtil.COMMA));
        List<CooperateVO> cooperateVOList = cooperateServiceProxy.findCooperateByIds(authUserHolder.getCurrentUser().getCurrentCompanyId(), suppliers);
        Map<Integer, CooperateVO> supplierMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(cooperateVOList)) {
            supplierMap = cooperateVOList.stream().collect(Collectors.toMap(CooperateVO::getId, e -> e));
        }

        // 项目map
        List<Integer> projects = materialDataVerifyDTOList.stream().map(MaterialDataVerifyDTO::getVerifyProjectId).filter(Objects::nonNull).collect(Collectors.toList());
        List<ProjectVO> simpleProjectList = projectServiceProxy.getSimpleProjects(projects);
        Map<Integer, ProjectVO> projectMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(simpleProjectList)) {
            projectMap = simpleProjectList.stream().collect(Collectors.toMap(ProjectVO::getProjectId, e -> e));
        }

        Map<String, EmployeeDetailDto> finalPersonMap = personMap;
        Map<Integer, CooperateVO> finalSupplierMap = supplierMap;
        Map<Integer, ProjectVO> finalProjectMap = projectMap;
        List<MaterialVerifyVO> materialVerifyVOList = materialDataVerifyDTOList.stream().map(materialDataVerifyDTO -> {
            MaterialVerifyVO materialDataVerifyVO = new MaterialVerifyVO();
            BeanUtils.copyProperties(materialDataVerifyDTO, materialDataVerifyVO);
            // 对账人
            String verifyPersonId = materialDataVerifyDTO.getVerifyPersonId();
            EmployeeDetailDto verifyPersonDTO = finalPersonMap.get(verifyPersonId);
            Optional.ofNullable(verifyPersonDTO).ifPresent(verifyPerson -> materialDataVerifyVO.setVerifyPerson(verifyPerson.getMemberName()));

            // 默认为false
            materialDataVerifyVO.setIsCreate(Boolean.FALSE);
            materialDataVerifyVO.setIsVerifier(Boolean.FALSE);
            // 当前用户是否是对账人
            IfBranchUtil.isTrue(havePermission()).trueHandle(() -> materialDataVerifyVO.setIsVerifier(Boolean.TRUE));
            // 当前用户是否是创建者
            AuthUser currentUser = authUserHolder.getCurrentUser();
            Optional.ofNullable(currentUser).ifPresent(cur ->
                    IfBranchUtil.isTrue(cur.getId().equals(verifyPersonId)).trueHandle(() -> materialDataVerifyVO.setIsCreate(Boolean.TRUE))
            );
            // 项目
            Integer verifyProjectId = materialDataVerifyDTO.getVerifyProjectId();
            ProjectVO projectVO = finalProjectMap.get(verifyProjectId);
            Optional.ofNullable(projectVO).ifPresent(project -> materialDataVerifyVO.setVerifyProject(project.getProjectTitle()));
            // 供应商
            Integer verifySupplierId = materialDataVerifyDTO.getVerifySupplierId();
            CooperateVO cooperateVO = finalSupplierMap.get(verifySupplierId);
            Optional.ofNullable(cooperateVO).ifPresent(cooperate -> materialDataVerifyVO.setVerifySupplier(cooperate.getName()));
            // 合同名
            String contractId = materialDataVerifyDTO.getContractId();
            Optional.ofNullable(contractId).ifPresent(id -> {
                PurchaseContract contract = contractService.getById(contractId);
                Optional.ofNullable(contract).ifPresent(ct -> materialDataVerifyVO.setVerifyContract(ct.getName()));
            });
            return materialDataVerifyVO;
        }).collect(Collectors.toList());
        IPage<MaterialVerifyVO> list = new Page<>();
        BeanUtils.copyProperties(page, list);
        list.setRecords(materialVerifyVOList);
        return list;
    }

    @Override
    public void removeMaterialVerifyById(String id) {
        Optional.ofNullable(id).orElseThrow(() -> new BOException(BOExceptionEnum.VERIFY_ID_NOT_NULL));
        if (!havePermission()) {
            throw new BOException(BOExceptionEnum.NO_PERMISSION);
        }
        // 对账表移除
        materialVerifyMapper.deleteById(id);
        // 关联表移除
        LambdaQueryWrapper<MaterialVerifyRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaterialVerifyRelation::getVerifyId, id);
        relationService.remove(wrapper);
    }

    @Override
    public void materialFile(String id) {
        Optional.ofNullable(id).orElseThrow(() -> new BOException(BOExceptionEnum.VERIFY_ID_NOT_NULL));
        if (!havePermission()) {
            throw new BOException(BOExceptionEnum.NO_PERMISSION);
        }
        materialVerifyMapper.updateFileStatusById(id);
    }

    @Override
    public List<VerifyExcelDataVO> getVerifyExcelVO(String id, VerifyExcelVO verifyExcelVO) {
        // 对账基本信息
        MaterialVerify materialVerify = materialVerifyMapper.selectById(id);
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer projectId = materialVerify.getVerifyProjectId();
        String createId = materialVerify.getCreateId();
        String supplierId = materialVerify.getSupplierId();
        String contractId = materialVerify.getContractId();
        // 对账人
        Optional.ofNullable(createId).ifPresent(personId -> {
            EmployeeDto employee = employeeServiceProxy.findEmployee(currentUser.getCurrentCompanyId(), personId);
            Optional.ofNullable(employee).ifPresent(employeeDto -> verifyExcelVO.setVerifyPerson(employeeDto.getMemberName()));
        });
        // 项目
        ProjectVO project = projectServiceProxy.getProjectById(projectId);
        Optional.ofNullable(project).ifPresent(proj -> verifyExcelVO.setProjectName(proj.getProjectTitle()));
        // 供应商
        Optional.ofNullable(supplierId).ifPresent(spId -> {
            List<CooperateVO> supplierVO = cooperateServiceProxy.findCooperateByIds(currentUser.getCurrentCompanyId(), supplierId);
            if (CollUtil.isNotEmpty(supplierVO)) {
                verifyExcelVO.setVerifySupplier(supplierVO.get(0).getName());
            }
        });
        // 合同名
        Optional.ofNullable(contractId).ifPresent(cid -> {
            PurchaseContract contract = contractService.getById(cid);
            Optional.ofNullable(contract).ifPresent(ct -> verifyExcelVO.setVerifyContract(ct.getName()));
        });
        // 导出时间
        verifyExcelVO.setExportTime(LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
        List<MaterialVerifyReceiveDTO> materialVerifyReceiveDTOS = materialVerifyMapper.verifyReceiveList(id);
        List<VerifyExcelDataVO> verifyExcelDataVOS = materialVerifyReceiveDTOS.stream().map(this::verifyReceiveDTO2ExcelDataVO).collect(Collectors.toList());
        return verifyExcelDataVOS;
    }

    @Override
    public List<MaterialVerifyScanVO> scanRangeCheck(String verifyId, String contractId, String inputVal, boolean scan) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer companyId = currentUser.getCurrentCompanyId();
        Integer projectId = currentUser.getCurrentProjectId();
        // todo 移动收料单据
        List<MaterialVerifyScanVO> result = (List<MaterialVerifyScanVO>) IfBranchUtil.isTrueOrFalseReturn(scan, inputVal).trueReturnHandle(
                // 扫码枪
                val -> {
                    Matcher matcher1 = tUuidScan(val);
                    Matcher matcher2 = sNoScan(val);
                    Matcher matcher3 = coIdScan(val);
                    Matcher matcher4 = pjIdScan(val);
                    // 判断扫码枪内容是否合法
                    if (matcher1.find() && matcher2.find() && matcher3.find() && matcher4.find()) {
                        // 终端称重记录ID
                        String weighId = matcher1.group(1);
                        // 收料单号
                        String receiveNo = matcher2.group(1);
                        // 企业ID
                        Integer cpId = Integer.parseInt(matcher3.group(1));
                        // 项目ID
                        Integer pjId = Integer.parseInt(matcher4.group(1));
                        // 根据终端称重记录ID查询收料详情
                        List<MaterialVerifyReceiveDetailScanDTO> details = materialVerifyMapper.receiveDetail(weighId, null);
                        // 判断是否本项目单据
                        if (ObjectUtil.isNotNull(companyId) && companyId.equals(cpId) && ObjectUtil.isNotNull(projectId) && projectId.equals(pjId)) {
                            // 范围判断
                            List<MaterialVerifyScanVO> scanVO = rangeCheck(verifyId, contractId, receiveNo, details);
                            return scanVO;
                        } else {
                            // case 4
                            MaterialVerifyScanVO scanVO = MaterialVerifyScanVO.builder().status(ScanVerityStatusEnum.FOUR.getStatus()).build();
                            List<MaterialVerifyScanVO> list = Lists.newArrayList();
                            list.add(scanVO);
                            return list;
                        }
                    } else {
                        // case 5
                        MaterialVerifyScanVO scanVO = MaterialVerifyScanVO.builder().status(ScanVerityStatusEnum.FIVE.getStatus()).build();
                        List<MaterialVerifyScanVO> list = Lists.newArrayList();
                        list.add(scanVO);
                        return list;
                    }
                },
                // 手输: 流水号=收料单号
                val -> {
                    // 根据收料单号查询收料详情
                    List<MaterialVerifyReceiveDetailScanDTO> details = materialVerifyMapper.receiveDetail(null, inputVal);
                    // 范围判断
                    List<MaterialVerifyScanVO> scanVO = rangeCheck(verifyId, contractId, inputVal, details);
                    return scanVO;
                }
        );
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<MaterialVerifyScanSubmitVO> scanSubmit(String supplierId, String contractId, String verifyId, List<String> reviseIds) {
        // 更新供应商和合同
        MaterialVerify verify = new MaterialVerify();
        verify.setId(verifyId);
        verify.setContractId(contractId);
        verify.setSupplierId(supplierId);
        materialVerifyMapper.updateById(verify);
        // 对账检查
        List<MaterialVerifyReceiveDetailScanCheckDTO> checkList = materialVerifyMapper.scanSubmitCheck(reviseIds);
        List<String> verifyReviseIds = (List<String>) IfBranchUtil.isTrueOrFalseReturn(CollectionUtil.isNotEmpty(checkList), checkList).trueReturnHandle(
                list -> list.stream().map(MaterialVerifyReceiveDetailScanCheckDTO::getReceiveId).collect(Collectors.toList()),
                Lists::newArrayList
        );
        // 入库
        reviseIds.removeAll(verifyReviseIds);
        IfBranchUtil.isTrue(CollectionUtil.isNotEmpty(reviseIds)).trueHandle(() -> {
            List<MaterialVerifyRelation> relations = reviseIds.stream().map(reviseId -> {
                MaterialVerifyRelation relation = new MaterialVerifyRelation();
                relation.setVerifyId(verifyId);
                relation.setReceiveDataId(reviseId);
                return relation;
            }).collect(Collectors.toList());
            relationService.saveBatch(relations);
        });
        return scanCheckDTOConvert2VO(checkList);
    }

    @Override
    public List<VerifyPersonVO> listVerifyPerson() {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer companyId = currentUser.getCurrentCompanyId();
        Integer projectId = currentUser.getCurrentProjectId();
        List<String> personIdList = materialVerifyMapper.selectVerifyPerson(companyId, projectId);
        List<VerifyPersonVO> result = Lists.newArrayList();
        if (CollUtil.isNotEmpty(personIdList)) {
            EmployeeDetailQueryDto employeeDetailQueryDto = new EmployeeDetailQueryDto();
            employeeDetailQueryDto.setCompanyId(companyId);
            employeeDetailQueryDto.setMemberIdList(personIdList);
            List<EmployeeDetailDto> employeeDetailDtoList = employeeServiceProxy.employeeList(employeeDetailQueryDto);
            Map<String, EmployeeDetailDto> dtoMap = employeeDetailDtoList.stream().collect(Collectors.toMap(EmployeeDetailDto::getMemberId, e -> e));
            result = personIdList.stream().map(personId -> {
                VerifyPersonVO verifyPersonVO = new VerifyPersonVO();
                verifyPersonVO.setVerifyPerson(personId);
                EmployeeDetailDto employeeDetailDto = dtoMap.get(personId);
                Optional.ofNullable(employeeDetailDto).ifPresent(emp ->  verifyPersonVO.setVerifyPersonName(emp.getMemberName()));
                return verifyPersonVO;
            }).collect(Collectors.toList());
        }
        return result;
    }

    private List<MaterialVerifyScanSubmitVO> scanCheckDTOConvert2VO(List<MaterialVerifyReceiveDetailScanCheckDTO> dtos) {
        if (CollectionUtil.isEmpty(dtos)) {
            return null;
        }
        List<MaterialVerifyScanSubmitVO> vos = dtos.stream().map(dto -> {
            MaterialVerifyScanSubmitVO vo = new MaterialVerifyScanSubmitVO();
            BeanUtils.copyProperties(dto, vo);
            return vo;
        }).collect(Collectors.toList());
        return vos;
    }

    /**
     * 范围判断
     * @param verifyId      对账ID
     * @param contractId    合同ID
     * @param receiveNo     收料单号
     * @param details       查询出来的收料数据
     * @return
     */
    private List<MaterialVerifyScanVO> rangeCheck(String verifyId, String contractId, String receiveNo,
                                                  List<MaterialVerifyReceiveDetailScanDTO> details) {
        List<MaterialVerifyScanVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(details)) {
            // case 3
            MaterialVerifyScanVO scanVO = MaterialVerifyScanVO.builder().status(ScanVerityStatusEnum.THREE.getStatus()).build();
            result.add(scanVO);
        } else {
            // 一车多料前的判断
            MaterialVerifyReceiveDetailScanDTO firstDTO = details.get(0);
            // 判断单据流水号是否存在
            if (ObjectUtil.isNotNull(firstDTO) && ObjectUtil.isNotNull(receiveNo) && receiveNo.equals(firstDTO.getReceiveNo())) {
                // 判断是否为收料单
                if (WeighTypeEnum.RECEIVE.value() == firstDTO.getType()) {
                    // 判断采购单存在且不属于本合同
                    if (StringUtils.isNotBlank(firstDTO.getOrderNo()) && !contractId.equals(firstDTO.getContractId())) {
                        // case 8
                        MaterialVerifyScanVO scanVO = MaterialVerifyScanVO.builder().status(ScanVerityStatusEnum.EIGHT.getStatus()).receiveNo(firstDTO.getReceiveNo()).build();
                        result.add(scanVO);
                    }
                } else {
                    // case 11
                    MaterialVerifyScanVO scanVO = MaterialVerifyScanVO.builder().status(ScanVerityStatusEnum.ELEVEN.getStatus()).receiveNo(firstDTO.getReceiveNo()).build();
                    result.add(scanVO);
                }
            } else {
                // case 3
                MaterialVerifyScanVO scanVO = MaterialVerifyScanVO.builder().status(ScanVerityStatusEnum.THREE.getStatus()).receiveNo(firstDTO.getReceiveNo()).build();
                result.add(scanVO);
            }
            // 出现case即返回
            if (CollectionUtil.isNotEmpty(result)) {
                return result;
            }
            // 一车多料
            for (MaterialVerifyReceiveDetailScanDTO detail : details) {
                // 判断是否已对账
                MaterialVerifyScanVO scanVO;
                if (detail.getIsVerify()) {
                    if (verifyId.equals(detail.getVerifyId())) {
                        // case 2
                        scanVO = MaterialVerifyScanVO.builder().status(ScanVerityStatusEnum.TWO.getStatus()).receiveNo(detail.getReceiveNo()).build();
                    } else {
                        // case 7
                        scanVO = MaterialVerifyScanVO.builder()
                                .status(ScanVerityStatusEnum.SEVEN.getStatus())
                                .receiveNo(detail.getReceiveNo())
                                .weighRecordId(detail.getWeighRecordId())
                                .receiveDataId(detail.getReceiveDataId())
                                .verifyId(detail.getVerifyId())
                                .verifyNo(detail.getVerifyNo())
                                .build();
                    }
                } else {
                    // case 0 判断该单据是否已添加【需要前端自己判断】
                    scanVO = MaterialVerifyScanVO.builder().status(ScanVerityStatusEnum.ZERO.getStatus()).build();
                    // 其他case都有问题，这里可能没问题，做VO转换
                    BeanUtils.copyProperties(detail, scanVO);
                    Optional.ofNullable(detail.getReceiveTime()).ifPresent(time -> scanVO.setReceiveTime(time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
                }
                Integer materialId = detail.getMaterialId();
                if (materialId != null) {
                    // 材料id存在取二级分类名称
                    scanVO.setCategoryName(ObjectUtil.isNotNull(detail.getCategoryName()) ?
                            detail.getCategoryName() :
                            detail.getMaterialName());
                    MaterialDto materialDto = materialServiceProxy.materialById(materialId);
                    if (materialDto != null) {
                        scanVO.setMaterialSpec(StrUtil.format("{}/{}", materialDto.getMaterialName(), materialDto.getMaterialSpec()));
                    }
                } else {
                    // 材料ID为空取值materialName
                    scanVO.setCategoryName(detail.getMaterialName());
                    scanVO.setMaterialSpec("--");
                }
                result.add(scanVO);
            }
        }
        return result;
    }

    /**
     * 终端称重记录ID
     * @param inputVal
     * @return
     */
    private static Matcher tUuidScan(String inputVal) {
        Matcher matcher = SCAN_CONTENT_PATTERN1.matcher(inputVal);
        return matcher;
    }
    /**
     * 收料单号
     * @param inputVal
     * @return
     */
    private Matcher sNoScan(String inputVal) {
        Matcher matcher = SCAN_CONTENT_PATTERN2.matcher(inputVal);
        return matcher;
    }
    /**
     * 企业ID
     * @param inputVal
     * @return
     */
    private Matcher coIdScan(String inputVal) {
        Matcher matcher = SCAN_CONTENT_PATTERN3.matcher(inputVal);
        return matcher;
    }
    /**
     * 项目ID
     * @param inputVal
     * @return
     */
    private Matcher pjIdScan(String inputVal) {
        Matcher matcher = SCAN_CONTENT_PATTERN4.matcher(inputVal);
        return matcher;
    }

    private VerifyExcelDataVO verifyReceiveDTO2ExcelDataVO(MaterialVerifyReceiveDTO materialVerifyReceiveDTO) {
        VerifyExcelDataVO verifyExcelDataVO = new VerifyExcelDataVO();
        BeanUtils.copyProperties(materialVerifyReceiveDTO, verifyExcelDataVO);
        verifyExcelDataVO.setEnterTime(LocalDateTimeUtil.format(materialVerifyReceiveDTO.getEnterTime(), "yyyy-MM-dd HH:mm:ss"));
        verifyExcelDataVO.setLeaveTime(LocalDateTimeUtil.format(materialVerifyReceiveDTO.getLeaveTime(), "yyyy-MM-dd HH:mm:ss"));

        Integer materialId = materialVerifyReceiveDTO.getMaterialId();
        if (materialId != null) {
            // 材料id存在取二级分类名称
            verifyExcelDataVO.setCategoryName(ObjectUtil.isNotNull(materialVerifyReceiveDTO.getCategoryName()) ?
                    materialVerifyReceiveDTO.getCategoryName() :
                    materialVerifyReceiveDTO.getMaterialName());
            MaterialDto materialDto = materialServiceProxy.materialById(materialId);
            if (materialDto != null) {
                verifyExcelDataVO.setMaterialSpec(StrUtil.format("{}/{}", materialDto.getMaterialName(), materialDto.getMaterialSpec()));
            }
        } else {
            // 材料ID为空取值materialName
            verifyExcelDataVO.setCategoryName(materialVerifyReceiveDTO.getMaterialName());
            verifyExcelDataVO.setMaterialSpec("--");
        }
        // 偏差率
        DecimalFormat df= new DecimalFormat("###.####");
        Optional.ofNullable(materialVerifyReceiveDTO.getDeviationRate()).ifPresent(deviationRate -> verifyExcelDataVO.setDeviationRate(df.format(deviationRate) + "%"));

        String contractDetailId = materialVerifyReceiveDTO.getContractDetailId();
        Optional.ofNullable(contractDetailId).ifPresent(cid -> {
            PurchaseContractDetail contractDetail = contractDetailService.getById(cid);
            Optional.ofNullable(contractDetail).ifPresent(cDetail -> {
                verifyExcelDataVO.setConversionRate(cDetail.getConversionRate());
                BigDecimal deviationFloor = cDetail.getDeviationFloor();
                BigDecimal deviationCeiling = cDetail.getDeviationCeiling();
                IfBranchUtil.isTrue(deviationFloor != null && deviationCeiling != null).trueHandle(() -> {
                    String deviationThreshold = df.format(deviationFloor) + "%-" + df.format(deviationCeiling) + "%";
                    verifyExcelDataVO.setDeviationThreshold(deviationThreshold);
                });
            });
        });

        // 偏差状态
        Optional.ofNullable(materialVerifyReceiveDTO.getDeviationStatus()).ifPresent(deviationStatus -> verifyExcelDataVO.setDeviationStatus(DeviationStatusEnum.chooseDeviationStatus(deviationStatus)));

        String warningSourceNo = materialVerifyReceiveDTO.getWarningSourceNo();
        IfBranchUtil.isTureOrFalse(StrUtil.isNotBlank(warningSourceNo)).trueOrFalseHandle(()-> {
                    verifyExcelDataVO.setHasWarning("有");
                    Boolean warningStatus = materialVerifyReceiveDTO.getWarningStatus();
                    IfBranchUtil.isTureOrFalse(warningStatus != null && warningStatus).trueOrFalseHandle(()->
                            verifyExcelDataVO.setIsHandle("是"), ()-> verifyExcelDataVO.setIsHandle("否"));
                }
                , ()->  {
                    verifyExcelDataVO.setHasWarning("无");
                    verifyExcelDataVO.setIsHandle("--");
                });
        Boolean isRevise = materialVerifyReceiveDTO.getIsRevise();
        IfBranchUtil.isTureOrFalse(isRevise != null && isRevise).trueOrFalseHandle(()->
                verifyExcelDataVO.setHasRevise("有"), ()-> verifyExcelDataVO.setHasRevise("无"));


        return verifyExcelDataVO;
    }


    /**
     * 当前用户是否有权限处理
     *
     * @return Boolean
     */
    private Boolean havePermission() {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (currentUser != null) {
            return materialHandlerService.enableHandle(currentUser.getId(), HandleTypeEnum.CHECK_HANDLE.value());
        }
        return false;
    }

    private List<MaterialVerifyReceiveDetailVO> verifyDetailDTOConvert2VO(List<MaterialVerifyReceiveDetailDTO> dtos) {

        List<MaterialVerifyReceiveDetailVO> vos = Lists.newArrayList();
        if (CollectionUtil.isEmpty(dtos)) {
            return vos;
        }
        vos = dtos.stream().map(dto -> {
            MaterialVerifyReceiveDetailVO vo = new MaterialVerifyReceiveDetailVO();
            BeanUtils.copyProperties(dto, vo);
            return vo;
        }).collect(Collectors.toList());

        Set<Integer> materialIds = dtos.stream().map(MaterialVerifyReceiveDetailDTO::getMaterialId).filter(ObjectUtil::isNotNull).collect(Collectors.toSet());
        Map<Integer, String> materialNameMap = new HashMap<>();
        Map<Integer, String> materialSpecMap = new HashMap<>();
        Map<Integer, String> categoryNameMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(materialIds)) {
            List<MaterialDto> materialDtos = materialServiceProxy.listMaterialByIds(materialIds);
            if (CollectionUtil.isNotEmpty(materialDtos)) {
                materialNameMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, MaterialDto::getMaterialName, (k1, k2) -> k1));
                materialSpecMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, MaterialDto::getMaterialSpec, (k1, k2) -> k1));
                categoryNameMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, MaterialDto::getMaterialCategoryName, (k1, k2) -> k1));
            }
        }

        for (MaterialVerifyReceiveDetailVO vo : vos) {
            Integer materialId = vo.getMaterialId();
            String materialName = materialNameMap.getOrDefault(materialId, "");
            String materialSpec = materialSpecMap.getOrDefault(materialId, "");
            String categoryName = categoryNameMap.getOrDefault(materialId, "");
            if (materialId != null) {
                // 材料id存在取二级分类名称
                vo.setCategoryName(StringUtils.isNotBlank(categoryName) ? categoryName : materialName);
                vo.setMaterialSpec(StrUtil.format("{}/{}", materialName, materialSpec));
            } else {
                // 材料ID为空取值materialName
                vo.setCategoryName(materialName);
                vo.setMaterialSpec("--");
            }
            // 设置偏差状态
            IfBranchUtil.isTrue(StringUtils.isNotBlank(vo.getDeviationStatus())).trueHandle(() ->
                    vo.setDeviationStatus(DeviationStatusEnum.chooseDeviationStatus(Byte.valueOf(vo.getDeviationStatus()))));
        }
        return vos;
    }

    private List<MaterialVerifyReceiveDetailVO> verifyDetailDTOSConvert2VOS(List<MaterialVerifyReceiveDetailDTO> dtos) {
        List<MaterialVerifyReceiveDetailVO> voList = verifyDetailDTOConvert2VO(dtos);
        return voList;
    }

    private MaterialVerifyReceiveVO verifyDTOConvert2VO(MaterialVerifyReceiveDTO dto) {
        if (dto == null) {
            return null;
        }
        MaterialVerifyReceiveVO vo = new MaterialVerifyReceiveVO();
        BeanUtils.copyProperties(dto, vo);
        Integer materialId = dto.getMaterialId();
        if (materialId != null) {
            // 材料id存在取二级分类名称
            vo.setCategoryName(ObjectUtil.isNotNull(dto.getCategoryName()) ?
                    dto.getCategoryName() :
                    dto.getMaterialName());
            MaterialDto materialDto = materialServiceProxy.materialById(materialId);
            if (materialDto != null) {
                vo.setMaterialSpec(StrUtil.format("{}/{}", materialDto.getMaterialName(), materialDto.getMaterialSpec()));
            }
        } else {
            // 材料ID为空取值materialName
            vo.setCategoryName(dto.getMaterialName());
            vo.setMaterialSpec("--");
        }
        vo.setDeviationStatus(DeviationStatusEnum.chooseDeviationStatus(dto.getDeviationStatus()));
        return vo;
    }

    private List<MaterialVerifyReceiveVO> verifyDTOSConvert2VOS(List<MaterialVerifyReceiveDTO> dtos) {
        if (CollectionUtil.isEmpty(dtos)) {
            return Lists.newArrayList();
        }
        List<MaterialVerifyReceiveVO> voList = dtos.stream().map(this::verifyDTOConvert2VO).collect(Collectors.toList());
        return voList;
    }

    /**
     * 过滤掉其中采购单存在，但不属于本合同的记录
     * @param list          数据
     * @param contractId    合同ID
     * @return
     */
    private List<MaterialVerifyReceiveDetailDTO> filterHaveOrderNoNotInContract(List<MaterialVerifyReceiveDetailDTO> list, String contractId) {
        if (CollectionUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().filter(item -> StringUtils.isBlank(item.getOrderNo()) ||
                        (StringUtils.isNotBlank(item.getOrderNo()) && contractId.equals(item.getContractId())))
                .collect(Collectors.toList());
    }

}