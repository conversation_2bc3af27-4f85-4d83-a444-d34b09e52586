package cn.pinming.microservice.material.management.common.aspectj;

import cn.hutool.json.JSONUtil;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.entity.MaterialIotLog;
import cn.pinming.microservice.material.management.common.annotation.IOTLog;
import cn.pinming.microservice.material.management.common.manager.AsyncFactory;
import cn.pinming.microservice.material.management.common.manager.AsyncManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.time.LocalDateTime;

@Slf4j
@Aspect
@Component
public class IOTLogAspect {

    @Pointcut("@annotation(cn.pinming.microservice.material.management.common.annotation.IOTLog)")
    public void logPointCut() {
    }

    @Around("logPointCut()")
    public Object process(ProceedingJoinPoint joinPoint) throws Throwable {
        Object object = null;
        try {
            object = joinPoint.proceed();
        } catch (Exception e) {
            handleLog(joinPoint, e);
        }
        return object;
    }

    protected void handleLog(final JoinPoint joinPoint, final Throwable e) {
        try {
            // 获得注解
            IOTLog controllerLog = getAnnotationLog(joinPoint);
            if (controllerLog == null) {
                return;
            }
            MaterialIotLog log = new MaterialIotLog();
            log.setCreateTime(LocalDateTime.now());
            log.setRequestParam(JSONUtil.toJsonStr(joinPoint.getArgs()[0]));
            if (e != null) {
                log.setErrorInfo(StringUtils.substring(e.getMessage(), 0, 2000));
            }
            AsyncManager.me().execute(AsyncFactory.recordIotOper(log));
        } catch (Exception exp) {
            log.error("异常信息:{}", exp.getMessage());
            exp.printStackTrace();
        }
    }

    /**
     * 是否存在注解，如果存在就获取
     */
    private IOTLog getAnnotationLog(JoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        if (method != null) {
            return method.getAnnotation(IOTLog.class);
        }
        return null;
    }
}
