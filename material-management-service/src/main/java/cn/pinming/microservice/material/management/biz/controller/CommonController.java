package cn.pinming.microservice.material.management.biz.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.material.v2.Material;
import cn.pinming.material.v2.MaterialClientBuilder;
import cn.pinming.materialManagement.dto.TruckInfoDTO;
import cn.pinming.materialManagement.dto.WeighInfoDTO;
import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.*;
import cn.pinming.microservice.material.management.biz.form.PreReportForm;
import cn.pinming.microservice.material.management.biz.form.PreReportNewForm;
import cn.pinming.microservice.material.management.biz.form.StandardMaterialDataForm;
import cn.pinming.microservice.material.management.biz.form.StandardMaterialDataItemFrom;
import cn.pinming.microservice.material.management.biz.iot.service.IWeighbridgeService;
import cn.pinming.microservice.material.management.biz.mapper.PurchaseContractMapper;
import cn.pinming.microservice.material.management.biz.providers.event.PushEvent;
import cn.pinming.microservice.material.management.biz.query.PurchaseQuery;
import cn.pinming.microservice.material.management.biz.query.ztcjExtWeightListQuery;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.biz.sync.CrccContractSyncTask;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.AppConstant;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.ExcelUtils;
import cn.pinming.microservice.material.management.common.util.NoUtil;
import cn.pinming.microservice.material.management.common.util.RedisUtil;
import cn.pinming.microservice.material.management.config.websocket.HeartBeatMessage;
import cn.pinming.microservice.material.management.proxy.CooperateServiceProxy;
import cn.pinming.microservice.material.management.proxy.FileServiceProxy;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material_unit.api.beans.MyPage;
import cn.pinming.microservice.material_unit.api.enterprise.dto.CooperateEnterpriseDto;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.model.FileInfos;
import cn.pinming.v2.common.api.service.FileService;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 免登接口.
 * z
 *
 * <AUTHOR>
 * @version 2021/9/9 1:28 下午
 */
@Api(tags = "免登接口", value = "zh")
@Slf4j
@RestController
@RequestMapping("/api/common")
public class CommonController {
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private CooperateServiceProxy cooperateServiceProxy;
    @Resource
    private IMaterialWeighbridgeService materialWeighbridgeService;
    @Resource
    private ITruckReportService truckReportService;
    @Resource
    private IPurchaseContractDetailService contractDetailService;
    @Resource
    private IPurchaseOrderService purchaseOrderService;
    @Resource
    private IWeighbridgeService weighbridgeService;
    @Resource
    private IPreTruckReportDetailService truckReportDetailService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private IPreWeighReportService preWeighReportService;
    @Resource
    private IPreTruckReportService preTruckReportService;
    @Resource
    private IOTMessageService iotMessageService;
    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private ApplicationContext applicationEventPublisher;
    @Resource
    private IClientLogService clientLogService;
    @Resource
    private IWeighbridgeService iWeighbridgeService;
    @Resource
    private IPurchaseContractService purchaseContractService;
    @Resource
    private NoUtil noUtil;
    @Resource
    private IMaterialVerifyService materialVerifyService;
    @Resource
    private PurchaseContractMapper purchaseContractMapper;
    @Reference
    private FileInfos fileInfos;
    @Reference
    private FileService fileService;
    @Value("${temporary.path}")
    private String temporaryPath;
    @Resource
    private ISupplierService supplierService;
    @Resource
    private ProjectServiceProxy projectServiceProxy;

    @Resource
    private CrccContractSyncTask crccContractSyncTask;
    @Resource
    private IMaterialSendReceiveService materialSendReceiveService;
    @Resource
    private IMaterialDataSaveService materialDataSaveService;

    @Value("${crcc.companyId:11541}")
    public Integer COMPANY_ID;
    @NacosValue("${sdk.host:https://weighmaster.pinming.cn}")
    private String sdkHost;
    @Resource
    private ISdkConfigService sdkConfigService;


    @ApiOperation(value = "获取企业下所有物料", response = MaterialDto.class)
    @Log(title = "免登 - 获取企业下所有物料", businessType = BusinessType.QUERY)
    @GetMapping("/material/list")
    public ResponseEntity<Response> materialList(Integer companyId, Long timestamp,
                                                 @RequestParam(defaultValue = "1") Integer page,
                                                 @RequestParam(defaultValue = "100") Integer pageSize) {
        MyPage<MaterialDto> myPage = materialServiceProxy.listMaterial(companyId, timestamp, page, pageSize);
        return ResponseEntity.ok(new SuccessResponse(myPage));
    }

    @ApiOperation(value = "供应商列表", response = CooperateEnterpriseDto.class)
    @Log(title = "免登 - 获取供应商列表", businessType = BusinessType.QUERY)
    @GetMapping("/cooperate/list")
    public ResponseEntity<Response> cooperateList(Integer companyId,
                                                  @RequestParam(defaultValue = "1") Integer page,
                                                  @RequestParam(defaultValue = "100") Integer pageSize, HttpServletRequest request) {
        //log.error("REQUEST IP ADDRESS :{}", IpUtil.getIpAddr(request));
        List<CooperateEnterpriseDto> list = cooperateServiceProxy.findByCompanyId(companyId, page, pageSize);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "地磅列表", response = WeighInfoDTO.class)
    @Log(title = "免登 - 获取地磅列表", businessType = BusinessType.QUERY)
    @GetMapping("/weighbridge/list")
    public ResponseEntity<Response> weighbridgeList(Integer companyId, Integer projectId) {
        List<WeighInfoDTO> list = materialWeighbridgeService.selectWeighInfo(companyId, projectId);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "车辆信息列表", response = TruckInfoDTO.class)
    @Log(title = "免登 - 获取车辆信息列表", businessType = BusinessType.QUERY)
    @GetMapping("/truck/list")
    public ResponseEntity<Response> truckList(Integer companyId, Integer projectId) {
        List<TruckInfoDTO> list = truckReportService.selectTruckNo(companyId, projectId);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "材料单位列表")
    @Log(title = "材料单位列表", businessType = BusinessType.QUERY)
    @GetMapping("/unit/list")
    public ResponseEntity<Response> materialUnitList(Integer companyId, Integer projectId) {
        List<String> list = contractDetailService.listUnitById(companyId, projectId);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "采购单列表")
    @Log(title = "采购单列表", businessType = BusinessType.QUERY)
    @GetMapping("/order/list")
    public ResponseEntity<Response> purchaseOrderList(Integer companyId, Integer projectId) {
        List<String> list = purchaseOrderService.listOrderById(companyId, projectId);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "mock地磅数据")
    @Log(title = "免登 - 造数据", businessType = BusinessType.OTHER)
    @PostMapping("/mock")
    public ResponseEntity<Response> mock(@Validated @Valid @RequestBody WeighbridgeAcceptDataDTO acceptDataDTO) {
        weighbridgeService.processWeighbridgeReceive(acceptDataDTO);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "发料-mock地磅数据")
    @Log(title = "免登 - 造数据", businessType = BusinessType.OTHER)
    @PostMapping("/mock/delivery")
    public ResponseEntity<Response> mockDelivery(@Validated @Valid @RequestBody WeighbridgeAcceptDataDTO acceptDataDTO) {
        iotMessageService.processPushData(acceptDataDTO);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "车辆预报备列表")
    @Log(title = "车辆预报备列表", businessType = BusinessType.QUERY)
    @GetMapping("/vehicle/list")
    public ResponseEntity<Response> vehicleInfoList(Integer companyId, Integer projectId) {
        List<VehicleInfoVO> list = truckReportDetailService.findVehicleList(companyId, projectId);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "报备车辆送货状态更改-单个")
    @Log(title = "报备车辆送货状态更改", businessType = BusinessType.UPDATE)
    @GetMapping("/update/{id}/{status}")
    public ResponseEntity<Response> updateTruckStatus(@PathVariable("id") String id, @PathVariable("status") Byte status) {
        truckReportDetailService.updateTruckStatus(id, status);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "报备车辆送货状态更改-批量")
    @Log(title = "报备车辆送货状态更改", businessType = BusinessType.UPDATE)
    @PostMapping("/updates")
    public ResponseEntity<Response> updateTrucksStatus(@RequestBody List<String> list) {
        List<PreTruckReportDetail> dtos = list.stream().map(e -> {
            PreTruckReportDetail preTruckReportDetail = new PreTruckReportDetail();
            preTruckReportDetail.setId(e);
            preTruckReportDetail.setStatus((byte) 2);
            return preTruckReportDetail;
        }).collect(Collectors.toList());
        truckReportDetailService.updateBatchById(dtos);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "送货车牌下拉")
    @Log(title = "送货车牌下拉", businessType = BusinessType.QUERY)
    @GetMapping("/truckNo/list")
    public ResponseEntity<Response> truckNoList(String code) {
        List<String> list = truckReportService.truckNoList(code);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "报备草稿状态回显")
    @Log(title = "报备草稿状态回显", businessType = BusinessType.QUERY)
    @GetMapping("/feedBack")
    public ResponseEntity<Response> feedBack(String code) {
        PreReportDetailVO result = new PreReportDetailVO();
        String viewKey = StrUtil.format(AppConstant.PAGE_VIEW_PREFIX, code);
        Map<Object, Object> map = redisUtil.hmget(viewKey);
        if (Objects.isNull(map)) {
            throw new BOException(BOExceptionEnum.EXT_PAGE_VIEW);
        }
        ExpirePageVO vo = BeanUtil.fillBeanWithMap(map, new ExpirePageVO(), true);
        if (vo.getCommit() > 1) {
            throw new BOException(BOExceptionEnum.EXT_PAGE_VIEW);
        }
        if (StrUtil.isNotBlank(vo.getId())) {
            result = preWeighReportService.selectDetail(vo.getId(), vo.getCompanyId());
        }
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "报备草稿状态提交")
    @Log(title = "报备草稿状态提交", businessType = BusinessType.INSERTORUPDATE)
    @PostMapping("/save")
    public ResponseEntity<Response> save(@RequestBody @Validated @Valid PreReportForm form) {
        String code = form.getCode();
        preWeighReportService.add(form, code);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "供应商填写页面-采购单信息")
    @Log(title = "供应商填写页面", businessType = BusinessType.QUERY)
    @GetMapping("/orderInfo")
    public ResponseEntity<Response> orderInfo(String code) {
        PurchaseSimpleVO result = null;
        String viewKey = StrUtil.format(AppConstant.PAGE_VIEW_PREFIX, code);
        Map<Object, Object> map = redisUtil.hmget(viewKey);
        if (Objects.isNull(map)) {
            throw new BOException(BOExceptionEnum.EXT_PAGE_VIEW);
        }
        ExpirePageVO vo = BeanUtil.fillBeanWithMap(map, new ExpirePageVO(), true);
        if (vo.getCommit() > 1) {
            throw new BOException(BOExceptionEnum.EXT_PAGE_VIEW);
        }
        PreWeighReport dto = preWeighReportService.lambdaQuery()
                .eq(PreWeighReport::getId, vo.getId())
                .one();
        if (ObjectUtil.isNotEmpty(dto)) {
            result = purchaseOrderService.selectOrderInfo(dto.getPurchaseOrderId(), vo.getCompanyId());
        }
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "供应商预报备 - 外部页面 - 过期检测", response = ExpirePageVO.class)
    @GetMapping("/external/expire/{code}")
    public ResponseEntity<Response> externalExpire(@PathVariable String code) {
        ExpirePageVO result = preWeighReportService.checkExternalCodeExpire(code);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "删除")
    @Log(title = "删除", businessType = BusinessType.DELETE)
    @PostMapping("/del/{id}")
    public ResponseEntity<Response> del(@PathVariable String id) {
        preTruckReportService.deletePreTruckReport(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "富阳推送http接口")
    @GetMapping("/pushFY")
    public ResponseEntity<Response> push() {
        List<MaterialDataForFY> list = materialDataService.getDataForFuYang();
        int size = list.size();
        PushEvent pushEvent = new PushEvent(this, list);
        applicationEventPublisher.publishEvent(pushEvent);
        String str = StrUtil.format("推送成功，共推送{}条数据", size);
        return ResponseEntity.ok(new SuccessResponse(str));
    }

    @ApiOperation(value = "地磅客户端心跳包发送")
    @PostMapping("/ping")
    public ResponseEntity<Response> ping(@RequestBody HeartBeatMessage message) {
        log.error("ping:{}", JSONUtil.toJsonStr(message));
        clientLogService.saveClientStatus(message);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "直取终端材料名称-历史遗留问题解决")
    @PostMapping("/solve")
    public ResponseEntity<Response> solve() {
        materialDataService.solve();
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "地磅客户端-称重数据同步")
    @PostMapping("/sync")
    public ResponseEntity<Response> sync(@RequestBody List<StandardMaterialDataForm> list) {
        log.error("processPushData pushDataDTO:{}", JSON.toJSON(list).toString());

        if (CollUtil.isNotEmpty(list)) {
            list.forEach(obj -> {
                List<StandardMaterialDataItemFrom> materialDataList = obj.getMaterialDataList();

                materialDataList.forEach(e -> {
                    e.setFlag(false);
                    String weighId = e.getWeighId();
                    if (StringUtils.isBlank(weighId)) {
                        log.warn("weighId为空");
                        e.setFlag(true);
                        return;
                    }
                    //weighId 幂等性检测
                    boolean idempotentCheck = iWeighbridgeService.weighIdIdempotentCheck(weighId);
                    if (idempotentCheck) {
                        log.warn("重复的weighId:{}", weighId);
                        e.setFlag(true);
                        return;
                    }
                    redisUtil.set(weighId, weighId, 30);

                    if (!e.getFlag()) {
                        iotMessageService.processPushData(obj);
                    }
                });

//                materialDataList = materialDataList.stream().filter(e -> !e.getFlag()).collect(Collectors.toList());
//                try {
//                    if (CollUtil.isNotEmpty(materialDataList)) {iotMessageService.processPushData(obj);}
//                } catch (Exception e) {
//                    e.getMessage();
//                }
            });
        }
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "查看照片")
    @GetMapping("/test")
    public ResponseEntity<Response> getUUIDS(String picStr) {
        List<String> list = new ArrayList<>();
        if (StrUtil.isNotBlank(picStr)) {
            List<String> uuids = StrUtil.split(picStr, ",");
            uuids = uuids.parallelStream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
            fileServiceProxy.confirmFiles(uuids, "material-management");
            list = fileServiceProxy.fileDownloadUrlByUUIDs(uuids);
        }
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation("脏数据清理")
    @PostMapping("/clear")
    public ResponseEntity<Response> clear(@RequestBody List<String> weighIds) {
        weighIds = weighIds.stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(weighIds)) {
            materialDataService.clear(weighIds);
        }
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "采购单详情", response = PurchasePdfVO.class)
    @Log(title = "采购单详情", businessType = BusinessType.QUERY)
    @PostMapping("/detail")
    public ResponseEntity<Response> detail(@RequestBody PurchaseQuery query) {
        PurchasePdfVO vo = purchaseOrderService.selectPdf(query);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "App-小程序追赶版-提交")
    @PostMapping("/submit")
    public ResponseEntity<Response> submit(@RequestBody @Validated @Valid PreReportNewForm form) {
        preWeighReportService.submit(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "自动创建合同本源对账")
    @GetMapping("/contractAuto")
    public ResponseEntity<Response> contractAuto() {
        List<PurchaseContract> contractList = purchaseContractMapper.selectContractAuto();
        if (CollUtil.isNotEmpty(contractList)) {
            List<MaterialVerify> result = new ArrayList<>();

            contractList.stream().forEach(e -> {
                List<MaterialVerify> collect = StrUtil.split(e.getBelongProjectId(), ",").stream().map(m -> {
                    MaterialVerify materialVerify = new MaterialVerify();
                    Integer projectId = Integer.valueOf(m);
                    String verifyNo = noUtil.getAutoVerifyNo(projectId, e.getName());
                    materialVerify.setIsOrigin((byte) 1);
                    materialVerify.setVerifyNo(verifyNo);
                    materialVerify.setVerifyProjectId(projectId);
                    materialVerify.setSupplierId(String.valueOf(e.getSupplierId()));
                    materialVerify.setContractId(e.getId());
                    materialVerify.setStatus((byte) 0);
                    materialVerify.setFileTime(LocalDateTime.now());
                    materialVerify.setCompanyId(e.getCompanyId());
                    materialVerify.setProjectId(projectId);

                    return materialVerify;
                }).collect(Collectors.toList());

                result.addAll(collect);
            });

            materialVerifyService.saveBatch(result);
        }
        return ResponseEntity.ok(new SuccessResponse());
    }


    @ApiOperation(value = "手动同步供应商")
    @GetMapping("/sync/supplier")
    public ResponseEntity<Response> syncSupplier() {
        supplierService.updateSupplierList();
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "查看oss文件地址")
    @GetMapping("/oss/{fileuuid}")
    public ResponseEntity<Response> view(@PathVariable String fileuuid) {
        String url = fileServiceProxy.fileDownloadUrlByUUID(fileuuid);
        return ResponseEntity.ok(new SuccessResponse(url));
    }

    @ApiOperation(value = "手动同步合同")
    @GetMapping("/sync/contract")
    public ResponseEntity<Response> syncContract() {
        crccContractSyncTask.syncTask();
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "通过外部项目编号获取桩桩项目id")
    @GetMapping("/getProject/{projectCode}")
    public ResponseEntity<Response> getProject(@PathVariable("projectCode") Long projectCode) {
        Integer projectId = projectServiceProxy.getProjectIdByCode(COMPANY_ID, projectCode);
        return ResponseEntity.ok(new SuccessResponse(projectId));
    }


    @ApiOperation(value = "外部收货列表查询", response = CrccMaterialWeighInfoDto.class)
    @Log(title = "地磅收货列表", businessType = BusinessType.QUERY)
    @PostMapping("/extWeightList")
    public CrccResponseDTO<IPage<CrccMaterialWeighInfoDto>> extWeightList(@RequestBody ztcjExtWeightListQuery query) {
        try {
            Integer projectId = projectServiceProxy.getProjectIdByCode(COMPANY_ID, query.getProjectCode());
            if (projectId == null) {
                log.info("外部项目code:{},企业id:{}无法查询到", query.getProjectCode(), COMPANY_ID);
                return CrccResponseDTO.success(new Page<>());
            }
            Integer materialId = null;
            if (query.getMaterialCode() != null) {
                MaterialDto materialDto = materialServiceProxy.materialByExtCode(query.getMaterialCode());
                if (materialDto == null || materialDto.getMaterialId() == null) {
                    return CrccResponseDTO.success(new Page<>());
                }
                materialId = materialDto.getMaterialId();
            }
            long size = query.getSize() > 500 ? 500 : query.getSize();
            query.setSize(size);
            IPage<CrccMaterialWeighInfoDto> materialWeighInfoVOList = materialSendReceiveService.extWeightList(projectId, materialId, query);
            return CrccResponseDTO.success(materialWeighInfoVOList);
        } catch (Exception e) {
            log.error("外部收货列表查询异常,projectCode:{},materialCode:{}", query.getProjectCode(), query.getMaterialCode(), e);
            return CrccResponseDTO.fail();
        }
    }

    @PostMapping("/ocr/sync")
    public ResponseEntity<Response> OCRSync(@RequestBody ReceiptRecyclePushDTO dto) {
        log.info("收到单据回收:{}", dto);
        List<String> fileIds = new ArrayList<>();
        String primaryCode = dto.getPrimaryCode();
        ProjectVO projectDTO = projectServiceProxy.getProjectById(Integer.valueOf(primaryCode));
        if (ObjectUtil.isNull(projectDTO)) {
            throw new BOException(BOExceptionEnum.PROJECT_ERROR);
        }
        Integer projectId = projectDTO.getProjectId();
        Integer companyId = projectDTO.getCompanyId();

        try {
            materialDataSaveService.OCRSync(dto, fileIds,companyId , projectId);
        } catch (Exception e) {
            log.error("OCRSync异常", e);
            fileIds = fileIds.stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(fileIds)) {
                fileServiceProxy.deleteFilesByUUIDs(fileIds);
            }
            throw new BOException(BOExceptionEnum.LOGIC_ERROR.errorCode(), "OCRSync异常");
        }
        // 回调确认
        SdkConfig sdkConfig = sdkConfigService.lambdaQuery().eq(SdkConfig::getCompanyId, companyId).eq(SdkConfig::getProjectId, projectId).one();
        log.error("host:{},sdkConfig:{}", sdkHost, JSON.toJSONString(sdkConfig));
        Material materialClient = new MaterialClientBuilder().build(sdkHost, sdkConfig.getAppKey(), sdkConfig.getAppSecretKey());
        materialClient.receiptRecyclePushConfirm(dto.getId());
        log.error("OCR同步完成");
        return ResponseEntity.ok(new SuccessResponse());
    }

    @Resource
    private IMaterialWarningService materialWarningService;

    @GetMapping("/test/alarm")
    public void alarm(HttpServletResponse response) {
        List<MaterialWarning> list = materialWarningService.list();
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<MaterialWarningExcelDTO> excelDTOS = BeanUtil.copyToList(list, MaterialWarningExcelDTO.class);
        ExcelUtils.export(response, excelDTOS, MaterialWarningExcelDTO.class, "test.xlsx");
    }

}
