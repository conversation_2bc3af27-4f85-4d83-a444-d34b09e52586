package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 拌合站配料通知明细
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 09:54:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_ingredient_notice_detail")
@ApiModel(value = "IngredientNoticeDetail对象", description = "拌合站配料通知明细")
public class IngredientNoticeDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "通知单/确认单id")
    @TableField("ingredient_notice_id")
    private String ingredientNoticeId;

    @ApiModelProperty(value = "配比材料id")
    @TableField("material_id")
    private Integer materialId;

    @ApiModelProperty(value = "配比材料名称(水专用)")
    @TableField("material_name")
    private String materialName;

    @ApiModelProperty(value = "计量单位")
    @TableField("unit")
    private String unit;

    @ApiModelProperty(value = "每单位用量(kg)")
    @TableField("unit_usage")
    private BigDecimal unitUsage;

    @ApiModelProperty(value = "每盘用量(kg)")
    @TableField("per_usage")
    private BigDecimal perUsage;

    @ApiModelProperty(value = "含水率")
    @TableField("moisture_content")
    private BigDecimal moistureContent;

    @ApiModelProperty(value = "每单位用量(kg)(结果)")
    @TableField("unit_usage_result")
    private BigDecimal unitUsageResult;

    @ApiModelProperty(value = "每盘用量(kg)(结果)")
    @TableField(value = "per_usage_result",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal perUsageResult;

    @ApiModelProperty(value = "尾盘每盘用量(kg)")
    @TableField("tail_per_usage")
    private BigDecimal tailPerUsage;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
