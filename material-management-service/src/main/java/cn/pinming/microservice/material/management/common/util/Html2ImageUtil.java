package cn.pinming.microservice.material.management.common.util;

import cn.hutool.core.util.StrUtil;
import cn.pinming.core.upload.AliyunOssUploadComponent;
import cn.pinming.core.upload.OssFile;
import cn.pinming.core.upload.UploadConfig;
import cn.pinming.core.upload.domain.enums.FileTypeEnums;
import cn.pinming.model.FileInfos;
import cn.pinming.v2.common.api.service.FileService;
import freemarker.cache.ClassTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import freemarker.template.TemplateExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.xhtmlrenderer.swing.Java2DRenderer;

import javax.imageio.ImageIO;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/22
 */
@Slf4j
@Component
public class Html2ImageUtil {

    @Value("${temporary.path}")
    private String temporaryPath;
    @Reference
    private FileInfos fileInfos;
    @Reference
    private FileService fileService;

    private String getTemplate(String ftlName, Map<String, Object> params) throws IOException, TemplateException {
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_25);
        cfg.setTemplateLoader(new ClassTemplateLoader(Thread.currentThread().getContextClassLoader(), "/templates"));
        cfg.setDefaultEncoding("UTF-8");
        cfg.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
        cfg.setLogTemplateExceptions(false);
        Template temp = cfg.getTemplate(ftlName, "UTF-8");
        StringWriter stringWriter = new StringWriter();
        temp.process(params, stringWriter);
        stringWriter.flush();
        stringWriter.close();
        String resutl = stringWriter.getBuffer().toString();
        return resutl;
    }

    public String turnImage(String ftlName, Map<String, Object> params) {
        ByteArrayInputStream bin = null;
        ByteArrayOutputStream dataOutputStream = null;
        try {
            String html = getTemplate(ftlName, params).replaceAll("&", "&amp;");
            byte[] bytes = html.getBytes("UTF-8");

            bin = new ByteArrayInputStream(bytes);
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(bin);

            Java2DRenderer renderer = new Java2DRenderer(document, 1000, 1500);

            BufferedImage img = renderer.getImage();
            // 转成流上传至服务器
            dataOutputStream = new ByteArrayOutputStream();
            ImageIO.write(img, "png", dataOutputStream);
            byte[] bts = dataOutputStream.toByteArray();
            return uploadFile(bts);
        } catch (Exception e) {
            log.error("Html2ImageUtil turnImage error. message:{}", e.getMessage());
        } finally {
            if (bin != null) {
                try {
                    bin.close();
                } catch (Exception e) {
                    log.error("Html2ImageUtil turnImage ByteArrayInputStream close error.", e.getMessage());
                }
            }
            if (dataOutputStream != null) {
                try {
                    dataOutputStream.close();
                } catch (Exception e) {
                    log.error("Html2ImageUtil turnImage ByteArrayOutputStream close error.", e.getMessage());
                }
            }
        }
        return null;
    }

    private String uploadFile(byte[] file) {
        String filePath = temporaryPath + StrUtil.format("{}", System.currentTimeMillis() + ".jpg");
        try {
            FileOutputStream fos = new FileOutputStream(filePath);
            ByteArrayInputStream in = new ByteArrayInputStream(file);
            byte[] b = new byte[1024];
            int n;
            while ((n = in.read(b)) != -1) {
                fos.write(b, 0, n);
            }
            fos.flush();
            in.close();
            fos.close();
        } catch (Exception e) {
            log.error("文件存储到临时目录出错", e.getMessage());
        }

        File tmp = new File(filePath);
        AliyunOssUploadComponent aliyunOssUploadComponent = new AliyunOssUploadComponent(fileInfos);
        OssFile ossFile = new OssFile(tmp, "jpg", FileTypeEnums.IMAGE);
        UploadConfig uploadConfig = new UploadConfig("ff8080815afee284015afee408680001");
        try {
            String uuid = aliyunOssUploadComponent.uploadFile(ossFile, uploadConfig);
            fileService.confirmFile(uuid, "material-management");
            String url = fileService.fileDownloadUrlByUUID(uuid);
            aliyunOssUploadComponent.close();
            return url;
        } catch (Exception e) {
            log.error("Html2ImageUtil uploadFile error.", e.getMessage());
        }
        return "";
    }

}
