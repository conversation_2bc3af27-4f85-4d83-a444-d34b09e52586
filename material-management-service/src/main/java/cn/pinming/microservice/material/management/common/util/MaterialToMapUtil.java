package cn.pinming.microservice.material.management.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class MaterialToMapUtil {

    @Resource
    private MaterialServiceProxy materialServiceProxy;

    public Map<Integer, MaterialDto> materialDtoMap(List<Integer> materialIdList) {
        Map<Integer, MaterialDto> map = new HashMap<>();

        List<Integer> list = materialIdList.stream().distinct().collect(Collectors.toList());
        List<MaterialDto> materialDtos = materialServiceProxy.listMaterialByIds(list);
        if (CollUtil.isNotEmpty(materialDtos)) {
            map = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, e -> e));
        }

        return map;
    }

}
