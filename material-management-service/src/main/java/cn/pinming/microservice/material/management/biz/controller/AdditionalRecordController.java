package cn.pinming.microservice.material.management.biz.controller;

import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.microservice.material.management.biz.form.AdditionalRecordForm;
import cn.pinming.microservice.material.management.biz.service.IAdditionalRecordSerivce;
import cn.pinming.microservice.material.management.biz.service.ICompanyConfigService;
import cn.pinming.microservice.material.management.biz.service.IMaterialHandlerService;
import cn.pinming.microservice.material.management.common.SuccessResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@Api(tags = "物料管理--手工补录")
@RestController
@Slf4j
@RequestMapping("/api/material/additionalRecord")
public class AdditionalRecordController {
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private IMaterialHandlerService materialHandlerService;
    @Resource
    private ICompanyConfigService companyConfigService;
    @Resource
    private IAdditionalRecordSerivce additionalRecordService;

    @ApiOperation(value = "是否有权限处理")
    @PostMapping("/enable")
    public ResponseEntity<Response> enableHandle() {
        String id = authUserHolder.getCurrentUser().getId();
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
        Integer projectId = authUserHolder.getCurrentUser().getCurrentProjectId();
        Boolean enable = materialHandlerService.enableHandle(id, (byte)2);
        Boolean companyEnable = companyConfigService.enable(companyId,projectId,(byte)6);
        return ResponseEntity.ok(new SuccessResponse(enable && companyEnable));
    }

    @ApiOperation(value = "手工补单")
    @PostMapping("/add")
    public ResponseEntity<Response> add(@Validated @Valid @RequestBody AdditionalRecordForm form) {
        additionalRecordService.additionalRecord(form);
        return ResponseEntity.ok(new cn.pinming.core.web.response.SuccessResponse());
    }
}
