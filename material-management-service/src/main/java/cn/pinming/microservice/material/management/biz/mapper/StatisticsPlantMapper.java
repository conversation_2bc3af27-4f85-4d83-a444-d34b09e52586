package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.MixingPlantMachine;
import cn.pinming.microservice.material.management.biz.query.MachineCapacityQuery;
import cn.pinming.microservice.material.management.biz.query.MachineExitDetailQuery;
import cn.pinming.microservice.material.management.biz.vo.MachineExitDetailVO;
import cn.pinming.microservice.material.management.biz.vo.RawMaterialDemandStockVO;
import cn.pinming.microservice.material.management.biz.vo.RawMaterialSendReceiveVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/22
 */
public interface StatisticsPlantMapper {

    /**
     * 查询配置：二级分类-单位-材料
     * @param projectIds
     * @param categoryIds
     * @return
     */
    List<StatisticsPlantCategoryDTO> queryStatisticsPlantCategory(@Param("projectIds") List<Integer> projectIds,
                                                                  @Param("categoryIds") List<Integer> categoryIds);

    /**
     * 查询计划需求量
     * @param projectIds
     * @param unit
     * @param materialIds
     * @return
     */
    List<StatisticsPlantDTO> queryPlanCount(@Param("projectIds") List<Integer> projectIds,
                                            @Param("unit") String unit,
                                            @Param("materialIds") List<Integer> materialIds);

    /**
     * 查询出站量
     * @param projectIds
     * @param unit
     * @param materialIds
     * @return
     */
    List<StatisticsPlantDTO> queryExitCount(@Param("projectIds") List<Integer> projectIds,
                                            @Param("unit") String unit,
                                            @Param("materialIds") List<Integer> materialIds);

    /**
     * 截止到某天的计划需求量
     * @param time
     * @param projectIds
     * @param unit
     * @param materialIds
     * @return
     */
    BigDecimal queryCutoffSomedayPlanCount(@Param("time") String time,
                                           @Param("projectIds") List<Integer> projectIds,
                                           @Param("unit") String unit,
                                           @Param("materialIds") List<Integer> materialIds);

    /**
     * 截止到某天的出站量
     * @param time
     * @param projectIds
     * @param unit
     * @param materialIds
     * @return
     */
    BigDecimal queryCutoffSomedayExitCount(@Param("time") String time,
                                           @Param("projectIds") List<Integer> projectIds,
                                           @Param("unit") String unit,
                                           @Param("materialIds") List<Integer> materialIds);

    /**
     * 查询时间范围内的计划需求量
     * @param start
     * @param end
     * @param projectIds
     * @param unit
     * @param materialIds
     * @return
     */
    List<StatisticsPlantDTO> queryTimeRangePlanCount(@Param("start") String start,
                                                     @Param("end") String end,
                                                     @Param("projectIds") List<Integer> projectIds,
                                                     @Param("unit") String unit,
                                                     @Param("materialIds") List<Integer> materialIds);

    /**
     * 查询时间范围内的出站量
     * @param start
     * @param end
     * @param projectIds
     * @param unit
     * @param materialIds
     * @return
     */
    List<StatisticsPlantDTO> queryTimeRangeExitCount(@Param("start") String start,
                                                     @Param("end") String end,
                                                     @Param("projectIds") List<Integer> projectIds,
                                                     @Param("unit") String unit,
                                                     @Param("materialIds") List<Integer> materialIds);

    /**
     * 总览--成品生产出站量（昨日、本月、本年）
     * @param projectIds
     * @return
     */
    List<StatisticsOverviewPlantExitDTO> queryOverviewExitCount(@Param("projectIds") List<Integer> projectIds);

    /**
     * 查询某天的计划需求量-按拌合站分组
     * @param time
     * @param projectIds
     * @param unit
     * @param materialIds
     * @return
     */
    List<StatisticsPlantDTO> querySomedayPlanCount(@Param("time") String time,
                                                   @Param("projectIds") List<Integer> projectIds,
                                                   @Param("unit") String unit,
                                                   @Param("materialIds") List<Integer> materialIds);

    /**
     * 截止到某天的计划需求量之和-按拌合站分组
     * @param time
     * @param projectIds
     * @param unit
     * @param materialIds
     * @return
     */
    List<StatisticsPlantDTO> queryCutoffSomedayPlanCountGroupByPlant(@Param("time") String time,
                                                                     @Param("projectIds") List<Integer> projectIds,
                                                                     @Param("unit") String unit,
                                                                     @Param("materialIds") List<Integer> materialIds);

    /**
     * 截止到某天的出站量之和-按拌合站分组
     * @param time
     * @param projectIds
     * @param unit
     * @param materialIds
     * @return
     */
    List<StatisticsPlantDTO> queryCutoffSomedayExitCountGroupByPlant(@Param("time") String time,
                                                                     @Param("projectIds") List<Integer> projectIds,
                                                                     @Param("unit") String unit,
                                                                     @Param("materialIds") List<Integer> materialIds);

    /**
     * 查询某天的出站量-按拌合站分组
     * @param time
     * @param projectIds
     * @param unit
     * @param materialIds
     * @return
     */
    List<StatisticsPlantDTO> querySomedayExitCount(@Param("time") String time,
                                                   @Param("projectIds") List<Integer> projectIds,
                                                   @Param("unit") String unit,
                                                   @Param("materialIds") List<Integer> materialIds);

    /**
     * 机组产能走势
     * @param machineCapacityQuery query
     * @param projectIds 项目ids
     * @return list
     */
    List<MachineCapacityDTO> queryMachineCapacity(@Param("query") MachineCapacityQuery machineCapacityQuery,
                                                  @Param("projectIds") List<Integer> projectIds,
                                                  @Param("materialIds") List<Integer> materialIds);

    /**
     * 机组生产出站明细
     * @param machineExitDetailQuery
     * @return
     */
    List<MachineExitDetailVO> queryMachineExitDetail(@Param("query") MachineExitDetailQuery machineExitDetailQuery,
                                                     @Param("projectIds") List<Integer> projectIds,
                                                     @Param("materialIds") List<Integer> materialIds);
    /**
     * 原料需求与库存分析-需求量
     * @param projectIds
     * @param deadline
     * @return
     */
    List<RawMaterialDemandStockVO> queryRawMaterialDemand(@Param("projectIds") List<Integer> projectIds,
                                                          @Param("deadline") String deadline);
    /**
     * 原料日耗用趋势分析-日耗量
     * @param projectIds
     * @param start
     * @param end
     * @return
     */
    List<PlantRawMaterialDailyConsumptionDTO> queryRawMaterialDailyConsumption(@Param("projectIds") List<Integer> projectIds,
                                                                               @Param("start") String start,
                                                                               @Param("end") String end);

    /**
     * 原料收发存分析-耗用量
     * @param projectIds
     * @return
     */
    List<RawMaterialSendReceiveVO> queryRawMaterialSendReceive(@Param("projectIds") List<Integer> projectIds);

    /**
     * 原料节超分析-指定物料账面实耗
     * @param start
     * @param end
     * @param projectIds
     * @param materialIds
     * @return
     */
    List<RawMaterialPaperInventoryDTO> queryRawMaterialPaperInventory(@Param("start") String start,
                                                                      @Param("end") String end,
                                                                      @Param("projectIds") List<Integer> projectIds,
                                                                      @Param("materialIds") List<Integer> materialIds);

    /**
     * 原料节超分析-累计节超量--不同物料对应最近盘点的账面实耗
     * @param projectIds
     * @return
     */
    List<RawMaterialPaperInventoryDTO> queryAllIngredientRawMaterialPaperInventory(@Param("projectIds") List<Integer> projectIds,
                                                                                   @Param("materialTimes") List<PaperInventoryMaterialTime> materialTimes);

    List<PaperInventoryPeriodsDTO> queryRawMaterialPaperInventoryPeriods(@Param("start") String start,
                                                                         @Param("end") String end,
                                                                         @Param("projectIds") List<Integer> projectIds,
                                                                         @Param("materialIds") List<Integer> materialIds);

    List<String> queryMachineList(@Param("materialIds") List<Integer> materialIds,
                                              @Param("projectIds") List<Integer> projectIds,
                                              @Param("unit") String unit);
}
