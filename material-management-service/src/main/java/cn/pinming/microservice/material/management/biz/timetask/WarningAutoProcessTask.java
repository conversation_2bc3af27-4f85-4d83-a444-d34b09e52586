package cn.pinming.microservice.material.management.biz.timetask;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.pinming.microservice.material.management.biz.entity.MaterialWarning;
import cn.pinming.microservice.material.management.biz.mapper.MaterialWarningMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.pinming.microservice.material.management.biz.enums.WarningSubTypeEnum.*;
import static cn.pinming.microservice.material.management.biz.enums.WarningTypeEnum.*;
import static cn.pinming.microservice.material.management.biz.timetask.strategy.WarningAutoProcessStrategy.ALL_STRATEGY;

/**
 * 预警自动处理定时任务
 * <AUTHOR>
 * @date 2022/6/14
 */
@Slf4j
@Component
public class WarningAutoProcessTask {

    // 需要自动处理的预警类型
    private static List<AutoProcessWarningType> AUTO_PROCESS_WARNING_TYPE = Lists.newArrayList();

    static {
        AutoProcessWarningType weighConvertWarning = AutoProcessWarningType.builder().type(WEIGH_CONVERT.value()).subTypes(Arrays.asList(WEIGH_CONVERT_ONE.subType(), WEIGH_CONVERT_TWO.subType())).build();
        AutoProcessWarningType receiveNumberWarning = AutoProcessWarningType.builder().type(RECEIVE_NUMBER.value()).subTypes(Arrays.asList(RECEIVE_NUMBER_ONE.subType())).build();
        AutoProcessWarningType chargeUnitWarning = AutoProcessWarningType.builder().type(CHARGE_UNIT.value()).subTypes(Arrays.asList(CHARGE_UNIT_ONE.subType(), CHARGE_UNIT_TWO.subType())).build();
        AutoProcessWarningType invalidWeightWarning = AutoProcessWarningType.builder().type(INVALID_WEIGHT.value()).subTypes(Arrays.asList(INVALID_WEIGHT_ONE.subType())).build();
        AutoProcessWarningType materialNameWarning = AutoProcessWarningType.builder().type(MATERIAL_NAME.value()).subTypes(Arrays.asList(MATERIAL_NAME_ONE.subType(), MATERIAL_NAME_TWO.subType())).build();
        AUTO_PROCESS_WARNING_TYPE.add(weighConvertWarning);
        AUTO_PROCESS_WARNING_TYPE.add(receiveNumberWarning);
        AUTO_PROCESS_WARNING_TYPE.add(chargeUnitWarning);
        AUTO_PROCESS_WARNING_TYPE.add(invalidWeightWarning);
        AUTO_PROCESS_WARNING_TYPE.add(materialNameWarning);
    }

    @Value("${warning.auto.process.switch}")
    private Boolean enable;

    @Resource
    private MaterialWarningMapper warningMapper;

    @Scheduled(cron = "0 0/10 * * * ?")
    public void warningAutoProcess() {
//        if (!enable) {
//            log.info("WarningAutoProcessTask warningAutoProcess enable: false");
//            return;
//        }
        log.error("WarningAutoProcessTask warningAutoProcess: start");
        // 查询所有需要自动处理的预警
        List<MaterialWarning> materialWarnings = warningMapper.queryAllMaterialWarning(AUTO_PROCESS_WARNING_TYPE);
        if (CollectionUtil.isEmpty(materialWarnings)) {
            log.error("WarningAutoProcessTask warningAutoProcess: no warning to deal with");
            return;
        }
        // 分组
        Map<Byte, List<MaterialWarning>> warningTypeMap = materialWarnings.stream().collect(Collectors.groupingBy(MaterialWarning::getWarningType));
        // 遍历执行策略
//        warningTypeMap.forEach((type, warnings) -> ALL_STRATEGY.get(type).autoProcessWay(warnings));

        warningTypeMap.forEach((type, warnings) -> {
            List<List<MaterialWarning>> split = ListUtil.split(warnings, 100);
            for (List<MaterialWarning> subList : split) {
                ALL_STRATEGY.get(type).autoProcessWay(subList);
            }
        });
        log.error("WarningAutoProcessTask warningAutoProcess: end");
    }


}
