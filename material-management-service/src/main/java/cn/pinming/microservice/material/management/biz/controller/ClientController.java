package cn.pinming.microservice.material.management.biz.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.service.IMaterialWeighbridgeService;
import cn.pinming.microservice.material.management.biz.vo.ProjectVO;
import cn.pinming.microservice.material.management.common.util.DownloadUtil;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;

/**
 * <AUTHOR> hao
 * @version 1.0.0
 * @since 2022/9/9 10:04
 */
@Slf4j
@RestController
@RequestMapping("/api/client")
public class ClientController {

    @Resource
    private AuthUserHolder authUserHolder;

    @Resource
    private ProjectServiceProxy projectServiceProxy;

    @Resource
    private IMaterialWeighbridgeService weighbridgeService;

    @Value("${temporary.path}")
    private String temporaryPath;

    private static final String JAR_CLASS_PATH = "client/MaterialDataClient.jar";
    private static final String YML_CLASS_PATH = "client/application.tmp.yml";


    /**
     * 1.   jar tvf MaterialDataClient.jar|grep application.yml  获取配置路径 (pass)
     * 2.   jar xvf MaterialDataClient.jar BOOT-INF/classes/application.yml 解压配置路径 (pass)
     * 3.   新建yml
     * 4.   jar uvf MaterialDataClient.jar BOOT-INF/classes/application.yml 更新配置路径
     *
     * @param sn 设备sn
     * @return
     */
    @ApiOperation(value = "地磅客户端下载")
    @GetMapping("/download/{projectId}/{sn}")
    public void init(@PathVariable Integer projectId, @PathVariable String sn, HttpServletResponse response) {
        AuthUser user = authUserHolder.getCurrentUser();
        Integer companyId = user.getCurrentCompanyId();

        ProjectVO project = projectServiceProxy.getProjectById(projectId);
        String projectName = project.getProjectTitle();

        Runtime runtime = Runtime.getRuntime();
        //客户端、配置文件暂存路径
        String clientTempPath = StrUtil.format("{}/{}/{}/{}/", temporaryPath, companyId, projectId, sn);
        String ymlTempPath = StrUtil.format("{}BOOT-INF/classes/application.yml", clientTempPath);
        try {
            //读取客户端文件 复制客户端
            ClassPathResource clientResource = new ClassPathResource(JAR_CLASS_PATH);
            InputStream clientIn = clientResource.getInputStream();
            String filePath = StrUtil.format("{}MaterialDataClient-{}-{}.jar", clientTempPath, sn, System.currentTimeMillis());
            File exportFile = new File(filePath);
            FileUtil.writeFromStream(clientIn, exportFile);

            //生成新的yml文件
            ClassPathResource ymlResource = new ClassPathResource(YML_CLASS_PATH);
            String sourceYml = IoUtil.read(ymlResource.getInputStream(), Charset.defaultCharset());
            //boolean isIot = isIotUploadType(projectId, sn);
            String targetYml = StrUtil.format(sourceYml, projectId, projectName, true, sn);
            FileWriter writer = new FileWriter(new File(ymlTempPath));
            writer.write(targetYml);

            //打包
            String execCmd = StrUtil.format("cd {}; jar uvf {} BOOT-INF/classes/application.yml", clientTempPath, filePath);
            log.error("执行命令:{}", execCmd);
            String[] cmd = {"/bin/sh", "-c", execCmd};
            Process exec = runtime.exec(cmd);
            int result = exec.waitFor();
            if (result == 0) {
                //下载
                DownloadUtil.download(exportFile, "MaterialDataClient.jar", response);
                log.error("{}-{}地磅客户端打包完成", companyId, projectId);
            }
            exec.destroyForcibly();
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        } finally {
            //删除文件
            FileUtil.del(clientTempPath);
        }
    }

//    private boolean isIotUploadType(Integer projectId, String sn) {
//        boolean isIot = false;
//        Integer count = weighbridgeService.lambdaQuery().eq(MaterialWeighbridge::getWeighSystemNo, sn)
//                .eq(MaterialWeighbridge::getProjectId, projectId).eq(MaterialWeighbridge::getUploadType, UpdateTypeEnum.IOT.getValue()).count();
//        if (count > 0) {
//            isIot = true;
//        }
//        return isIot;
//    }

}
