package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.core.cookie.AuthUser;
import cn.pinming.materialManagement.dto.TruckInfoDTO;
import cn.pinming.microservice.material.management.biz.entity.TruckReport;
import cn.pinming.microservice.material.management.biz.vo.TruckReportVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 车辆报备 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-09-06 14:04:33
 */
public interface ITruckReportService extends IService<TruckReport> {

    /**
     * 车辆报备
     *
     * @param user
     * @param truckNo
     */
    void add(AuthUser user, String truckNo);

    void addTruckReport(TruckReport truckReport);

    /**
     * 车辆报备列表
     *
     * @param user
     * @param truckNo
     * @return
     */
    List<TruckReportVO> listTruckReport(AuthUser user, String truckNo);

    /**
     * 显示项目级下车辆信息
     *
     * @param companyId
     * @param projectId
     * @return
     */
    List<TruckInfoDTO> selectTruckNo(Integer companyId, Integer projectId);

    TruckReport gettTruckInfo(Integer projectId, String truckNo);

    /**
     * 删除车辆
     *
     * @param user
     * @param id
     */
    void del(AuthUser user,String id);

    List<String> truckNoList(String code);




}
