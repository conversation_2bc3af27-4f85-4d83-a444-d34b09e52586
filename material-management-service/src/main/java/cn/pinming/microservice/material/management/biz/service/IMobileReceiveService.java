package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.dto.QrcodeInfoDTO;
import cn.pinming.microservice.material.management.biz.entity.MobileReceive;
import cn.pinming.microservice.material.management.biz.form.*;
import cn.pinming.microservice.material.management.biz.query.DeviationInfoQuery;
import cn.pinming.microservice.material.management.biz.query.MobileWeighInfoQuery;
import cn.pinming.microservice.material.management.biz.query.SummaryDeliveryQuery;
import cn.pinming.microservice.material.management.biz.query.WeighInfoQuery;
import cn.pinming.microservice.material.management.biz.vo.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 移动收料表 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:30:10
 */
public interface IMobileReceiveService extends IService<MobileReceive> {

    /**
     * 新增收料记录
     *
     * @param form
     */
    void add(MobileReceiveForm form);

    /**
     * 收料单详情
     *
     * @param receiveId
     * @return
     */
    ReceiveCardDetailVO detail(String receiveId);

    /**
     * 统计移动收料信息
     *
     * @return
     */
    SummaryAnalysisVO countMobileReceiveSummary(SummaryDeliveryQuery query);

    WeighInfoVO select(MobileWeighInfoQuery query);

    IPage<WeighDeviationDetailVO> selectDeviationList(DeviationInfoQuery query);


    /**
     * 根据收料id获取收料类型
     * @param warningSourceId
     * @return
     */
    Byte getReceiveTypeByReceiveId(String warningSourceId);

    QrcodeInfoDTO processQrcodeInfo(String input);

    void checkBillQrcode(QrcodeInfoDTO qrcodeInfoDTO);

    WeighReceiveResultVO weighReceiveCommit(WeighReceiveCommitForm form);

    void billFix(WeighReceiveFixForm form);

    Map<String, WeighCarVO> showWeighCar(WeighInfoQuery query);

    SuccessOrFailVO batch(List<MobileMaterialBatchForm> list);

    void fresh(MobileReceiveUpdateForm form);
}
