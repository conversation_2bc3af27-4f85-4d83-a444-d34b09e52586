package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.entity.IngredientList;
import cn.pinming.microservice.material.management.biz.form.IngredientForm;
import cn.pinming.microservice.material.management.biz.form.NoticeConfirmForm;
import cn.pinming.microservice.material.management.biz.form.NoticeForm;
import cn.pinming.microservice.material.management.biz.query.IngredientQuery;
import cn.pinming.microservice.material.management.biz.vo.IngredientListVO;
import cn.pinming.microservice.material.management.biz.vo.IngredientPageVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 拌合站配料单 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 09:54:01
 */
public interface IIngredientListService extends IService<IngredientList> {

    String addOrUpdateFirst(IngredientForm ingredientForm);

    void addOrUpdateSecond(NoticeForm form);

    void addOrUpdateThird(NoticeConfirmForm form);

    void file(String id);

    IngredientListVO detail(String id);

    IPage<IngredientPageVO> selectPage(IngredientQuery query);

    Map<Byte, Integer> statistics();

    Map<String,List<String>> requirement();
}
