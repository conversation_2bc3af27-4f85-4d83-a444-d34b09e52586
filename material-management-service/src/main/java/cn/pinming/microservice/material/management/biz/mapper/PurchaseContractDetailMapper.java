package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.ContractDecisionDTO;
import cn.pinming.microservice.material.management.biz.dto.ContractDetailDTO;
import cn.pinming.microservice.material.management.biz.dto.ContractMaterialDetailDTO;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContractDetail;
import cn.pinming.microservice.material.management.biz.vo.ContractMaterialVO;
import cn.pinming.microservice.material.management.biz.vo.GoodsSimpleVO;
import cn.pinming.microservice.material.management.biz.vo.PurchaseContractDetailVO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 采购合同物料明细 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface PurchaseContractDetailMapper extends BaseMapper<PurchaseContractDetail> {

    List<ContractDetailDTO> selectByContractIds(@Param("contractIds") List<String> contractIds);

    @InterceptorIgnore(tenantLine = "true")
    List<ContractDetailDTO> selectByContractDetailIds(@Param("ids") List<String> contractIds);

    @InterceptorIgnore(tenantLine = "true")
    List<String> selectUnitById(@Param("companyId")Integer companyId, @Param("projectId")Integer projectId);

    @InterceptorIgnore(tenantLine = "true")
    Integer count(@Param("contractId") String contractId, @Param("ids") List<String> contractDetailId );

    @InterceptorIgnore(tenantLine = "true")
    List<PurchaseContractDetail> list(@Param("id")String id);

    @InterceptorIgnore(tenantLine = "true")
    List<String> listRemoveIdsById(@Param("contractId")String contractId,@Param("ids")List<Integer> materialIdList);

    @InterceptorIgnore(tenantLine = "true")
    void remove(@Param("ids")List<String> removeIds);

    List<ContractMaterialDetailDTO> listMaterial(@Param("contractId")String contractId);

    ContractMaterialVO selectInfo(@Param("contractId")String contractId,@Param("materialId")Integer materialId);

    ContractDecisionDTO selectThreshold(@Param("contractDetailId") String contractDetailId);

    List<PurchaseContractDetailVO> selectContractDetails(@Param("id") String id);

    List<ContractDecisionDTO> selectByContractDetailIdList(@Param("contractDetailIds") List<String> contractDetailIds);

    List<GoodsSimpleVO> history(@Param("contractId") String contractId);

    List<PurchaseContractDetail> ocrFilter(@Param("contractId")String id,@Param("name") String name,@Param("spec") String spec);

}
