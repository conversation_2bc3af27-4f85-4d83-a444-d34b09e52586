package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 拌合站配料通知单
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 09:54:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_ingredient_notice")
@ApiModel(value = "IngredientNotice对象", description = "拌合站配料通知单")
public class IngredientNotice implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "配料单id")
    @TableField("ingredient_list_id")
    private String ingredientListId;

    @ApiModelProperty(value = "配合比编号")
    private String no;

    @ApiModelProperty(value = "选定生产机组id")
    @TableField(value = "machine_id")
    private String machineId;

    @ApiModelProperty(value = "试验单位")
    @TableField("test_unit")
    private String testUnit;

    @ApiModelProperty(value = "报告日期")
    @TableField("report_date")
    private LocalDate reportDate;

    @ApiModelProperty(value = "水胶比")
    @TableField("water_binder_ratio")
    private BigDecimal waterBinderRatio;

    @ApiModelProperty(value = "砂率")
    @TableField("sand_ratio")
    private BigDecimal sandRatio;

    @ApiModelProperty(value = "通知单附件JSON")
    private String enclosure;

    @ApiModelProperty(value = "是否有结果 1 无, 2 有")
    private Byte status;

    @ApiModelProperty(value = "标准盘数")
    @TableField("standard_coils")
    private Integer standardCoils;

    @ApiModelProperty(value = "标准盘数总量(kg)")
    @TableField(value = "standard_coils_total",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal standardCoilsTotal;

    @ApiModelProperty(value = "尾盘总量(kg)")
    @TableField("tail_coils_total")
    private BigDecimal tailCoilsTotal;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
