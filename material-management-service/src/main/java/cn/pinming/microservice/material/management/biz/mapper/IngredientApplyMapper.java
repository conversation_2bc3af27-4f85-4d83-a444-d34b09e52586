package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.entity.IngredientApply;
import cn.pinming.microservice.material.management.biz.entity.IngredientNotice;
import cn.pinming.microservice.material.management.biz.vo.ApplyVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 拌合站配料申请单 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 09:54:01
 */
public interface IngredientApplyMapper extends BaseMapper<IngredientApply> {

    IngredientApply applyFinished(@Param("id") String ingredientListId);

    int size(@Param("id") String id);

    ApplyVO selectApplyVO(@Param("id") String id);
}
