package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.entity.ProjectConfig;
import cn.pinming.microservice.material.management.biz.enums.ProjectConfigEnum;
import cn.pinming.microservice.material.management.biz.mapper.ProjectConfigMapper;
import cn.pinming.microservice.material.management.biz.service.IProjectConfigService;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 项目配置表 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-09-05 15:43:24
 */
@Service
public class ProjectConfigServiceImpl extends ServiceImpl<ProjectConfigMapper, ProjectConfig> implements IProjectConfigService {

    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private IProjectConfigService projectConfigService;
    @Override
    public ProjectConfig show(Byte type) {
        AuthUser user = authUserHolder.getCurrentUser();
        if (user.getCurrentProjectId() == null) { throw new BOException(BOExceptionEnum.IS_PROJECT_CONFIG); }

        ProjectConfig projectConfig = projectConfigService.lambdaQuery()
                .eq(ProjectConfig::getCompanyId, user.getCurrentCompanyId())
                .eq(ProjectConfig::getProjectId, user.getCurrentProjectId())
                .eq(ProjectConfig::getType, type)
                .one();
        if (type == ProjectConfigEnum.TWO.value() && ObjectUtil.isNull(projectConfig)) {
            ProjectConfig entity = new ProjectConfig();
            entity.setType(ProjectConfigEnum.TWO.value());
            entity.setScope("0.00");
            this.save(entity);
            return entity;
        } else if (type == ProjectConfigEnum.ONE.value() && ObjectUtil.isNull(projectConfig)) {
            ProjectConfig entity = new ProjectConfig();
            entity.setType(ProjectConfigEnum.ONE.value());
            this.save(entity);
            return entity;
        }
        return projectConfig;
    }
}
