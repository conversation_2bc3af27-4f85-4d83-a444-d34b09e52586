package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.*;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.controller.FileUploadController;
import cn.pinming.microservice.material.management.biz.dto.*;
import cn.pinming.microservice.material.management.biz.entity.*;
import cn.pinming.microservice.material.management.biz.enums.*;
import cn.pinming.microservice.material.management.biz.form.PackageForm;
import cn.pinming.microservice.material.management.biz.form.PicForm;
import cn.pinming.microservice.material.management.biz.form.PicUploadForm;
import cn.pinming.microservice.material.management.biz.iot.service.IWeighbridgeService;
import cn.pinming.microservice.material.management.biz.mapper.MaterialDataMapper;
import cn.pinming.microservice.material.management.biz.mapper.MobileReceiveMapper;
import cn.pinming.microservice.material.management.biz.query.*;
import cn.pinming.microservice.material.management.biz.service.*;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.NoUtil;
import cn.pinming.microservice.material.management.common.util.PicEchoUtil;
import cn.pinming.microservice.material.management.common.util.WeightConverter;
import cn.pinming.microservice.material.management.proxy.*;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.model.FileInfos;
import cn.pinming.model.dto.FileQueryDto;
import cn.pinming.v2.company.api.dto.EmployeeDto;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.PropertyPlaceholderHelper;

import javax.annotation.Resource;
import java.io.FileNotFoundException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 收货/发货明细 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:32
 */
@Slf4j
@Service
public class MaterialDataServiceImpl extends ServiceImpl<MaterialDataMapper, MaterialData> implements IMaterialDataService {

    @Resource
    private ProjectServiceProxy projectServiceProxy;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private CooperateServiceProxy cooperateServiceProxy;
    @Resource
    private FileServiceProxy fileServiceProxy;
    @Resource
    private MobileReceiveMapper mobileReceiveMapper;
    @Resource
    private IMaterialSendReceiveService materialSendReceiveService;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private EmployeeServiceProxy employeeServiceProxy;
    @Resource
    private ILeaveTemplateService leaveTemplateService;
    @Resource
    private NoUtil noUtil;
    @Autowired
    private IWeighbridgeService weighbridgeService;
    @Resource
    private PicEchoUtil picEchoUtil;
    @Resource
    private WeightConverter weightConverter;
    @Resource
    private PurchaseOrderServiceImpl purchaseOrderService;
    @Resource
    private IPurchaseContractDetailService purchaseContractDetailService;
    @Resource
    private MaterialDataMapper materialDataMapper;
    @Resource
    private IMaterialWarningService materialWarningService;
    @Resource
    private IMaterialVerifyRelationService materialVerifyRelationService;
    @Reference
    private FileInfos fileInfos;

    /**
     * 地磅收货明细
     *
     * @param query
     * @return
     */
    @Override
    public WeighReceiveVO showWeightDetail(MaterialWeighQuery query) {
        BigDecimal zero = new BigDecimal(0);
        if (query.getId() == null || "".equals(query.getId())) {
            throw new BOException(BOExceptionEnum.DATAID_CAN_NOT_NULL);
        }

        WeighReceiveVO vo = new WeighReceiveVO();
        MaterialDataDetailDTO dto = this.getBaseMapper().selectWeightDetail(query);
        if (ObjectUtil.isNotEmpty(dto)) {
            if (StringUtils.isNotBlank(dto.getPurchaseId())) {
                vo.setIsUsed("是");
            } else {
                vo.setIsUsed("否");
            }
            BeanUtils.copyProperties(dto, vo);

            EmployeeDto employee = employeeServiceProxy.findEmployee(query.getCompanyId(), dto.getCreateId());
            if (Objects.nonNull(employee)) {
                vo.setCreateName(employee.getMemberName());
            }

//            收货项目
            SimpleConstructionProjectDto projectDto = projectServiceProxy.findSimpleProject(dto.getReceiverProject());
            Optional.ofNullable(projectDto).ifPresent(e -> vo.setProjectName(e.getProjectTitle()));

//             供应商
            if (dto.getSupplierId() != null) {
                List<CooperateVO> supplierVO = cooperateServiceProxy.findCooperateByIds(query.getCompanyId(), String.valueOf(dto.getSupplierId()));
                if (CollUtil.isNotEmpty(supplierVO)) {
                    vo.setSupplierName(supplierVO.get(0).getName());
                }
            }

            ReceiveModeEnum receiveModeEnum = ReceiveModeEnum.getReceiveMode(dto.getReceiveMode(), dto.getMaterialValidity());
            vo.setReceiveMode(receiveModeEnum.value());

            List<MaterialDatasVO> materialDataVOList = this.getBaseMapper().selectWeighDetailsWithPurchase(query);
            if (CollUtil.isNotEmpty(materialDataVOList)) {
                materialDataVOList.parallelStream().forEach(e -> {
//                  材料类目
                    if (e.getMaterialId() != null) {
                        MaterialDto materialDto = materialServiceProxy.materialById(e.getMaterialId());
                        if (ObjectUtil.isNotEmpty(materialDto)) {
                            e.setMaterialCategoryName(materialDto.getMaterialCategoryName());
                            e.setMaterialName(materialDto.getMaterialName());
                            e.setMaterialSpec(materialDto.getMaterialSpec());
                            String str = StrUtil.format("{}/{}/{}", materialDto.getMaterialCategoryName(), materialDto.getMaterialName(), materialDto.getMaterialSpec());
                            e.setType(str);
                        }
                    }
                    if (e.getMaterialId() == null && StrUtil.isNotBlank(e.getMaterialName())) {
                        e.setType(e.getMaterialName());
                    }

//                  偏差状态
                    e.setDeviationStatus(DeviationStatusEnum.chooseDeviationStatus(e.getDeviationStatusByte()));
                });
            }
            vo.setList(materialDataVOList);

            //处理图片
            vo.setEnterPics(getPic(vo.getEnterPic(),4));
            vo.setLeavePics(getPic(vo.getLeavePic(),4));
            vo.setDocumentPics(getPic(vo.getDocumentPic(),-1));
        }
        return vo;
    }

    private List<String> getPic(String pic, int limit) {
        if (StrUtil.isBlank(pic)) {
            return null;
        }

        List<String> result = new ArrayList<>();
        List<String> collect = StrUtil.split(pic, ",").stream().filter(e -> StrUtil.isNotBlank(e) & !e.startsWith("D:")).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            collect.forEach(e -> {
                if (limit != -1 && result.size() >= limit ) {
                    return;
                }
                if (e.startsWith("http")) {
                    result.add(e);
                } else {
                    try {
                        FileQueryDto queryDto = new FileQueryDto();
                        queryDto.setFileId(e);
                        result.add(fileInfos.fileDownloadUrl(queryDto));
                    } catch (Exception ex) {
                        log.info("获取图片出错");
                    }
                }
            });
        }
        return result;
    }

    @Override
    public SupplierAnalysisVO getSupplierAnalysisByQuery(SupplierAnalysisQuery query) {
        Byte type = query.getType();
        if (Objects.equals(ReceiveQueryTypeEnum.WEIGHBRIDGE.value(), type)) {
            //报备收料、临时收料
            return this.getBaseMapper().selectSupplierAnalysisByQuery(query);
        } else if (Objects.equals(ReceiveQueryTypeEnum.MOBILE.value(), type)) {
            //移动收料 有合同
            return mobileReceiveMapper.selectSupplierAnalysisByMobileQuery(query);
        } else {
            //全部
            return this.getBaseMapper().selectSupplierAnalysisUnionByQuery(query);
        }
    }

    @Override
    public List<SupplierAnalysisDetailVO> getSupplierAnalysisPageVO(SupplierAnalysisQuery query) {
        Byte type = query.getType();
        if (Objects.equals(ReceiveQueryTypeEnum.WEIGHBRIDGE.value(), type)) {
            //报备收料、临时收料
            return this.getBaseMapper().selectSupplierAnalysisPageVO(query);
        } else if (Objects.equals(ReceiveQueryTypeEnum.MOBILE.value(), type)) {
            //移动收料 有合同
            return mobileReceiveMapper.selectSupplierMobileAnalysisPageVO(query);
        } else {
            //全部
            return this.getBaseMapper().selectSupplierUnionAnalysisPageVO(query);
        }
    }

    @Override
    public List<String> listUnitByCompanyId(Integer companyId) {
        return this.getBaseMapper().selectUnitListByCompanyId(companyId);
    }

    @Override
    public WeighbridgeSendDetailVO showWeighBridgeSendDetail(WeighbridgeSendQuery query) {
        if (StrUtil.isEmpty(query.getId())) {
            throw new BOException(BOExceptionEnum.DATAID_NOT_NULL);
        }

        WeighbridgeSendDetailVO weighbridgeSendDetailVO = new WeighbridgeSendDetailVO();
        // 查询基础信息
        WeighbridgeSendDetailDTO weighbridgeSendDetailDTO = this.getBaseMapper().selectWeighbridgeSendDetail(query);
        if (weighbridgeSendDetailDTO != null) {
            BeanUtils.copyProperties(weighbridgeSendDetailDTO, weighbridgeSendDetailVO);
            // 发货单位
            String sendProjectId = weighbridgeSendDetailDTO.getSendProjectId();
            if (sendProjectId != null) {
                ProjectVO project = projectServiceProxy.getProjectById(Integer.parseInt(sendProjectId));
                weighbridgeSendDetailVO.setSendProject(project.getProjectTitle());
            }
            // 发货人
            EmployeeDto employee = employeeServiceProxy.findEmployee(authUserHolder.getCurrentUser().getCurrentCompanyId(), weighbridgeSendDetailDTO.getSender());
            if (ObjectUtil.isNotEmpty(employee)) {
                weighbridgeSendDetailVO.setSender(employee.getMemberName());
            }
            // 接收方
            if (StrUtil.isNotBlank(weighbridgeSendDetailDTO.getReceiverName())) {
                weighbridgeSendDetailVO.setReceiveProject(weighbridgeSendDetailDTO.getReceiverName());
            }

            // 查询物资明细列表
            MaterialSendDetailDTO materialSendDetailDTO = this.baseMapper.selectWeighbridgeSendMaterialDetail(query);
            if (materialSendDetailDTO != null) {
                // 封装物资明细信息
                MaterialSendDetailVO materialSendDetailVO = new MaterialSendDetailVO();
                BeanUtils.copyProperties(materialSendDetailDTO, materialSendDetailVO);
                String materialId = materialSendDetailDTO.getMaterialId();
                // 材料id存在二级分类名称
                if (materialId != null) {
                    MaterialDto materialDto = materialServiceProxy.materialById(Integer.valueOf(materialId));
                    if (materialDto != null) {
                        materialSendDetailVO.setMaterialName(materialDto.getMaterialCategoryName());
                        materialSendDetailVO.setMaterialSpec(StrUtil.format("{}/{}", materialDto.getMaterialName(), materialDto.getMaterialSpec()));
                    }
                }
                weighbridgeSendDetailVO.setMaterialSendDetailList(Collections.singletonList(materialSendDetailVO));

                weighbridgeSendDetailVO.setWeightTarePics(picEchoUtil.echo(weighbridgeSendDetailDTO.getWeightTarePic(),4));
                weighbridgeSendDetailVO.setWeightGrossPics(picEchoUtil.echo(weighbridgeSendDetailDTO.getWeightGrossPic(),4));
                weighbridgeSendDetailVO.setDocumentPics(picEchoUtil.echo(materialSendDetailDTO.getDocumentPic(),-1));
            }
        }

        return weighbridgeSendDetailVO;
    }

    @Override
    public List<MaterialDataForFY> getDataForFuYang() {
        return this.getBaseMapper().getDataForFuYang();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDataForPhoto(PicDTO dto, PicUploadForm form) {
        String weighId = form.getWeighId();
        Integer type = form.getType();
        String uuid = form.getUuid();
        if (ObjectUtil.isEmpty(dto)) {
            Integer companyId = form.getCompanyId();
            Integer projectId = form.getProjectId();
            MaterialData materialData = new MaterialData();
            materialData.setWeighId(weighId);
            if (type.equals(PicTypeEnum.HIGH.getCode())) {
                materialData.setDocumentPic(uuid);
            } else if (type.equals(PicTypeEnum.ENTER.getCode())) {
                materialData.setEnterPic(uuid);
            } else if (type.equals(PicTypeEnum.LEAVE.getCode())) {
                materialData.setLeavePic(uuid);
            }
            materialData.setReceiveId(IdUtil.simpleUUID());
            materialData.setMaterialValidity(MaterialValidityEnum.INVALID.value());
            materialData.setCompanyId(companyId);
            materialData.setProjectId(projectId);

            MaterialSendReceive materialSendReceive = new MaterialSendReceive();
            materialSendReceive.setId(materialData.getReceiveId());
            materialSendReceive.setCompanyId(companyId);
            materialSendReceive.setProjectId(projectId);
            materialSendReceiveService.save(materialSendReceive);
            save(materialData);
        } else {
            MaterialData materialData = new MaterialData();
            materialData.setId(dto.getId());
            if (type.equals(PicTypeEnum.HIGH.getCode())) {
                materialData.setDocumentPic(StrUtil.join(StrPool.COMMA, dto.getDocumentPic(), uuid));
            } else if (type.equals(PicTypeEnum.ENTER.getCode())) {
                if (dto.getEnterPic() == null) {
                    materialData.setEnterPic(uuid);
                } else {
                    List<String> split = StrUtil.split(dto.getEnterPic(), StrPool.COMMA).stream().filter(s -> !"null".equals(s) && StrUtil.isNotBlank(s) && !s.startsWith("D:")).collect(Collectors.toList());
                    if (split.size() < 4) {
                        materialData.setEnterPic(StrUtil.join(StrPool.COMMA, dto.getEnterPic(), uuid));
                    }
                }
            } else if (type.equals(PicTypeEnum.LEAVE.getCode())) {
                if (dto.getLeavePic() == null) {
                    materialData.setLeavePic(uuid);
                } else {
                    List<String> split = StrUtil.split(dto.getLeavePic(), StrPool.COMMA).stream().filter(s -> !"null".equals(s) && StrUtil.isNotBlank(s) && !s.startsWith("D:")).collect(Collectors.toList());
                    if (split.size() < 4) {
                        materialData.setLeavePic(StrUtil.join(StrPool.COMMA, dto.getLeavePic(), uuid));
                    }
                }
            }
            updateById(materialData);
        }
    }

    @Override
    public void saveSdk(PackageForm form) {
        AuthUser currentUser = authUserHolder.getCurrentUser();
        Integer companyId = currentUser.getCurrentCompanyId();
        Integer projectId = currentUser.getCurrentProjectId();
        WeighbridgeAcceptDataDTO dto = new WeighbridgeAcceptDataDTO();
        MaterialDataDTO dataDTO = new MaterialDataDTO();
        List<MaterialDataDTO> col = new ArrayList<>();

        if (CollUtil.isNotEmpty(form.getDocumentPic())) {
            dto.setDocumentPicList(getUrlByBase64(form.getDocumentPic()));
        }

        dataDTO.setWeightGross(weightConverter.toTons(form.getWeightGross(),form.getUnit()));
        dataDTO.setWeightTare(weightConverter.toTons(form.getWeightTare(),form.getUnit()));
        dataDTO.setWeightNet(NumberUtil.sub(form.getWeightGross(),form.getWeightTare()));
        dataDTO.setWeightDeduct(BigDecimal.ZERO);
        dataDTO.setContractDetailId(form.getContractDetailId());
        dataDTO.setMaterialId(form.getMaterialId());
        if (StrUtil.isNotBlank(form.getMaterial())) {
            dataDTO.setMaterial(form.getMaterial());
        }
        if (StrUtil.isNotBlank(form.getMaterialName()) || StrUtil.isNotBlank(form.getSpec())) {
            dataDTO.setMaterial(form.getMaterialName() + form.getSpec());
        }
        dataDTO.setRatio(ObjUtil.isNotNull(form.getRatio()) ? form.getRatio() : BigDecimal.ONE);
        dataDTO.setWeightUnit(StrUtil.isNotBlank(form.getUnit()) ? form.getUnit() : "吨");
        dataDTO.setWeightSend(form.getWeightSend());
        dto.setEnterTime(form.getEnterTime());
        dto.setLeaveTime(form.getLeaveTime());
        dto.setDeviceSn(form.getDeviceSn());
        dto.setReceiveNo(noUtil.getReceiveNo(form.getCode()));
        dto.setReceiveTime(form.getType().equals(WeighTypeEnum.DELIVERY.value()) ? form.getLeaveTime() : form.getEnterTime());
        dto.setTruckNo(form.getTruckNo());
        dto.setIsDevice((byte)2);
        dto.setType(form.getType());
        dto.setEnterPicList(form.getEnterPic());
        dto.setLeavePicList(form.getLeavePic());
        dto.setRecordId1(form.getRecordId1());
        dto.setRecordId2(form.getRecordId2());
        dto.setIsWebPushed((byte)1);
        dto.setPosition(form.getPosition());
        col.add(dataDTO);
        dto.setMaterialDataList(col);
        dto.setReceiveModeType((byte)1);
        RemarkDTO remarkDTO = RemarkDTO.builder()
                .companyId(companyId).projectId(projectId).weighId(form.getWeighId()).supplierName(form.getSupplierName())
                .build();
        dto.setRemark(JSONUtil.toJsonStr(remarkDTO));

        if (dto.getType().equals(WeighTypeEnum.DELIVERY.value())) {
            weighbridgeService.processWeighbridgeDelivery(dto);
        } else {
            weighbridgeService.processWeighbridgeReceive(dto);
        }
    }

    @Resource
    private FileUploadController fileUploadController;

    @Override
    public List<SupplierRankVO> negativeFrequencyRankListByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeFrequencyRankListByQuery(query);
    }

    @Override
    public List<SupplierRankVO> negativeFrequencyMobileRankListByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeFrequencyMobileRankListByQuery(query);
    }

    @Override
    public List<SupplierRankVO> negativeFrequencyAllRankListByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeFrequencyAllRankListByQuery(query);
    }

    @Override
    public List<SupplierRankVO> negativeFrequencyProportionByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeFrequencyProportionByQuery(query);
    }

    @Override
    public List<SupplierRankVO> negativeFrequencyMobileProportionByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeFrequencyMobileProportionByQuery(query);
    }

    @Override
    public List<SupplierRankVO> negativeFrequencyAllProportionByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeFrequencyAllProportionByQuery(query);
    }

    @Override
    public List<SupplierRankVO> deductRankByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().deductRankByQuery(query);
    }

    @Override
    public List<SupplierRankVO> deductProportionByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().deductProportionByQuery(query);
    }

    @Override
    public List<SupplierRankVO> deductTotalRankByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().deductTotalRankByQuery(query);
    }

    @Override
    public List<SupplierRankVO> deductTotalProportionByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().deductTotalProportionByQuery(query);
    }

    @Override
    public List<SupplierRankDeviationVO> negativeTotalRankByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeTotalRankByQuery(query);
    }

    @Override
    public List<SupplierRankDeviationVO> negativeTotalMobileRankByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeTotalMobileRankByQuery(query);
    }

    @Override
    public List<SupplierRankDeviationVO> negativeTotalAllRankByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeTotalAllRankByQuery(query);
    }

    @Override
    public List<SupplierRankDeviationVO> negativeTotalProportionByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeTotalProportionByQuery(query);
    }

    @Override
    public List<SupplierRankDeviationVO> negativeTotalMobileProportionByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeTotalMobileProportionByQuery(query);
    }

    @Override
    public List<SupplierRankDeviationVO> negativeTotalAllProportionByQuery(SupplierRankQuery query) {
        return this.getBaseMapper().negativeTotalAllProportionByQuery(query);
    }

    @Override
    public Map<String, WeighCarVO> showWeighCar(WeighInfoQuery query) {
        Map<String, WeighCarVO> voMap = new HashMap<>();
        Map<String, List<Integer>> map = new HashMap<>();

        AuthUser user = authUserHolder.getCurrentUser();
        query.setCompanyId(user.getCurrentCompanyId());
//      用户在项目层，顶点也只能是项目（组织树选分公司，最外面就是项目）
        if (user.getCurrentProjectId() != null) {
            query.setProjectIdList(Collections.singletonList(user.getCurrentProjectId()));
            SimpleConstructionProjectDto simpleProject = projectServiceProxy.findSimpleProject(user.getCurrentProjectId());
            if (ObjectUtil.isNotEmpty(simpleProject)) {
                map.put(user.getCurrentProjectId() + "-" + simpleProject.getProjectTitle(), Collections.singletonList(user.getCurrentProjectId()));
            }
        } else {
//          直属节点 -> 项目列表 （已包含用户设置项目范围）
            map = projectServiceProxy.directlyUnderDeptOrProject(user.getCurrentCompanyId(), query.getPoint());
            if (CollUtil.isEmpty(map)) {
                return null;
            }
            List<Integer> projectIdList = map.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
            query.setProjectIdList(projectIdList);
        }
        if (CollUtil.isEmpty(map)) {
            return null;
        }

        List<WeighCarDTO> weighCarDTOList = this.getBaseMapper().showWeighCar(query);
        List<WeighCarDetailDTO> weighCarDetailDTOList = this.getBaseMapper().showWeighCarDetail(query);

        Map<String, List<Integer>> finalMap1 = map;
        map.keySet().forEach(e -> {
            WeighCarVO vo = new WeighCarVO();
            List<WeighCarDTO> list = new ArrayList<>();
            List<WeighCarDetailDTO> detailList = new ArrayList<>();

            if (CollUtil.isNotEmpty(weighCarDTOList)) {
                weighCarDTOList.forEach(m -> {
                    if (finalMap1.get(e).contains(m.getProjectId())) {
                        list.add(m);
                    }
                });
                if (CollUtil.isNotEmpty(list)) {
                    Integer carTotal = list.stream().map(WeighCarDTO::getCarTotal).reduce(Integer::sum).orElse(0);
                    Integer carMonthly = list.stream().map(WeighCarDTO::getCarMonthly).reduce(Integer::sum).orElse(0);
                    vo.setCarTotal(carTotal);
                    vo.setCarMonthly(carMonthly);
                } else {
                    vo.setCarTotal(0);
                    vo.setCarMonthly(0);
                }
            }

            if (CollUtil.isNotEmpty(weighCarDetailDTOList)) {
                weighCarDetailDTOList.forEach(m -> {
                    if (finalMap1.get(e).contains(m.getProjectId())) {
                        detailList.add(m);
                    }
                });
                if (CollUtil.isNotEmpty(detailList)) {
                    Map<String, List<WeighCarDetailDTO>> dateListMap = detailList.stream().collect(Collectors.groupingBy(WeighCarDetailDTO::getDate));
                    Map<String, WeighCarDetailDTO> result = new HashMap<>();

                    dateListMap.keySet().forEach(m -> {
                        WeighCarDetailDTO weighCarDetailDTO = new WeighCarDetailDTO();
                        int sendingCarNum = dateListMap.get(m).stream().mapToInt(WeighCarDetailDTO::getSendingCarNumber).sum();
                        int weighingCarNum = dateListMap.get(m).stream().mapToInt(WeighCarDetailDTO::getWeighingCarNumber).sum();
                        weighCarDetailDTO.setSendingCarNumber(sendingCarNum);
                        weighCarDetailDTO.setWeighingCarNumber(weighingCarNum);
                        result.put(m, weighCarDetailDTO);
                    });

                    List<WeighCarDetailVO> detailVOS = this.getList(query, result);
                    vo.setList(detailVOS);
                } else {
                    List<WeighCarDetailVO> detailVOS = this.getList(query, null);
                    vo.setList(detailVOS);
                }
            }

            voMap.put(e, vo);
        });

        return voMap;
    }

    private List<WeighCarDetailVO> getList(WeighInfoQuery query, Map<String, WeighCarDetailDTO> map) {
        Date start = Date.from(query.getStartDate().atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
        Date end = Date.from(query.getEndDate().atStartOfDay(ZoneOffset.ofHours(8)).toInstant());
        List<DateTime> dateTimes = DateUtil.rangeToList(start, end, DateField.MONTH);
        DateFormat df = new SimpleDateFormat("yyyy-MM");

        List<WeighCarDetailVO> result = new ArrayList<>();
        for (DateTime dateTime : dateTimes) {
            WeighCarDetailVO vo = new WeighCarDetailVO();
            if (CollUtil.isNotEmpty(map)) {
                WeighCarDetailDTO dto = map.get(df.format(dateTime));
                if (ObjectUtil.isNotEmpty(dto)) {
                    vo.setSendingCarNumber(dto.getSendingCarNumber());
                    vo.setWeighingCarNumber(dto.getWeighingCarNumber());
                } else {
                    vo.setWeighingCarNumber(0);
                    vo.setSendingCarNumber(0);
                }
            } else {
                vo.setWeighingCarNumber(0);
                vo.setSendingCarNumber(0);
            }
            vo.setDate(df.format(dateTime));
            result.add(vo);
        }
        return result;
    }


    @Override
    public void solve() {
        List<MaterialIdDTO> list = this.getBaseMapper().solve();
        Set<Integer> materialList = list.stream().map(MaterialIdDTO::getMaterialId).collect(Collectors.toSet());
        Map<Integer, MaterialDto> collect = materialServiceProxy.listMaterialByIds(materialList).stream().collect(Collectors.toMap(MaterialDto::getMaterialId, e -> e));
        List<MaterialData> result = list.stream().map(e -> {
            MaterialData data = new MaterialData();
            data.setId(e.getId());
            if (ObjectUtil.isNotEmpty(collect.get(e.getMaterialId()))) {
                String format = StrUtil.format("{}/{}/{}", collect.get(e.getMaterialId()).getMaterialId(), collect.get(e.getMaterialId()).getMaterialName(), collect.get(e.getMaterialId()).getMaterialSpec());
                data.setMaterialName(format);
            }
            return data;
        }).collect(Collectors.toList());

        this.updateBatchById(result);

    }

    @Override
    public void pic(PicForm form) {
        if (CollUtil.isEmpty(form.getDocumentPics())) {
            return;
        }

        fileServiceProxy.confirmFiles(form.getDocumentPics(), "material-management");
        String str = String.join(",", form.getDocumentPics());
        String pic = null;

        PicDTO dto = this.getBaseMapper().checkByWeighId(form.getTUuid());
        if (ObjectUtil.isNotEmpty(dto)) {
            MaterialData data = new MaterialData();

            if (StrUtil.isNotBlank(dto.getDocumentPic())) {
//              追加
                pic = StrUtil.format("{},{}", dto.getDocumentPic(), str);
                data.setDocumentPic(pic);
            } else {
//              保存
                data.setDocumentPic(str);
            }
            data.setId(dto.getId());
            this.updateById(data);
        }
    }

    @Override
    public List<String> needPushData() {
        return this.getBaseMapper().selectNeedPushDataIds();
    }

    @Override
    public void updatePushDataState(List<String> successIds, byte status) {
        this.baseMapper.updatePushState(successIds, status);
    }

    @Override
    public void clear(List<String> weighIds) {
        this.getBaseMapper().updateDataByWeighIds(weighIds);
    }

    @Override
    public String print(MaterialWeighQuery query, String templateId) {
        LeaveTemplate leaveTemplate = leaveTemplateService.getById(templateId);
        if (null == leaveTemplate || StrUtil.isEmpty(leaveTemplate.getTemplateContent())) {
            throw new BOException(BOExceptionEnum.TEMPLATE_CONTENT_EMPTY);
        }
        WeighReceiveVO weighReceiveVO = showWeightDetail(query);
        Properties properties = new Properties();
        properties.put(WeightParamEnum.TRUCK_NO.value(), weighReceiveVO.getTruckNo());
        List<MaterialDatasVO> list = weighReceiveVO.getList();
        MaterialDatasVO materialDatasVO = list.get(0);
        properties.put(WeightParamEnum.SPEC.value(), buildParam(materialDatasVO.getMaterialName()) + buildParam(materialDatasVO.getMaterialSpec()));
        properties.put(WeightParamEnum.SEND_COUNT.value(), buildParam(materialDatasVO.getWeightSend()));
        properties.put(WeightParamEnum.SUPPLIER_NAME.value(), buildParam(weighReceiveVO.getSupplierName()));
        properties.put(WeightParamEnum.RECEIVE_PROJECT.value(), buildParam(weighReceiveVO.getProjectName()));
        properties.put(WeightParamEnum.GROSS_WEIGHT.value(), buildParam(weighReceiveVO.getWeightGross()));
        properties.put(WeightParamEnum.GROSS_TIME.value(), buildParam(LocalDateTimeUtil.format(weighReceiveVO.getEnterTime(), DatePattern.NORM_DATETIME_FORMATTER)));
        properties.put(WeightParamEnum.TARE_WEIGHT.value(), buildParam(weighReceiveVO.getWeightTare()));
        properties.put(WeightParamEnum.TARE_TIME.value(), buildParam(LocalDateTimeUtil.format(weighReceiveVO.getLeaveTime(), DatePattern.NORM_DATETIME_FORMATTER)));
        properties.put(WeightParamEnum.DEDUCT_WEIGHT.value(), buildParam(weighReceiveVO.getWeightDeduction()));
        properties.put(WeightParamEnum.MOISTURE_CONTENT.value(), buildParam(weighReceiveVO.getMoistureContent()));
        properties.put(WeightParamEnum.NET_WEIGHT.value(), buildParam(weighReceiveVO.getWeightNet()));
        properties.put(WeightParamEnum.ACTUAL_WEIGHT.value(), buildParam(weighReceiveVO.getWeightActual()));
        AuthUser currentUser = authUserHolder.getCurrentUser();
        properties.put(WeightParamEnum.PRINTER.value(), buildParam(currentUser.getMemberName()));
        properties.put(WeightParamEnum.PRINT_TIME.value(), LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_FORMATTER));
        properties.put(WeightParamEnum.QRCODE_NO.value(), buildParam(weighReceiveVO.getReceiveNo()));
        PropertyPlaceholderHelper helper = new PropertyPlaceholderHelper("${", "}");
        return helper.replacePlaceholders(leaveTemplate.getTemplateContent(), properties);
    }

    @Override
    public List<MaterialData> algsNeedPushData(Integer companyId, Integer projectId) {
        return this.baseMapper.needPushDataList(companyId, projectId);
    }

    @Override
    public MaterialReviseDetailVO mobileHistory() {
        MaterialReviseDetailVO vo = new MaterialReviseDetailVO();
        AuthUser user = authUserHolder.getCurrentUser();
        MaterialData materialData = this.lambdaQuery()
                .eq(MaterialData::getCompanyId, user.getCurrentCompanyId())
                .eq(MaterialData::getProjectId, user.getCurrentProjectId())
                .and(item -> item.eq(MaterialData::getReceiveMode,1)
                .or()
                .eq(MaterialData::getReceiveMode,2))
                .orderByDesc(MaterialData::getUpdateTime)
                .last("limit 1")
                .one();

        if (ObjectUtil.isNotNull(materialData)) {
            MaterialSendReceive sendReceive = materialSendReceiveService.lambdaQuery()
                    .eq(MaterialSendReceive::getId, materialData.getReceiveId())
                    .one();
            PurchaseOrder purchaseOrder = purchaseOrderService.lambdaQuery()
                    .eq(PurchaseOrder::getId, materialData.getPurchaseOrderId())
                    .one();
            PurchaseContractDetail contractDetail = purchaseContractDetailService.lambdaQuery()
                    .eq(PurchaseContractDetail::getId, materialData.getContractDetailId())
                    .one();
            if (ObjectUtil.isNotEmpty(purchaseOrder)) {
                vo.setOrderNo(purchaseOrder.getOrderNo());
            }
            vo.setMaterialId(String.valueOf(materialData.getMaterialId()));
            if (materialData.getMaterialId() != null) {
                MaterialDto materialDto = materialServiceProxy.materialById(materialData.getMaterialId());
                Optional.ofNullable(materialDto).ifPresent(e -> vo.setMaterialName(e.getMaterialName() + e.getMaterialSpec()));
            }
            vo.setSupplierId(materialData.getSupplierId());
            if (materialData.getSupplierId() != null) {
                List<CooperateVO> cooperateByIds = cooperateServiceProxy.findCooperateByIds(user.getCurrentCompanyId(), String.valueOf(materialData.getSupplierId()));
                Optional.ofNullable(cooperateByIds).ifPresent(e -> vo.setSupplierName(e.get(0).getName()));
            }
            if (ObjectUtil.isNotNull(contractDetail)) {
                vo.setDeviationCeiling(contractDetail.getDeviationCeiling());
                vo.setDeviationFloor(contractDetail.getDeviationFloor());
                vo.setConversionRate(contractDetail.getConversionRate());
            }
            if (ObjectUtil.isNotNull(materialData.getPosition())) {
                vo.setPosition(materialData.getPosition());
            }
            vo.setExtNo(sendReceive.getExtNo());
            vo.setPurchaseOrderId(materialData.getPurchaseOrderId());
            vo.setContractDetailId(materialData.getContractDetailId());
            vo.setRatio(materialData.getRatio());
            vo.setTypeDetail(sendReceive.getTypeDetail());
            vo.setUnit(materialData.getWeightUnit());
        }

        return vo;
    }

    @Override
    public void delete(String id) {
        AuthUser user = authUserHolder.getCurrentUser();
        int count = materialDataMapper.selectVerifyByDataId(id, user.getCurrentCompanyId());
        if (count > 0) {
            throw new BOException(BOExceptionEnum.LOGIC_ERROR.errorCode(), "此过磅单据已完成对账,不可继续操作");
        }

        MaterialData materialData = this.lambdaQuery()
                .eq(MaterialData::getId, id)
                .one();
        if (ObjectUtil.isNotNull(materialData)) {
            // materialReceive 删除
            materialSendReceiveService.lambdaUpdate()
                    .eq(MaterialSendReceive::getId, materialData.getReceiveId())
                    .set(MaterialSendReceive::getIsDeleted, 1)
                    .update();

            // 预警删除
            materialWarningService.lambdaUpdate()
                    .eq(MaterialWarning::getWarningSourceId, id)
                    .set(MaterialWarning::getIsDeleted, 1)
                    .update();

            // 对账删除
            materialVerifyRelationService.lambdaUpdate()
                    .eq(MaterialVerifyRelation::getReceiveDataId, id)
                    .set(MaterialVerifyRelation::getIsDeleted, 1)
                    .update();

            // 称重删除
            this.lambdaUpdate()
                    .eq(MaterialData::getId, id)
                    .set(MaterialData::getIsDeleted, 1)
                    .update();
        }
    }

    @Override
    public MaterialReviseDetailVO mobileHistoryByType(Byte type,Byte receiveType) {
        MaterialReviseDetailVO vo = new MaterialReviseDetailVO();
        AuthUser user = authUserHolder.getCurrentUser();
        MaterialData materialData = this.getBaseMapper().mobileHistoryByType(type,receiveType,user.getCurrentCompanyId(),user.getCurrentProjectId());

        if (ObjectUtil.isNotNull(materialData)) {
            MaterialSendReceive sendReceive = materialSendReceiveService.lambdaQuery()
                    .eq(MaterialSendReceive::getId, materialData.getReceiveId())
                    .one();
            PurchaseOrder purchaseOrder = purchaseOrderService.lambdaQuery()
                    .eq(PurchaseOrder::getId, materialData.getPurchaseOrderId())
                    .one();
            PurchaseContractDetail contractDetail = purchaseContractDetailService.lambdaQuery()
                    .eq(PurchaseContractDetail::getId, materialData.getContractDetailId())
                    .one();
            if (ObjectUtil.isNotEmpty(purchaseOrder)) {
                vo.setOrderNo(purchaseOrder.getOrderNo());
            }
            vo.setMaterialId(String.valueOf(materialData.getMaterialId()));
            if (materialData.getMaterialId() != null) {
                MaterialDto materialDto = materialServiceProxy.materialById(materialData.getMaterialId());
                Optional.ofNullable(materialDto).ifPresent(e -> vo.setMaterialName(e.getMaterialName() + e.getMaterialSpec()));
            }
            vo.setSupplierId(materialData.getSupplierId());
            if (materialData.getSupplierId() != null) {
                List<CooperateVO> cooperateByIds = cooperateServiceProxy.findCooperateByIds(user.getCurrentCompanyId(), String.valueOf(materialData.getSupplierId()));
                Optional.ofNullable(cooperateByIds).ifPresent(e -> vo.setSupplierName(e.get(0).getName()));
            }
            if (ObjectUtil.isNotNull(contractDetail)) {
                vo.setDeviationCeiling(contractDetail.getDeviationCeiling());
                vo.setDeviationFloor(contractDetail.getDeviationFloor());
                vo.setConversionRate(contractDetail.getConversionRate());
            }
            if (ObjectUtil.isNotNull(materialData.getPosition())) {
                vo.setPosition(materialData.getPosition());
            }
            vo.setExtNo(sendReceive.getExtNo());
            vo.setPurchaseOrderId(materialData.getPurchaseOrderId());
            vo.setContractDetailId(materialData.getContractDetailId());
            vo.setRatio(materialData.getRatio());
            vo.setTypeDetail(sendReceive.getTypeDetail());
            vo.setUnit(materialData.getWeightUnit());
            vo.setMoistureContent(materialData.getMoistureContent());
            vo.setPosition(materialData.getPosition());
        }

        return vo;
    }

    private List<String> getUrlByBase64(List<String> list){
        List<String> result = new ArrayList<>();
        list.stream().forEach(e -> {
            try {
                ResponseEntity<Response> upload = fileUploadController.upload(e);
                SuccessResponse body = (SuccessResponse)upload.getBody();
                result.add(body.getData().toString());
            } catch (FileNotFoundException ex) {
                log.error("单据照片上传出错");
            }
        });
//        if (CollUtil.isNotEmpty(result)) {
//            return fileServiceProxy.fileDownloadUrlByUUIDs(result);
//        }
        return result;
    }

    public String buildParam(Object param) {
        return param == null ? "/" : param.toString();
    }
}
