package cn.pinming.microservice.material.management.biz.iot.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.management.biz.dto.WeighbridgeAcceptDataDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialData;
import cn.pinming.microservice.material.management.biz.entity.MaterialVerify;
import cn.pinming.microservice.material.management.biz.entity.MaterialVerifyRelation;
import cn.pinming.microservice.material.management.biz.entity.ProjectConfig;
import cn.pinming.microservice.material.management.biz.enums.ProjectConfigEnum;
import cn.pinming.microservice.material.management.biz.iot.service.IWeighbridgeDecisionService;
import cn.pinming.microservice.material.management.biz.service.IMaterialDataService;
import cn.pinming.microservice.material.management.biz.service.IMaterialVerifyRelationService;
import cn.pinming.microservice.material.management.biz.service.IMaterialVerifyService;
import cn.pinming.microservice.material.management.biz.service.IProjectConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service(value = "truckReportReceive")
public class WeighbridgeTruckReportReceiveServiceImpl extends AbstractWeighbridgeDecisionService implements IWeighbridgeDecisionService {

    @Resource
    private IProjectConfigService projectConfigService;
    @Resource
    private IMaterialVerifyService materialVerifyService;
    @Resource
    private IMaterialVerifyRelationService materialVerifyRelationService;
    @Resource
    private IMaterialDataService materialDataService;

    @Override
    public WeighbridgeAcceptDataDTO getAcceptDataDTO() {
        return super.getAcceptDataDTO();
    }

    @Override
    public void setAcceptDataDTO(WeighbridgeAcceptDataDTO acceptDataDTO) {
        super.setAcceptDataDTO(acceptDataDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WeighbridgeAcceptDataDTO decisionWeighbridgeReceive(WeighbridgeAcceptDataDTO acceptDataDTO) {

        setAcceptDataDTO(acceptDataDTO);

//      塞车辆报备信息，若该车没有报备过，就新增
        handleTruckReport();
//      塞采购单信息，并更新采购单状态
        handlePurchaseOrder();
//      塞收料物料信息，并判断有效无效，在foreach中加入我的逻辑
        handleValidity();
        saveWeighbridgeReceive();
        saveWeighbridgeMaterialData();

        // 是否自动对账
        Integer companyId = acceptDataDTO.getCompanyId();
        Integer projectId = acceptDataDTO.getProjectId();
        if (projectId != null) {
            ProjectConfig projectConfig = projectConfigService.lambdaQuery()
                    .select(ProjectConfig::getIsEnable)
                    .eq(ProjectConfig::getProjectId, projectId)
                    .eq(ProjectConfig::getCompanyId, companyId)
                    .eq(ProjectConfig::getType, ProjectConfigEnum.ONE.value())
                    .one();
            if (ObjectUtil.isNotNull(projectConfig) && projectConfig.getIsEnable() == 1) {
                String contractId = acceptDataDTO.getPurchaseOrder().getContractId();
                if (CollUtil.isNotEmpty(acceptDataDTO.getDataIdList())) {
                    MaterialVerify materialVerify = materialVerifyService.lambdaQuery()
                            .eq(MaterialVerify::getContractId, contractId)
                            .eq(MaterialVerify::getIsOrigin, 1)
                            .eq(MaterialVerify::getProjectId,projectId)
                            .one();
                    if (ObjectUtil.isNotNull(materialVerify)) {
                        List<MaterialVerifyRelation> collect = acceptDataDTO.getDataIdList().stream().map(e -> {
                            MaterialVerifyRelation materialVerifyRelation = new MaterialVerifyRelation();
                            materialVerifyRelation.setVerifyId(materialVerify.getId());
                            materialVerifyRelation.setReceiveDataId(e);
                            materialVerifyRelation.setCompanyId(companyId);
                            materialVerifyRelation.setProjectId(projectId);

                            return materialVerifyRelation;
                        }).collect(Collectors.toList());

                        materialVerifyRelationService.saveBatch(collect);

                        // 更新收料明细的对账id
                        List<MaterialData> materialDataList = acceptDataDTO.getDataIdList().stream().map(e -> {
                            MaterialData materialData = new MaterialData();

                            materialData.setId(e);
                            materialData.setReconciliationId(materialVerify.getId());

                            return materialData;
                        }).collect(Collectors.toList());
                        materialDataService.updateBatchById(materialDataList);
                    }
                }
            }
        }
        return null;
    }

}
