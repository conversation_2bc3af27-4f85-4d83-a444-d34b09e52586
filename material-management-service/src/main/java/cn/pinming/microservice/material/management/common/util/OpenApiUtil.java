package cn.pinming.microservice.material.management.common.util;

import cn.pinming.core.common.util.StringUtil;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.v2.company.api.dto.CompanyDto;
import cn.pinming.v2.company.api.service.CompanyService;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * Create by WangSJ on 2021/11/02
 */
@Slf4j
@Component
public class OpenApiUtil {

    private static CompanyService companyService;
    @Reference
    public void setCompanyService(CompanyService companyService) {
        OpenApiUtil.companyService = companyService;
    }

    // 内存级缓存
    private static final Cache<String, Object> LOCAL_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .softValues()
            .maximumSize(1000)
            .build();


    /**
     * 从OpenApi参数中，获取指定参数
     */
    public static String getMapValue(Map<String, String[]> map, String paramKey) {
        String[] sArray = map.get(paramKey);
        if (null == sArray) {
            return null;
        } else {
            String s = StringUtil.decode(sArray[0]);
            return s.trim();
        }
    }

    /**
     * 解析data参数
     */
    public static <T> T parseObject(Map<String, String[]> map, String paramKey, Class<T> clazz) {
        String dataStr = getMapValue(map,paramKey);
        if (StringUtil.isBlank(dataStr))
            throw new BOException(BOExceptionEnum.PARAM_CAN_NOT_EMPTY);
        try {
            return JSONObject.parseObject(dataStr, clazz);
        } catch (Exception e) {
            log.warn("data参数解析失败：" + dataStr, e);
            throw new BOException(BOExceptionEnum.PARAM_EXPLAIN_FAIL);
        }
    }

    /**
     * 根据企业编号 cono, 获取企业id
     */
    public static Integer getCompanyId(Map<String, String[]> map) {
        String companyNoStr = getMapValue(map,"cono");
        if (StringUtil.isBlank(companyNoStr))
            throw new BOException(BOExceptionEnum.PARAM_CAN_NOT_EMPTY);

        Object obj;
        try {
            obj = LOCAL_CACHE.get(companyNoStr, () -> {
                CompanyDto company = companyService.findCompanyByNo(companyNoStr);
                if (company == null)
                    throw new BOException(BOExceptionEnum.COMPANY_GET_FAIL);
                return company;
            });
        } catch (ExecutionException e) {
            log.error("获取企业信息失败", e);
            throw new BOException(BOExceptionEnum.COMPANY_GET_FAIL);
        }

        return ((CompanyDto) obj).getCompanyId();
    }

    /**
     * 对象转json
     */
    public static String toJSONString(Object obj) {
        if (obj == null)
            return "";
        try {
            return JSONObject.toJSONString(obj);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error(e.getMessage(), e);
            }
        }
        return "";
    }

}
