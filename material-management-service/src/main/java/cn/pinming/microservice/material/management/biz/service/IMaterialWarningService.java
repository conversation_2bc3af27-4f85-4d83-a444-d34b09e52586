package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.entity.MaterialWarning;
import cn.pinming.microservice.material.management.biz.form.HandleAdviceForm;
import cn.pinming.microservice.material.management.biz.form.MaterialWarningForm;
import cn.pinming.microservice.material.management.biz.query.WarningDetailQuery;
import cn.pinming.microservice.material.management.biz.query.WarningInfoQuery;
import cn.pinming.microservice.material.management.biz.vo.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 预警信息 服务类
 * </p>
 *
 * <AUTHOR> yuan
 * @since 2022-01-17 10:46:38
 */
public interface IMaterialWarningService extends IService<MaterialWarning> {

    IPage<WarningInfoVO> pageListByQuery(WarningInfoQuery warningInfoQuery);

    /**
     * 预警处理
     * @param handleAdviceForm
     */
    void warningHandle(HandleAdviceForm handleAdviceForm);

    /**
     * 保存物料预警信息
     * @param materialWarningForm
     */
    void saveMaterialWarning(MaterialWarningForm materialWarningForm);

    /**
     * 预警状态列表
     * @return
     */
    List<WarningStatusVO> listWarningStatus();

    /**
     * 预警类型列表
     * @return
     */
    List<WarningTypeVO> listWarningType();

    /**
     * 预警来源列表
     * @return
     */
    List<WarningSourceVO> listWarningSource();

    /**
     * 预警详情
     * @param warningDetailQuery
     * @return
     */
    List<WarningDetailVO> getWarningDetail(WarningDetailQuery warningDetailQuery);

}
