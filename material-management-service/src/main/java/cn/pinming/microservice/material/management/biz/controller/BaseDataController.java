package cn.pinming.microservice.material.management.biz.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.core.common.model.PageList;
import cn.pinming.core.common.model.Pagination;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.service.IMaterialDataService;
import cn.pinming.microservice.material.management.biz.service.IPurchaseContractService;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.proxy.CooperateServiceProxy;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.v2.common.QueryPagination;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import cn.pinming.v2.project.api.service.ConstructionProjectService;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 基础数据.
 *
 * <AUTHOR>
 * @version 2021/9/1 4:41 下午
 */
@Api(tags = "基础数据", value = "lh")
@RestController
@RequestMapping("/api/base")
@Validated
@Valid
@Slf4j
public class BaseDataController {
    @Resource
    public ProjectServiceProxy projectServiceProxy;
    @Resource
    public MaterialServiceProxy materialServiceProxy;
    @Resource
    public CooperateServiceProxy cooperateServiceProxy;
    @Resource
    public IMaterialDataService materialDataService;
    @Resource
    public AuthUserHolder authUserHolder;
    @Resource
    public IPurchaseContractService purchaseContractService;
    @Reference
    private ConstructionProjectService constructionProjectService;

    @NacosValue(value = "${iot.url}", autoRefreshed = true)
    public String iotUrl;

    @NacosValue(value = "${category}", autoRefreshed = true)
    public String category;

    @ApiOperation(value = "项目列表", response = ProjectVO.class)
    @Log(title = "项目列表", businessType = BusinessType.QUERY)
    @GetMapping("/project/list")
    public ResponseEntity<Response> projectList() {
        AuthUser user = authUserHolder.getCurrentUser();
        List<ProjectVO> list = new ArrayList<>();
        if (user.getCurrentProjectId() != null) {
            ProjectVO project = projectServiceProxy.getProjectById(user.getCurrentProjectId());
            list.add(project);
        } else if (user.getCurrentDepartmentId() != null) {
            list = projectServiceProxy.getAllProjectsByCompanyDeptId(user.getCurrentCompanyId(), user.getCurrentDepartmentId());
        } else {
            list = projectServiceProxy.getProjectsByCompanyId(user.getCurrentCompanyId());
        }
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "根据企业id拿项目")
    @Log(title = "根据企业id拿项目", businessType = BusinessType.QUERY)
    @GetMapping("/projectPage")
    public ResponseEntity<Response> projectPageByCompanyId() {
        Integer companyId = authUserHolder.getCurrentUser().getCurrentCompanyId();
        QueryPagination<Integer> query = new QueryPagination<>();
        Pagination pagination = new Pagination();
        pagination.setPageSize(999);
        query.setPagination(pagination);
        query.setT(companyId);
        PageList<SimpleConstructionProjectDto> companyProjects = constructionProjectService.findCompanyProjects(query);
        List<ProjectVO> result = companyProjects.getDataList().stream().map(e -> {
            ProjectVO vo = new ProjectVO();
            BeanUtil.copyProperties(e, vo);
            return vo;
        }).collect(Collectors.toList());
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "根据公司id拿项目", response = ProjectVO.class)
    @Log(title = "项目详细", businessType = BusinessType.QUERY)
    @GetMapping("/projectDetail")
    public ResponseEntity<Response> projectDetailById(@RequestParam @NotNull(message = "请传项目Id") Integer projectId) {
        ProjectVO project = projectServiceProxy.getProjectById(projectId);
        return ResponseEntity.ok(new SuccessResponse(project));
    }


    @ApiOperation(value = "项目", response = ProjectVO.class)
    @Log(title = "项目", businessType = BusinessType.QUERY)
    @GetMapping("/project")
    public ResponseEntity<Response> project() {
        AuthUser user = authUserHolder.getCurrentUser();
        ProjectVO project = projectServiceProxy.getProjectById(user.getCurrentProjectId());
        return ResponseEntity.ok(new SuccessResponse(project));
    }

    @ApiOperation(value = "材料单位列表", response = MaterialUnitVO.class)
    @Log(title = "材料单位列表", businessType = BusinessType.QUERY)
    @GetMapping("/unit/list")
    public ResponseEntity<Response> materialUnitList(@RequestParam @NotNull(message = "请选择材料") Integer materialId) {
        AuthUser user = authUserHolder.getCurrentUser();
        MaterialDto materialDto = materialServiceProxy.materialById(materialId);
        Optional.ofNullable(materialDto).orElseThrow(() -> new BOException(BOExceptionEnum.MATERIAL_NOT_EXISTS));
        List<MaterialUnitVO> list = materialServiceProxy.unitListByCategoryId(user.getCurrentCompanyId(), materialDto.getMaterialCategoryId());
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "供应商列表", response = CooperateVO.class)
    @Log(title = "供应商列表", businessType = BusinessType.QUERY)
    @GetMapping("/cooperate/list")
    public ResponseEntity<Response> cooperateList() {
        AuthUser user = authUserHolder.getCurrentUser();
        List<CooperateVO> list = cooperateServiceProxy.findCooperateByCompanyId(user.getCurrentCompanyId());
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "本地 - 供应商列表")
    @Log(title = "本地 - 供应商列表", businessType = BusinessType.QUERY)
    @GetMapping("/cooperate/lists")
    public ResponseEntity<Response> cooperateLists() {
        List<CooperateVO> list = purchaseContractService.getCooperateList();
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "材料类别列表", response = MaterialCategoryVO.class)
    @Log(title = "材料类别列表", businessType = BusinessType.QUERY)
    @GetMapping("/category/list")
    public ResponseEntity<Response> materialCategoryList() {
        AuthUser user = authUserHolder.getCurrentUser();
        List<MaterialCategoryVO> list = materialServiceProxy.listCategoryByCompanyId(user.getCurrentCompanyId());
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "材料类别 - 单位列表", response = MaterialUnitVO.class)
    @Log(title = "材料类别 - 单位列表", businessType = BusinessType.QUERY)
    @GetMapping("/category/unit/list")
    public ResponseEntity<Response> materialCategoryUnitList(@RequestParam @NotNull(message = "请选择材料分类") Integer categoryId) {
        AuthUser user = authUserHolder.getCurrentUser();
        List<MaterialUnitVO> list = materialServiceProxy.unitListByCategoryId(user.getCurrentCompanyId(), categoryId);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "材料类别 - 单位列表", response = MaterialUnitVO.class)
    @Log(title = "材料类别 - 单位列表", businessType = BusinessType.QUERY)
    @PostMapping("/category/unit/list")
    public ResponseEntity<Response> materialCategoryUnitList(@RequestBody List<Integer> categoryIds) {
        AuthUser user = authUserHolder.getCurrentUser();
        List<MaterialUnitVO> list = materialServiceProxy.unitListByCategoryIds(user.getCurrentCompanyId(), categoryIds);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "收料单位列表")
    @Log(title = "收料单位列表", businessType = BusinessType.QUERY)
    @GetMapping("/unit/lists")
    public ResponseEntity<Response> materialUnitList() {
        AuthUser user = authUserHolder.getCurrentUser();
        List<String> list = materialDataService.listUnitByCompanyId(user.getCurrentCompanyId());
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "设备厂商列表")
    @Log(title = "设备厂商列表", businessType = BusinessType.QUERY)
    @GetMapping("/supplier/lists")
    public ResponseEntity supplierList() {
        Map<String, Object> requestMap = MapUtil.newHashMap();
        requestMap.put("deviceType", 3023);
        String url = StrUtil.format("{}/zhgd/openSite/api/productization/getDeviceVendorList", iotUrl);
        return ResponseEntity.ok(HttpUtil.post(url, JSONUtil.toJsonStr(requestMap)));
    }

    @ApiOperation(value = "检查设备SN")
    @Log(title = "检查设备SN", businessType = BusinessType.QUERY)
    @GetMapping("/check/device")
    public ResponseEntity checkDeviceSN(@NotNull(message = "设备供应商为空") Integer dataSourceType, @NotNull(message = "设备SN为空") String deviceSn) {
        Map<String, Object> requestMap = MapUtil.newHashMap();
        requestMap.put("deviceType", 3023);
        requestMap.put("dataSourceType", dataSourceType);
        requestMap.put("deviceSn", deviceSn);
        String url = StrUtil.format("{}/zhgd/openSite/api/productization/checkDeviceSn", iotUrl);
        return ResponseEntity.ok(HttpUtil.post(url, JSONUtil.toJsonStr(requestMap)));
    }

    @ApiOperation(value = "材料分类快捷方式", response = CategoryVO.class)
    @GetMapping("category")
    public ResponseEntity<Response> category() {
        AuthUser user = authUserHolder.getCurrentUser();
        //String result = new String(category.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
        List<CategoryVO> list = JSONUtil.toList(category, CategoryVO.class);
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(e -> {
                if (CollUtil.isNotEmpty(e.getCategoryId())) {
                    List<MaterialUnitVO> unitList = materialServiceProxy.unitListByCategoryIds(user.getCurrentCompanyId(), e.getCategoryId());
                    if (CollUtil.isNotEmpty(unitList)) {
                        e.setUnit(unitList.stream().map(MaterialUnitVO::getTargetUnitName).distinct().collect(Collectors.toList()));
                    }
                }
            });
        }
        return ResponseEntity.ok(new SuccessResponse(list));
    }
}
