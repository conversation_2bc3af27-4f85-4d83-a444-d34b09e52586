package cn.pinming.microservice.material.management.biz.controller;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.entity.Supplier;
import cn.pinming.microservice.material.management.biz.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.biz.query.SupplierRankQuery;
import cn.pinming.microservice.material.management.biz.service.ISupplierAnalysisService;
import cn.pinming.microservice.material.management.biz.service.ISupplierService;
import cn.pinming.microservice.material.management.biz.vo.MaterialReceiveVO;
import cn.pinming.microservice.material.management.biz.vo.SupplierAnalysisDetailVO;
import cn.pinming.microservice.material.management.biz.vo.SupplierAnalysisVO;
import cn.pinming.microservice.material.management.biz.vo.SupplierRankVO;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import cn.pinming.microservice.material.management.common.util.ExcelUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 供应商分析
 *
 * <AUTHOR>
 * @version 2021/9/1 4:31 下午
 */
@Api(tags = "供应商分析", value = "lh")
@RestController
@RequestMapping("/api/supplier/analysis")
@AllArgsConstructor
public class SupplierAnalysisController {

    private final ISupplierAnalysisService supplierAnalysisService;

    @Resource
    private ISupplierService supplierService;

    @ApiOperation(value = "汇总", response = SupplierAnalysisVO.class)
    @Log(title = "汇总", businessType = BusinessType.QUERY)
    @PostMapping("/summary")
    public ResponseEntity<Response> summary(@RequestBody SupplierAnalysisQuery query) {
        SupplierAnalysisVO result = supplierAnalysisService.getSummaryByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "收料情况列表", response = SupplierAnalysisDetailVO.class)
    @Log(title = "收料情况列表", businessType = BusinessType.QUERY)
    @PostMapping("/list")
    public ResponseEntity<Response> list(@RequestBody SupplierAnalysisQuery query) {
        IPage<?> page = supplierAnalysisService.pageListByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "收料情况列表 - 详情", response = MaterialReceiveVO.class)
    @Log(title = "收料情况列表 - 详情", businessType = BusinessType.QUERY)
    @PostMapping("/detail")
    public ResponseEntity<Response> detail(@RequestBody SupplierAnalysisQuery query) {
        IPage<?> page = supplierAnalysisService.pageListOrderByQuery(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "收料情况列表 - 导出", response = MaterialReceiveVO.class)
    @Log(title = "收料情况列表 - 导出", businessType = BusinessType.QUERY)
    @PostMapping("/detail/export")
    public void detailExport(HttpServletResponse response, @RequestBody SupplierAnalysisQuery query) {
        query.setPages(0);
        query.setSize(Integer.MAX_VALUE);
        IPage<?> page = supplierAnalysisService.pageListOrderByQuery(query);
        LocalDate now = LocalDate.now();
        String date = LocalDateTimeUtil.format(now, "yyyyMMdd");
        Supplier supplier = supplierService.getById(query.getSupplierId());
        ExcelUtils.export(response, page.getRecords(), MaterialReceiveVO.class, supplier.getName() + "收料记录导出" + date);
    }

    @ApiOperation(value = "超负差次数排行", response = SupplierRankVO.class)
    @Log(title = "超负差次数排行", businessType = BusinessType.QUERY)
    @PostMapping("/negative/frequency/rank")
    public ResponseEntity<Response> negativeFrequencyRank(@RequestBody SupplierRankQuery query) {
        List<SupplierRankVO> list = supplierAnalysisService.negativeFrequencyRankList(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "超负差次数占比", response = SupplierRankVO.class)
    @Log(title = "超负差次数占比", businessType = BusinessType.QUERY)
    @PostMapping("/negative/frequency/proportion")
    public ResponseEntity<Response> negativeFrequencyProportion(@RequestBody SupplierRankQuery query) {
        List<SupplierRankVO> list = supplierAnalysisService.negativeFrequencyProportion(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "超负差总量排行", response = SupplierRankVO.class)
    @Log(title = "超负差总量排行", businessType = BusinessType.QUERY)
    @PostMapping("/negative/total/rank")
    public ResponseEntity<Response> negativeTotalRank(@RequestBody SupplierRankQuery query) {
        List<SupplierRankVO> list = supplierAnalysisService.negativeTotalRank(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "超负差总量占比排行", response = SupplierRankVO.class)
    @Log(title = "超负差总量占比排行", businessType = BusinessType.QUERY)
    @PostMapping("/negative/total/proportion")
    public ResponseEntity<Response> negativeTotalProportion(@RequestBody SupplierRankQuery query) {
        List<SupplierRankVO> list = supplierAnalysisService.negativeTotalProportion(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "扣重次数排行(车次数)", response = SupplierRankVO.class)
    @Log(title = "扣重次数排行(车次数)", businessType = BusinessType.QUERY)
    @PostMapping("/deduct/rank")
    public ResponseEntity<Response> deductRank(@RequestBody SupplierRankQuery query) {
        List<SupplierRankVO> list = supplierAnalysisService.deductRank(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "扣重次数占比排行", response = SupplierRankVO.class)
    @Log(title = "扣重次数占比排行", businessType = BusinessType.QUERY)
    @PostMapping("/deduct/proportion")
    public ResponseEntity<Response> deductProportion(@RequestBody SupplierRankQuery query) {
        List<SupplierRankVO> list = supplierAnalysisService.deductProportion(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "扣重总量排行", response = SupplierRankVO.class)
    @Log(title = "扣重总量排行", businessType = BusinessType.QUERY)
    @PostMapping("/deduct/total/rank")
    public ResponseEntity<Response> deductTotalRank(@RequestBody SupplierRankQuery query) {
        List<SupplierRankVO> list = supplierAnalysisService.deductTotalRank(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "扣重总量占比排行", response = SupplierRankVO.class)
    @Log(title = "扣重总量占比排行", businessType = BusinessType.QUERY)
    @PostMapping("/deduct/total/proportion")
    public ResponseEntity<Response> deductTotalProportion(@RequestBody SupplierRankQuery query) {
        List<SupplierRankVO> list = supplierAnalysisService.deductTotalProportion(query);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

}
