package cn.pinming.microservice.material.management.proxy.impl;

import cn.pinming.microservice.material.management.proxy.DepartmentTreeServiceProxy;
import cn.pinming.v2.company.api.dto.departmentTree.DepartmentTreeDto;
import cn.pinming.v2.company.api.service.DepartmentTreeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/30
 */
@Slf4j
@Component
public class DepartmentTreeServiceProxyImpl implements DepartmentTreeServiceProxy {

    @Reference
    private DepartmentTreeService departmentTreeService;

    @Override
    public String getDepartmentParent(Integer deptId) {
        List<DepartmentTreeDto> departmentParent = departmentTreeService.getDepartmentParent(deptId);
        return departmentParent.stream().map(DepartmentTreeDto::getDepartmentName).collect(Collectors.joining(" / "));
    }
}
