package cn.pinming.microservice.material.management.biz.service;

import cn.pinming.microservice.material.management.biz.entity.LeaveTemplate;
import cn.pinming.microservice.material.management.biz.form.LeaveTemplateContentForm;
import cn.pinming.microservice.material.management.biz.form.LeaveTemplateImageForm;
import cn.pinming.microservice.material.management.biz.form.LeaveTemplateNameForm;
import cn.pinming.microservice.material.management.biz.form.TemplateImageForm;
import cn.pinming.microservice.material.management.biz.vo.LeaveTemplateNameVO;
import cn.pinming.microservice.material.management.biz.vo.LeaveTemplateParamVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 出场单模板 服务类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-28 13:58:49
 */
public interface ILeaveTemplateService extends IService<LeaveTemplate> {

    /**
     * 保存模板
     * @param leaveTemplateForm 模板信息
     */
    void saveLeaveTemplate(LeaveTemplateContentForm leaveTemplateForm);

    /**
     * 添加模板名
     * @param leaveTemplateNameForm 模板信息
     */
    void saveLeaveTemplateName(LeaveTemplateNameForm leaveTemplateNameForm);

    /**
     * 根据模板名获取模板
     * @param templateName 模板名
     * @param type 模板类型
     * @return 模板内容
     */
    String findLeaveTemplate(String templateName, Byte type);

    /**
     * 删除模板
     * @param templateName 模板名
     * @param type 模板类型
     */
    void deleteLeaveTemplate(String templateName, Byte type);

    /**
     * 获取模板名称列表
     * @param type 模板类型
     * @return 列表
     */
    List<LeaveTemplateNameVO> listLeaveTemplate(Byte type);

    /**
     * 保存模板图片
     * @param leaveTemplateForm 模板FORM
     */
    void saveLeaveTemplateImage(LeaveTemplateImageForm leaveTemplateForm);

    /**
     * 根据模板名称获取图片列表
     * @param name 模板名
     * @param type 模板类型
     * @return 模板图片列表
     */
    List<TemplateImageForm> findLeaveTemplateImage(String name, Byte type);

    /**
     * 根据图片UUID删除模板图片
     * @param leaveTemplateImageForm 模板FORM
     */
    void deleteLeaveTemplateImage(LeaveTemplateImageForm leaveTemplateImageForm);

    /**
     * 参数列表
     * @param type 模板类型
     * @return 参数列表
     */
    List<LeaveTemplateParamVO> paramList(Byte type);

}
