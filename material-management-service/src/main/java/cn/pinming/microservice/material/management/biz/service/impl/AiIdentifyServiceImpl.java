package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.pinming.algModelApi.api.dto.RebarResultDto;
import cn.pinming.microservice.material.management.biz.entity.MaterialAiIdentifyRebar;
import cn.pinming.microservice.material.management.biz.entity.MaterialAiIdentifyRecord;
import cn.pinming.microservice.material.management.biz.enums.AiIdentifyEnum;
import cn.pinming.microservice.material.management.biz.mapper.MaterialAiIdentifyRebarMapper;
import cn.pinming.microservice.material.management.biz.mapper.MaterialAiIdentifyRecordMapper;
import cn.pinming.microservice.material.management.biz.service.AiIdentifyService;
import cn.pinming.microservice.material.management.common.util.UUIDUtil;
import cn.pinming.microservice.material.management.proxy.AiIdentifyProxy;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/2/9
 * @description
 */
@Slf4j
@Service
public class AiIdentifyServiceImpl implements AiIdentifyService {

    @Resource
    private AiIdentifyProxy aiIdentifyProxy;

    @Resource
    private MaterialAiIdentifyRecordMapper aiIdentifyRecordMapper;

    @Resource
    private MaterialAiIdentifyRebarMapper aiIdentifyRebarMapper;

    @Override
    public String getToken(AiIdentifyEnum bizId, String userId, Integer projectId, Integer companyId) {
        String ticketId = UUIDUtil.randomUUIDWithoutConnector();
        Long timeStamp = System.currentTimeMillis();
        String token = aiIdentifyProxy.getToken(bizId, ticketId, userId, projectId, timeStamp, companyId);
        log.info("AiIdentifyServiceImpl getToken. bizId:{}, ticketId:{}, userId:{}, projectId:{}, timeStamp:{}, companyId:{} ==> token:{}",
                bizId, ticketId, userId, projectId, timeStamp, companyId, token);
        // 数据持久化
        MaterialAiIdentifyRecord record = new MaterialAiIdentifyRecord();
        record.setBizId(bizId.getCode());
        record.setUserId(userId);
        record.setProjectId(projectId);
        record.setTicketId(ticketId);
        record.setCompanyId(companyId);
        record.setTokenTimeStamp(timeStamp);
        record.setResultTimeStamp(0L);
        record.setToken(token);
        aiIdentifyRecordMapper.insert(record);
        return token;
    }

    @Override
    public RebarResultDto getRebarCalculateResult(AiIdentifyEnum bizId, String token) {
        QueryWrapper<MaterialAiIdentifyRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MaterialAiIdentifyRecord::getBizId, AiIdentifyEnum.REBAR.getCode()).eq(MaterialAiIdentifyRecord::getToken, token);
        MaterialAiIdentifyRecord materialAiIdentifyRecord = aiIdentifyRecordMapper.selectOne(queryWrapper);
        String ticketId = Optional.ofNullable(materialAiIdentifyRecord).orElse(new MaterialAiIdentifyRecord()).getTicketId();
        log.info("AiIdentifyServiceImpl getRebarCalculateResult getTicketIdByToken. token:{} ==> ticketId:{}", token, ticketId);
        Long timeStamp = System.currentTimeMillis();
        RebarResultDto rebarCalculateResult = Optional.ofNullable(aiIdentifyProxy.getRebarCalculateResult(bizId, ticketId, timeStamp)).orElse(new RebarResultDto());
        log.info("AiIdentifyServiceImpl getRebarCalculateResult. bizId:{}, ticketId:{}, timeStamp:{} ==> ticketId:{}",
                bizId, ticketId, timeStamp, rebarCalculateResult);
        // 更新结果请求时间戳
        MaterialAiIdentifyRecord update = new MaterialAiIdentifyRecord();
        update.setResultTimeStamp(timeStamp);
        UpdateWrapper<MaterialAiIdentifyRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(MaterialAiIdentifyRecord::getTicketId, ticketId);
        aiIdentifyRecordMapper.update(update, updateWrapper);
        // 保存结果
        QueryWrapper<MaterialAiIdentifyRebar> query = new QueryWrapper<>();
        query.lambda().eq(MaterialAiIdentifyRebar::getTicketId, ticketId);
        MaterialAiIdentifyRebar aiIdentifyRebar = aiIdentifyRebarMapper.selectOne(query);
        if (ObjectUtil.isNull(aiIdentifyRebar)) {
            aiIdentifyRebar = new MaterialAiIdentifyRebar();
        } else {
            aiIdentifyRebarMapper.deleteById(aiIdentifyRebar.getId());
        }
        aiIdentifyRebar.setOriginPicId(rebarCalculateResult.getOriginPicId());
        aiIdentifyRebar.setRebarCount(rebarCalculateResult.getRebarCount());
        aiIdentifyRebar.setPicId(rebarCalculateResult.getPicId());
        aiIdentifyRebar.setOriginRebarCount(rebarCalculateResult.getOriginRebarCount());
        aiIdentifyRebar.setTicketId(ticketId);
        aiIdentifyRebarMapper.insert(aiIdentifyRebar);
        return rebarCalculateResult;
    }

}