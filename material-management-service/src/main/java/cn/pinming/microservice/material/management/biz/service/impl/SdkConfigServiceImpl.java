package cn.pinming.microservice.material.management.biz.service.impl;

import cn.pinming.microservice.material.management.biz.dto.SdkConfigDTO;
import cn.pinming.microservice.material.management.biz.entity.SdkConfig;
import cn.pinming.microservice.material.management.biz.mapper.SdkConfigMapper;
import cn.pinming.microservice.material.management.biz.service.ISdkConfigService;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 基石平台配置表 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2023-07-06 13:52:11
 */
@Service
public class SdkConfigServiceImpl extends ServiceImpl<SdkConfigMapper, SdkConfig> implements ISdkConfigService {

    @NacosValue("${sdk.host}")
    private String host;

    @Override
    public SdkConfigDTO getSdkConfig(Integer companyId, Integer projectId) {
        SdkConfigDTO result = new SdkConfigDTO();
        result.setHost(host);
        SdkConfig sdkConfig = null;
        if (projectId != null) {
            sdkConfig = lambdaQuery().eq(SdkConfig::getCompanyId, companyId)
                    .eq(SdkConfig::getProjectId, projectId).one();
        } else if (companyId != null) {
            sdkConfig = lambdaQuery().eq(SdkConfig::getCompanyId, companyId)
                    .isNull(SdkConfig::getProjectId).one();
        }
        if (sdkConfig != null) {
            result.setAppKey(sdkConfig.getAppKey());
            result.setAppSecretKey(sdkConfig.getAppSecretKey());
        }
        return result;
    }
}
