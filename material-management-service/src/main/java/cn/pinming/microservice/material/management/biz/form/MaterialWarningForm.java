package cn.pinming.microservice.material.management.biz.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * 预警信息form
 * <AUTHOR>
 * @Date 2022/1/18 13:29
 */
@Data
public class MaterialWarningForm extends BaseForm{

    @ApiModelProperty(value = "预警类型")
    @NotNull(message = "预警类型为空")
    private Byte warningType;

    @ApiModelProperty(value = "预警类型子分类")
    private Byte warningSubType;

    @ApiModelProperty(value = "预警信息")
    @NotBlank(message = "预警信息为空")
    private String warningInfo;

    @ApiModelProperty(value = "预警来源")
    @NotNull(message = "预警来源为空")
    private Byte warningSource;

    @ApiModelProperty(value = "预警来源记录id")
    @NotBlank(message = "预警来源记录id为空")
    private String warningSourceId;

    @ApiModelProperty(value = "预警来源记录编号")
    @NotBlank(message = "预警来源记录编号为空")
    private String warningSourceNo;

    @ApiModelProperty(value = "发生项目id")
    private Integer sourceProjectId;

    @ApiModelProperty(value = "预警来源")
    private String warningSourceStr;
}
