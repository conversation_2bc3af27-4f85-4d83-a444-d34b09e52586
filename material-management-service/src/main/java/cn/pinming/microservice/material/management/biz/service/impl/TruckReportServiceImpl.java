package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.materialManagement.dto.TruckInfoDTO;
import cn.pinming.microservice.material.management.biz.entity.TruckReport;
import cn.pinming.microservice.material.management.biz.enums.DeleteEnum;
import cn.pinming.microservice.material.management.biz.mapper.TruckReportMapper;
import cn.pinming.microservice.material.management.biz.service.ITruckReportService;
import cn.pinming.microservice.material.management.biz.vo.ExpirePageVO;
import cn.pinming.microservice.material.management.biz.vo.TruckReportVO;
import cn.pinming.microservice.material.management.common.AppConstant;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.RedisUtil;
import cn.pinming.microservice.material.management.wrapper.CreateNameWrapper;
import cn.pinming.weaponx.wrapper.wrapper.MemberNameWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 车辆报备 服务实现类
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-09-06 14:04:33
 */
@Service
public class TruckReportServiceImpl extends ServiceImpl<TruckReportMapper, TruckReport> implements ITruckReportService {

    @Resource
    private CreateNameWrapper memberNameWrapper;
    @Resource
    private RedisUtil redisUtil;

    /**
     * 车辆报备
     *
     * @param user
     * @param truckNo
     */
    @Override
    public void add(AuthUser user, String truckNo){
        TruckReport result = this.lambdaQuery()
                .eq(TruckReport::getCompanyId,user.getCurrentCompanyId())
                .eq(TruckReport::getProjectId,user.getCurrentProjectId())
                .eq(TruckReport::getTruckNo,truckNo)
                .one();
        if (ObjectUtil.isNotEmpty(result)){
            throw new BOException(BOExceptionEnum.TRUCK_HAS_EXISTS);
        }

        TruckReport truckReport = new TruckReport();
        truckReport.setTruckNo(truckNo);
        this.save(truckReport);
    }

    /**
     * 车辆报备
     *
     * @param truckReport
     */
    @Override
    public void addTruckReport(TruckReport truckReport){
        if (ObjectUtil.isEmpty(truckReport)){
            log.error("addTruckReport truckReport is empty");
            return;
        }

        this.save(truckReport);
    }

    /**
     * 车辆报备列表
     *
     * @param user
     * @param truckNo
     * @return
     */
    @Override
    public List<TruckReportVO> listTruckReport(AuthUser user, String truckNo){

        List<TruckReportVO> list = this.getBaseMapper().selectTruck(user.getCurrentCompanyId(),user.getCurrentProjectId(),truckNo);
        if (CollUtil.isNotEmpty(list)){
            memberNameWrapper.wrap(list,user.getCurrentCompanyId());
        }
        return list;

    }

    @Override
    public List<TruckInfoDTO> selectTruckNo(Integer companyId, Integer projectId){
        return this.getBaseMapper().selectTruckNo(companyId,projectId);
    }

    @Override
    public TruckReport gettTruckInfo(Integer projectId, String truckNo){
        return this.getBaseMapper().selectTruckInfoById(projectId, truckNo);
    }

    /**
     * 删除车辆
     *
     * @param user
     * @param id
     */
    @Override
    public void del(AuthUser user,String id){
        TruckReport truckReport = this.lambdaQuery()
                .eq(TruckReport::getCompanyId,user.getCurrentCompanyId())
                .eq(TruckReport::getProjectId,user.getCurrentProjectId())
                .eq(TruckReport::getId,id)
                .one();

        if(ObjectUtil.isEmpty(truckReport)){
            throw new BOException(BOExceptionEnum.TRUCK_IS_NOT_EXIST);
        }

        this.lambdaUpdate()
                .eq(TruckReport::getCompanyId,user.getCurrentCompanyId())
                .eq(TruckReport::getProjectId,user.getCurrentProjectId())
                .eq(TruckReport::getId,id)
                .set(TruckReport::getIsDeleted, DeleteEnum.DELETE.value())
                .update();
    }

    @Override
    public List<String> truckNoList(String code){
        List<String> list = new ArrayList<>();

        String viewKey = StrUtil.format(AppConstant.PAGE_VIEW_PREFIX, code);
        Map<Object, Object> map = redisUtil.hmget(viewKey);
        if (Objects.isNull(map)) {
            throw new BOException(BOExceptionEnum.EXT_PAGE_VIEW);
        }
        ExpirePageVO vo = BeanUtil.fillBeanWithMap(map, new ExpirePageVO(), true);
        if (vo.getCommit() > 1) {
            throw new BOException(BOExceptionEnum.EXT_PAGE_VIEW);
        }
        List<TruckReport> truckReportList = this.lambdaQuery()
                .eq(TruckReport::getProjectId,vo.getProjectId())
                .list();
        if(CollUtil.isNotEmpty(truckReportList)){
            list = truckReportList.stream().map(TruckReport::getTruckNo).collect(Collectors.toList());
        }

        return list;
    }



}
