package cn.pinming.microservice.material.management.biz.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.entity.ProjectConfig;
import cn.pinming.microservice.material.management.biz.form.PjConfigForm;
import cn.pinming.microservice.material.management.biz.service.IProjectConfigService;
import cn.pinming.microservice.material.management.biz.vo.CompanyConfigVO;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 项目配置表 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-09-05 15:43:24
 */
@Api(tags = "项目层配置", value = "zh")
@RestController
@RequestMapping("api/biz/project-config")
public class ProjectConfigController {

    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private IProjectConfigService projectConfigService;

    @ApiOperation("新增、编辑配置")
    @PostMapping("/saveOrUpdate")
    @Log(title = "新增、编辑配置", businessType = BusinessType.INSERTORUPDATE)
    public ResponseEntity<Response> saveOrUpdate(@Validated @Valid @RequestBody PjConfigForm form){
        AuthUser user = authUserHolder.getCurrentUser();
        form.setCompanyId(user.getCurrentCompanyId());
        form.setProjectId(user.getCurrentProjectId());
        if (user.getCurrentProjectId() == null) {
            throw new BOException(BOExceptionEnum.IS_PROJECT_CONFIG);
        }
        if (form.getId() == null) {
            ProjectConfig one = projectConfigService.lambdaQuery()
                    .eq(ProjectConfig::getCompanyId, user.getCurrentCompanyId())
                    .eq(ProjectConfig::getProjectId, user.getCurrentProjectId())
                    .eq(ProjectConfig::getType, form.getType())
                    .one();
            if (ObjectUtil.isNotNull(one)) {
                return null;
            }
        }
        ProjectConfig projectConfig = new ProjectConfig();
        BeanUtils.copyProperties(form,projectConfig);
        projectConfigService.saveOrUpdate(projectConfig);

        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "回显",response = ProjectConfig.class)
    @GetMapping("/show/{type}")
    @Log(title = "回显", businessType = BusinessType.QUERY)
    public ResponseEntity<Response> show(@PathVariable("type")Byte type){
        ProjectConfig projectConfig = projectConfigService.show(type);
        return ResponseEntity.ok(new SuccessResponse(projectConfig));
    }

}
