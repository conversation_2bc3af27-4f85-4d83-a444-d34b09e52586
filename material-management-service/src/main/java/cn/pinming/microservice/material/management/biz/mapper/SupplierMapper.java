package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.SupplierDTO;
import cn.pinming.microservice.material.management.biz.entity.Supplier;
import cn.pinming.microservice.material.management.biz.query.SupplierQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 供应商表 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-04-13 13:17:24
 */
public interface SupplierMapper extends BaseMapper<Supplier> {

    Page<SupplierDTO> selectSupplierPageListByQuery(@Param("query") SupplierQuery query);

    void updateStatusById(@Param("id") String id,@Param("mid")String mid);

    List<Supplier> selectSupplierList(@Param("companyId") Integer companyId, @Param("supplierIds") List<Integer> supplierIds);
}
