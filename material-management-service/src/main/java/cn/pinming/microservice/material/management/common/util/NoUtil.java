package cn.pinming.microservice.material.management.common.util;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Component
@AllArgsConstructor
public class NoUtil {

    private final RedisUtil redisUtil;

    private static final String RECEIVE_KEY_PREFIX = "SMSL-";
    private static final String CONFIRM_KEY_PREFIX = "QRSL-";
    private static final String MOBILE_KEY_PREFIX = "YDSL-";
    private static final String OCR_KEY_PREFIX = "HSSL-";
    private static final String OCR_SEND_KEY_PREFIX = "HSFL-";
    private static final String MUL_KEY_PREFIX = "YCDL-";
    private static final String WEIGH_KEY_PREFIX = "SMFL-";
    private static final String CONFIRM_SEND_KEY_PREFIX = "QRFL-";
    private static final String WARNING_KEY_PREFIX = "YJJL-";
    // 对账编号前缀
    public static final String DZ_KEY_PREFIX = "DZ-";

    private static final String PURCHASE_ORDER_KEY_PREFIX = "CGXQ-";
    private static final String REPORT_KEY_PREFIX = "BBJL-";
    // 对账编号前缀
    // 拌合站订单编号前缀
    public static final String PLANT_KEY_PREFIX = "BHZDD-";
    // 拌合站出场单前缀
    public static final String EXIT_TICKET_KEY_PREFIX = "BHZCCD-";
    // 配料单前缀
    public static final String INGREDIENTLIST_KEY_PREFIX = "PLD-";
    // 合同自动对账前缀
    public static final String AUTO_VERIFY_KEY_PREFIX = "DZ-";



    /**
     * 采购单号
     *
     * @param projectId 项目id
     * @return
     */
    public String getPurchaseOrderNo(Integer projectId) {
        String redisKeyPrefix = getRedisKeyPrefix(PURCHASE_ORDER_KEY_PREFIX + projectId);
        long incrNum = redisUtil.incr(redisKeyPrefix, 1);
        return redisKeyPrefix + String.format("%1$03d", incrNum);
    }

    /**
     * 收货单号
     *
     * @param projectId 项目id
     * @return
     */
    public String getReceiveNo(Integer projectId) {
        String redisKeyPrefix = getRedisKeyPrefix(RECEIVE_KEY_PREFIX + projectId);
        long incrNum = redisUtil.incr(redisKeyPrefix, 1);
        return redisKeyPrefix + String.format("%1$03d", incrNum);
    }

    /**
     * 报备记录单号
     *
     * @param projectId 项目id
     * @return
     */
    public String getReportNo(Integer projectId) {
        String redisKeyPrefix = getRedisKeyPrefix(REPORT_KEY_PREFIX + projectId);
        long incrNum = redisUtil.incr(redisKeyPrefix, 1);
        return redisKeyPrefix + String.format("%1$03d", incrNum);
    }

    /**
     * 发货单号
     *
     * @param projectId 项目id
     * @return
     */
    public String getSendNo(Integer projectId) {
        String redisKeyPrefix = getRedisKeyPrefix(WEIGH_KEY_PREFIX + projectId);
        long incrNum = redisUtil.incr(redisKeyPrefix, 1);
        return redisKeyPrefix + String.format("%1$03d", incrNum);
    }


    /**
     * 收货单号
     *
     * @param code 归属方code
     * @return
     */
    public String getReceiveNo(String code) {
        String redisKeyPrefix = getRedisKeyPrefix(WEIGH_KEY_PREFIX + code);
        long incrNum = redisUtil.incr(redisKeyPrefix, 1);
        return redisKeyPrefix + String.format("%1$03d", incrNum);
    }

    /**
     * 采购过磅记录单号
     *
     * @param projectId 项目id
     * @return
     */
    public String getWeighNo(Integer projectId) {
        String redisKeyPrefix = getRedisKeyPrefix(WEIGH_KEY_PREFIX + projectId);
        long incrNum = redisUtil.incr(redisKeyPrefix, 1);
        return redisKeyPrefix + String.format("%1$03d", incrNum);
    }

    /**
     * 物料预警记录编号
     * @param projectId 项目id
     * @return
     */
    public String getWarningNo(Integer projectId) {
        String redisKeyPrefix = getRedisKeyPrefix(WARNING_KEY_PREFIX + projectId);
        long incrNum = redisUtil.incr(redisKeyPrefix, 1);
        return redisKeyPrefix + String.format("%1$03d", incrNum);
    }


    /**
     * 拌合站出场单编号
     * @param projectId
     * @return
     */
    public String getExitTicketNo(Integer projectId) {
        String redisKeyPrefix = getRedisKeyPrefix(EXIT_TICKET_KEY_PREFIX + projectId);
        long incrNum = redisUtil.incr(redisKeyPrefix, 1);
        return redisKeyPrefix + String.format("%1$03d", incrNum);
    }

    /**
     * 合同自动生成对账编号
     * @param projectId
     * @return
     */
    public String getAutoVerifyNo(Integer projectId,String contractName) {
        return StrUtil.format("{}{}-{}",AUTO_VERIFY_KEY_PREFIX,projectId,contractName);
    }

    /**
     * 配料单号
     *
     * @param projectId 项目id
     * @return
     */
    public String getIngredientListKeyPrefix(Integer projectId) {
        String redisKeyPrefix = getRedisKeyPrefix(INGREDIENTLIST_KEY_PREFIX + projectId);
        long incrNum = redisUtil.incr(redisKeyPrefix, 1);
        return redisKeyPrefix + String.format("%1$03d", incrNum);
    }
    public String getRedisKeyPrefix(String keyPrefix) {
        LocalDate today = LocalDate.now();
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dateStr = today.format(fmt);
        return keyPrefix + "-" + dateStr;
    }

    /**
     * 获取业务编号
     * @param keyPrefix 编号前缀
     * @param projectId 项目ID
     * @return
     */
    public String getBizNo(String keyPrefix, Integer projectId) {
        String redisKeyPrefix = getRedisKeyPrefix(keyPrefix + projectId);
        long incrNum = redisUtil.incr(redisKeyPrefix, 1);
        return redisKeyPrefix + String.format("%1$03d", incrNum);
    }

    /**
     * 单据回收收货单号
     *
     * @param projectId 项目id
     * @return
     */
    public String getOCRReceiveNo(Integer projectId) {
        String redisKeyPrefix = getRedisKeyPrefix(OCR_KEY_PREFIX + projectId);
        long incrNum = redisUtil.incr(redisKeyPrefix, 1);
        return redisKeyPrefix + String.format("%1$03d", incrNum);
    }


    /**
     * 单据回收发货单号
     *
     * @param projectId 项目id
     * @return
     */
    public String getOCRSendNo(Integer projectId) {
        String redisKeyPrefix = getRedisKeyPrefix(OCR_SEND_KEY_PREFIX + projectId);
        long incrNum = redisUtil.incr(redisKeyPrefix, 1);
        return redisKeyPrefix + String.format("%1$03d", incrNum);
    }

    /**
     * 确认单同步数据收货单号
     *
     * @param projectId 项目id
     * @return
     */
    public String getConfirmReceiveNo(Integer projectId) {
        String redisKeyPrefix = getRedisKeyPrefix(CONFIRM_KEY_PREFIX + projectId);
        long incrNum = redisUtil.incr(redisKeyPrefix, 1);
        return redisKeyPrefix + String.format("%1$03d", incrNum);
    }

    /**
     * 确认发货单号
     *
     * @param projectId 项目id
     * @return
     */
    public String getConfirmSendNo(Integer projectId) {
        String redisKeyPrefix = getRedisKeyPrefix(CONFIRM_SEND_KEY_PREFIX + projectId);
        long incrNum = redisUtil.incr(redisKeyPrefix, 1);
        return redisKeyPrefix + String.format("%1$03d", incrNum);
    }
}
