package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 送货车辆预报备
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-11-16 15:07:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_pre_truck_report")
@ApiModel(value = "PreTruckReport对象", description = "送货车辆预报备")
public class PreTruckReport implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "报备记录id")
    @TableField("pre_weigh_report_id")
    private String preWeighReportId;

    @ApiModelProperty(value = "车牌号码")
    @TableField("truck_no")
    private String truckNo;

    @ApiModelProperty(value = "车辆皮重")
    private BigDecimal tare;

    @ApiModelProperty(value = "单车装货量")
    private BigDecimal capacity;

    @ApiModelProperty(value = "本车辆计划送货次数")
    private Integer times;

    @ApiModelProperty(value = "第一次计划到场时间")
    @TableField("arrive_time")
    private LocalDateTime arriveTime;

    @ApiModelProperty(value = "后续到场间隔时长(预估) 分钟")
    @TableField("interval_time")
    private Integer intervalTime;

    @ApiModelProperty(value = "微信人员id")
    @TableField("weChat_id")
    private String weChatId;

    @ApiModelProperty(value = "公司id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
