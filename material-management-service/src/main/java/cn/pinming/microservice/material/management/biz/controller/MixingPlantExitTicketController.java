package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.form.ExitTicketForm;
import cn.pinming.microservice.material.management.biz.mapper.MixingPlantExitTicketMapper;
import cn.pinming.microservice.material.management.biz.query.BaseQuery;
import cn.pinming.microservice.material.management.biz.service.IMixingPlantExitTicketService;
import cn.pinming.microservice.material.management.biz.vo.ExistTicketDetailVO;
import cn.pinming.microservice.material.management.biz.vo.ExistTicketHistoryVO;
import cn.pinming.microservice.material.management.biz.vo.ExitTicketPageVO;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 拌合站出场单 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-27 15:11:44
 */
@Api(tags = "拌合站", value = "zh")
@RestController
@RequestMapping("/api/biz/mixing-plant-exit-ticket")
public class MixingPlantExitTicketController {

    @Resource
    private IMixingPlantExitTicketService mixingPlantExitTicketService;
    @Resource
    private MixingPlantExitTicketMapper mixingPlantExitTicketMapper;
    @Resource
    private AuthUserHolder authUserHolder;

    @ApiOperation("新增")
    @Log(title = "新增", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public ResponseEntity<Response> add(@RequestBody @Validated ExitTicketForm form) {
        String id = mixingPlantExitTicketService.add(form);
        return ResponseEntity.ok(new SuccessResponse(id));
    }

    @ApiOperation("作废")
    @Log(title = "作废", businessType = BusinessType.DELETE)
    @GetMapping("/cancel/{id}/{mixingPlantOrderDetailId}")
    public ResponseEntity<Response> cancel(@PathVariable("id")String id,@PathVariable("mixingPlantOrderDetailId")String mixingPlantOrderDetailId) {
        mixingPlantExitTicketService.cancel(id,mixingPlantOrderDetailId);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation("编辑")
    @Log(title = "编辑", businessType = BusinessType.UPDATE)
    @PostMapping("/fix")
    public ResponseEntity<Response> fix(@RequestBody @Validated ExitTicketForm form) {
        mixingPlantExitTicketService.fix(form);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "查看-列表",response = ExitTicketPageVO.class)
    @Log(title = "查看-列表", businessType = BusinessType.QUERY)
    @PostMapping("/page")
    public ResponseEntity<Response> page(@RequestBody BaseQuery query) {
        IPage<ExitTicketPageVO> page = mixingPlantExitTicketService.selectPage(query);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "查看-详情",response = ExistTicketDetailVO.class)
    @Log(title = "查看-详情", businessType = BusinessType.QUERY)
    @GetMapping("/detail/{id}")
    public ResponseEntity<Response> detail(@PathVariable("id")String id) {
        ExistTicketDetailVO vo = mixingPlantExitTicketService.selectDetail(id);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation("车牌选择")
    @Log(title = "车牌选择", businessType = BusinessType.QUERY)
    @PostMapping("/truckNo")
    public ResponseEntity<Response> truckNo(@RequestParam(value = "truckNo", required = false) String truckNo) {
        List<String> list = mixingPlantExitTicketService.truckNoHistory(truckNo);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

//    @ApiOperation(value = "历史（车次、载货量、发货时间、模板）",response = ExistTicketHistoryVO.class)
//    @Log(title = "历史", businessType = BusinessType.QUERY)
//    @GetMapping("/history/{mixingPlantOrderDetailId}")
//    public ResponseEntity<Response> history(@PathVariable("mixingPlantOrderDetailId")String id) {
//        ExistTicketHistoryVO vo = mixingPlantExitTicketMapper.history(id);
//        return ResponseEntity.ok(new SuccessResponse(vo));
//    }

    @ApiOperation(value = "打印")
    @Log(title = "打印", businessType = BusinessType.QUERY)
    @GetMapping("/print/{id}/{templateId}/{mixingPlantOrderDetailId}")
    public ResponseEntity<Response> print(@PathVariable String id, @PathVariable String templateId,@PathVariable String mixingPlantOrderDetailId) {
        return ResponseEntity.ok(new SuccessResponse(mixingPlantExitTicketService.print(id, templateId,mixingPlantOrderDetailId)));
    }
}
