package cn.pinming.microservice.material.management.config;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.core.cookie.support.AuthUserHolder;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.List;

@Configuration
@MapperScan(basePackages = {"cn.pinming.microservice.material.management.biz.mapper"})
public class MybatisPlusConfig {

    private static final String TENANT_ID_COLUMN = "company_id";

    private static final List<String> TABLES = ListUtil.of("配置忽略租户的表");

    @Resource
    private AuthUserHolder authUserHolder;

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new TenantLineInnerInterceptor(new TenantLineHandler() {
            @Override
            public Expression getTenantId() {
                return new LongValue(authUserHolder.getCurrentUser().getCurrentCompanyId());
            }

            @Override
            public String getTenantIdColumn() {
                return "company_id";
            }

            @Override
            public boolean ignoreTable(String tableName) {
                if (ObjectUtil.isEmpty(authUserHolder.getCurrentUser())){
                    return true;
                }
                return TABLES.stream().anyMatch(e -> e.equalsIgnoreCase(tableName));
            }
        }));
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }
}
