package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.materialManagement.dto.WeighInfoDTO;
import cn.pinming.microservice.material.management.biz.dto.FourCircleDTO;
import cn.pinming.microservice.material.management.biz.dto.FourCircleUsageDTO;
import cn.pinming.microservice.material.management.biz.dto.SimpleWeighbridgeDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialWeighbridge;
import cn.pinming.microservice.material.management.biz.query.MaterialWeighbridgeQuery;
import cn.pinming.microservice.material.management.biz.vo.FourCircleVO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 地磅信息 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-08-23 21:38:33
 */
public interface MaterialWeighbridgeMapper extends BaseMapper<MaterialWeighbridge> {

    /**
     * 根据公司id查找地磅信息
     *
     * @param query
     * @return
     */
    List<SimpleWeighbridgeDTO> selectSimpleWeighbridge(MaterialWeighbridgeQuery query);

    @InterceptorIgnore(tenantLine = "true")
    List<WeighInfoDTO> selectWeighInfo(@Param("companyId") Integer companyId, @Param("projectId") Integer projectId);

    @InterceptorIgnore(tenantLine = "true")
    MaterialWeighbridge selectByDeviceSn(@Param("companyId") Integer companyId
            , @Param("projectId") Integer projectId
            , @Param("deviceSn") String deviceSn);

    BigDecimal selectInstallationNum(List<Integer> projectIdList);

    BigDecimal selectOnlineRate(List<Integer> projectIdList);

    List<FourCircleDTO> selectInstallationRateList(List<Integer> projectIdList);

    List<Integer> selectInstallationList(List<Integer> projectIdList);

    List<Integer> selectOnlineList(List<Integer> projectIdList);

    List<Integer> selectInstallationPJList(List<Integer> projectIdList);
}
