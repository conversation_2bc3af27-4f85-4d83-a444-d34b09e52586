package cn.pinming.microservice.material.management.proxy;

import cn.pinming.microservice.material.management.biz.vo.CooperateVO;
import cn.pinming.microservice.material_unit.api.enterprise.dto.CooperateEnterpriseDto;
import lombok.NonNull;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2021/9/7 9:36 上午
 */
public interface CooperateServiceProxy {

    List<CooperateVO> findCooperateByCompanyId(Integer companyId);

    List<CooperateVO> findCooperateByIds(@NonNull Integer companyId,
                                         @NonNull String cooperateIds);

    List<CooperateEnterpriseDto> findByCompanyId(Integer companyId, Integer current, Integer size);

}
