package cn.pinming.microservice.material.management.common.manager;

import cn.pinming.microservice.material.management.biz.entity.MaterialApiLog;
import cn.pinming.microservice.material.management.biz.entity.MaterialIotLog;
import cn.pinming.microservice.material.management.biz.service.IMaterialApiLogService;
import cn.pinming.microservice.material.management.biz.service.IMaterialIotLogService;
import cn.pinming.microservice.material.management.common.util.SpringUtils;
import java.util.TimerTask;

public class AsyncFactory {

    public static TimerTask recordOper(final MaterialApiLog apiLog) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(IMaterialApiLogService.class).save(apiLog);
            }
        };
    }

    public static TimerTask recordIotOper(final MaterialIotLog iotLog) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(IMaterialIotLogService.class).save(iotLog);
            }
        };
    }
}
