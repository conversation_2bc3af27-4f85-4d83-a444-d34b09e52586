package cn.pinming.microservice.material.management.biz.openapi;

import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.form.PurchaseContractForm;
import cn.pinming.microservice.material.management.biz.query.PurchaseContractQuery;
import cn.pinming.microservice.material.management.biz.service.IPurchaseContractService;
import cn.pinming.microservice.material.management.biz.vo.ProjectVO;
import cn.pinming.microservice.material.management.biz.vo.PurchaseContractVO;
import cn.pinming.microservice.material.management.common.OpenApiAdd;
import cn.pinming.microservice.material.management.common.ValidCommon;
import cn.pinming.microservice.material.management.common.response.Result;
import cn.pinming.microservice.material.management.common.util.OpenApiUtil;
import cn.pinming.openapi.client.OpenIStrategy;
import cn.pinming.openapi.client.annotation.OpenApi;
import cn.pinming.openapi.client.annotation.OpenApiEndpoint;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Validator;
import java.util.List;
import java.util.Map;

@Component
@OpenApiEndpoint
public class ContractOpenApi extends OpenIStrategy {

    @Resource
    private IPurchaseContractService contractService;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private Validator validator;

    /**
     * 新增合同
     */
    @OpenApi(itype = "/test/saveContract")
    public String saveContract(Map<String, String[]> map) {
        Integer companyId = OpenApiUtil.getCompanyId(map);
        String data = getMapValue(map,"data");
        log.info("----保存元数据data ------>");
        PurchaseContractForm form = JSONObject.parseObject(data, PurchaseContractForm.class);
        ValidCommon.validRequestParams(validator.validate(form, OpenApiAdd.class));
        form.setCompanyId(companyId);
        String str = contractService.savePurchaseContract(form);
        return OpenApiUtil.toJSONString(Result.success(str));
    }

    /**
     * 删除合同
     */
    @OpenApi(itype = "/test/delContract")
    public String delContract(Map<String, String[]> map) {
        String data = getMapValue(map,"data");
        log.info("----删除元数据data ------>{}" + data);
        contractService.deletePurchaseContractById(data);
        return buildOk();
    }

    /**
     * 查看合同列表
     */
    @OpenApi(itype = "/test/listContract")
    public String listContract(Map<String, String[]> map) {
        Integer companyId = OpenApiUtil.getCompanyId(map);
        String data = getMapValue(map,"data");
        log.info("----根据条件data查看合同列表 ------>{}" + data);
        PurchaseContractQuery query = JSONObject.parseObject(data, PurchaseContractQuery.class);
        query.setCompanyId(companyId);
        IPage<?> result = contractService.pageListByQuery(query);
        return OpenApiUtil.toJSONString(Result.success(result));
    }

    /**
     * 查看合同详情
     */
    @OpenApi(itype = "/test/detailContract")
    public String detailContract(Map<String, String[]> map) {
        String data = getMapValue(map,"data");
        Integer companyId = OpenApiUtil.getCompanyId(map);
        log.info("----根据条件data查看合同详情 ------>{}" + data);
        PurchaseContractVO vo = contractService.queryDetailById(data,companyId);
        return OpenApiUtil.toJSONString(Result.success(vo));
    }

    /**
     * 采购合同-所属项目
     */
    @OpenApi(itype = "/test/belongContract")
    public String belongContract(Map<String, String[]> map) {
        String data = getMapValue(map,"data");
        log.info("----查看合同所属项目 ------>{}" + data);
        List<ProjectVO> list =  contractService.listBelongProjects(data);
        return OpenApiUtil.toJSONString(Result.success(list));
    }

    @Override
    protected String toJson(Object o) {
        return JSONObject.toJSONString(o);
    }
}
