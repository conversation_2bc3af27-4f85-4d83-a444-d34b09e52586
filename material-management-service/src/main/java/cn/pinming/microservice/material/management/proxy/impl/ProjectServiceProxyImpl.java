package cn.pinming.microservice.material.management.proxy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import cn.pinming.core.common.model.PageList;
import cn.pinming.core.common.model.Pagination;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.entity.StatisticsProjectConfig;
import cn.pinming.microservice.material.management.biz.query.MaterialWeighbridgeQuery;
import cn.pinming.microservice.material.management.biz.service.IStatisticsProjectConfigService;
import cn.pinming.microservice.material.management.biz.vo.ProjectVO;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.v2.common.QueryPagination;
import cn.pinming.v2.common.api.service.PlugService;
import cn.pinming.v2.company.api.dto.department.DepartmentDto;
import cn.pinming.v2.company.api.dto.department.DepartmentQueryDto;
import cn.pinming.v2.company.api.service.DepartmentService;
import cn.pinming.v2.project.api.dto.ConstructionProjectDto;
import cn.pinming.v2.project.api.dto.ConstructionProjectQueryDto;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import cn.pinming.v2.project.api.service.ConstructionProjectService;
import cn.pinming.yfzx.cxptz.thirdbutt.api.service.ThirdpartDataApiService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2021/9/1 4:45 下午
 */
@Slf4j
@Component
public class ProjectServiceProxyImpl implements ProjectServiceProxy {

    @Reference(parameters = {
            "cache.seconds", "3600"
    }, cache = "expiring", mock = "return null")
    private ConstructionProjectService constructionProjectService;

    @Reference(parameters = {
            "cache.seconds", "3600"
    }, cache = "expiring", mock = "return null")
    private ThirdpartDataApiService thirdpartDataApiService;

    @Reference(parameters = {
            "return null"
    })
    private DepartmentService departmentService;

    @Reference
    private PlugService plugService;

    @Resource
    private AuthUserHolder authUserHolder;

    @Resource
    private IStatisticsProjectConfigService projectConfigService;

    /**
     * 在建项目列表
     *
     * @param companyId
     * @return
     */
    @Override
    public List<ProjectVO> getProjectsByCompanyId(@NotNull Integer companyId) {
        ConstructionProjectQueryDto queryDto = new ConstructionProjectQueryDto();
        queryDto.setCompanyId(companyId);
        queryDto.setPage(0);
        queryDto.setPageSize(Integer.MAX_VALUE);
        List<ConstructionProjectDto> projectListWithOutPage = constructionProjectService.findProjectListWithOutPage(queryDto);
        List<ProjectVO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(projectListWithOutPage)) {
            list = projectListWithOutPage.stream().map(obj -> {
                ProjectVO vo = new ProjectVO();
                BeanUtils.copyProperties(obj, vo);
                return vo;
            }).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public List<ProjectVO> getAllProjectsByCompanyId(Integer companyId) {
        ConstructionProjectQueryDto queryDto = new ConstructionProjectQueryDto();
        queryDto.setCompanyId(companyId);
        queryDto.setPage(0);
        queryDto.setPageSize(Integer.MAX_VALUE);
        List<ConstructionProjectDto> projectListWithOutPage = constructionProjectService.findProjectListWithOutPage(queryDto);
        List<ProjectVO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(projectListWithOutPage)) {
            list = projectListWithOutPage.stream().map(obj -> {
                ProjectVO vo = new ProjectVO();
                BeanUtils.copyProperties(obj, vo);
                return vo;
            }).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public List<ProjectVO> getAllProjectsByCompanyDeptId(@NotNull Integer companyId, Integer deptId) {
        ConstructionProjectQueryDto queryDto = new ConstructionProjectQueryDto();
        queryDto.setCompanyId(companyId);
        queryDto.setPage(1);
        queryDto.setPageSize(Integer.MAX_VALUE);
        Optional.ofNullable(deptId).ifPresent(id -> queryDto.setDepartmentId(id));
        List<ConstructionProjectDto> projectListWithOutPage = constructionProjectService.findProjectListWithOutPage(queryDto);
        List<ProjectVO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(projectListWithOutPage)) {
            list = projectListWithOutPage.stream().map(obj -> {
                ProjectVO vo = new ProjectVO();
                BeanUtils.copyProperties(obj, vo);
                return vo;
            }).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public List<ProjectVO> getProjectsByProjectIds(@NotNull Collection<Integer> projectIds) {
        ConstructionProjectQueryDto queryDto = new ConstructionProjectQueryDto();
        queryDto.setProjectIds(new ArrayList<>(projectIds));
        queryDto.setPage(0);
        queryDto.setPageSize(Integer.MAX_VALUE);
        List<ConstructionProjectDto> projectListWithOutPage = constructionProjectService.findProjectListWithOutPage(queryDto);
        List<ProjectVO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(projectListWithOutPage)) {
            list = projectListWithOutPage.stream().map(obj -> {
                ProjectVO vo = new ProjectVO();
                BeanUtils.copyProperties(obj, vo);
                return vo;
            }).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public List<ProjectVO> getSimpleProjects(@NotNull List<Integer> projectIds) {
        List<ProjectVO> list = projectIds.stream().distinct().map(obj -> {
            SimpleConstructionProjectDto simpleProject = constructionProjectService.findSimpleProject(obj);
            if (Objects.nonNull(simpleProject)) {
                ProjectVO vo = new ProjectVO();
                BeanUtils.copyProperties(simpleProject, vo);
                return vo;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return list;
    }


    @Override
    public ProjectVO getProjectById(@NotNull Integer projectId) {
        ConstructionProjectDto constructionProjectDto = constructionProjectService.projectDetail(projectId);
        ProjectVO vo = new ProjectVO();
        if (constructionProjectDto != null) {
            BeanUtils.copyProperties(constructionProjectDto, vo);
        }
        return vo;
    }

    @Override
    public Integer getProjectIdByCode(@NotNull Integer companyId, Long projectCode) {
        Map<Long, Integer> thirdpartProjectMap = thirdpartDataApiService.findProjectIdByThirdpartProjectId(companyId, Collections.singletonList(projectCode), "zh");
        if (CollUtil.isEmpty(thirdpartProjectMap)) {
            return null;
        }
        return thirdpartProjectMap.get(projectCode);
    }

    @Override
    public PageList<ConstructionProjectDto> getProjectsPageByQuery(MaterialWeighbridgeQuery query) {
        ConstructionProjectQueryDto projectQuery = new ConstructionProjectQueryDto();
        projectQuery.setCompanyId(query.getCompanyId());
        projectQuery.setPage((int) query.getCurrent());
        projectQuery.setPageSize((int) query.getSize());
        if (query.getStatus() != null) {
            projectQuery.setStatus(query.getStatus());
        }
        if (query.getProjectIds() != null) {
            projectQuery.setProjectIds(query.getProjectIds());
        }
        if (query.getProjectId() != null) {
            List<Integer> projectIds = new ArrayList<>(2);
            projectIds.add(query.getProjectId());
            projectQuery.setProjectIds(projectIds);
        }
        return constructionProjectService.findProjects(projectQuery);
    }

    @Override
    public SimpleConstructionProjectDto findSimpleProject(Integer projectId) {
        if (projectId == null) {
            return null;
        }
        return constructionProjectService.findSimpleProject(projectId);
    }

    @Override
    public ConstructionProjectDto findProject(Integer projectId) {
        if (projectId == null) {
            return null;
        }
        return constructionProjectService.projectDetail(projectId);
    }

    @Override
    public List<Integer> getPluginProjectIds(String pluginNo) {
        List<Integer> list = Lists.newArrayList();
        QueryPagination<String> plugNo = new QueryPagination<>();
        plugNo.setT(pluginNo);
        Pagination page =new Pagination();
        page.setPage(1);
        page.setPageSize(Integer.MAX_VALUE);
        plugNo.setPagination(page);

        // 开启插件的项目列表
        PageList<Integer> enablePlugProject = plugService.findEnablePlugProject(plugNo);
        if (ObjectUtil.isNotNull(enablePlugProject) && CollectionUtil.isNotEmpty(enablePlugProject.getDataList())) {
            list = enablePlugProject.getDataList();
        }
        return list;
    }

    @Override
    public List<Integer> getAllProjectIdByCompanyDeptId(@NotNull Integer companyId, Integer deptId) {
        ConstructionProjectQueryDto queryDto = new ConstructionProjectQueryDto();
        queryDto.setCompanyId(companyId);
        queryDto.setPage(1);
        queryDto.setPageSize(Integer.MAX_VALUE);
        Optional.ofNullable(deptId).ifPresent(queryDto::setDepartmentId);
        List<ConstructionProjectDto> projectListWithOutPage = constructionProjectService.findProjectListWithOutPage(queryDto);
        List<Integer> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(projectListWithOutPage)) {
            list = projectListWithOutPage.stream().map(ConstructionProjectDto::getProjectId).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public List<SimpleConstructionProjectDto> findProjectsByProjectIds(List<Integer> projectIds) {
        if (CollUtil.isEmpty(projectIds)) {
            return null;
        }
        return constructionProjectService.findProjectsByProjectIds(projectIds);
    }

    @Override
    public List<ProjectVO> getProjectsByDepartmentId(@NotNull List<Integer> departments) {
        List<SimpleConstructionProjectDto> constructionProjectDtos = constructionProjectService.findProjectsByDepartmentIds(departments);
        List<ProjectVO> projectVOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(constructionProjectDtos)) {
            projectVOS = constructionProjectDtos.stream().map(obj -> {
                ProjectVO projectVO = new ProjectVO();
                BeanUtils.copyProperties(obj, projectVO);
                return projectVO;
            }).collect(Collectors.toList());
        }
        return projectVOS;
    }

    /**
     * 先查询所选企业组织节点下所有项目，无项目则无数据，有项目则与配置的项目范围取交集，有交集则返回交集项目，没交集则返回所选节点下所有项目
     *
     * @param compId 企业ID
     * @param deptId 部门ID
     * @return
     */
    @Override
    public List<Integer> statisticsProjectIds(@NonNull Integer compId, Integer deptId) {
        // 组织树根节点
        if (ObjectUtil.isNotNull(deptId)) {
            deptId = deptId == -1 ? null : deptId;
        }
        // 获取当前组织节点下的所有项目
        DepartmentQueryDto param = new DepartmentQueryDto();
        param.setCompanyId(compId);
        param.setDepartmentDepth(2);
        List<Byte> types = Lists.newArrayList();
        types.add((byte) 3);
        param.setTypeList(types);
        // 部门ID
        Optional.ofNullable(deptId).ifPresent(id -> param.setParentDepartmentId(id));
        log.info("ProjectServiceProxyImpl statisticsProjectIds findDepartmentListByParam param:{}", JSONObject.toJSONString(param));
        List<DepartmentDto> projectList = departmentService.findDepartmentListByParam(param);
        log.info("ProjectServiceProxyImpl statisticsProjectIds findDepartmentListByParam project id:{}",
                CollectionUtil.isNotEmpty(projectList) ? JSONObject.toJSONString(projectList.stream().map(DepartmentDto::getProjectId).collect(Collectors.toList())) : null);
        if (CollectionUtil.isNotEmpty(projectList)) {
            List<Integer> pjds = projectList.stream().map(DepartmentDto::getProjectId).collect(Collectors.toList());
            AuthUser currentUser = authUserHolder.getCurrentUser();
            if (ObjectUtil.isNotNull(currentUser)) {
                String userid = currentUser.getId();
                QueryWrapper<StatisticsProjectConfig> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(StatisticsProjectConfig::getCreateId, userid);
                StatisticsProjectConfig config = projectConfigService.getOne(queryWrapper);
                if (ObjectUtil.isNotNull(config) && StringUtils.isNotBlank(config.getBelongProjectId())
                        && StringUtils.isNotBlank(config.getStatus())) {
                    String belongProjectId = config.getBelongProjectId();
                    String status = config.getStatus();
                    // 查询项目状态
                    ConstructionProjectQueryDto query = new ConstructionProjectQueryDto();
                    query.setDisableLogo(true);
                    query.setProjectIds(Arrays.stream(belongProjectId.split(",")).map(Integer::parseInt).collect(Collectors.toList()));
                    List<ConstructionProjectDto> dtoList = constructionProjectService.findProjectListWithOutPage(query);
                    // 过滤项目状态
                    List<String> statusList = Arrays.stream(status.split(",")).collect(Collectors.toList());
                    List<Integer> projectIds = dtoList.stream().filter(item ->
                                    ObjectUtil.isNotNull(item.getStatus()) && statusList.contains(item.getStatus().toString()))
                            .map(ConstructionProjectDto::getProjectId).collect(Collectors.toList());
                    // 交集
                    List<Integer> intersection = (List<Integer>) CollectionUtils.intersection(projectIds, pjds);
                    return CollectionUtil.isNotEmpty(intersection) ? intersection : pjds;
                }
                return pjds;
            }
        }
        return Arrays.asList(-1);
    }

    /**
     * 直属下级单位：
     * 直属下级包含项目和企业
     * 1. 项目：与配置的项目范围取交集，有交集则返回交集项目，没交集则pass
     * 2. 企业：先取企业下所有项目，与配置的项目范围取交集，有交集则返回交集项目，没交集则pass
     *
     * @param compId 企业ID
     * @param deptId 部门ID
     * @return
     */
    @Override
    public Map<String, List<Integer>> directlyUnderDeptOrProject(@NonNull Integer compId, Integer deptId) {
        if (ObjectUtil.isNotNull(deptId)) {
            deptId = deptId == -1 ? null : deptId;
        }
        Map<String, List<Integer>> resultMap = new HashMap<>();
        // 项目范围配置项目ID
        List<Integer> scopeProjectIds = Lists.newArrayList();
        // 项目范围配置
        AuthUser currentUser = authUserHolder.getCurrentUser();
        if (ObjectUtil.isNotNull(currentUser)) {
            String userid = currentUser.getId();
            QueryWrapper<StatisticsProjectConfig> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StatisticsProjectConfig::getCreateId, userid);
            StatisticsProjectConfig config = projectConfigService.getOne(queryWrapper);
            if (ObjectUtil.isNotNull(config) && StringUtils.isNotBlank(config.getBelongProjectId())
                    && StringUtils.isNotBlank(config.getStatus())) {
                String belongProjectId = config.getBelongProjectId();
                String status = config.getStatus();
                // 查询项目状态
                ConstructionProjectQueryDto query = new ConstructionProjectQueryDto();
                query.setDisableLogo(true);
                query.setProjectIds(Arrays.stream(belongProjectId.split(",")).map(Integer::parseInt).collect(Collectors.toList()));
                List<ConstructionProjectDto> dtoList = constructionProjectService.findProjectListWithOutPage(query);
                // 过滤项目状态
                if (CollectionUtil.isNotEmpty(dtoList)) {
                    List<String> statusList = Arrays.stream(status.split(",")).collect(Collectors.toList());
                    scopeProjectIds = dtoList.stream().filter(item ->
                                    ObjectUtil.isNotNull(item.getStatus()) && statusList.contains(item.getStatus().toString()))
                            .map(ConstructionProjectDto::getProjectId).collect(Collectors.toList());
                }
            }
        }
        // 获取当前组织节点下直属下级单位(分公司或项目)
        DepartmentQueryDto param = new DepartmentQueryDto();
        param.setCompanyId(compId);
        param.setDepartmentDepth(1);
        List<Byte> types = Lists.newArrayList();
        types.add((byte) 1);
        types.add((byte) 3);
        param.setTypeList(types);
        // 部门ID
        Optional.ofNullable(deptId).ifPresent(id -> param.setParentDepartmentId(id));
        log.info("ProjectServiceProxyImpl statisticsProjectIds directlyUnderDeptOrProject param:{}", JSONObject.toJSONString(param));
        List<DepartmentDto> projectList = departmentService.findDepartmentListByParam(param);
        log.info("ProjectServiceProxyImpl statisticsProjectIds directlyUnderDeptOrProject project id:{}",
                CollectionUtil.isNotEmpty(projectList) ? JSONObject.toJSONString(projectList.stream().map(DepartmentDto::getProjectId).collect(Collectors.toList())) : null);
        if (CollectionUtil.isNotEmpty(projectList)) {
            Map<Boolean, List<DepartmentDto>> deptMap = projectList.stream().collect(Collectors.partitioningBy(item -> 1 == item.getType().byteValue()));
            // 过滤出分公司
            List<DepartmentDto> departmentDtos = deptMap.getOrDefault(true, Lists.newArrayList());
            // 过滤出项目
            List<DepartmentDto> projects = deptMap.getOrDefault(false, Lists.newArrayList());
            // 项目
            if (CollectionUtil.isNotEmpty(projects)) {
                List<Integer> projectIds = projects.stream().map(DepartmentDto::getProjectId).collect(Collectors.toList());
                // 交集
                List<Integer> intersection = (List<Integer>) CollectionUtils.intersection(scopeProjectIds, projectIds);
                if (CollectionUtil.isNotEmpty(intersection)) {
                    List<DepartmentDto> dtoList = projects.stream().filter(project -> intersection.contains(project.getProjectId())).collect(Collectors.toList());
                    for (DepartmentDto departmentDto : dtoList) {
                        Integer projectId = departmentDto.getProjectId();
                        String key = projectId + "-" + departmentDto.getDepartmentName();
                        resultMap.put(key, Arrays.asList(projectId));
                    }
                }
            }
            // 分公司
            if (CollectionUtil.isNotEmpty(departmentDtos)) {
                List<Pair<Integer, Integer>> compDeptIds = departmentDtos.stream().map(item -> Pair.of(item.getCompanyId(), item.getDepartmentId())).collect(Collectors.toList());
                Map<Integer, String> departmentMap = departmentDtos.stream().collect(Collectors.toMap(DepartmentDto::getDepartmentId, DepartmentDto::getDepartmentName));
                for (Pair<Integer, Integer> pair : compDeptIds) {
                    Integer cId = pair.getKey();
                    Integer dId = pair.getValue();
                    Map<String, List<Integer>> map = new HashMap<>();
                    String key = dId + "-" + departmentMap.get(dId);
                    map.put(key, Lists.newArrayList());
                    // 获取当前组织节点下的所有项目
                    DepartmentQueryDto queryDto = new DepartmentQueryDto();
                    queryDto.setCompanyId(cId);
                    queryDto.setDepartmentDepth(2);
                    List<Byte> queryTypes = Lists.newArrayList();
                    queryTypes.add((byte) 3);
                    queryDto.setTypeList(queryTypes);
                    // 部门ID
                    queryDto.setParentDepartmentId(dId);
                    log.info("ProjectServiceProxyImpl statisticsProjectIds directlyUnderDeptOrProject child company param:{}", JSONObject.toJSONString(queryDto));
                    List<DepartmentDto> dtoList = departmentService.findDepartmentListByParam(queryDto);
                    log.info("ProjectServiceProxyImpl statisticsProjectIds directlyUnderDeptOrProject child company project id:{}",
                            CollectionUtil.isNotEmpty(dtoList) ? JSONObject.toJSONString(dtoList.stream().map(DepartmentDto::getProjectId).collect(Collectors.toList())) : null);
                    if (CollectionUtil.isNotEmpty(dtoList)) {
                        List<Integer> companyProjectIds = dtoList.stream().map(DepartmentDto::getProjectId).collect(Collectors.toList());
                        // 交集
                        List<Integer> intersection = (List<Integer>) CollectionUtils.intersection(scopeProjectIds, companyProjectIds);
                        if (CollectionUtil.isNotEmpty(intersection)) {
                            resultMap.put(key, intersection);
                        }
                    }
                }
            }
        }
        return resultMap;
    }

    @Override
    public List<Integer> statisticsDeptProjectIds(@NotNull Integer compId, Integer deptId) {
        // 组织树根节点
        if (ObjectUtil.isNotNull(deptId)) {
            deptId = deptId == -1 ? null : deptId;
        }
        // 获取当前组织节点下的所有项目
        DepartmentQueryDto param = new DepartmentQueryDto();
        param.setCompanyId(compId);
        param.setDepartmentDepth(2);
        List<Byte> types = Lists.newArrayList();
        types.add((byte) 3);
        param.setTypeList(types);
        // 部门ID
        Optional.ofNullable(deptId).ifPresent(id -> param.setParentDepartmentId(id));
        List<DepartmentDto> projectList = departmentService.findDepartmentListByParam(param);
        if (CollectionUtil.isNotEmpty(projectList)) {
            List<Integer> pjds = projectList.stream().map(DepartmentDto::getProjectId).collect(Collectors.toList());
            return pjds;
        }
        return Lists.newArrayList();
    }

    @Override
    public Map<String, List<Integer>> allDirectlyUnderDeptOrProject(@NotNull Integer compId, Integer deptId) {
        if (ObjectUtil.isNotNull(deptId)) {
            deptId = deptId == -1 ? null : deptId;
        }
        Map<String, List<Integer>> resultMap = new HashMap<>();
        // 获取当前组织节点下直属下级单位(分公司或项目)
        DepartmentQueryDto param = new DepartmentQueryDto();
        param.setCompanyId(compId);
        param.setDepartmentDepth(1);
        List<Byte> types = Lists.newArrayList();
        types.add((byte) 1);
        types.add((byte) 3);
        param.setTypeList(types);
        // 部门ID
        Optional.ofNullable(deptId).ifPresent(id -> param.setParentDepartmentId(id));
        List<DepartmentDto> projectList = departmentService.findDepartmentListByParam(param);
        if (CollectionUtil.isNotEmpty(projectList)) {
            Map<Boolean, List<DepartmentDto>> deptMap = projectList.stream().collect(Collectors.partitioningBy(item -> 1 == item.getType().byteValue()));
            // 过滤出分公司
            List<DepartmentDto> departmentDtos = deptMap.getOrDefault(true, Lists.newArrayList());
            // 过滤出项目
            List<DepartmentDto> projects = deptMap.getOrDefault(false, Lists.newArrayList());
            // 项目
            if (CollectionUtil.isNotEmpty(projects)) {
                for (DepartmentDto item : projects) {
                    Integer projectId = item.getProjectId();
                    List<Integer> integers = Arrays.asList(projectId);
                    String key = projectId + "-" + item.getDepartmentName();
                    resultMap.put(key, integers);
                }
            }
            // 分公司
            if (CollectionUtil.isNotEmpty(departmentDtos)) {
                List<Pair<Integer, Integer>> compDeptIds = departmentDtos.stream().map(item -> Pair.of(item.getCompanyId(), item.getDepartmentId())).collect(Collectors.toList());
                Map<Integer, String> departmentMap = departmentDtos.stream().collect(Collectors.toMap(DepartmentDto::getDepartmentId, DepartmentDto::getDepartmentName));
                for (Pair<Integer, Integer> pair : compDeptIds) {
                    Integer cId = pair.getKey();
                    Integer dId = pair.getValue();
                    Map<String, List<Integer>> map = new HashMap<>();
                    String key = dId + "-" + departmentMap.get(dId);
                    map.put(key, Lists.newArrayList());
                    // 获取当前组织节点下的所有项目
                    DepartmentQueryDto queryDto = new DepartmentQueryDto();
                    queryDto.setCompanyId(cId);
                    queryDto.setDepartmentDepth(2);
                    List<Byte> queryTypes = Lists.newArrayList();
                    queryTypes.add((byte) 3);
                    queryDto.setTypeList(queryTypes);
                    // 部门ID
                    queryDto.setParentDepartmentId(dId);
                    List<DepartmentDto> dtoList = departmentService.findDepartmentListByParam(queryDto);
                    if (CollectionUtil.isNotEmpty(dtoList)) {
                        List<Integer> companyProjectIds = dtoList.stream().map(DepartmentDto::getProjectId).collect(Collectors.toList());
                        resultMap.put(key, companyProjectIds);
                    }
                }
            }
        }
        return resultMap;
    }
}
