package cn.pinming.microservice.material.management.common.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.material.management.biz.dto.PurchaseOrderDetailSimpleDTO;
import cn.pinming.microservice.material.management.biz.dto.SimpleContractDetailDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialData;
import cn.pinming.microservice.material.management.biz.entity.MaterialWarning;
import cn.pinming.microservice.material.management.biz.enums.DeviationStatusEnum;
import cn.pinming.microservice.material.management.biz.enums.WarningSubTypeEnum;
import cn.pinming.microservice.material.management.biz.enums.WarningTypeEnum;
import cn.pinming.microservice.material.management.biz.enums.WeighTypeEnum;
import cn.pinming.microservice.material.management.biz.form.MaterialWarningForm;
import cn.pinming.microservice.material.management.biz.service.IMaterialWarningService;
import cn.pinming.microservice.material.management.biz.service.IPurchaseContractDetailService;
import cn.pinming.microservice.material.management.biz.service.IPurchaseContractService;
import cn.pinming.microservice.material.management.biz.service.IPurchaseOrderDetailService;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 预警工具
 * <AUTHOR>
 */
@Component
public class WarningUtil {
    @Resource
    private IMaterialWarningService materialWarningService;
    @Resource
    private NoUtil noUtil;
    @Resource
    private UserUtil userUtil;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private IPurchaseContractDetailService materialContractDetailService;
    @Resource
    private IPurchaseContractService materialContractService;
    @Resource
    private IPurchaseOrderDetailService purchaseOrderDetailService;


    public void saveWarning(MaterialWarningForm form) {
        MaterialWarning materialWarning = new MaterialWarning();
        BeanUtils.copyProperties(form,materialWarning);
        materialWarning.setWarningNo(noUtil.getWarningNo(form.getSourceProjectId()));
        materialWarning.setWarningSource(form.getWarningSourceStr());
        materialWarningService.save(materialWarning);
    }

    public List<MaterialWarning> createWarnings(MaterialData materialData, String receiveNo, Byte receiveType) {
        List<MaterialWarning>  list = new ArrayList<>();
        return this.warn(list,materialData,receiveNo,receiveType);
    }

    private List<MaterialWarning> warn(List<MaterialWarning> list,MaterialData materialData,String warningSourceNo,Byte receiveType) {
        String warningSource = receiveType.equals(WeighTypeEnum.RECEIVE.value()) ? "地磅收料" : "地磅发料";

        // 基本信息
        SimpleContractDetailDTO simpleContractDetailDTO = new SimpleContractDetailDTO();
        if (receiveType.equals(WeighTypeEnum.RECEIVE.value()) && StrUtil.isNotBlank(materialData.getContractDetailId())) {
            simpleContractDetailDTO = materialContractService.querySimpleContractDetail(materialData.getContractDetailId());
        }

        // 规范性预警
        // 称重转换系数异常
        if (ObjectUtil.isNull(materialData.getRatio())) {
            // 未设置转换系数
            String warningInfo = WarningSubTypeEnum.WEIGH_CONVERT_TWO.desc();
            MaterialWarning warning = createWarningEntity(warningSourceNo,(byte) 2, WarningTypeEnum.WEIGH_CONVERT.value(), WarningSubTypeEnum.WEIGH_CONVERT_TWO.subType(), warningInfo, warningSource, materialData.getId());
            list.add(warning);
        }
        // 收料
        if (receiveType.equals(WeighTypeEnum.RECEIVE.value())) {
            if (ObjectUtil.isNotNull(materialData.getRatio()) && StrUtil.isNotBlank(materialData.getContractDetailId())) {
                // 转换系数与合同约定系数不一致
                if (ObjectUtil.isNotNull(simpleContractDetailDTO) && !materialData.getRatio().equals(simpleContractDetailDTO.getConversionRate())) {
                    String warningInfo = StrUtil.format(WarningSubTypeEnum.WEIGH_CONVERT_ONE.desc(),simpleContractDetailDTO.getConversionRate(),materialData.getRatio(),materialData.getRatio());
                    MaterialWarning warning = createWarningEntity(warningSourceNo,(byte) 2, WarningTypeEnum.WEIGH_CONVERT.value(), WarningSubTypeEnum.WEIGH_CONVERT_ONE.subType(), warningInfo, warningSource, materialData.getId());
                    list.add(warning);
                }
            }
            // 超负差
            if (ObjectUtil.isNotNull(materialData.getDeviationStatus()) && materialData.getDeviationStatus().equals(DeviationStatusEnum.NEGATIVEDIFFERENCE.value())) {
                String warningInfo = StrUtil.format(WarningSubTypeEnum.NEGATIVE_DEVIATION_ONE.desc(),simpleContractDetailDTO.getDeviationFloor(),simpleContractDetailDTO.getDeviationCeiling(),materialData.getDeviationRate());
                MaterialWarning warning = createWarningEntity(warningSourceNo,(byte) 2, WarningTypeEnum.NEGATIVE_DEVIATION.value(), WarningSubTypeEnum.NEGATIVE_DEVIATION_ONE.subType(), warningInfo, warningSource, materialData.getId());
                list.add(warning);
            }
        }

        // ===============================================================================

        // 完整性预警
        // 毛皮重异常
        if (ObjectUtil.isNull(materialData.getWeightGross())) {
            // 缺少毛重
            String warningInfo = WarningSubTypeEnum.TARE_GROSS_WEIGH_ONE.desc();
            MaterialWarning warning = createWarningEntity(warningSourceNo,(byte) 1, WarningTypeEnum.TARE_GROSS_WEIGH.value(), WarningSubTypeEnum.TARE_GROSS_WEIGH_ONE.subType(), warningInfo, warningSource, materialData.getId());
            list.add(warning);
        }
        if (ObjectUtil.isNull(materialData.getWeightTare())) {
            // 缺少皮重
            String warningInfo = WarningSubTypeEnum.TARE_GROSS_WEIGH_TWO.desc();
            MaterialWarning warning = createWarningEntity(warningSourceNo,(byte) 1, WarningTypeEnum.TARE_GROSS_WEIGH.value(), WarningSubTypeEnum.TARE_GROSS_WEIGH_TWO.subType(), warningInfo, warningSource, materialData.getId());
            list.add(warning);
        }
        // 无效称重
        if (ObjectUtil.isNull(materialData.getMaterialId())) {
            // 未设置物料
            String warningInfo = WarningSubTypeEnum.INVALID_WEIGHT_ONE.desc();
            MaterialWarning warning = createWarningEntity(warningSourceNo,(byte) 1, WarningTypeEnum.INVALID_WEIGHT.value(), WarningSubTypeEnum.INVALID_WEIGHT_ONE.subType(), warningInfo, warningSource, materialData.getId());
            list.add(warning);
        }
        if (ObjectUtil.isNotNull(materialData.getWeightGross()) && materialData.getWeightGross().compareTo(BigDecimal.ZERO) < 0) {
            // 毛重为负数
            String warningInfo = WarningSubTypeEnum.INVALID_WEIGHT_TWO.desc();
            MaterialWarning warning = createWarningEntity(warningSourceNo,(byte) 1, WarningTypeEnum.INVALID_WEIGHT.value(), WarningSubTypeEnum.INVALID_WEIGHT_TWO.subType(), warningInfo, warningSource, materialData.getId());
            list.add(warning);
        }
        if (ObjectUtil.isNotNull(materialData.getWeightTare()) && materialData.getWeightTare().compareTo(BigDecimal.ZERO) < 0) {
            // 皮重为负数
            String warningInfo = WarningSubTypeEnum.INVALID_WEIGHT_THREE.desc();
            MaterialWarning warning = createWarningEntity(warningSourceNo,(byte) 1, WarningTypeEnum.INVALID_WEIGHT.value(), WarningSubTypeEnum.INVALID_WEIGHT_THREE.subType(), warningInfo, warningSource, materialData.getId());
            list.add(warning);
        }
        if (ObjectUtil.isNotNull(materialData.getWeightTare()) && ObjectUtil.isNotNull(materialData.getWeightGross()) && materialData.getWeightTare() .equals(materialData.getWeightGross()) ){
            // 车辆重复过磅
            String warningInfo = WarningSubTypeEnum.INVALID_WEIGHT_FOUR.desc();
            MaterialWarning warning = createWarningEntity(warningSourceNo,(byte) 1, WarningTypeEnum.INVALID_WEIGHT.value(), WarningSubTypeEnum.INVALID_WEIGHT_FOUR.subType(), warningInfo, warningSource, materialData.getId());
            list.add(warning);
        }
        // 物料名称异常
        if (ObjectUtil.isNotNull(materialData.getMaterialId())) {

            MaterialDto materialDto = materialServiceProxy.materialById(materialData.getMaterialId());
            if (ObjectUtil.isNull(materialDto)) {
                // 材料库不存在物料
                String warningInfo = WarningSubTypeEnum.MATERIAL_NAME_ONE.desc();
                MaterialWarning warning = createWarningEntity(warningSourceNo,(byte) 1, WarningTypeEnum.MATERIAL_NAME.value(), WarningSubTypeEnum.MATERIAL_NAME_ONE.subType(), warningInfo, warningSource, materialData.getId());
                list.add(warning);
            }
            if (StrUtil.isNotBlank(materialData.getPurchaseOrderId())) {
                PurchaseOrderDetailSimpleDTO purchaseOrderDetail = purchaseOrderDetailService.getPurchaseOrderDetail(materialData.getPurchaseOrderId(), materialData.getMaterialId());
                if (ObjectUtil.isNull(purchaseOrderDetail)) {
                    // 材料在采购单中不存在
                    String warningInfo = WarningSubTypeEnum.MATERIAL_NAME_TWO.desc();
                    MaterialWarning warning = createWarningEntity(warningSourceNo,(byte) 1, WarningTypeEnum.MATERIAL_NAME.value(), WarningSubTypeEnum.MATERIAL_NAME_TWO.subType(), warningInfo, warningSource, materialData.getId());
                    list.add(warning);
                }
            }
        }
        // 收料
        if (receiveType.equals(WeighTypeEnum.RECEIVE.value())) {
            // 面单应收数量异常
            if (ObjectUtil.isNull(materialData.getWeightSend()) || materialData.getWeightSend().compareTo(BigDecimal.ZERO) < 0) {
                String warningInfo = WarningSubTypeEnum.RECEIVE_NUMBER_ONE.desc();
                MaterialWarning warning = createWarningEntity(warningSourceNo,(byte) 1, WarningTypeEnum.RECEIVE_NUMBER.value(), WarningSubTypeEnum.RECEIVE_NUMBER_ONE.subType(), warningInfo, warningSource, materialData.getId());
                list.add(warning);
            }

            // 结算单位
            if (StrUtil.isBlank(materialData.getWeightUnit())) {
                // 结算单位为空
                String warningInfo = StrUtil.format(WarningSubTypeEnum.CHARGE_UNIT_TWO.desc(),simpleContractDetailDTO.getUnit(),materialData.getWeightUnit());
                MaterialWarning warning = createWarningEntity(warningSourceNo,(byte) 1, WarningTypeEnum.CHARGE_UNIT.value(), WarningSubTypeEnum.CHARGE_UNIT_TWO.subType(), warningInfo, warningSource, materialData.getId());
                list.add(warning);
            }
            if (StrUtil.isNotBlank(materialData.getWeightUnit()) && StrUtil.isNotBlank(materialData.getContractDetailId()) && !materialData.getWeightUnit().equals(simpleContractDetailDTO.getUnit())) {
                // 结算单位与合同约定结算单位不一致
                String warningInfo = StrUtil.format(WarningSubTypeEnum.CHARGE_UNIT_ONE.desc(),simpleContractDetailDTO.getUnit(),materialData.getWeightUnit(),materialData.getWeightUnit());
                MaterialWarning warning = createWarningEntity(warningSourceNo,(byte) 1, WarningTypeEnum.CHARGE_UNIT.value(), WarningSubTypeEnum.CHARGE_UNIT_ONE.subType(), warningInfo, warningSource, materialData.getId());
                list.add(warning);
            }
        }

        return list;
    }

    private MaterialWarning createWarningEntity(String warningSourceNo,Byte warningKind,Byte warningType,Byte warningSubType,String warningInfo,String warningSource,String warningSourceId) {
        Integer projectId = userUtil.getProjectId();
        MaterialWarning warning = new MaterialWarning();
        warning.setWarningNo(noUtil.getWarningNo(projectId));
        warning.setWarningKind((byte)1);
        warning.setWarningType(warningType);
        warning.setWarningSubType(warningSubType);
        warning.setWarningInfo(warningInfo);
        warning.setWarningSource(warningSource);
        warning.setWarningSourceId(warningSourceId);
        warning.setWarningSourceNo(warningSourceNo);
        warning.setSourceProjectId(projectId);
        return warning;
    }
}
