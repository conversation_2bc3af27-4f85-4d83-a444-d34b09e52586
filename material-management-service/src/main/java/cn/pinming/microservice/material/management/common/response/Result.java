package cn.pinming.microservice.material.management.common.response;

import cn.pinming.microservice.material_unit.api.exceptions.ResultCode;

import java.io.Serializable;
import java.util.Objects;

public class Result<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 响应代码 */
    private Integer code;
    /** 是否成功，兼容桩桩的响应格式 */
    private Boolean success;
    /** 响应描述信息 */
    private String msg;
    /** 响应数据 */
    private T data;


    // 默认构造
    public Result() {
        this.code = CommonResultCode.SUCCESS.getCode();
        this.msg = CommonResultCode.SUCCESS.getMsg();
        this.success = true;
    }


    //getter setter
    public T getData() {
        return data;
    }
    public void setData(T data) {
        this.data = data;
    }
    public Integer getCode() {
        return code;
    }
    public void setCode(Integer code) {
        this.code = code;
    }
    public String getMsg() {
        return msg;
    }
    public void setMsg(String msg) {
        this.msg = msg;
    }
    public Boolean getSuccess() {
        return success;
    }
    public void setSuccess(Boolean success) {
        this.success = success;
    }

    @Override
    public String toString() {
        return "Result{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Result<?> result = (Result<?>) o;
        if (!Objects.equals(code, result.code)) return false;
        if (!Objects.equals(msg, result.msg)) return false;
        return Objects.equals(data, result.data);
    }
    @Override
    public int hashCode() {
        int result = code != null ? code.hashCode() : 0;
        result = 31 * result + (msg != null ? msg.hashCode() : 0);
        result = 31 * result + (data != null ? data.hashCode() : 0);
        return result;
    }


    // build  一般用来返回错误信息
    public static <T> Result<T> build(String msg) {
        return build(CommonResultCode.ERR.getCode(), msg, null);
    }
    public static <T> Result<T> build(ResultCode code) {
        return build(code.getCode(), code.getMsg(), null);
    }
    // 当第二个参数为String或null时，会走这个重载
    public static <T> Result<T> build(ResultCode code, String msg) {
        return build(code.getCode(), msg, null);
    }
    // 当第二个参数，非String，非null时，才会走这个重载
    public static <T> Result<T> build(ResultCode code, T data) {
        return build(code.getCode(), code.getMsg(), data);
    }
    public static <T> Result<T> build(ResultCode code, String msg, T data) {
        return build(code.getCode(), msg, data);
    }

    public static <T> Result<T> build(int code, String msg) {
        return Result.build(code, msg, null);
    }

    // Base
    public static <T> Result<T> build(int code, String msg, T data) {
        Result<T> response = new Result<>();
        response.setCode(code);
        response.setMsg(msg);
        response.setData(data);
        response.setSuccess(code == CommonResultCode.SUCCESS.getCode());
        return response;
    }


    //success 一般用来返回成功信息
    public static <T> Result<T> success() {
        return build(CommonResultCode.SUCCESS.getCode(), CommonResultCode.SUCCESS.getMsg(), null);
    }
    public static <T> Result<T> success(T data) {
        return build(CommonResultCode.SUCCESS.getCode(), CommonResultCode.SUCCESS.getMsg(), data);
    }
}