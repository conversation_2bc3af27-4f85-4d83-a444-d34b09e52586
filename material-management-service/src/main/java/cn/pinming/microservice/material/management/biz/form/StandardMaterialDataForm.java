package cn.pinming.microservice.material.management.biz.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel("标准物料数据，品茗提供")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StandardMaterialDataForm {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "标准物料数据id，StandardMaterialData的主键")
    private String dataId;

    @ApiModelProperty(value = "设备sn号")
    private String deviceNo;

    @ApiModelProperty(value = "企业id")
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    private Integer projectId;

    @ApiModelProperty(value = "外部系统项目id")
    private String clientProjectId;

    @ApiModelProperty(value = "车辆预报备数据id")
    private String preReportId;

    @ApiModelProperty(value = "供应商id")
    private String supplierId;

    @ApiModelProperty(value = "外部系统供应商id")
    private String clientSupplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "合同id")
    private String contractId;

    @ApiModelProperty(value = "外部系统合同id")
    private String clientContractId;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "采购单id")
    private String purchaseId;

    @ApiModelProperty(value = "称重类型，1：收货；2：发货；3：简单过磅；4：其他过磅")
    private Byte type;

    @ApiModelProperty(value = "收货单号")
    private String receiveNo;

    @ApiModelProperty(value = "收货人")
    private String receiver;

    @ApiModelProperty(value = "卡车车牌")
    private String truckNo;

    @ApiModelProperty(value = "司磅员")
    private String operator;

    @ApiModelProperty(value = "司机")
    private String driver;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "物料收货数据")
    private List<StandardMaterialDataItemFrom> materialDataList;

    @ApiModelProperty(value = "高拍仪图片")
    private List<String> picList;


}
