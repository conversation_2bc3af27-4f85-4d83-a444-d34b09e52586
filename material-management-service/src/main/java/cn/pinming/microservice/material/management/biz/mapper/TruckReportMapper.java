package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.materialManagement.dto.TruckInfoDTO;
import cn.pinming.microservice.material.management.biz.entity.TruckReport;
import cn.pinming.microservice.material.management.biz.vo.TruckReportVO;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 车辆报备 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-09-06 14:04:33
 */
public interface TruckReportMapper extends BaseMapper<TruckReport> {

    @InterceptorIgnore(tenantLine = "true")
    List<TruckInfoDTO> selectTruckNo(@Param("companyId")Integer companyId,@Param("projectId")Integer projectId);

    @InterceptorIgnore(tenantLine = "true")
    TruckReport selectTruckInfoById(@Param("projectId")Integer projectId, @Param("truckNo")String truckNo);

    List<TruckReportVO> selectTruck(@Param("companyId")Integer companyId,@Param("projectId")Integer projectId,@Param("truckNo")String truckNo);
}
