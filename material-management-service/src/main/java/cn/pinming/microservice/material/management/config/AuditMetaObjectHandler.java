package cn.pinming.microservice.material.management.config;

import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Component
@Slf4j
public class AuditMetaObjectHandler implements MetaObjectHandler {

    @Resource
    private AuthUserHolder siteContextHolder;

    private static final String CREATE_TIME = "createTime";
    private static final String UPDATE_TIME = "updateTime";
    private static final String CREATE_ID = "createId";
    private static final String UPDATE_ID = "updateId";
    private static final String COMPANY_ID = "companyId";
    private static final String PROJECT_ID = "projectId";

    @Override
    public void insertFill(MetaObject metaObject) {
        LocalDateTime localDateNow = LocalDateTime.now();
        if (metaObject.hasGetter(CREATE_TIME)) {
            this.setFieldValByName(CREATE_TIME, localDateNow, metaObject);
        }
        if (metaObject.hasGetter(UPDATE_TIME)) {
            this.setFieldValByName(UPDATE_TIME, localDateNow, metaObject);
        }

        AuthUser currentUser = siteContextHolder.getCurrentUser();
        if (metaObject.hasGetter(CREATE_ID)) {
            if (currentUser != null) {
                String id = currentUser.getId();
                String createId = (String) this.getFieldValByName(CREATE_ID, metaObject);
                if (createId == null) {
                    this.strictInsertFill(metaObject, CREATE_ID, String.class, id);
                    this.strictInsertFill(metaObject, UPDATE_ID, String.class, id);
                }
            }
        }

        if (metaObject.hasGetter(COMPANY_ID)) {
            if (currentUser != null) {
                Integer companyId = currentUser.getCurrentCompanyId();
                this.strictInsertFill(metaObject, COMPANY_ID, Integer.class, companyId);
            }
        }

        if (metaObject.hasGetter(PROJECT_ID)) {
            if (currentUser != null) {
                Integer projectId = currentUser.getCurrentProjectId();
                this.strictInsertFill(metaObject, PROJECT_ID, Integer.class, projectId);
            }
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        LocalDateTime localDateNow = LocalDateTime.now();
        if (metaObject.hasGetter(UPDATE_TIME)) {
            this.setFieldValByName(UPDATE_TIME, localDateNow, metaObject);
        }

        AuthUser currentUser = siteContextHolder.getCurrentUser();
        if (metaObject.hasGetter(UPDATE_ID)) {
            if (currentUser != null) {
                String id = currentUser.getId();
                this.setFieldValByName(UPDATE_ID, id, metaObject);
            }
        }
    }
}
