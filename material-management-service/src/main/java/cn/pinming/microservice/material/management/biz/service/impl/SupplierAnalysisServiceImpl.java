package cn.pinming.microservice.material.management.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.microservice.material.management.biz.enums.DeviationStatusEnum;
import cn.pinming.microservice.material.management.biz.enums.ReceiveQueryTypeEnum;
import cn.pinming.microservice.material.management.biz.query.SupplierAnalysisQuery;
import cn.pinming.microservice.material.management.biz.query.SupplierRankQuery;
import cn.pinming.microservice.material.management.biz.service.IMaterialDataService;
import cn.pinming.microservice.material.management.biz.service.IPurchaseOrderDetailService;
import cn.pinming.microservice.material.management.biz.service.ISupplierAnalysisService;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.proxy.MaterialServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.microservice.material.management.wrapper.SupplierWrapper;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.weaponx.wrapper.wrapper.ProjectTitleWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2021/9/6 11:03 上午
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class SupplierAnalysisServiceImpl implements ISupplierAnalysisService {

    @Resource
    private IPurchaseOrderDetailService orderDetailService;
    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private MaterialServiceProxy materialServiceProxy;
    @Resource
    private SupplierWrapper supplierWrapper;
    @Resource
    private ProjectTitleWrapper projectTitleWrapper;
    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private ProjectServiceProxy projectServiceProxy;

    private AuthUser getAuthUser() {
        return authUserHolder.getCurrentUser();
    }

    @Override
    public SupplierAnalysisVO getSummaryByQuery(SupplierAnalysisQuery query) {
        Integer projectId = getAuthUser().getCurrentProjectId();
        Integer companyId = getAuthUser().getCurrentCompanyId();
        if (ObjectUtil.isNull(projectId)) {
            List<Integer> projectIds = projectServiceProxy.statisticsProjectIds(companyId, query.getDepartmentId());
            query.setProjectIds(projectIds);
        } else {
            query.setProjectIds(Collections.singletonList(projectId));
        }

        BigDecimal purchaseAmount = orderDetailService.getPurchaseAmountByQuery(query);
        List<Integer> materialIds = materialServiceProxy.getMaterialIdsByCategoryIds(companyId, query.getCategoryIds());
        if (CollUtil.isEmpty(materialIds)) {
            return null;
        }
        query.setMaterialIds(materialIds);
        SupplierAnalysisVO analysisVO = materialDataService.getSupplierAnalysisByQuery(query);
        SupplierAnalysisVO result = new SupplierAnalysisVO();
        result.setPurchaseAmount(purchaseAmount);
        if (analysisVO != null) {
            result.setWeightSendAmount(analysisVO.getWeightSendAmount());
            result.setWeightActualAmount(analysisVO.getWeightActualAmount());
            result.setActualReceive(analysisVO.getActualReceive());
            result.setDeviationAmount(NumberUtil.sub(analysisVO.getWeightActualAmount(), analysisVO.getWeightSendAmount()));
            result.setDeviationRateAmount(NumberUtil.div(result.getDeviationAmount().floatValue() * 100, result.getWeightSendAmount(), 2));
        }
        return result;
    }

    @Override
    public IPage<?> pageListByQuery(SupplierAnalysisQuery query) {
        Integer projectId = getAuthUser().getCurrentProjectId();
        Integer companyId = getAuthUser().getCurrentCompanyId();
        query.setCompanyId(companyId);
        if (Objects.isNull(projectId)) {
            List<Integer> projectIds = projectServiceProxy.statisticsProjectIds(companyId, query.getDepartmentId());
            query.setProjectIds(projectIds);
        } else {
            query.setProjectIds(Collections.singletonList(projectId));
        }

        //供应商
        IPage<SupplierAnalysisDetailVO> supplerPage = orderDetailService.selectSupplierPageVO(query);
        List<SupplierAnalysisDetailVO> supplerRecords = supplerPage.getRecords();
        List<Integer> supplierIds = supplerRecords.stream().map(SupplierAnalysisDetailVO::getSupplierId).collect(Collectors.toList());
        query.setSupplierIds(supplierIds);

        //采购下单量
        SupplierAnalysisQuery purchaseQuery = new SupplierAnalysisQuery();
        BeanUtils.copyProperties(query, purchaseQuery);
        List<SupplierAnalysisDetailVO> records = orderDetailService.selectSupplierAnalysisPageVO(purchaseQuery);
        Map<Integer, SupplierAnalysisDetailVO> purchaseAmountMap = records.stream().collect(Collectors.toMap(SupplierAnalysisDetailVO::getSupplierId, e -> e));

        List<Integer> materialIds = materialServiceProxy.getMaterialIdsByCategoryIds(companyId, query.getCategoryIds());
        if (CollUtil.isEmpty(materialIds)) {
            return null;
        }
        query.setMaterialIds(materialIds);
        SupplierAnalysisQuery analysisQuery = new SupplierAnalysisQuery();
        BeanUtils.copyProperties(query, analysisQuery);

        //供应商 面单应收量 实际收料 偏差量 偏差率 实收数量
        List<SupplierAnalysisDetailVO> list = materialDataService.getSupplierAnalysisPageVO(analysisQuery);
        Map<Integer, SupplierAnalysisDetailVO> analysisMap = list.stream().collect(Collectors.toMap(SupplierAnalysisDetailVO::getSupplierId, e -> e));

        for (SupplierAnalysisDetailVO record : supplerRecords) {
            SupplierAnalysisDetailVO detailVO = analysisMap.get(record.getSupplierId());
            SupplierAnalysisDetailVO analysisDetailVO = purchaseAmountMap.get(record.getSupplierId());
            if (analysisDetailVO != null) {
                record.setPurchaseAmount(analysisDetailVO.getPurchaseAmount());
            }

            if (detailVO != null) {
                record.setWeightSendAmount(detailVO.getWeightSendAmount());
                record.setWeightActualAmount(detailVO.getWeightActualAmount());
                record.setActualReceive(detailVO.getActualReceive());
                record.setDeviationAmount(NumberUtil.sub(detailVO.getWeightActualAmount(), detailVO.getWeightSendAmount()));
                record.setDeviationRateAmount(NumberUtil.div(NumberUtil.mul(record.getDeviationAmount(), 100), record.getWeightSendAmount(), 2));
            }
        }
        supplierWrapper.wrap(supplerRecords, companyId);
        return supplerPage;
    }

    @Override
    public IPage<?> pageListOrderByQuery(SupplierAnalysisQuery query) {
        Integer companyId = getAuthUser().getCurrentCompanyId();
        List<Integer> materialIds = materialServiceProxy.getMaterialIdsByCategoryIds(companyId, query.getCategoryIds());
        if (CollUtil.isEmpty(materialIds)) {
            return new Page<>();
        }
        query.setMaterialIds(materialIds);
        IPage<MaterialReceiveVO> page = orderDetailService.selectSupplierAnalysisDetailPageVO(query);
        List<MaterialReceiveVO> records = page.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            materialIds = records.parallelStream().map(MaterialReceiveVO::getMaterialId).distinct().collect(Collectors.toList());
            List<MaterialDto> materialList = materialServiceProxy.listMaterialByIds(materialIds);
            Map<Integer, MaterialDto> materialDtoMap = materialList.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, e -> e));
            for (MaterialReceiveVO record : records) {
                if (Objects.nonNull(record.getActualCount()) && Objects.nonNull(record.getWeightSend()) && record.getWeightSend().compareTo(BigDecimal.ZERO) != 0) {
                    record.setDeviationCount(NumberUtil.sub(record.getActualCount(), record.getWeightSend()));
                    record.setDeviation(String.valueOf(NumberUtil.div(NumberUtil.mul(record.getDeviationCount(), 100), record.getWeightSend(), 2)));
                    String deviationStatus = record.getDeviationStatus();
                    if (deviationStatus.equals(StrUtil.SLASH)) {
                        record.setDeviationStatus(DeviationStatusEnum.chooseDeviationStatus(null));
                    } else {
                        record.setDeviationStatus(DeviationStatusEnum.chooseDeviationStatus(Byte.valueOf(deviationStatus)));
                    }
                }
                MaterialDto materialDto = materialDtoMap.get(record.getMaterialId());
                if (materialDto != null) {
                    record.setMaterialName(materialDto.getMaterialName());
                    record.setCategoryName(materialDto.getMaterialCategoryName());
                    record.setMaterialSpec(materialDto.getMaterialSpec());
                    record.setName(StrUtil.format("{}/{}/{}", record.getCategoryName(), record.getMaterialName(), record.getMaterialSpec()));
                }
            }
        }
        supplierWrapper.wrap(records, companyId);
        projectTitleWrapper.wrap(records, companyId);
        return page;
    }

    @Override
    public List<SupplierRankVO> negativeFrequencyRankList(SupplierRankQuery query) {
        this.initQuery(query);
        //1 全部 2 地磅收料(报备、临时) 3 移动收料(有合同)
        List<SupplierRankVO> result;
        Byte type = query.getType();
        if (Objects.equals(ReceiveQueryTypeEnum.WEIGHBRIDGE.value(), type)) {
            //报备收料、临时收料
            result = materialDataService.negativeFrequencyRankListByQuery(query);
        } else if (Objects.equals(ReceiveQueryTypeEnum.MOBILE.value(), type)) {
            //移动收料 有合同
            result = materialDataService.negativeFrequencyMobileRankListByQuery(query);
        } else {
            //全部
            result = materialDataService.negativeFrequencyAllRankListByQuery(query);
        }
        supplierWrapper.wrap(result, query.getCompanyId());
        return wrapSupplierRankVO(result, query.getLimit());
    }

    @Override
    public List<SupplierRankVO> negativeFrequencyProportion(SupplierRankQuery query) {
        this.initQuery(query);
        List<SupplierRankVO> result;
        Byte type = query.getType();
        if (Objects.equals(ReceiveQueryTypeEnum.WEIGHBRIDGE.value(), type)) {
            //报备收料、临时收料
            result = materialDataService.negativeFrequencyProportionByQuery(query);
        } else if (Objects.equals(ReceiveQueryTypeEnum.MOBILE.value(), type)) {
            //移动收料 有合同
            result = materialDataService.negativeFrequencyMobileProportionByQuery(query);
        } else {
            //全部
            result = materialDataService.negativeFrequencyAllProportionByQuery(query);
        }
        // 供应商对应车次总量
        Map<Integer, BigDecimal> supplierCountMap = new HashMap<>(8);
        Map<Integer, List<SupplierRankVO>> resMap = result.stream().collect(Collectors.groupingBy(SupplierRankVO::getSupplierId));
        resMap.forEach((k, v) -> {
            BigDecimal count = v.stream().map(SupplierRankVO::getNum).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            supplierCountMap.put(k, count);
        });

        supplierWrapper.wrap(result, query.getCompanyId());

        List<SupplierRankVO> res = result.stream().filter(supplierRankVO -> supplierRankVO.getDeviationStatus() != null).filter(supplierRankVO -> supplierRankVO.getDeviationStatus() == 1 || supplierRankVO.getDeviationStatus() == 2)
                .map(supplierRankVO -> {
                    BigDecimal total = supplierCountMap.get(supplierRankVO.getSupplierId());
                    supplierRankVO.setNum(supplierRankVO.getNum().multiply(new BigDecimal("100")).divide(total, 4, RoundingMode.HALF_UP));
                    return supplierRankVO;
                }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(res) && query.getLimit() != null) {
            res = res.stream().sorted(Comparator.comparing(SupplierRankVO::getNum).reversed()).limit(query.getLimit()).collect(Collectors.toList());
        }
        return wrapSupplierRankVO(res, query.getLimit());
    }

    @Override
    public List<SupplierRankVO> negativeTotalRank(SupplierRankQuery query) {
        this.initQuery(query);
        List<SupplierRankDeviationVO> result;
        Byte type = query.getType();
        if (Objects.equals(ReceiveQueryTypeEnum.WEIGHBRIDGE.value(), type)) {
            //报备收料、临时收料
            result = materialDataService.negativeTotalRankByQuery(query);
        } else if (Objects.equals(ReceiveQueryTypeEnum.MOBILE.value(), type)) {
            //移动收料 有合同
            result = materialDataService.negativeTotalMobileRankByQuery(query);
        } else {
            //全部
            result = materialDataService.negativeTotalAllRankByQuery(query);
        }

        supplierWrapper.wrap(result, query.getCompanyId());
        List<SupplierRankVO> res = result.stream().map(supplierRankDeviationVO -> {
            SupplierRankVO supplierRankVO = new SupplierRankVO();
            BeanUtils.copyProperties(supplierRankDeviationVO, supplierRankVO);
            if (DeviationStatusEnum.POSITIVEDIFFERENCE.value() == supplierRankDeviationVO.getDeviationStatus()) {
                supplierRankVO.setNum(supplierRankDeviationVO.getPositiveNum());
            }
            if (DeviationStatusEnum.NEGATIVEDIFFERENCE.value() == supplierRankDeviationVO.getDeviationStatus()) {
                supplierRankVO.setNum(supplierRankDeviationVO.getNegativeNum());
            }
            return supplierRankVO;
        }).collect(Collectors.toList());
        return wrapSupplierRankVO(res, query.getLimit());
    }

    private List<SupplierRankVO> wrapSupplierRankVO(List<SupplierRankVO> supplierRankVOList, Integer limit) {
        List<SupplierRankVO> result = Lists.newArrayList();
        // 供应商对应的偏差状态列表
        Map<Integer, List<SupplierRankVO>> supplierRankMap = supplierRankVOList.stream().collect(Collectors.groupingBy(SupplierRankVO::getSupplierId));
        Map<Integer, List<Byte>> deviationStatusMap = new HashMap<>(4);
        supplierRankMap.forEach((k, v) -> {
            List<Byte> deviationList = v.stream().map(SupplierRankVO::getDeviationStatus).collect(Collectors.toList());
            deviationStatusMap.put(k, deviationList);
        });

        // 封装空数据并排序
        supplierRankVOList.forEach(supplierRankVO -> {
            List<Byte> deviations = deviationStatusMap.get(supplierRankVO.getSupplierId());
            if (!deviations.contains(DeviationStatusEnum.NEGATIVEDIFFERENCE.value())) {
                SupplierRankVO targetSupplierRank = new SupplierRankVO();
                BeanUtils.copyProperties(supplierRankVO, targetSupplierRank);
                targetSupplierRank.setNum(BigDecimal.ZERO);
                targetSupplierRank.setDeviationStatus(DeviationStatusEnum.NEGATIVEDIFFERENCE.value());
                result.add(targetSupplierRank);
            }
            if (!deviations.contains(DeviationStatusEnum.POSITIVEDIFFERENCE.value())) {
                SupplierRankVO targetSupplierRank = new SupplierRankVO();
                BeanUtils.copyProperties(supplierRankVO, targetSupplierRank);
                targetSupplierRank.setNum(BigDecimal.ZERO);
                targetSupplierRank.setDeviationStatus(DeviationStatusEnum.POSITIVEDIFFERENCE.value());
                result.add(targetSupplierRank);
            }
            result.add(supplierRankVO);
        });
        List<SupplierRankVO> rankVOList = Lists.newArrayList();
        Map<Boolean, List<SupplierRankVO>> statusMap = result.stream().filter(supplierRankVO -> null != supplierRankVO.getDeviationStatus()).collect(Collectors.partitioningBy(item -> item.getDeviationStatus() == DeviationStatusEnum.NEGATIVEDIFFERENCE.value()));
        List<SupplierRankVO> negativeSupplierRankVO = statusMap.get(true).stream().sorted(Comparator.comparing(SupplierRankVO::getNum)).collect(Collectors.toList());
        List<SupplierRankVO> positiveSupplierRankVO = statusMap.get(false);
        Map<Integer, SupplierRankVO> positiveSupplierRankMap = positiveSupplierRankVO.stream().collect(Collectors.toMap(SupplierRankVO::getSupplierId, Function.identity(), (k1, k2) -> k1));
        negativeSupplierRankVO.forEach(item -> {
            rankVOList.add(item);
            rankVOList.add(positiveSupplierRankMap.get(item.getSupplierId()));
        });

//        if (limit != null) {
//            return rankVOList.stream().sorted(Comparator.comparing(SupplierRankVO::getNum).reversed()).limit(limit).collect(Collectors.toList());
//        }
        return rankVOList;
    }

    private List<SupplierRankVO> rank(List<SupplierRankDeviationVO> list) {
        List<SupplierRankVO> result = Lists.newArrayList();
        if (CollUtil.isNotEmpty(list)) {
            Map<Integer, List<SupplierRankVO>> map = list.stream().collect(Collectors.groupingBy(SupplierRankDeviationVO::getSupplierId,
                    Collectors.collectingAndThen(Collectors.toList(), voList -> {
                        Integer supplierId = voList.get(0).getSupplierId();
                        String supplierName = voList.get(0).getSupplierTitle();
                        List<SupplierRankVO> vos = Lists.newArrayList();
                        Double total = voList.stream().collect(Collectors.summingDouble(item ->
                                Optional.ofNullable(item.getTotal()).orElse(BigDecimal.ZERO).doubleValue()));
                        Double negativeNum = voList.stream().filter(item -> item.getDeviationStatus() == 1).collect(Collectors.summingDouble(item ->
                                Optional.ofNullable(item.getNegativeNum()).orElse(BigDecimal.ZERO).doubleValue()));
                        Double positiveNum = voList.stream().filter(item -> item.getDeviationStatus() == 2).collect(Collectors.summingDouble(item ->
                                Optional.ofNullable(item.getPositiveNum()).orElse(BigDecimal.ZERO).doubleValue()));
                        SupplierRankVO negativeVO = new SupplierRankVO();
                        negativeVO.setSupplierId(supplierId);
                        negativeVO.setDeviationStatus((byte) 1);
                        negativeVO.setSupplierTitle(supplierName);
                        negativeVO.setNum(BigDecimal.valueOf(negativeNum).divide(BigDecimal.valueOf(total), 4, BigDecimal.ROUND_HALF_UP));
                        vos.add(negativeVO);
                        SupplierRankVO positiveVO = new SupplierRankVO();
                        positiveVO.setSupplierId(supplierId);
                        positiveVO.setDeviationStatus((byte) 2);
                        positiveVO.setSupplierTitle(supplierName);
                        positiveVO.setNum(BigDecimal.valueOf(positiveNum).divide(BigDecimal.valueOf(total), 4, BigDecimal.ROUND_HALF_UP));
                        vos.add(positiveVO);
                        return vos;
                    })));
            result = map.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public List<SupplierRankVO> negativeTotalProportion(SupplierRankQuery query) {
        this.initQuery(query);
        List<SupplierRankDeviationVO> result;
        Byte type = query.getType();
        if (Objects.equals(ReceiveQueryTypeEnum.WEIGHBRIDGE.value(), type)) {
            //报备收料、临时收料
            result = materialDataService.negativeTotalProportionByQuery(query);
        } else if (Objects.equals(ReceiveQueryTypeEnum.MOBILE.value(), type)) {
            //移动收料 有合同
            result = materialDataService.negativeTotalMobileProportionByQuery(query);
        } else {
            //全部
            result = materialDataService.negativeTotalAllProportionByQuery(query);
        }
        supplierWrapper.wrap(result, query.getCompanyId());

        Integer limit = query.getLimit();
        List<SupplierRankVO> rank = rank(result);

//        List<SupplierRankVO> res = result.stream().map(supplierRankDeviationVO -> {
//            SupplierRankVO supplierRankVO = new SupplierRankVO();
//            BeanUtils.copyProperties(supplierRankDeviationVO, supplierRankVO);
//            if (DeviationStatusEnum.POSITIVEDIFFERENCE.value() == supplierRankDeviationVO.getDeviationStatus()) {
//                supplierRankVO.setNum(supplierRankDeviationVO.getPositiveNum());
//            }
//            if (DeviationStatusEnum.NEGATIVEDIFFERENCE.value() == supplierRankDeviationVO.getDeviationStatus()) {
//                supplierRankVO.setNum(supplierRankDeviationVO.getNegativeNum());
//            }
//            return supplierRankVO;
//        }).collect(Collectors.toList());
        return wrapSupplierRankVO(rank, query.getLimit());
    }

    @Override
    public List<SupplierRankVO> deductRank(SupplierRankQuery query) {
        this.initQuery(query);
        List<SupplierRankVO> result = materialDataService.deductRankByQuery(query);
        supplierWrapper.wrap(result, query.getCompanyId());
        return result;
    }

    @Override
    public List<SupplierRankVO> deductProportion(SupplierRankQuery query) {
        this.initQuery(query);
        List<SupplierRankVO> result = materialDataService.deductProportionByQuery(query);
        supplierWrapper.wrap(result, query.getCompanyId());
        return result;
    }

    @Override
    public List<SupplierRankVO> deductTotalRank(SupplierRankQuery query) {
        this.initQuery(query);
        List<SupplierRankVO> result = materialDataService.deductTotalRankByQuery(query);
        supplierWrapper.wrap(result, query.getCompanyId());
        return result;
    }

    @Override
    public List<SupplierRankVO> deductTotalProportion(SupplierRankQuery query) {
        this.initQuery(query);
        List<SupplierRankVO> result = materialDataService.deductTotalProportionByQuery(query);
        supplierWrapper.wrap(result, query.getCompanyId());
        return result;
    }


    /**
     * 查询条件初始化
     *
     * @param query
     */
    private void initQuery(SupplierRankQuery query) {
        Integer companyId = getAuthUser().getCurrentCompanyId();
        Integer projectId = getAuthUser().getCurrentProjectId();
        query.setCompanyId(companyId);
        if (Objects.isNull(projectId)) {
            List<Integer> projectIds = projectServiceProxy.statisticsProjectIds(companyId, query.getDepartmentId());
            query.setProjectId(projectIds);
        } else {
            query.setProjectId(Collections.singletonList(projectId));
        }
    }
}
