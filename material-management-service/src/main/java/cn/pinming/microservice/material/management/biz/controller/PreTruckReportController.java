package cn.pinming.microservice.material.management.biz.controller;


import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.form.PreReportForm;
import cn.pinming.microservice.material.management.biz.service.IPreTruckReportService;
import cn.pinming.microservice.material.management.common.PreCommit;
import cn.pinming.microservice.material.management.common.annotation.Log;
import cn.pinming.microservice.material.management.common.enums.BusinessType;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 送货车辆预报备 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-11-16 15:07:43
 */
@RestController
@RequestMapping("/api/pre/truck")
public class PreTruckReportController {

    @Resource
    private IPreTruckReportService truckReportService;

    @ApiOperation(value = "删除")
    @Log(title = "删除", businessType = BusinessType.DELETE)
    @PostMapping("/del/{id}")
    public ResponseEntity<Response> del(@PathVariable String id) {
        truckReportService.deletePreTruckReport(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "检查车牌")
    @Log(title = "检查车牌", businessType = BusinessType.QUERY)
    @PostMapping("/check")
    public ResponseEntity<Response> check(@RequestBody @Validated(PreCommit.class) @Valid PreReportForm form) {
        String result = truckReportService.checkTruckNo(form);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

}
