package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 拌合站出场单
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-27 15:11:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_mixing_plant_exit_ticket")
@ApiModel(value = "MixingPlantExitTicket对象", description = "拌合站出场单")
public class MixingPlantExitTicket implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "出场单单号")
    @TableField("ticket_no")
    private String ticketNo;

    @ApiModelProperty(value = "订单id")
    @TableField("mixing_plant_order_id")
    private String mixingPlantOrderId;

    @ApiModelProperty(value = "订单明细id")
    @TableField("mixing_plant_order_detail_id")
    private String mixingPlantOrderDetailId;

    @ApiModelProperty(value = "材料ID")
    @TableField("material_id")
    private Integer materialId;

    @ApiModelProperty(value = "车牌号")
    @TableField("truck_no")
    private String truckNo;

    @ApiModelProperty(value = "载货数量")
    @TableField("actual_count")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "拌合站机组id")
    @TableField("plant_machine_id")
    private String plantMachineId;

    @ApiModelProperty(value = "打印模板id")
    @TableField("print_template_id")
    private String printTemplateId;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
