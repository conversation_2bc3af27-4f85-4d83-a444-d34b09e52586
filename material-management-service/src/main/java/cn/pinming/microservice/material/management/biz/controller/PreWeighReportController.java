package cn.pinming.microservice.material.management.biz.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.core.cookie.AuthUser;
import cn.pinming.core.cookie.support.AuthUserHolder;
import cn.pinming.core.web.response.Response;
import cn.pinming.core.web.response.SuccessResponse;
import cn.pinming.microservice.material.management.biz.dto.QrcodeRedisDTO;
import cn.pinming.microservice.material.management.biz.entity.PreTruckReport;
import cn.pinming.microservice.material.management.biz.entity.PurchaseOrder;
import cn.pinming.microservice.material.management.biz.form.PreReportForm;
import cn.pinming.microservice.material.management.biz.form.PreReportNewForm;
import cn.pinming.microservice.material.management.biz.query.PreReportQuery;
import cn.pinming.microservice.material.management.biz.service.IPreTruckReportService;
import cn.pinming.microservice.material.management.biz.service.IPreWeighReportService;
import cn.pinming.microservice.material.management.biz.service.IPurchaseContractService;
import cn.pinming.microservice.material.management.biz.service.IPurchaseOrderService;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.AppConstant;
import cn.pinming.microservice.material.management.common.PreCommit;
import cn.pinming.microservice.material.management.common.ValidCommon;
import cn.pinming.microservice.material.management.common.util.RedisUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 进出场预报备 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-11-16 15:07:43
 */
@Api(tags = "预报备-controller", value = "zh")
@RestController
@RequestMapping("/api/pre/weigh")
public class PreWeighReportController {

    @Resource
    private AuthUserHolder authUserHolder;
    @Resource
    private IPurchaseOrderService orderService;
    @Resource
    private IPreWeighReportService preWeighReportService;
    @Resource
    private IPurchaseContractService purchaseContractService;
    @Resource
    private IPreTruckReportService preTruckReportService;
    @Resource
    private Validator validator;
    @Resource
    RedisUtil redisUtil;

    @ApiOperation(value = "供应商列表-模糊查询", response = CooperateVO.class)
    @GetMapping("/supplierList")
    public ResponseEntity<Response> supplierList(@RequestParam(value = "supplierName", required = false) String supplierName) {
        List<CooperateVO> vo = purchaseContractService.listSupplier(supplierName);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "采购单列表", response = PrePurchaseVO.class)
    @GetMapping("/purchaseList/{supplierId}")
    public ResponseEntity<Response> purchaseList(@PathVariable("supplierId") Integer supplierId) {
        List<PrePurchaseVO> vos = orderService.listPurchase(supplierId);
        return ResponseEntity.ok(new SuccessResponse(vos));
    }

    @ApiOperation(value = "单一规格过磅预报备-暂存")
    @PostMapping("/add")
    public ResponseEntity<Response> add(@RequestBody @Validated @Valid PreReportForm form) {
        String id = preWeighReportService.add(form,null);
        return ResponseEntity.ok(new SuccessResponse(id));
    }

    @ApiOperation(value = "单一规格过磅预报备-提交")
    @PostMapping("/commit")
    public ResponseEntity<Response> commit(@RequestBody @Validated @Valid PreReportForm form) {
        ValidCommon.validRequestParams(validator.validate(form, PreCommit.class));
        String id = preWeighReportService.add(form,null);
        return ResponseEntity.ok(new SuccessResponse(id));
    }

    @ApiOperation(value = "单一规格过磅预报备-草稿状态删除")
    @PostMapping("/delete/{id}")
    public ResponseEntity<Response> delete(@PathVariable(value = "id") String id) {
        preWeighReportService.del(id);
        return ResponseEntity.ok(new SuccessResponse());
    }

    @ApiOperation(value = "预报备列表页", response = PreReportVO.class)
    @PostMapping("/page")
    public ResponseEntity<Response> list(@RequestBody PreReportQuery query) {
        AuthUser user = authUserHolder.getCurrentUser();
        IPage<PreReportVO> page = preWeighReportService.pageByQuery(query, user);
        return ResponseEntity.ok(new SuccessResponse(page));
    }

    @ApiOperation(value = "预报备详情页", response = PreReportDetailVO.class)
    @GetMapping("/detail/{id}")
    public ResponseEntity<Response> detail(@PathVariable("id") String id) {
        PreReportDetailVO vo = preWeighReportService.selectDetail(id,null);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

    @ApiOperation(value = "供应商预报备 - 外部页面", response = ExpirePageVO.class)
    @GetMapping("/external/{id}")
    public ResponseEntity<Response> external(@PathVariable String id) {
        ExpirePageVO result = preWeighReportService.generateExternalPageInfo(id);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "供应商预报备 - 外部页面 - 过期检测", response = ExpirePageVO.class)
    @GetMapping("/external/expire/{code}")
    public ResponseEntity<Response> externalExpire(@PathVariable String code) {
        ExpirePageVO result = preWeighReportService.checkExternalCodeExpire(code);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "扫码拿采购单id")
    @GetMapping("/get/{no}")
    public ResponseEntity<Response> get(@PathVariable String no) {
        String id = null;
        PurchaseOrder dto = orderService.lambdaQuery()
                .select(PurchaseOrder::getId)
                .eq(PurchaseOrder::getOrderNo,no)
                .in(PurchaseOrder::getStatus,3,4)
                .one();
        if(ObjectUtil.isNotEmpty(dto)){
            id = dto.getId();
        }
        return ResponseEntity.ok(new SuccessResponse(id));
    }

    @ApiOperation(value = "App-报备记录列表-根据采购单~~", response = PreReportVO.class)
    @GetMapping("/list/{purchaseId}")
    public ResponseEntity<Response> list(@PathVariable("purchaseId")String purchaseId) {
        List<PreReportVO> list = preWeighReportService.listByPurchaseId(purchaseId);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "App-车辆过磅记录列表-根据采购单~~", response = PreTruckReportDetailVO.class)
    @GetMapping("/TruckList/{purchaseId}")
    public ResponseEntity<Response> listTruck(@PathVariable("purchaseId")String purchaseId) {
        List<PreTruckReportDetailVO> list = preWeighReportService.listTruckByPurchaseId(purchaseId);
        return ResponseEntity.ok(new SuccessResponse(list));
    }
}
