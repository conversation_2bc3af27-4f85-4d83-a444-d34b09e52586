package cn.pinming.microservice.material.management.biz.iot.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.pinming.microservice.material.management.biz.dto.WeighbridgeAcceptDataDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialData;
import cn.pinming.microservice.material.management.biz.enums.IsWeightIntegralityEnum;
import cn.pinming.microservice.material.management.biz.enums.WarningSourceEnum;
import cn.pinming.microservice.material.management.biz.enums.WarningTypeEnum;
import cn.pinming.microservice.material.management.biz.iot.service.IWeighbridgeDecisionService;
import cn.pinming.microservice.material.management.biz.service.IMaterialDataService;
import cn.pinming.microservice.warehouse.management.dto.EnterLeaveRecord;
import cn.pinming.microservice.warehouse.management.service.EnterLeaveRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.pinming.microservice.material.management.biz.enums.WarningSubTypeEnum.WEIGH_INTEGRITY_ONE;

/**
 * <AUTHOR>
 */
@Slf4j
@Transactional
@Service(value = "delivery")
public class WeighbridgeDeliveryServiceImpl extends AbstractWeighbridgeDecisionService implements IWeighbridgeDecisionService {

    @Reference
    private EnterLeaveRecordService enterLeaveRecordService;
    @Resource
    private IMaterialDataService materialDataService;

    private final BigDecimal zero = BigDecimal.ZERO;

    @Override
    public WeighbridgeAcceptDataDTO getAcceptDataDTO() {
        return super.getAcceptDataDTO();
    }

    @Override
    public void setAcceptDataDTO(WeighbridgeAcceptDataDTO acceptDataDTO) {
        super.setAcceptDataDTO(acceptDataDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WeighbridgeAcceptDataDTO decisionWeighbridgeReceive(WeighbridgeAcceptDataDTO acceptDataDTO) {
        setAcceptDataDTO(acceptDataDTO);
        handleValidity();
        saveWeighbridgeReceive();
        saveWeighbridgeMaterialData();

        if (acceptDataDTO.getIsAutoVerify() == null || acceptDataDTO.getIsAutoVerify() == 1) {
            List<MaterialData> materialListResult = acceptDataDTO.getMaterialListResult();
            List<String> success = new ArrayList<>();
            Map<String,String> fail = new HashMap<>();
            materialListResult.forEach(e -> {
                try {
                    EnterLeaveRecord record = new EnterLeaveRecord();
                    record.setCategoryId(e.getCategoryId());
                    record.setType(2);
                    // 终端的发料可能没有materialId
                    record.setMaterialId(e.getMaterialId());
                    // 发货的供应商就是自己
                    record.setSupplierId(acceptDataDTO.getProjectId());
                    if (e.getSupplierId() != null) {
                        record.setReceiveId(String.valueOf(e.getSupplierId()));
                    }
                    record.setReceiveName(e.getSupplierName());
                    record.setMaterialUnit(e.getWeightUnit());
                    record.setMaterialDataId(e.getId());
                    record.setMaterialNum(e.getWeightSend());
                    record.setCompanyId(acceptDataDTO.getCompanyId());
                    record.setProjectId(acceptDataDTO.getProjectId());
                    // 终端上来的数据没有创建者/修订者信息

                    enterLeaveRecordService.createEnterLeaveRecord(record);
                    success.add(e.getId());
                    materialDataService.lambdaUpdate()
                            .eq(MaterialData::getId,e.getId())
                            .set(MaterialData::getIsPushed,1)
                            .update();
                } catch (Exception exception) {
                    fail.put(e.getId(),exception.getMessage());
                }
            });
            log.info("本次成功推送{}条发料记录,推送数据id为:{}",success.size(),String.join(",",success));
            log.info("本次共有{}条发料记录推送失败,具体情况为{}",fail.size(),fail.toString());
        }

        return null;
    }

    @Override
    protected void handleReceiveMaterialData(MaterialData materialData) {
        boolean dataIntegrality = super.isDataIntegrality(materialData);
        boolean ratioCheck = materialData.getRatio() == null || (zero.compareTo(materialData.getRatio()) >= 0);
        boolean flag = materialData.getWeightGross() != null && materialData.getWeightTare() != null && materialData.getWeightGross().compareTo(zero) > 0 && materialData.getWeightTare().compareTo(zero) > 0;
        if(flag){
            materialData.setIsWeightIntegrality(IsWeightIntegralityEnum.COMPLETE.value());
        }else {
            materialData.setIsWeightIntegrality(IsWeightIntegralityEnum.INCOMPLETE.value());
        }
        if(dataIntegrality){
            if (ratioCheck){
                materialData.setActualCount(null);
            }
//          净重
            materialData.setWeightNet(NumberUtil.sub(materialData.getWeightGross(), materialData.getWeightTare()));
//          实重
            materialData.setWeightActual(NumberUtil.sub(materialData.getWeightNet(), materialData.getWeightDeduct()));
//          实际数量
            if(materialData.getRatio() != null && materialData.getRatio().compareTo(zero) > 0){
                materialData.setActualCount(NumberUtil.mul(materialData.getWeightActual(), materialData.getRatio()));
            }
        }else {
            triggerWarning(materialData, WarningSourceEnum.WEIGHT_BRIDGE_DELIVERY.value(), WarningTypeEnum.WEIGH_INTEGRITY.value(),
                    WEIGH_INTEGRITY_ONE.subType(), WEIGH_INTEGRITY_ONE.desc());
            materialData.setWeightNet(null);
            materialData.setActualCount(null);
            materialData.setWeightActual(null);
        }
    }

}
