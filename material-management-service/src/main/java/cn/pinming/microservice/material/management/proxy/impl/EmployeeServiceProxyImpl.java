package cn.pinming.microservice.material.management.proxy.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.management.biz.dto.EmployeeSimpleDTO;
import cn.pinming.microservice.material.management.proxy.EmployeeServiceProxy;
import cn.pinming.v2.company.api.dto.EmployeeDetailDto;
import cn.pinming.v2.company.api.dto.EmployeeDetailQueryDto;
import cn.pinming.v2.company.api.dto.EmployeeDto;
import cn.pinming.v2.company.api.service.EmployeeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;

/**
 * yhj 人员代理服务
 */
@Slf4j
@Component
public class EmployeeServiceProxyImpl implements EmployeeServiceProxy {

    @Reference
    private EmployeeService employeeService;

    @Override
    public EmployeeDto findEmployee(Integer companyId, String memberId) {
        if (companyId == null || memberId == null) {
            return null;
        }
        return employeeService.findEmployee(companyId, memberId);
    }

    @Override
    public List<EmployeeDetailDto> employeeList(@NotNull EmployeeDetailQueryDto employeeDetailQueryDto) {
        if(employeeDetailQueryDto.getCompanyId() == null || CollUtil.isEmpty(employeeDetailQueryDto.getMemberIdList())){return null;}
        List<EmployeeDetailDto> employeeDetailDtos = employeeService.employeeList(employeeDetailQueryDto);
        return employeeDetailDtos;
    }

    @Override
    public List<EmployeeSimpleDTO> employeeList(Integer companyId, List<String> memberIds) {
        EmployeeDetailQueryDto query = new EmployeeDetailQueryDto();
        query.setCompanyId(companyId);
        query.setMemberIdList(memberIds);
        List<EmployeeDetailDto> list = employeeService.employeeList(query);
        if (CollUtil.isNotEmpty(list)) {
            return BeanUtil.copyToList(list, EmployeeSimpleDTO.class);
        }
        return Collections.emptyList();
    }
}
