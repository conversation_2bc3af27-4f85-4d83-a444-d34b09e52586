package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.ExistTicketDetailDTO;
import cn.pinming.microservice.material.management.biz.dto.MixingPlantOrderDetailItemDTO;
import cn.pinming.microservice.material.management.biz.dto.PreInfoDTO;
import cn.pinming.microservice.material.management.biz.entity.MixingPlantOrderDetail;
import cn.pinming.microservice.material.management.biz.form.IngredientAutoAddForm;
import cn.pinming.microservice.material.management.biz.vo.ExistTicketDetailVO;
import cn.pinming.microservice.material.management.biz.vo.MixingPlantOrderDetailItemVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 拌合站订单明细 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-06-28 15:25:44
 */
public interface MixingPlantOrderDetailMapper extends BaseMapper<MixingPlantOrderDetail> {

    List<MixingPlantOrderDetailItemDTO> choose(@Param("companyId") Integer companyId,@Param("projectId") Integer projectId,@Param("name") String name,@Param("no") String no);

    ExistTicketDetailDTO selectOrderDetail(@Param("id")String id);

    PreInfoDTO selectPreInfo(@Param("id") String mixingPlantOrderDetailId);

    List<IngredientAutoAddForm> selectAutoAddForm(List<String> ids);
}
