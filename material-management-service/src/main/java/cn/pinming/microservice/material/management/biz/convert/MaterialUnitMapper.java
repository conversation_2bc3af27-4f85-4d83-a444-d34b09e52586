package cn.pinming.microservice.material.management.biz.convert;

import cn.pinming.microservice.material.management.biz.vo.MaterialUnitVO;
import cn.pinming.microservice.material_unit.api.material.dto.MaterialUnitConversionDto;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 对象转化
 *
 * <AUTHOR>
 * @version 2021/9/3 4:21 下午
 */
@Mapper
public interface MaterialUnitMapper {

    MaterialUnitMapper INSTANCE = Mappers.getMapper(MaterialUnitMapper.class);

    @Mappings({
        @Mapping(source = "conversionRate",target = "conversionRate"),
        @Mapping(source = "sourceUnit.unitName",target = "sourceUnitName"),
        @Mapping(source = "sourceUnit.unitChSymbols",target = "sourceUnitChSymbols"),
        @Mapping(source = "sourceUnit.unitEnSymbols",target = "sourceUnitEnSymbols"),
        @Mapping(source = "targetUnit.unitName",target = "targetUnitName"),
        @Mapping(source = "targetUnit.unitChSymbols",target = "targetUnitChSymbols"),
        @Mapping(source = "targetUnit.unitEnSymbols",target = "targetUnitEnSymbols")
    })
    MaterialUnitVO unitToUnitVO(MaterialUnitConversionDto dto);

    List<MaterialUnitVO> unitToUnitVOs(List<MaterialUnitConversionDto> dtos);

}
