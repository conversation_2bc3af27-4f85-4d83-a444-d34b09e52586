package cn.pinming.microservice.material.management.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 移动收料表
 * </p>
 *
 * <AUTHOR> hao
 * @since 2021-10-18 13:30:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("d_mobile_receive")
@ApiModel(value = "MobileReceive对象", description = "移动收料表")
public class MobileReceive implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "收货单id")
    @TableField("receive_id")
    private String receiveId;

    @ApiModelProperty(value = "收货单号")
    @TableField("receive_no")
    private String receiveNo;

    @ApiModelProperty(value = "收货状态 1，合格进场；2，不合格进场")
    @TableField("receive_status")
    private Byte receiveStatus;

    @ApiModelProperty(value = "采购单ID")
    @TableField("purchase_id")
    private String purchaseId;

    @ApiModelProperty(value = "收料类型 1有合同收料-按合同，2有合同收料-按采购单，3无合同收料")
    private Byte receiveType;

    @ApiModelProperty(value = "合同id")
    private String contractId;

    @ApiModelProperty(value= "供应商id")
    private Integer supplierId;

    @ApiModelProperty(value = "实际收货人")
    private String receiver;

    @ApiModelProperty(value = "验收意见")
    private String comment;

    @ApiModelProperty(value = "是否修订过 1 是 2 否")
    private Byte isRevise;

    @ApiModelProperty(value = "偏差状态 0 正常 1 负偏差 2 正偏差")
    private Byte deviationStatus;

    @ApiModelProperty(value = "企业id")
    @TableField(value = "company_id", fill = FieldFill.INSERT)
    private Integer companyId;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id", fill = FieldFill.INSERT)
    private Integer projectId;

    @ApiModelProperty(value = "组织id")
    @TableField(value = "department_id", fill = FieldFill.INSERT)
    private Integer departmentId;

    @ApiModelProperty(value = "创建者id")
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    @ApiModelProperty(value = "修改者id")
    @TableField(value = "update_id", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记 0 正常； 1删除")
    @TableField("is_deleted")
    @TableLogic
    private Byte isDeleted;


}
