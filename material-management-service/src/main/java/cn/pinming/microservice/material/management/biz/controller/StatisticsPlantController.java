package cn.pinming.microservice.material.management.biz.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.pinming.microservice.material.management.biz.query.MachineCapacityQuery;
import cn.pinming.microservice.material.management.biz.query.MachineExitDetailQuery;
import cn.pinming.microservice.material.management.biz.query.WarehouseIdQuery;
import cn.pinming.microservice.material.management.biz.service.StatisticsPlantService;
import cn.pinming.microservice.material.management.biz.vo.*;
import cn.pinming.microservice.material.management.common.SuccessResponse;
import cn.pinming.microservice.material.management.common.util.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <p>
 * 统计-拌合站 前端控制器
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-21 10:09:58
 */
@Api(tags = "拌合站-统计")
@RestController
@RequestMapping("/api/statistics/plant")
public class StatisticsPlantController {

    @Resource
    private StatisticsPlantService plantService;

    @ApiOperation(value = "产能分析-二级分类&单位&材料", response = StatisticsPlantCategoryVO.class)
    @GetMapping("config")
    public ResponseEntity<SuccessResponse> queryCategories() {
        List<StatisticsPlantCategoryVO> list = plantService.queryStatisticsPlantCategory();
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "产能分析-柱状图&表格", response = StatisticsPlantVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unit", value = "单位", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "materialIds", value = "材料ID，英文逗号分隔", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping
    public ResponseEntity<SuccessResponse> queryHistoryPlantHistogram(@RequestParam("unit") String unit,
                                                                      @RequestParam("materialIds") String materialIds) {
        if (StringUtils.isBlank(unit) || StringUtils.isBlank(materialIds)) {
            return ResponseEntity.ok(new SuccessResponse(new StatisticsPlantVO()));
        }
        List<Integer> materialIdList = Arrays.stream(materialIds.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        StatisticsPlantVO plantVO = plantService.queryHistoryPlantHistogram(unit, materialIdList);
        return ResponseEntity.ok(new SuccessResponse(plantVO));
    }

    @ApiOperation(value = "产能分析-产能走势折线图", response = StatisticsPlantLineVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "start", value = "开始时间(yyyy-MM-dd)", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "end", value = "结束时间(yyyy-MM-dd)", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "unit", value = "单位", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "materialIds", value = "材料ID，英文逗号分隔", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/line")
    public ResponseEntity<SuccessResponse> queryHistoryPlantLine(@RequestParam("start") String start,
                                                                 @RequestParam("end") String end,
                                                                 @RequestParam("unit") String unit,
                                                                 @RequestParam("materialIds") String materialIds) {
        if (StringUtils.isBlank(unit) || StringUtils.isBlank(materialIds)) {
            return ResponseEntity.ok(new SuccessResponse(Lists.newArrayList()));
        }
        List<Integer> materialIdList = Arrays.stream(materialIds.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        List<StatisticsPlantLineVO> rangePlantLine = plantService.queryTimeRangePlantLine(start, end, unit, materialIdList);
        return ResponseEntity.ok(new SuccessResponse(rangePlantLine));
    }

    @ApiOperation(value = "产能分析-柱状图(昨日&今日)下钻", response = StatisticsPlantHistoryVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型：1 昨日，2 今日", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "unit", value = "单位", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "materialIds", value = "材料ID，英文逗号分隔", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/history/second")
    public ResponseEntity<SuccessResponse> queryHistoryPlantSecond(@RequestParam("type") Integer type,
                                                                   @RequestParam("unit") String unit,
                                                                   @RequestParam("materialIds") String materialIds) {
        if (ObjectUtil.isNull(type) || (type != 1 && type != 2) || StringUtils.isBlank(unit) || StringUtils.isBlank(materialIds)) {
            return ResponseEntity.ok(new SuccessResponse(Lists.newArrayList()));
        }
        List<Integer> materialIdList = Arrays.stream(materialIds.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        List<StatisticsPlantHistoryVO> result = plantService.queryHistoryPlantHistogramSecond(type == 1 ? DateUtil.yesterdayStr() : DateUtil.todayStr(), unit, materialIdList);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "产能分析-柱状图(明日)下钻", response = StatisticsPlantFutureVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unit", value = "单位", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "materialIds", value = "材料ID，英文逗号分隔", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/today/second")
    public ResponseEntity<SuccessResponse> queryTodayPlantSecond(@RequestParam("unit") String unit,
                                                                 @RequestParam("materialIds") String materialIds) {
        if (StringUtils.isBlank(unit) || StringUtils.isBlank(materialIds)) {
            return ResponseEntity.ok(new SuccessResponse(Lists.newArrayList()));
        }
        List<Integer> materialIdList = Arrays.stream(materialIds.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        List<StatisticsPlantFutureVO> result = plantService.queryTomorrowPlantHistogramSecond(unit, materialIdList);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "产能分析-机组列表", response = MixingPlantMachineVO.class)
    @PostMapping("/machine/list")
    public ResponseEntity<SuccessResponse> machineList(@RequestBody MachineCapacityQuery machineCapacityQuery) {
        List<Integer> materialIdList = Arrays.stream(machineCapacityQuery.getMaterialIds().split(",")).map(Integer::parseInt).collect(Collectors.toList());
        List<MixingPlantMachineVO> mixingPlantMachineVOList = plantService.queryMachineList(materialIdList, machineCapacityQuery.getUnit());
        return ResponseEntity.ok(new SuccessResponse(mixingPlantMachineVOList));
    }

    @ApiOperation(value = "产能分析-机组产能走势", response = MachineCapacityVO.class)
    @PostMapping("/machine/capacity")
    public ResponseEntity<SuccessResponse> machineCapacity(@RequestBody MachineCapacityQuery machineCapacityQuery) {
        List<Integer> materialIdList = Arrays.stream(machineCapacityQuery.getMaterialIds().split(",")).map(Integer::parseInt).collect(Collectors.toList());
        List<MachineCapacityVO> result = plantService.queryMachineCapacity(machineCapacityQuery, materialIdList);
        return ResponseEntity.ok(new SuccessResponse(result));
    }

    @ApiOperation(value = "产能分析-机组生产出站明细", response = MachineCapacityVO.class)
    @PostMapping("/machine/exitDetail")
    public ResponseEntity<SuccessResponse> machineExitDetail(@RequestBody MachineExitDetailQuery machineExitDetailQuery) {
        List<Integer> materialIdList = Arrays.stream(machineExitDetailQuery.getMaterialIds().split(",")).map(Integer::parseInt).collect(Collectors.toList());
        List<MachineExitDetailVO> machineExitDetailVOList = plantService.queryMachineExitDetail(machineExitDetailQuery, materialIdList);
        return ResponseEntity.ok(new SuccessResponse(machineExitDetailVOList));
    }

    @ApiOperation(value = "原料分析-原料需求与库存分析-柱状图-是否开通仓储管理插件")
    @GetMapping("/raw/material/demand/stock/warehouse/open")
    public ResponseEntity<SuccessResponse> queryRawMaterialDemandStockHistogramOpenWarehouse() {
        Boolean openWarehousePlugin = plantService.openWarehousePlugin();
        return ResponseEntity.ok(new SuccessResponse(openWarehousePlugin));
    }

    @ApiOperation(value = "原料分析-原料需求与库存分析-柱状图", response = RawMaterialDemandStockVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型-1：账面库存，2：盘点库存", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "deadline", value = "截止日期(yyyy-MM-dd)", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isOverview", value = "是否总览页面查询", defaultValue = "false", dataType = "Boolean", paramType = "query")
    })
    @GetMapping("/raw/material/demand/stock/histogram")
    public ResponseEntity<SuccessResponse> queryRawMaterialDemandStockHistogram(@RequestParam("type") String type,
                                                                                @RequestParam("deadline") String deadline,
                                                                                @RequestParam(value = "isOverview", required = false, defaultValue = "false") Boolean isOverview) {
        List<RawMaterialDemandStockVO> list = plantService.queryRawMaterialDemandStock(type, deadline, isOverview);
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "原料分析-原料日耗用趋势分析-曲线图", response = RawMaterialLineVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "start", value = "开始日期(yyyy-MM-dd)", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "end", value = "结束日期(yyyy-MM-dd)", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/raw/material/daily/consumption/line")
    public ResponseEntity<SuccessResponse> queryRawMaterialDailyConsumptionLine(@RequestParam("start") String start,
                                                                                @RequestParam("end") String end) {
        List<RawMaterialLineVO> voList = plantService.queryRawMaterialDailyConsumption(start, end);
        return ResponseEntity.ok(new SuccessResponse(voList));
    }

    @ApiOperation(value = "原料分析-原料收发存分析-表格", response = RawMaterialSendReceiveVO.class)
    @GetMapping("/raw/material/send/receive/table")
    public ResponseEntity<SuccessResponse> queryRawMaterialSendReceiveTable() {
        List<RawMaterialSendReceiveVO> voList = plantService.queryRawMaterialSendReceive();
        return ResponseEntity.ok(new SuccessResponse(voList));
    }

    @ApiOperation(value = "节超分析-仓库下拉框", response = RawMaterialWarehouseVO.class)
    @GetMapping("/raw/material/warehouse")
    @ApiImplicitParam(name = "isOverview", value = "是否总览页面查询", defaultValue = "false", dataType = "Boolean", paramType = "query")
    public ResponseEntity<SuccessResponse> queryRawMaterialWarehouseList(@RequestParam(value = "isOverview", required = false, defaultValue = "false") Boolean isOverview) {
        List<RawMaterialWarehouseVO> voList = plantService.queryRawMaterialWarehouseList(isOverview);
        return ResponseEntity.ok(new SuccessResponse(voList));
    }

    @ApiOperation(value = "节超分析-原料节超率趋势分析-曲线图", response = RawMaterialLineVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "periods", value = "盘点期数：12-近12期，18-近18期，ALL-所有期次", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "warehouseId", value = "仓库ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isOverview", value = "是否总览页面查询", defaultValue = "false", dataType = "Boolean", paramType = "query")
    })
    @GetMapping("/raw/material/paper/inventory/line")
    public ResponseEntity<SuccessResponse> queryRawMaterialPaperInventoryLine(@RequestParam("periods") String periods,
                                                                              @RequestParam("warehouseId") String warehouseId,
                                                                              @RequestParam(value = "isOverview", required = false, defaultValue = "false") Boolean isOverview) {
        List<RawMaterialLineVO> voList = plantService.queryRawMaterialPaperInventoryRate(periods, warehouseId, isOverview);
        return ResponseEntity.ok(new SuccessResponse(voList));
    }

    @ApiOperation(value = "节超分析-原料累计节超量-柱状图", response = RawMaterialPaperInventoryAmountVO.class)
    @PostMapping("/raw/material/paper/inventory/histogram")
    public ResponseEntity<SuccessResponse> queryRawMaterialPaperInventoryHistogram(@RequestBody WarehouseIdQuery param) {
        List<RawMaterialPaperInventoryAmountVO> list = plantService.queryRawMaterialPaperInventoryAmount(param.getWarehouseIds(), param.getIsOverview());
        return ResponseEntity.ok(new SuccessResponse(list));
    }

    @ApiOperation(value = "节超分析-最近一期原料节超明细-表格", response = RawMaterialPaperInventoryTableVO.class)
    @PostMapping("/raw/material/paper/inventory/table")
    public ResponseEntity<SuccessResponse> queryRawMaterialPaperInventoryTable(@RequestBody WarehouseIdQuery param) {
        RawMaterialPaperInventoryTableVO tableVO = plantService.queryRawMaterialPaperInventoryDetail(param.getWarehouseIds());
        return ResponseEntity.ok(new SuccessResponse(tableVO));
    }

    @ApiOperation(value = "总览-成品生产出站量", response = PlantOverviewOutboundVO.class)
    @GetMapping("/overview/outbound")
    public ResponseEntity<SuccessResponse> queryOverviewOutbound() {
        Map<String, List<PlantOverviewOutboundVO>> map = plantService.queryOverviewOutbound();
        return ResponseEntity.ok(new SuccessResponse(map));
    }

    @ApiOperation(value = "总览-今日产能需求完成情况-材料及单位", response = PlantOverviewCapacityMaterialVO.class)
    @GetMapping("/overview/capacity/material/unit")
    public ResponseEntity<SuccessResponse> queryOverviewCapacityMaterialAndUnit() {
        List<PlantOverviewCapacityMaterialVO> voList = plantService.queryOverviewCapacityMaterialAndUnit();
        return ResponseEntity.ok(new SuccessResponse(voList));
    }

    @ApiOperation(value = "总览-今日产能需求完成情况", response = StatisticsPlantHistoryVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unit", value = "单位", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "materialId", value = "材料ID", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/overview/capacity")
    public ResponseEntity<SuccessResponse> queryOverviewCapacity(@RequestParam("unit") String unit,
                                                                 @RequestParam("materialId") Integer materialId) {
        StatisticsPlantHistoryVO vo = plantService.queryOverviewCapacity(materialId, unit);
        return ResponseEntity.ok(new SuccessResponse(vo));
    }

}
