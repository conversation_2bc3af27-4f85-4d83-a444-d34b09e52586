package cn.pinming.microservice.material.management.wrapper;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.management.biz.dto.PurchaseOrderDTO;
import cn.pinming.microservice.material.management.proxy.EmployeeServiceProxy;
import cn.pinming.microservice.material.management.proxy.ProjectServiceProxy;
import cn.pinming.v2.company.api.dto.EmployeeDetailDto;
import cn.pinming.v2.company.api.dto.EmployeeDetailQueryDto;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import cn.pinming.weaponx.wrapper.dto.MemberDTO;
import cn.pinming.weaponx.wrapper.dto.SimpleProjectDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ProjectNameWrapper {
    @Resource
    private ProjectServiceProxy projectServiceProxy;

    public void wrap(List<? extends SimpleProjectDTO> list) {
        if (CollUtil.isNotEmpty(list)) {
            List<Integer> projectIdList = list.stream().map(SimpleProjectDTO::getProjectId).distinct().collect(Collectors.toList());
            List<SimpleConstructionProjectDto> simpleConstructionProjectDtos = projectServiceProxy.findProjectsByProjectIds(projectIdList);
            if (CollUtil.isNotEmpty(simpleConstructionProjectDtos)) {
                Map<Integer, String> collect = simpleConstructionProjectDtos.stream().collect(Collectors.toMap(SimpleConstructionProjectDto::getProjectId,SimpleConstructionProjectDto::getProjectTitle));
                list.forEach(SimpleProjectDTO -> SimpleProjectDTO.setProjectTitle(collect.get(SimpleProjectDTO.getProjectId())));
            }
        }
    }

    public void wrap(SimpleProjectDTO obj) {
        wrap(Collections.singletonList(obj));
    }

}
