package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.entity.ClientLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * ai对接记录信息 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-03-01 17:37:10
 */
public interface ClientLogMapper extends BaseMapper<ClientLog> {

    String selectLastClientInfo(@Param("companyId") Integer companyId, @Param("projectId") Integer projectId);

}
