package cn.pinming.microservice.material.management.biz.mapper;

import cn.pinming.microservice.material.management.biz.dto.OrderDetailDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialFinishedBom;
import cn.pinming.microservice.material.management.biz.query.BaseQuery;
import cn.pinming.microservice.material.management.biz.vo.ApplyVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 成品BOM包设置 Mapper 接口
 * </p>
 *
 * <AUTHOR> hao
 * @since 2022-07-26 14:31:34
 */
public interface MaterialFinishedBomMapper extends BaseMapper<MaterialFinishedBom> {

    /**
     * 查询BOM包信息
     * @param baseQuery query
     * @return 查询BOM包信息
     */
    IPage<MaterialFinishedBom> selectPageByQuery(@Param("query") BaseQuery baseQuery);

    MaterialFinishedBom selectMaterialList(@Param("categoryId") Integer categoryId,@Param("companyId") Integer companyId,@Param("projectId") Integer projectId);

    OrderDetailDTO selectOrderDetail(@Param("mixingPlantOrderDetailId") String mixingPlantOrderDetailId, @Param("companyId") Integer companyId, @Param("projectId") Integer projectId);
}
