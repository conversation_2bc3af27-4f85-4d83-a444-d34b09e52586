#中铁城建物料私有化
spring.profiles.active: dev

#tomcat/undertow配置
server:
  port: 9090
  undertow:
    accesslog:
      enabled: true
      dir: log
      pattern: '%{time,yyyy-MM-dd HH:mm:ss} %m %U %s %{i,Referer}'
      prefix: access_log.
      rotate: true
      suffix: log
  servlet:
    context-path: /

#日志配置
logging:
  config:
    classpath: logback-spring.xml
  # 分割文件设置 超过 100MB就进行分割，最大保留历史 90天
  maxFileSize: 100MB
  maxHistory: 90
  level:
    root: info
    cn.pinming.material: DEBUG

spring:
  application.name: Material-Client
  transaction:
    rollback-on-commit-failure: true
  main:
    allow-bean-definition-overriding: true
  #数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 默认数据源
    url: ********************************************************************************************************************************************
    username: root
    password: 123456
  http:
    #设置响应为utf-8
    encoding:
      force-response: true
    #文件上传配置
    multipart:
      enabled: true  #默认支持文件上传
      file-size-threshold: 0  #支持文件写入磁盘
      location: upload  # 上传文件的临时目录
      max-file-size: 40Mb  # 最大支持文件大小
      max-request-size: 100Mb  # 最大支持请求大小

  #模板引擎
  freemarker:
    cache: false
    request-context-attribute: request
    template-loader-path: classpath:/template
    contentType: text/html;charset=UTF-8
    suffix: .html
    prefix: /html/

  #监控配置
  security:
    user:
      name: admin
      password: 123456
  boot:
    admin:
      client:
        url: ${sys.server-url}
        username: ${spring.security.user.name}
        password: ${spring.security.user.password}
        instance:
          prefer-ip: true
          service-url: http://localhost:9090

#mybatis配置：dao层接口路径+sql文件mapper
mybatis-plus:
  mapper-locations: classpath*:mybatis/mapper/*.xml
  type-aliases-package: cn.pinming.material.entity
  configuration.map-underscore-to-camel-case: false
  global-config:
    db-config:
      logic-delete-field: isDeleted
      logic-delete-value: 1
      logic-not-delete-value: 0

#开放关闭节点 curl -X POST http://localhost:9090/actuator/shutdown
management:
  endpoint:
    shutdown:
      enabled: true
  endpoints:
    web:
      exposure:
        include: '*'


#客户端信息配置
sys:
  # 1.企业id
  company-id: 11541
  # 2.企业名称
  company-name: 中铁城建
  # 3.项目id
  project-id: {}
  # 4.项目名称
  project-name: {}
  # 5.推送间隔（定时任务数据推送每隔5分钟进行）
  push-interval: 5
  # 5.表达式
  cron: 0 0/${push-interval} * * * ?
  #物料后台服务器地址
  pull-url: https://zhgd.crucg.com/material-management
  #1.称重图片存放路径
  weighimg-path: D:/pms/weighimg/
  #2.高拍仪图片存放路径
  billpic-path: D:/pms/bill-pic/
  #3.车辆识别图片存放路径
  truckpic-path: D:/pms/truck-pic/车牌识别抓图/*************/
  #客户端本地存储地址
  client-file-path: D:/pms/pmsClient/
  #后台管理服务端地址
  server-host: zhuang.pinming.cn/material-operation-center
  server-url: http://${sys.server-host}
  #iot参数
  iot:
    #是否开启数据推送iot
    is-enable: {}
    #数据推送iot平台地址
    iot-url: https://zhgd-iotsite.crucg.com/open.api
    iot-method: MATERIAL.LIVE
    iot-format: json
    iot-version: 2.4.3
    # 4.设备sn
    iot-device-sn: {}
    # 5.iot平台appid
    iot-appid: 6865e67fbc7c40aca242c30966bec24d
    # 6.iot平台app密码
    iot-appsecret: ty8e@zGCJi/S?s0$8S8//uS0RSyalVs#
  #websocket参数
  websocket:
    #是否开启websocket
    is-enable: true
    #客户端更新地址
    client-update-url: ${sys.server-url}/test/version
    #客户端下载地址
    client-download-url: ${sys.server-url}/test/download
    #websocket服务端地址(物料大屏)
    websocket-server-url: ws://${sys.server-host}/material-management/websocket/
    #websocket服务端地址(后台管理)
    websocket-admin-url: ws://${sys.server-host}/websocket/
    #websocket心跳间隔
    websocket-heart-interval: 30