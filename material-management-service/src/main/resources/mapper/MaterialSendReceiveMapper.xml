<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MaterialSendReceiveMapper">
    <select id="selectWeightInfo" resultType="cn.pinming.microservice.material.management.biz.dto.WeighInfoDTO">
        select
            count(case b.type when 1 then a.id end) as weighingCarNumber,
            count(case b.type when 2 then a.id end) as sendingCarNumber,
            count(case b.type when 1 then a.id end) + count(case b.type when 2 then a.id end) as weighingCarCount,
            sum(a.weight_actual) as weighWeight
        from d_material_data a
        left join d_material_send_receive b on b.id = a.receive_id and b.is_deleted = 0
        where a.company_id = #{companyId}
        and a.is_deleted = 0
        <if test="projectIdList.size != 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="startDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m') <![CDATA[ >= ]]> DATE_FORMAT(#{startDate},'%Y%m')
        </if>
        <if test="endDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m') <![CDATA[ <= ]]> DATE_FORMAT(#{endDate},'%Y%m')
        </if>
    </select>

    <select id="selectWeightInfoByDay"
            resultType="cn.pinming.microservice.material.management.biz.dto.WeighTruckChangeDTO">
        select
        date_format(a.receive_time,'%Y-%m') as weighTime,
        count(case b.type when 1 then a.id end) as receiveNum,
        count(case b.type when 2 then a.id end) as sendNum
        from d_material_data a
        left join d_material_send_receive b on b.id = a.receive_id and b.is_deleted = 0
        where a.company_id = #{companyId}
        and a.is_deleted = 0
        <if test="projectIdList.size != 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="startDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m') <![CDATA[ >= ]]> DATE_FORMAT(#{startDate},'%Y%m')
        </if>
        <if test="endDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m') <![CDATA[ <= ]]> DATE_FORMAT(#{endDate},'%Y%m')
        </if>
        group by weighTime
    </select>

    <select id="selectWeightInfos" resultType="cn.pinming.microservice.material.management.biz.dto.WeighInfosDTO">
        select
            SUM(a.weight_actual) as weighWeight,
            MIN(a.receive_time) as firstUsedTime,
            MAX(a.receive_time) as lastUsedTime,
            ifnull (count(case b.type when 1 then a.id end),0) as weighingCarNumber,
            ifnull (count(case b.type when 2 then a.id end),0) as sendingCarNumber,
            a.project_id
        from d_material_data a
        left join d_material_send_receive b on b.id = a.receive_id and b.is_deleted = 0
        where a.company_id = #{companyId}
        and a.is_deleted = 0
        <if test="projectIdList != null and projectIdList.size != 0 ">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="startDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m') <![CDATA[ >= ]]> DATE_FORMAT(#{startDate},'%Y%m')
        </if>
        <if test="endDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m') <![CDATA[ <= ]]> DATE_FORMAT(#{endDate},'%Y%m')
        </if>
        GROUP BY a.project_id
    </select>

    <select id="querySummary" resultType="cn.pinming.microservice.material.management.biz.vo.SummaryAnalysisVO">
        select count(type = 1 and date_format(a.receive_time, '%Y%m') = date_format(now(), '%Y%m') or null) as monthReceiveCarsNum,
               count(type = 1 or null)                                                                    as countReceiveCarsNum,
               count(type = 2 and date_format(a.receive_time, '%Y%m') = date_format(now(), '%Y%m') or null) as monthSendCarsNum,
               count(type = 2 or null)                                                                    as countSendCarsNum,
               count(deviation_status = 1 and date_format(a.receive_time, '%Y%m') = date_format(now(), '%Y%m') or null) as monthMinusCarsNum,
               count(deviation_status = 1 or null)                                                                    as countMinusCarsNum
        from d_material_data a left join d_material_send_receive b on a.receive_id = b.id and b.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.receive_no is not null
        <if test="query.projectIdList !=null and query.projectIdList.size > 0">
            and a.project_id in
            <foreach collection="query.projectIdList" item="project" open="(" close=")" separator="," index="">
                #{project}
            </foreach>
        </if>
        and a.is_deleted = 0
<!--        and a.receive_time is not null-->
    </select>

    <select id="selectStatistics" resultType="cn.pinming.microservice.material.management.biz.dto.StatisticsDataDTO">
        select sum(a.weight_send) as sendAmount,sum(a.actual_count) as receiveAmount,sum(a.actual_receive) as actualReceive
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id  and b.is_deleted = 0
        where b.type = 1 and a.material_validity = 1
        and a.company_id = #{query.companyId}
        and a.weight_unit = #{query.unit}
        and b.material_validity = 1
        and a.is_deleted = 0
        <if test="query.projectIdList !=null and query.projectIdList.size > 0">
            and a.project_id in
            <foreach collection="query.projectIdList" item="project" open="(" close=")" separator="," index="">
                #{project}
            </foreach>
        </if>
        <if test="query.startDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m%d') <![CDATA[ >= ]]> DATE_FORMAT(#{query.startDate},'%Y%m%d')
        </if>
        <if test="query.endDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m%d') <![CDATA[ <= ]]> DATE_FORMAT(#{query.endDate},'%Y%m%d')
        </if>
        <if test="query.categoryIds !=null and query.categoryIds.size > 0">
            and a.category_id in
            <foreach collection="query.categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test='query.receiveType == "4" or query.receiveType == "8" '>
            and a.receive_mode in (1,2)
        </if>
        <if test='query.receiveType == "5" or query.receiveType == "7" '>
            and a.receive_mode = 3
        </if>
    </select>

    <select id="selectDeviationPage" resultType="cn.pinming.microservice.material.management.biz.vo.WeighDeviationDetailVO">
        select *
        from
        (
                select a.receive_no,b.id as receiveId,b.project_id,b.supplier_id,4 as receiveType,a.purchase_id
                ,a.truck_no
                ,b.material_id,b.weight_send as sendSettlementTotal,b.actual_count as actualSettlementTotal,b.actual_receive as actualReceive
                from d_material_data b
                left join d_material_send_receive a on b.receive_id = a.id and a.is_deleted = 0
                where b.material_validity = 1
                and a.type = 1
                and b.is_deleted = 0
                and b.weight_unit = #{param1.unit}
                and b.company_id = #{param1.companyId}
                <if test="query.projectIdList != null and query.projectIdList.size > 0">
                    and b.project_id in
                    <foreach collection="query.projectIdList" item="project" open="(" close=")" separator="," index="">
                        #{project}
                    </foreach>
                </if>
                <if test="query.startDate != null">
                    AND DATE_FORMAT(b.receive_time,'%Y%m%d') <![CDATA[ >= ]]> DATE_FORMAT(#{query.startDate},'%Y%m%d')
                </if>
                <if test="query.endDate != null">
                    AND DATE_FORMAT(b.receive_time,'%Y%m%d') <![CDATA[ <= ]]> DATE_FORMAT(#{query.endDate},'%Y%m%d')
                </if>
                <if test="query.categoryIds !=null and query.categoryIds.size > 0">
                    and b.category_id in
                    <foreach collection="query.categoryIds" item="id" open="(" close=")" separator="," index="">
                        #{id}
                    </foreach>
                </if>
                <if test='query.receiveType == "4" or query.receiveType == "8" '>
                    and b.receive_mode in (1,2)
                </if>
                <if test='query.receiveType == "5" or query.receiveType == "7" '>
                    and b.receive_mode = 3
                </if>
                <if test='query.receiveType == "3" '>
                    and 1 = 0
                </if>
                <if test='query.receiveType == "6" '>
                    and 1 = 0
                </if>


                union all

                select m.receive_no,m.receive_id,m.project_id,m.supplier_id,m.receive_type,m.purchase_id
                ,o.truck_no
                ,n.material_id,n.send_settlement_total,n.actual_settlement_total,n.actual_settlement_total as actualReceive
                from d_mobile_receive m
                left join d_mobile_receive_total n on n.receive_id = m.receive_id
                left join d_mobile_receive_truck o on o.receive_id = m.receive_id
                where m.is_deleted = 0
                and n.settlement_unit = #{param1.unit}
                and m.company_id = #{param1.companyId}
                <if test="query.projectIdList != null and query.projectIdList.size > 0">
                    and m.project_id in
                    <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                        #{id}
                    </foreach>
                </if>
                <if test="query.startDate != null">
                    AND DATE_FORMAT(m.create_time,'%Y%m%d') <![CDATA[ >= ]]> DATE_FORMAT(#{query.startDate},'%Y%m%d')
                </if>
                <if test="query.endDate != null">
                    AND DATE_FORMAT(m.create_time,'%Y%m%d') <![CDATA[ <= ]]> DATE_FORMAT(#{query.endDate},'%Y%m%d')
                </if>
                <if test="query.categoryIds !=null and query.categoryIds.size > 0">
                    and n.category_id in
                    <foreach collection="query.categoryIds" item="id" open="(" close=")" separator="," index="">
                        #{id}
                    </foreach>
                </if>
                <if test='query.receiveType == "6" or query.receiveType == "8" '>
                    and m.receive_type in (1,2)
                </if>
                <if test='query.receiveType == "3" or query.receiveType == "7" '>
                    and m.receive_type = 3
                </if>
                <if test='query.receiveType == "4" '>
                    and 1 = 0
                </if>
                <if test='query.receiveType == "5" '>
                    and 1 = 0
                </if>
        ) x
    </select>

    <select id="checkByWeighId" resultType="string">
        select a.id
        from d_material_data a
        where a.weigh_id = #{weighId}
        and a.is_deleted = 0
    </select>

    <select id="findDataByWeighId"
            resultType="cn.pinming.microservice.material.management.biz.entity.MaterialSendReceive">
        select *
        from d_material_data a
        where a.weigh_id = #{weighId}
        and a.is_deleted = 0
    </select>

    <select id="findAllWeighId" resultType="java.lang.String">
        select distinct weigh_id
        from d_material_data
        where is_deleted = 0
          and weigh_id is not null
    </select>

    <select id="findWeighDataByWeighId" resultType="java.lang.Integer">
        select count(id)
        from d_material_data
        where weigh_id = #{weighId}
          and is_deleted = 0
    </select>

</mapper>
