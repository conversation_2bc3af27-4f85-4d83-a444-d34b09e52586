<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.TruckReportMapper">

    <select id="selectTruckNo" resultType="cn.pinming.materialManagement.dto.TruckInfoDTO">
        select t.truck_no
        from d_truck_report t
        where t.company_id = #{companyId}
        and t.project_id = #{projectId}
        and t.is_deleted = 0
    </select>
    <select id="selectTruckInfoById"
            resultType="cn.pinming.microservice.material.management.biz.entity.TruckReport">
        select id from d_truck_report where truck_no = #{truckNo} and project_id = #{projectId}
        and is_deleted = 0
        limit 1
    </select>

    <select id="selectTruck" resultType="cn.pinming.microservice.material.management.biz.vo.TruckReportVO">
        select a.id,a.truck_no,a.create_id,a.create_time
        FROM d_truck_report a
        where a.company_id = #{companyId}
        <if test="projectId != null and projectId != ''">
            and a.project_id = #{projectId}
        </if>
        <if test="truckNo != null and truckNo != ''">
            and a.truck_no like concat('%',#{truckNo},'%')
        </if>
        and a.is_deleted = 0
        order by a.create_time desc
    </select>
</mapper>
