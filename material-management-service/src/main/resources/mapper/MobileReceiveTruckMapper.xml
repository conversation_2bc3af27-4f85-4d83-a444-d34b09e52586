<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MobileReceiveTruckMapper">

    <select id="listByPurchaseId" resultType="cn.pinming.microservice.material.management.biz.vo.MobileReceiveVO">
        select a.truck_no,b.create_time,b.receive_status,a.receive_id as id
        from d_mobile_receive_truck a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        where b.purchase_id = #{purchaseId}
        and b.is_deleted = 0
        order by b.create_time
    </select>
</mapper>
