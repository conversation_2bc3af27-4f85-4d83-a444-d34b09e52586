<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MaterialVerifyRelationMapper">

    <select id="check" resultType="integer">
        select count(a.id)
        from d_material_verify_relation a
        left join d_material_verify b on b.id = a.verify_id and b.is_deleted = 0
        where a.receive_data_id = #{id}
        and b.status = 0
        and a.is_deleted = 0
    </select>

    <select id="selectPushed" resultType="cn.pinming.microservice.material.management.biz.entity.MaterialData">
        select b.id,b.material_id,b.category_id,b.supplier_id,b.project_id,b.weight_unit,b.actual_count,b.company_id
        from d_material_verify_relation a
        left join d_material_data b on b.id = a.receive_data_id and b.is_deleted = 0
        where a.verify_id in
        <foreach collection="verifyIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and a.is_deleted = 0
        and b.is_pushed = 0
    </select>
</mapper>
