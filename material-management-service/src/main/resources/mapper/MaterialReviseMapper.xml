<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MaterialReviseMapper">

    <select id="selectMaterialRevise"
            resultType="cn.pinming.microservice.material.management.biz.dto.MaterialReviseDataDTO">

        select id as reviseId,
               receive_id,
               material_data_id,
               revise_detail,
               create_id as reviserId,
               company_id,
               create_time as reviseTime
        from d_material_revise
        where receive_id = #{receiveId} and material_data_id = #{id} and is_deleted = 0 and original_data is null
        order by create_time desc
    </select>

    <select id="selectMaterialReviseDetail"
            resultType="cn.pinming.microservice.material.management.biz.dto.MaterialReviseDetailDTO">

        select a.id,
               b.id as receive_id,
               a.weigh_id as tUuid,
               b.receive_mode,
               b.receive_no,
               b.order_no,
               a.document_pic,
               b.supplier_id,
               a.supplier_name,
               b.truck_no,
               a.material_id,
               a.material_name,
               a.weight_send,
               a.purchase_order_id,
               a.contract_detail_id,
               a.unit_price,
               a.total_price,
               a.weight_gross,
               a.weight_tare,
               a.weight_deduct,
               a.weight_net as nWeight,
               a.ratio,
               a.actual_count,
               a.actual_receive,
               a.weight_unit,
               a.deviation_rate,
               a.deviation_status,
               a.moisture_content,
               c.unit,
               c.conversion_rate,
               c.deviation_ceiling,
               c.deviation_floor,
               a.position,
               b.type_detail,
               b.ext_no,
                b.is_addition,
               a.is_device
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id and b.is_deleted = 0
        left join d_purchase_contract_detail c on a.contract_detail_id = c.id
        where a.receive_id = #{receiveId}
        and a.id = #{id}

    </select>
</mapper>
