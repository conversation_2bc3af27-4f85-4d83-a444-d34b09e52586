<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.SupplierMapper">
    <update id="updateStatusById">
        update s_supplier
        set is_enable = is_enable ^ 1,update_time = now(),update_id = #{mid}
        where id = #{id}
    </update>

    <select id="selectSupplierPageListByQuery"
            resultType="cn.pinming.microservice.material.management.biz.dto.SupplierDTO">
        select id, name, credit_code, ext_code, create_id, create_time, is_enable, plant_id
        from s_supplier
        where is_deleted = 0
        <if test="query.name !=null and query.name != ''">
            and name like concat('%', #{query.name},'%')
        </if>
        <if test="query.creditCode !=null and query.creditCode != ''">
            and credit_code like concat('%', #{query.creditCode},'%')
        </if>
        <if test="query.extCode !=null and query.extCode != ''">
            and ext_code like concat('%', #{query.extCode},'%')
        </if>
        order by create_time desc
    </select>
    <select id="selectSupplierList"
            resultType="cn.pinming.microservice.material.management.biz.entity.Supplier">
        SELECT id, name
        FROM s_supplier
        WHERE id IN
        <foreach collection="supplierIds" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
        and company_id = #{companyId}

    </select>
</mapper>
