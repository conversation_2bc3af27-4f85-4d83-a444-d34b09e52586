<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MobileReceiveMapper">

    <select id="selectReceivePage" resultType="cn.pinming.microservice.material.management.biz.vo.MobileReceiveCardVO">
            select distinct a.receive_id as id,a.receive_no,a.receive_status,a.receiver,a.project_id,a.purchase_id,a.receive_type,a.contract_id,a.supplier_id,a.deviation_status
            ,c.truck_no,c.truck_time as createTime
            from d_mobile_receive a
            left join d_mobile_receive_truck c on c.receive_id = a.receive_id
            left join d_mobile_receive_total m on m.receive_id = a.receive_id
            where a.company_id = #{companyId}
            and a.is_deleted = 0
            <if test="deviationStatus != null and deviationStatus != 99">
                and a.deviation_status = #{deviationStatus}
            </if>
            <if test="deviationStatus == 99">
                and a.deviation_status is null
            </if>
            <if test="truckNo != null and truckNo != ''">
                and c.truck_no like concat ('%', #{truckNo},'%')
            </if>
            <if test="categoryId != null and categoryId != ''">
                and m.category_id = #{categoryId}
            </if>
            <if test="projectId != null and projectId != '' ">
                and a.project_id = #{projectId}
            </if>
            <if test="projectIds != null and projectIds.size != 0">
                and a.project_id in
                <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                    #{id}
                </foreach>
            </if>
            <if test="supplierId != null and supplierId != '' ">
                and a.supplier_id = #{supplierId}
            </if>
            <if test="receiveStatus != null and receiveStatus != '' ">
                and a.receive_status = #{receiveStatus}
            </if>
            <if test="receiveNo != null and receiveNo != ''">
                and a.receive_no like concat ('%',#{receiveNo},'%')
            </if>
            <if test="receiveType != null and receiveType.size() > 0">
                and a.receive_type in
                <foreach collection="receiveType" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and date_format(c.truck_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{startTime}
            </if>
            <if test="endTime != null">
                and date_format(c.truck_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}
            </if>
            <if test="sort != 2">
                order by c.truck_time desc
            </if>
            <if test="sort == 2">
                order by c.truck_time asc
            </if>
    </select>

    <select id="detail" resultType="cn.pinming.microservice.material.management.biz.vo.ReceiveCardDetailVO">
			select a.receive_type,a.receive_no,a.receive_status,a.receiver,a.`comment`,a.create_time,a.project_id as receiverProject,a.deviation_status,a.supplier_id
			,b.order_no,b.position,b.create_id,b.remark,b.id as purchaseId
			,c.truck_no,c.truck_time,c.truck_pic,c.goods_pic,c.send_pic, c.longitude, c.latitude, c.location
			,d.name as contractName,d.id as contractId
            from d_mobile_receive a
            left join d_purchase_order b on a.purchase_id = b.id
            left join d_mobile_receive_truck c on a.receive_id = c.receive_id
            left join d_purchase_contract d on a.contract_id = d.id
            where a.receive_id = #{receiveId}
            and a.is_deleted = 0
    </select>

    <select id="countSummary" resultType="cn.pinming.microservice.material.management.biz.vo.SummaryAnalysisVO">
        SELECT
        count( date_format( a.create_time, '%Y%m' ) = date_format( now(), '%Y%m' ) OR NULL ) AS monthReceiveCarsNum,
        count( a.id ) AS countReceiveCarsNum,
        sum(IF(a.deviation_status = 1 AND date_format( a.create_time, '%Y%m' ) = date_format( now(), '%Y%m' ), 1, 0)) as monthMinusCarsNum,
        sum(IF(a.deviation_status = 1, 1, 0)) AS countMinusCarsNum
        from d_mobile_receive a
        WHERE a.is_deleted = 0
        AND a.company_id = #{query.companyId}
        <if test="query.projectIdList != null and query.projectIdList.size != 0">
            and a.project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectStatistics" resultType="cn.pinming.microservice.material.management.biz.dto.StatisticsDataDTO">
        select sum(b.send_settlement_total) as sendAmount,sum(b.actual_settlement_total) as receiveAmount
        from d_mobile_receive a
        left join d_mobile_receive_total b on b.receive_id = a.receive_id and b.is_deleted = 0
        where a.company_id = #{companyId}
        and a.is_deleted = 0
        and b.settlement_unit = #{unit}
        <if test="projectIdList.size != 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="startDate != null">
            AND DATE_FORMAT(a.create_time,'%Y%m%d') <![CDATA[ >= ]]> DATE_FORMAT(#{startDate},'%Y%m%d')
        </if>
        <if test="endDate != null">
            AND DATE_FORMAT(a.create_time,'%Y%m%d') <![CDATA[ <= ]]> DATE_FORMAT(#{endDate},'%Y%m%d')
        </if>
        <if test="categoryIds != null and categoryIds.size != 0">
            and b.category_id in
            <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test='receiveType == 6 or receiveType == 8'>
            and a.receive_type in (1,2)
        </if>
        <if test='receiveType == 3 or receiveType == 7'>
            and a.receive_type = 3
        </if>
    </select>

    <select id="selectSupplierMobileAnalysisPageVO"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierAnalysisDetailVO">
        select a.supplier_id,sum(b.send_settlement_total) as weightSendAmount,sum(actual_settlement_total) as weightActualAmount,sum(actual_settlement_total) as actualReceive
        from d_mobile_receive a
        left join d_mobile_receive_total b on b.receive_id = a.receive_id
        where a.is_deleted = 0
        and b.settlement_unit = #{unit}
        and a.receive_type in (1,2)
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="receiveStartDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
        </if>
        <if test="receiveEndDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
        </if>
        <if test="categoryIds.size != 0">
            and b.category_id in
            <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by a.supplier_id having supplier_id is not null
    </select>

    <select id="selectSupplierAnalysisByMobileQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierAnalysisVO">
        select sum(b.send_settlement_total) as weightSendAmount,sum(actual_settlement_total) as weightActualAmount,sum(actual_settlement_total) as actualReceive
        from d_mobile_receive a
        left join d_mobile_receive_total b on b.receive_id = a.receive_id
        where a.is_deleted = 0
        and b.settlement_unit = #{unit}
        and a.receive_type in (1,2)
        <if test="supplierId != null">
            and a.supplier_id = #{supplierId}
        </if>
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="receiveStartDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
        </if>
        <if test="receiveEndDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
        </if>
        <if test="categoryIds.size != 0">
            and b.category_id in
            <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectReceiveTypeByReceiveId" resultType="java.lang.Byte">
        select receive_type
        from d_mobile_receive
        where receive_id = #{receiveId} and is_deleted = 0
    </select>

    <select id="select" resultType="cn.pinming.microservice.material.management.biz.dto.WeighInfoDTO">
        select count(a.id) as weighingCarNumber
        from d_mobile_receive a
        where a.company_id = #{companyId}
        and a.is_deleted = 0
        <if test="projectIdList.size != 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="startDate != null">
            AND DATE_FORMAT(a.create_time,'%Y%m') <![CDATA[ >= ]]> DATE_FORMAT(#{startDate},'%Y%m')
        </if>
        <if test="endDate != null">
            AND DATE_FORMAT(a.create_time,'%Y%m') <![CDATA[ <= ]]> DATE_FORMAT(#{endDate},'%Y%m')
        </if>
    </select>

    <select id="selectMobileList" resultType="cn.pinming.microservice.material.management.biz.dto.WeighTruckChangeDTO">
        select
        date_format(a.create_time,'%Y-%m') as weighTime,
        0 as sendNum,
        count(a.id) as receiveNum
        from d_mobile_receive a
        where a.company_id = #{companyId}
        and a.is_deleted = 0
        <if test="projectIdList.size != 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="startDate != null">
            AND DATE_FORMAT(a.create_time,'%Y%m') <![CDATA[ >= ]]> DATE_FORMAT(#{startDate},'%Y%m')
        </if>
        <if test="endDate != null">
            AND DATE_FORMAT(a.create_time,'%Y%m') <![CDATA[ <= ]]> DATE_FORMAT(#{endDate},'%Y%m')
        </if>
        group by weighTime
    </select>

    <select id="selectUsageNum" resultType="java.lang.Integer">
        select count(distinct a.project_id)
        from d_mobile_receive a
        where a.is_deleted = 0
        <if test="projectIdList != null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="day != null and day != 0">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> now()
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> date_sub(now(),interval #{day} day )
        </if>
    </select>

    <select id="selectMobileUsageList" resultType="java.lang.Integer">
        select distinct a.project_id
        from d_mobile_receive a
        where a.is_deleted = 0
        <if test="projectIdList != null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="day != null and day != 0">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> now()
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> date_sub(now(),interval #{day} day )
        </if>
    </select>

    <select id="showWeighCar" resultType="cn.pinming.microservice.material.management.biz.dto.WeighCarDTO">
        select sum(c.carTotal) as carTotal,sum(c.carMonthly) as carMonthly,c.project_id
        from
        (
        select ifnull(count(a.id),0) as carTotal,0 as carMonthly,a.project_id
        from d_mobile_receive a
        where a.is_deleted = 0
        and a.project_id is not null
        <if test="query.projectIdList.size != 0">
            and a.project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m') <![CDATA[ >= ]]> date_format(#{query.startDate},'%Y-%m')
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m') <![CDATA[ <= ]]> date_format(#{query.endDate},'%Y-%m')
        </if>
        group by a.project_id

        union all

        select 0 as carTotal,ifnull(count(a.id),0) as carMonthly,a.project_id
        from d_mobile_receive a
        where a.is_deleted = 0
        and a.project_id is not null
        <if test="query.projectIdList.size != 0">
            and a.project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m') <![CDATA[ >= ]]> date_format(#{query.startDate},'%Y-%m')
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m') <![CDATA[ <= ]]> date_format(#{query.endDate},'%Y-%m')
        </if>
        and date_format(a.create_time,'%Y%m') = date_format(curdate(),'%Y%m')
        group by a.project_id
        )c
        group by c.project_id
    </select>

    <select id="showWeighCarDetail" resultType="cn.pinming.microservice.material.management.biz.dto.WeighCarDetailDTO">
        select
        ifnull(count(a.id) , 0) as weighingCarNumber,
        0 as sendingCarNumber,
        date_format(a.create_time,'%Y-%m') as date,
        a.project_id
        from d_mobile_receive a
        where a.is_deleted = 0
        and a.project_id is not null
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m') <![CDATA[ >= ]]> date_format(#{query.startDate},'%Y-%m')
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m') <![CDATA[ <= ]]> date_format(#{query.endDate},'%Y-%m')
        </if>
        <if test="query.projectIdList.size != 0">
            and a.project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by a.project_id,date_format(a.create_time,'%Y%m')
    </select>
</mapper>
