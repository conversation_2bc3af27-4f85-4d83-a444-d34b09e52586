<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MixingPlantOrderDetailMapper">

    <select id="choose" resultType="cn.pinming.microservice.material.management.biz.dto.MixingPlantOrderDetailItemDTO">
        select a.id as orderDetailId,b.id as orderId,b.order_no,b.plant_id as projectId,c.order_no as purchaseOrderNo,
                ( case when a.purchase_order_detail_id is not null then e.material_id else a.material_id end ) as materialId,
                ( case when a.purchase_order_detail_id is not null then d.parameter_requirements else a.parameter_requirements end ) as parameterRequirements,
                ( case when a.purchase_order_detail_id is not null then d.count else a.count end ) as count,
                ( case when a.purchase_order_detail_id is not null then d.remark else a.position end ) as position,
                ( case when a.purchase_order_detail_id is not null then d.unit else a.unit end ) as unit,
				( case when a.purchase_order_detail_id is not null then c.create_id else b.create_id end) as createId,
				( case when a.purchase_order_detail_id is not null then c.receive_time else b.receive_time end) as receiveTime,
				( case when a.purchase_order_detail_id is not null then c.create_time else b.create_time end ) as orderDate,
				( case when a.purchase_order_detail_id is not null then c.project_id else b.receiver_project end ) as receiverProject,
				( case when a.purchase_order_detail_id is not null then c.receiver else b.receiver end ) as receiver,
                ( case when a.purchase_order_detail_id is not null then c.receiver_tel else b.receiver_tel end ) as receiverTel,
                ( case when a.purchase_order_detail_id is not null then c.receiver_address else b.receiver_address end ) as receiverAddress,
                ( case when a.purchase_order_detail_id is not null then c.remark else b.remark end ) as remark,
                ( case when f.id is null then 1 else 2 end ) as isIngredient
        from d_mixing_plant_order_detail a
		left join d_mixing_plant_order b on a.mixing_plant_order_id = b.id and b.is_deleted = 0
		left join d_purchase_order c on b.purchase_order_id = c.id and c.is_deleted = 0
        left join d_purchase_order_detail d on d.id = a.purchase_order_detail_id and d.is_deleted = 0
        left join d_purchase_contract_detail e on e.id = d.contract_detail_id and e.is_deleted = 0
        left join d_ingredient_list f on f.mixing_plant_order_detail_id = a.id and f.is_deleted = 0
        where a.company_id = #{companyId}
		and a.project_id = #{projectId}
        and a.is_deleted = 0
        and a.status not in (5,6,7)
        <if test="name != '' and name != null">
            and b.receiver_project_title like concat('%',#{name},'%')
        </if>
        <if test="no != '' and no != null">
            and b.order_no = #{no}
        </if>
		order by receiveTime desc
    </select>

    <select id="selectOrderDetail" resultType="cn.pinming.microservice.material.management.biz.dto.ExistTicketDetailDTO">
        select b.id as mixingPlantOrderId,a.id as mixingPlantOrderDetailId,b.order_no,b.project_id as projectId,d.order_no as purchaseOrderNo
            ,( case when a.purchase_order_detail_id is not null then d.create_id else b.create_id end ) as createId
            ,( case when a.purchase_order_detail_id is not null then d.create_time else b.create_time end ) as createTime
            ,( case when a.purchase_order_detail_id is not null then e.material_id else a.material_id end ) as materialId
            ,( case when a.purchase_order_detail_id is not null then c.parameter_requirements else a.parameter_requirements end ) as parameterRequirements
            ,( case when a.purchase_order_detail_id is not null then c.count else a.count end ) as count
            ,( case when a.purchase_order_detail_id is not null then c.remark else a.position end ) as position
            ,( case when a.purchase_order_detail_id is not null then c.unit else a.unit end ) as unit
            ,( case when a.purchase_order_detail_id is not null then d.project_id else b.receiver_project end ) as receiverProject
            ,( case when a.purchase_order_detail_id is not null then d.receiver else b.receiver end ) as receiver
            ,( case when a.purchase_order_detail_id is not null then d.receiver_tel else b.receiver_tel end ) as receiverTel
            ,( case when a.purchase_order_detail_id is not null then d.receiver_address else b.receiver_address end ) as receiverAddress
            ,( case when a.purchase_order_detail_id is not null then d.receive_time else b.receive_time end ) as receiveTime
            ,( case when a.purchase_order_detail_id is not null then d.remark else b.remark end ) as remark
        from d_mixing_plant_order_detail a
        left join d_mixing_plant_order b on a.mixing_plant_order_id = b.id and b.is_deleted = 0
        left join d_purchase_order_detail c on a.purchase_order_detail_id = c.id and c.is_deleted = 0
        left join d_purchase_order d on c.order_id = d.id and d.is_deleted = 0
        left join d_purchase_contract_detail e on e.id = c.contract_detail_id and e.is_deleted = 0
        where a.id = #{id}
        and a.is_deleted = 0
    </select>

    <select id="selectPreInfo" resultType="cn.pinming.microservice.material.management.biz.dto.PreInfoDTO">
        select c.id as purchaseOrderId,b.id as purchaseOrderDetailId,d.id as contractDetailId
        from d_mixing_plant_order_detail a
        left join d_purchase_order_detail b on a.purchase_order_detail_id = b.id and b.is_deleted = 0
        left join d_purchase_order c on b.order_id = c.id and c.is_deleted = 0
        left join d_purchase_contract_detail d on b.contract_detail_id = d.id and d.is_deleted = 0
        where a.id = #{id}
        and a.is_deleted = 0
    </select>

    <select id="selectAutoAddForm"
            resultType="cn.pinming.microservice.material.management.biz.form.IngredientAutoAddForm">
        select a.id as mixingPlantOrderDetailId
        ,( case when a.purchase_order_detail_id is not null then c.material_id else a.material_id end ) as materialId
        ,( case when a.purchase_order_detail_id is not null then c.category_id else a.category_id end ) as categoryId
        from d_mixing_plant_order_detail a
        left join d_purchase_order_detail b on b.id = a.purchase_order_detail_id and b.is_deleted = 0
        left join d_purchase_contract_detail c on c.id = b.contract_detail_id and c.is_deleted = 0
        where a.id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
