<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.IngredientNoticeMapper">

    <select id="confirmFinished" resultType="cn.pinming.microservice.material.management.biz.entity.IngredientNotice">
        select *
        from d_ingredient_notice
        where ingredient_list_id = #{id}
        and is_deleted = 0
        <if test="flag == 1">
            and status = 1
        </if>
        <if test="flag == 2">
            and status = 2
        </if>
    </select>
</mapper>
