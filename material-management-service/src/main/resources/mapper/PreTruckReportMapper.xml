<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.PreTruckReportMapper">

    <select id="selectDetail" resultType="cn.pinming.microservice.material.management.biz.vo.PreTruckReportVO">
        select a.truck_no, a.capacity, a.times, a.arrive_time, a.interval_time
        from d_pre_truck_report a
        where a.pre_weigh_report_id = #{id}
          and a.is_deleted = 0
    </select>

    <select id="selectData" resultType="cn.pinming.microservice.material.management.biz.vo.PreTruckReportDetailVO">
        select a.truck_no, b.material_data_id, b.status, b.arrive_time,b.id
        from d_pre_truck_report a
                 left join d_pre_truck_report_detail b on a.id = b.pre_truck_report_id and b.is_deleted = 0
        where a.pre_weigh_report_id = #{id}
          and a.is_deleted = 0
        order by b.arrive_time
    </select>
    <select id="selectTruckAnomalStatus" resultType="java.lang.String">
        select distinct a.truck_no
        from d_pre_truck_report a
        left join d_pre_truck_report_detail b on a.id = b.pre_truck_report_id
        where a.project_id = #{projectId}
        <if test="list!=null and list.size > 0">
            and a.truck_no in
            <foreach collection="list" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and b.status in (1, 2)
    </select>
    <select id="selectTruckDetailIdsByTruckNo" resultType="java.lang.String">
        select b.id
        from d_pre_truck_report a
        left join d_pre_truck_report_detail b on a.id = b.pre_truck_report_id
        where b.is_deleted = 0
        and a.project_id = #{projectId}
        and b.status in (1, 2)
        <if test="list!=null and list.size > 0">
            and a.truck_no in
            <foreach collection="list" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectId" resultType="java.lang.String">
        select id
        from d_pre_truck_report
        where pre_weigh_report_id = #{preWeighReportId}
        and is_deleted = 0
    </select>
</mapper>
