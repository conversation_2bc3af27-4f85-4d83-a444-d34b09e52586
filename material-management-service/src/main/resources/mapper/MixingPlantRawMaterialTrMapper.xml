<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MixingPlantRawMaterialTrMapper">

    <select id="seek" resultType="cn.pinming.microservice.material.management.biz.vo.MixingPlantRawMaterialTrVO">
        select id,lab_report_no
        from d_mixing_plant_raw_material_tr
        where company_id = #{companyId}
        and project_id = #{projectId}
        and is_deleted = 0
        and find_in_set(#{materialId},lab_material_id)
        order by create_time desc
    </select>
</mapper>
