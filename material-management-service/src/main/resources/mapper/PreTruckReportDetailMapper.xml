<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.PreTruckReportDetailMapper">

    <select id="selectPageDTO"
            resultType="cn.pinming.microservice.material.management.biz.dto.PreTruckReportDetailDTO">
        select a.id,a.`no`,`status`,a.arrive_time,a.pre_truck_report_id,a.create_id,a.material_data_id,a.pre_weigh_report_id,
        b.truck_no,
        e.material_id,
        f.supplier_id
        from d_pre_truck_report_detail a
        left join d_pre_truck_report b on a.pre_truck_report_id = b.id
        left join d_pre_material_report c on c.pre_weigh_report_id = a.pre_weigh_report_id and c.is_deleted = 0
        left join d_purchase_order_detail d on d.id = c.purchase_order_detail_id
        left join d_purchase_contract_detail e on e.id = d.contract_detail_id
        left join d_purchase_contract f on e.contract_id = f.id
        where a.is_deleted = 0
        <if test="projectId != null">
            and a.project_id = #{projectId}
        </if>
        <if test="status != null">
            and a.status = #{status}
        </if>
        <if test="truckNo != null and truckNo != ''">
            and b.truck_no like concat('%', #{truckNo},'%')
        </if>
        <if test="supplierId != null and supplierId != ''">
            and f.supplier_id = #{supplierId}
        </if>
        <if test="materialIds != null and materialIds.size > 0">
            and e.material_id in
            <foreach collection="materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="startTime != null">
            and date_format(a.arrive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and date_format(a.arrive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}
        </if>
        order by FIELD(a.status,7,3,2,1,4,5,6,0), arrive_time
    </select>

    <select id="selectPreTruckInfoById"
            resultType="cn.pinming.microservice.material.management.biz.dto.PreTruckReportInfoDTO">
        select pre_truck_report_id,
               a.no,
               a.status,
               a.arrive_time,
               b.id              as reportId,
               b.no              as reportNo,
               truck_no,
               capacity,
               d.conversion_rate as preConversionRate,
               b.create_id,
               f.category_name,
               f.material_name,
               f.material_spec,
               f.unit,
               f.conversion_rate,
               f.deviation_ceiling,
               f.deviation_floor,
               a.material_data_id,
               g.order_no,
               supplier_id,
               receiver_project  as projectId
        from d_pre_truck_report_detail a
                 left join d_pre_weigh_report b on a.pre_weigh_report_id = b.id and b.is_deleted = 0
                 left join d_pre_truck_report c on a.pre_truck_report_id = c.id and c.is_deleted = 0
                 left join d_pre_material_report d on d.pre_weigh_report_id = b.id and d.is_deleted = 0
                 left join d_purchase_order_detail e on d.purchase_order_detail_id = e.id and e.is_deleted = 0
                 left join d_purchase_contract_detail f on e.contract_detail_id = f.id and f.is_deleted = 0
                 left join d_purchase_order g on g.id = e.order_id and g.is_deleted = 0
        where a.id = #{id}
        and a.is_deleted = 0
    </select>
    <select id="selectRemoveVehicleList" resultType="java.lang.String">
        select a.truck_no
        from d_pre_truck_report a left join d_pre_truck_report_detail b on a.id = b.pre_truck_report_id
        where status in (2, 3)
        <if test="ids.size != 0">
            and a.truck_no in
            <foreach collection="ids" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectPreTruckIds" resultType="java.lang.String">
        select b.id
        from d_pre_truck_report a
                 left join d_pre_truck_report_detail b
                           on a.id = b.pre_truck_report_id
        where a.id = (select pre_truck_report_id
                      from d_pre_truck_report_detail
                      where id = #{id})
          and b.status in (1, 2)
          and b.id != #{id}
    </select>

    <select id="listTruckByPurchaseId" resultType="cn.pinming.microservice.material.management.biz.vo.PreTruckReportDetailVO">
  		select a.id as materialDataId,a.receive_time as arriveTime,c.truck_no,b.status,b.id,a.actual_receive,a.weight_unit
		from d_material_data a
		left join d_pre_truck_report_detail b on b.material_data_id = a.id and b.is_deleted = 0
		left join d_material_send_receive c on c.id = a.receive_id and c.is_deleted = 0
		where a.purchase_order_id = #{purchaseId}
		and a.is_deleted = 0
		order by a.receive_time desc
    </select>

    <select id="selectInfoForWarn" resultType="cn.pinming.microservice.material.management.biz.dto.TruckReportDetailInfoForWarnDTO">
        select a.id,a.arrive_time,a.no,a.company_id,a.project_id,b.truck_no,c.supplier_id,a.status
        from d_pre_truck_report_detail a
        left join d_pre_truck_report b on b.pre_weigh_report_id = a.pre_weigh_report_id and b.is_deleted = 0
        left join d_material_data c on a.material_data_id = c.id and c.is_deleted = 0
        where a.id = #{preTruckReportId}
        and a.is_deleted = 0
    </select>

    <select id="selectDataById" resultType="string">
        select id
        from d_pre_truck_report_detail a
        where a.id = #{id}
        and a.is_deleted = 0
    </select>

    <select id="selectId" resultType="java.lang.String">
        select id
        from d_pre_truck_report_detail
        where pre_weigh_report_id = #{preWeighReportId}
        and is_deleted = 0
    </select>

    <select id="reportHistory" resultType="cn.pinming.microservice.material.management.biz.vo.PreReportHistoryVO">
        select a.`status`,
            c.truck_no,c.capacity as weightSend,
            d.weight_gross,d.weight_tare,d.weight_net,d.enter_time as weightGrossTime,d.leave_time as weightTareTime,
            f.material_id,f.unit
        from d_pre_truck_report_detail a
        left join d_pre_weigh_report b on b.id = a.pre_weigh_report_id and b.is_deleted = 0
        left join d_pre_truck_report c on c.id = a.pre_truck_report_id and c.is_deleted = 0
        left join d_material_data d on a.material_data_id = d.id and d.is_deleted = 0
        left join d_pre_material_report e on e.pre_weigh_report_id = a.pre_weigh_report_id and e.is_deleted = 0
        left join d_purchase_contract_detail f on  f.id = e.contract_detail_id and f.is_deleted = 0
        where a.pre_weigh_report_id = #{preWeighId}
        and c.weChat_id = #{weChatId}
        and a.is_deleted = 0
        order by a.create_time desc
        limit 1
    </select>
</mapper>
