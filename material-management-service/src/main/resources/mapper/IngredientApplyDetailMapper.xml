<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.IngredientApplyDetailMapper">

    <select id="queryIngredientApplyDetailByReportIds" resultType="cn.pinming.microservice.material.management.biz.entity.IngredientApplyDetail">
        select * from d_ingredient_apply_detail where is_deleted = 0
        <if test="reportIds != null and reportIds.size > 0">
            and report_id in
            <foreach collection="reportIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectApplyDetailVO" resultType="cn.pinming.microservice.material.management.biz.vo.MaterialReportVO">
        select a.id,a.material_id,b.id as reportId,b.lab_report_no as reportNo,a.material_name
        from d_ingredient_apply_detail a
        left join d_mixing_plant_raw_material_tr b on b.id = a.report_id and b.is_deleted = 0
        where a.ingredient_apply_id = #{id}
        and a.is_deleted = 0
    </select>
</mapper>
