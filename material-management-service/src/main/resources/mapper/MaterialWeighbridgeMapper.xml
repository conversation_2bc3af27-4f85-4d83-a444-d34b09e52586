<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MaterialWeighbridgeMapper">


    <select id="selectSimpleWeighbridge"
            resultType="cn.pinming.microservice.material.management.biz.dto.SimpleWeighbridgeDTO">
        select w.project_id,count (*) as weighbridgeCount
        from s_material_weighbridge w
        where w.company_id = #{companyId}
        <if test="projectIds != null and projectIds.size > 0">
            and w.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and w.is_deleted = 0
        group by w.project_id
        order by w.create_time desc
    </select>
    <select id="selectWeighInfo" resultType="cn.pinming.materialManagement.dto.WeighInfoDTO">
        select m.weigh_system_name as weigh_name, m.weigh_system_no as weigh_no
        from s_material_weighbridge m
        where m.company_id = #{companyId}
          and m.project_id = #{projectId}
          and m.is_deleted = 0
    </select>
    <select id="selectByDeviceSn"
            resultType="cn.pinming.microservice.material.management.biz.entity.MaterialWeighbridge">
        select company_id, project_id
        from s_material_weighbridge
        where company_id = #{companyId}
          and project_id = #{projectId}
          and weigh_system_no = #{deviceSn}
          and is_deleted = 0
    </select>

    <select id="selectInstallationNum" resultType="java.math.BigDecimal">

        select count(DISTINCT a.project_id) as installationNum
        from s_material_weighbridge a
        where a.is_deleted = 0
        <if test="projectIdList != null and projectIdList.size > 0">
            and a.project_id in
             <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
             </foreach>
        </if>

    </select>

    <select id="selectOnlineRate" resultType="java.math.BigDecimal">
        select convert(count(case a.status when 0 then a.id end) / count(a.id) * 100 ,decimal(3,0)) as onlineRate
        from s_material_weighbridge a
        where a.is_deleted = 0
        <if test="projectIdList != null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectInstallationRateList"
            resultType="cn.pinming.microservice.material.management.biz.dto.FourCircleDTO">
		select a.project_id,(count(a.id) / c.num) as installationRate,
		    (count(case a.status when 0 then a.id end) / num) as onlineRate
		from s_material_weighbridge a
		join
		(
			select count(b.id) as num
			from s_material_weighbridge b
			where b.is_deleted = 0
		)c
		where a.is_deleted = 0
		group by a.project_id
    </select>



    <select id="selectInstallationList" resultType="java.lang.Integer">
        select DISTINCT a.project_id
        from s_material_weighbridge a
        where a.is_deleted = 0
        <if test="projectIdList != null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectOnlineList" resultType="java.lang.Integer">
        select DISTINCT a.project_id
        from s_material_weighbridge a
        where a.is_deleted = 0
        and a.status = 0
        <if test="projectIdList != null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectInstallationPJList" resultType="java.lang.Integer">
        select DISTINCT a.project_id
        from s_material_weighbridge a
        where a.is_deleted = 0
        <if test="projectIdList != null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>
