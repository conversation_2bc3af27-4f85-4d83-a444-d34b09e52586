<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MobileReceiveDetailMapper">

    <select id="selectReceiveDetail" resultType="cn.pinming.microservice.material.management.biz.dto.MobileGoodsCardDTO">
        select  a.receive_id,a.material_id,sum(a.settlement_total) as settlementTotal,a.settlement_unit,b.deviation_status
        from d_mobile_receive_detail a
        left join d_mobile_receive_total b on a.total_id = b.id
        where a.receive_id in
        <foreach collection = "receiveIdList" item = "id" open ="(" close=")" separator="," index="">
            #{id}
        </foreach>
        and a.is_deleted = 0
        group by a.total_id
    </select>

    <select id="detail" resultType="cn.pinming.microservice.material.management.biz.dto.ReceiveCardMaterialDetailDTO">
		select t.remark as purchaseRemark,t.purchase_brand,t.deviation_rate,t.deviation_status,t.send_number,t.send_content,t.send_settlement_total,t.unit as sendUnit,t.settlement_unit as sendSettlementUnit
		,t.actual_number,t.actual_content,t.actual_settlement_total,t.id as totalId,t.brand,t.material_id
		,r.purchase_id,r.contract_id
		from d_mobile_receive_total t
		left join d_mobile_receive r on r.receive_id = t.receive_id
		where t.receive_id = #{receiveId}
    </select>

	<select id="selectHistory" resultType="cn.pinming.microservice.material.management.biz.vo.MobileReceiveDetailHistoryVO">
		select a.is_standard,a.number,a.content,a.settlement_total,a.ratio,a.points_pic
		from d_mobile_receive_detail a
		where a.total_id = #{totalId}
	</select>

    <select id="getPointsPic" resultType="string">
        select points_pic
        from d_mobile_receive_detail
        where receive_id = #{receiveId}
        and points_pic is not null
        and is_deleted = 0
    </select>
</mapper>
