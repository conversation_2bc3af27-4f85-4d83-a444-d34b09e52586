<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.IngredientNoticeDetailMapper">

    <select id="selectMaterialIdList" resultType="cn.pinming.microservice.material.management.biz.dto.NoticeMaterialSimpleDTO">
        select material_id,unit
        from d_ingredient_notice_detail
        where ingredient_notice_id = #{id}
        and is_deleted = 0
    </select>

    <select id="selectConfirmDetail"
            resultType="cn.pinming.microservice.material.management.biz.entity.IngredientNoticeDetail">
        select *
        from d_ingredient_notice_detail
        where ingredient_notice_id = #{id}
        and is_deleted = 0
    </select>
</mapper>
