<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.IngredientListMapper">

    <select id="isFile" resultType="cn.pinming.microservice.material.management.biz.entity.IngredientList">
        select status
        from d_ingredient_list
        where id = #{id}
        and is_deleted = 0
    </select>

    <select id="judge" resultType="cn.pinming.microservice.material.management.biz.entity.IngredientList">
        select *
        from d_ingredient_list
        where mixing_plant_order_detail_id = #{id}
        and is_deleted = 0
    </select>

    <select id="applyNoCheck" resultType="java.lang.String">
        select b.no
        from d_ingredient_list a
        left join d_ingredient_apply b on b.ingredient_list_id = a.id and b.is_deleted = 0
        where b.no = #{no}
        and a.project_id = #{projectId}
        and a.company_id = #{companyId}
        and a.is_deleted = 0
    </select>

    <select id="selectIngredientListVO"
            resultType="cn.pinming.microservice.material.management.biz.vo.IngredientListVO">
        select a.id,a.no,a.status,c.order_no,a.mixing_plant_order_detail_id
        ,( case when b.purchase_order_detail_id is not null then e.material_id else b.material_id end ) as materialId
        ,( case when b.purchase_order_detail_id is not null then e.category_id else b.category_id end ) as categoryId
        ,( case when b.purchase_order_detail_id is not null then e.project_id else c.receiver_project end ) as projectId
        ,( case when b.purchase_order_detail_id is not null then d.remark else b.position end ) as position
        ,( case when b.purchase_order_detail_id is not null then d.unit else b.unit end ) as unit
        ,( case when b.purchase_order_detail_id is not null then d.count else b.count end ) as count
        from d_ingredient_list a
        left join d_mixing_plant_order_detail b on b.id = a.mixing_plant_order_detail_id and b.is_deleted = 0
        left join d_mixing_plant_order c on c.id = b.mixing_plant_order_id and c.is_deleted = 0
        left join d_purchase_order_detail d on b.purchase_order_detail_id = d.id
        left join d_purchase_contract_detail e on e.id = d.contract_detail_id
        where a.id = #{id}
        and a.is_deleted = 0
    </select>

    <select id="selectPages" resultType="cn.pinming.microservice.material.management.biz.vo.IngredientPageVO">
        select a.id,a.`no`,a.material_id,a.category_id,a.`status`,a.create_time
        ,c.order_no
        ,( case when b.purchase_order_detail_id is not null then d.parameter_requirements else b.parameter_requirements end ) as parameterRequirements
        ,( case when b.purchase_order_detail_id is not null then d.count else b.count end ) as count
        ,( case when b.purchase_order_detail_id is not null then d.unit else b.unit end ) as unit
        ,( case when b.purchase_order_detail_id is not null then e.receive_time else c.receive_time end ) as receive_time
        ,f.`no` as mixNo,f.test_unit,f.report_date
        from d_ingredient_list a
        left join d_mixing_plant_order_detail b on a.mixing_plant_order_detail_id = b.id and b.is_deleted = 0
        left join d_mixing_plant_order c on c.id = b.mixing_plant_order_id and c.is_deleted = 0
        left join d_purchase_order_detail d on d.id = b.purchase_order_detail_id and d.is_deleted = 0
        left join d_purchase_order e on e.id = d.order_id and e.is_deleted = 0
        left join d_ingredient_notice f on f.ingredient_list_id = a.id and f.is_deleted = 0 and f.status = 1
        where a.company_id = #{companyId}
        and a.project_id = #{projectId}
        and a.is_deleted = 0
        <if test="status != null and status != 9">
            and a.status = #{status}
        </if>
        order by a.create_time desc
    </select>

    <select id="statistics" resultType="cn.pinming.microservice.material.management.biz.dto.IngredientStatisticsDTO">
        select status,count(id) as count
        from d_ingredient_list
        where company_id = #{companyId}
        and project_id = #{projectId}
        and is_deleted = 0
        group by status
    </select>

    <select id="requirement" resultType="java.lang.String">
        select a.parameter_requirements
        from d_ingredient_apply a
        left join d_ingredient_list b on a.ingredient_list_id = b.id and b.is_deleted = 0
        where b.company_id = #{companyId}
        and b.project_id = #{projectId}
        and a.is_deleted = 0
        and a.parameter_requirements is not null
    </select>

    <select id="selectRequirement" resultType="java.math.BigDecimal">
        select case when b.purchase_order_detail_id is not null then c.count else b.count end
        from d_ingredient_list a
        left join d_mixing_plant_order_detail b on a.mixing_plant_order_detail_id = b.id and b.is_deleted = 0
        left join d_purchase_order_detail c on c.id = b.purchase_order_detail_id and c.is_deleted = 0
        where a.id = #{ingredientListId}
        and a.is_deleted = 0
    </select>
</mapper>
