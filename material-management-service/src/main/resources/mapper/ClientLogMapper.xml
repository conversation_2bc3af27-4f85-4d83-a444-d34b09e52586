<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.ClientLogMapper">

    <select id="selectLastClientInfo" resultType="java.lang.String">
        select id
        from d_client_log
        where company_id = #{companyId}
          and project_id = #{projectId}
          and status = 0
          and is_deleted = 0
          and update_time >= DATE_SUB(NOW(), INTERVAL 2 MINUTE)
        limit 1
    </select>

</mapper>
