<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.IngredientApplyMapper">

    <select id="applyFinished" resultType="cn.pinming.microservice.material.management.biz.entity.IngredientApply">
        select *
        from d_ingredient_apply
        where ingredient_list_id = #{id}
        and is_deleted = 0
    </select>

    <select id="size" resultType="int">
        select count(b.id)
        from d_ingredient_apply a
        left join d_ingredient_apply_detail b on b.ingredient_apply_id = a.id and b.is_deleted = 0
        where a.ingredient_list_id = #{id}
        and a.is_deleted = 0
    </select>

    <select id="selectApplyVO" resultType="cn.pinming.microservice.material.management.biz.vo.ApplyVO">
        select id,`no`,position,apply_date,use_date,parameter_requirements
        from d_ingredient_apply
        where ingredient_list_id = #{id}
        and is_deleted = 0
    </select>
</mapper>
