<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MaterialFinishedBomMapper">

    <select id="selectPageByQuery"
            resultType="cn.pinming.microservice.material.management.biz.entity.MaterialFinishedBom">
        select *
        from d_material_finished_bom
        where is_deleted = 0 and project_id = #{query.projectId}
        order by create_time desc
    </select>

    <select id="selectMaterialList"
            resultType="cn.pinming.microservice.material.management.biz.entity.MaterialFinishedBom">
        select category_materials,other_parameter,exist_water
        from d_material_finished_bom
        where category_id = #{categoryId}
        and company_id = #{companyId}
        and project_id = #{projectId}
        and is_deleted = 0
    </select>

    <select id="selectOrderDetail"
            resultType="cn.pinming.microservice.material.management.biz.dto.OrderDetailDTO">
       	select t.*,b.category_materials,b.other_parameter,b.exist_water
				from
				(
						select
						 ( case when a.purchase_order_detail_id is not null then d.material_id else a.material_id end ) as materialId
						,( case when a.purchase_order_detail_id is not null then d.category_id else a.category_id end ) as categoryId
						,( case when a.purchase_order_detail_id is not null then c.unit else a.unit end ) as unit
						,( case when a.purchase_order_detail_id is not null then c.remark else a.position end ) as position
						,( case when a.purchase_order_detail_id is not null then c.project_id else e.receiver_project end ) as projectId
						,( case when a.purchase_order_detail_id is not null then c.parameter_requirements else a.parameter_requirements end ) as parameterRequirements
						,e.order_no
						from d_mixing_plant_order_detail a
						left join d_purchase_order_detail c on c.id = a.purchase_order_detail_id and c.is_deleted = 0
						left join d_purchase_contract_detail d on d.id = c.contract_detail_id and d.is_deleted = 0
						left join d_mixing_plant_order e on a.mixing_plant_order_id = e.id and e.is_deleted = 0
						where a.id = #{mixingPlantOrderDetailId}
						and a.company_id = #{companyId}
						and a.project_id = #{projectId}
						and a.is_deleted = 0
				)t
				left join d_material_finished_bom b on t.categoryId = b.category_id and b.is_deleted = 0
				where b.project_id = #{projectId}
    </select>
</mapper>
