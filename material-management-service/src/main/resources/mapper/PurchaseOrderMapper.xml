<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.PurchaseOrderMapper">

    <update id="updateStatusById">
        update d_purchase_order set status = #{status}
        where id = #{purchaseId}
          and company_id = #{companyId}
          and receiver_project = #{projectId}
          and is_deleted = 0
    </update>

    <select id="selectPageDTO"
            resultType="cn.pinming.microservice.material.management.biz.dto.PurchaseOrderDTO">
        select distinct a.id,
               order_no,
               order_name,
               receiver_project as projectId,
               b.name as contractName,
               a.category,
               a.supplier_id,
               a.create_id,
               a.create_time,
               receive_time,
               status,
               a.position,
               a.status as orderStandard
        from d_purchase_order a
        left join d_purchase_contract b on a.contract_id = b.id and a.company_id = b.company_id and b.is_deleted = 0
        left join d_purchase_contract_detail c on b.id = c.contract_id and c.company_id = b.company_id and c.is_deleted = 0
        where a.is_deleted = 0
        and a.company_id = #{companyId}
        <if test="orderNo !=null and orderNo != ''">
           and a.order_no like concat('%', #{orderNo},'%')
        </if>
        <if test="orderName !=null and orderName != ''">
            and a.order_name like concat('%', #{orderName},'%')
        </if>
        <if test="receiverProject != null">
           and a.receiver_project = #{receiverProject}
        </if>
        <if test="projectId != null">
            and a.project_id = #{projectId}
        </if>
        <if test="projectIds != null and projectIds.size != 0">
            and a.receiver_project in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="contractId != null">
           and a.contract_id = #{contractId}
        </if>
        <if test="supplierId != null">
           and a.supplier_id = #{supplierId}
        </if>
        <if test="categoryId != null">
           and c.category_id = #{categoryId}
        </if>
        <if test="createId != null">
           and a.create_id = #{createId}
        </if>
        <if test="status != null">
           and a.status = #{status}
        </if>
        order by orderStandard asc,a.create_time desc
    </select>

    <select id="selectPurchaseOrderById"
            resultType="cn.pinming.microservice.material.management.biz.dto.PurchaseOrderDTO">
        select a.id,
               a.order_name,
               a.supplier_id,
               a.contract_id,
               a.order_time,
               a.order_no,
               a.position,
               a.receive_time,
               a.receiver_project,
               a.receiver_project as project_id,
               a.receiver_address,
               a.receiver,
               a.receiver_tel,
               a.remark,
               a.create_id,
               a.status,
               a.create_time,
               b.name as contractName,
               a.create_id,
               a.company_id,
               a.is_push
        from d_purchase_order a
        left join d_purchase_contract b on a.contract_id = b.id and b.is_deleted = 0
        where a.id = #{id}
    </select>
    <select id="selectMaterialIdInfo"
            resultType="cn.pinming.microservice.material.management.biz.entity.PurchaseContractDetail">
        select distinct c.id, c.material_id,c.category_id, c.category_name, c.deviation_floor, c.deviation_ceiling
        from d_purchase_order a
                 left join d_purchase_order_detail b on a.id = b.order_id and b.is_deleted = 0
                 left join d_purchase_contract_detail c on b.contract_detail_id = c.id and c.is_deleted = 0
        where a.is_deleted = 0 and a.order_no = #{purchaseId}
    </select>
    <select id="selectOrderById" resultType="java.lang.String">
        select order_no from d_purchase_order where company_id = #{companyId} and receiver_project = #{projectId}
        and status in (3,4)
        and is_deleted = 0 order by create_time desc
    </select>

    <select id="seek" resultType="cn.pinming.microservice.material.management.biz.vo.OrderNoVO">
        select a.id as orderId,a.order_no as orderNo
        from d_purchase_order a
        where a.company_id = #{param2.currentCompanyId}
        and a.project_id = #{param2.currentProjectId}
        and a.is_deleted = 0
        and a.status in (3,4)
        and a.order_no like concat('%',#{orderNo},'%')
    </select>

    <select id="selectOrderInfo" resultType="cn.pinming.microservice.material.management.biz.vo.PurchaseSimpleVO">
        select a.id,a.supplier_id,a.order_no,a.position,a.create_id,a.receiver,a.receiver_project,a.remark,a.create_time
        from d_purchase_order a
        where a.id = #{orderId}
        and a.is_deleted = 0
    </select>

    <update id="remove">
        update d_purchase_order a
        set a.is_deleted = 1
        where a.id = #{id}
        and a.status = 1
    </update>

    <select id="count" resultType="int">
        select count(*)
        from d_purchase_order a
        where a.contract_id = #{id}
        and a.is_deleted = 0
    </select>

    <select id="listPurchase" resultType="cn.pinming.microservice.material.management.biz.dto.PrePurchaseDTO">
        select a.id,a.order_no,a.create_id,a.create_time,a.supplier_id,a.remark,a.receiver_project as projectId,a.receiver,a.position
        from d_purchase_order a
        where a.supplier_id = #{supplierId}
        and a.is_deleted = 0
        and a.receiver_project = #{projectId}
        and a.company_id = #{companyId}
        and a.status in (3,4)
        order by a.create_time desc
    </select>

    <select id="selectMaterial" resultType="cn.pinming.microservice.material.management.biz.dto.MaterialSimpleDTO">
        select a.count,b.deviation_floor,b.deviation_ceiling
        from d_purchase_order_detail a
        right join d_purchase_contract_detail b on a.contract_detail_id = b.id and b.material_id = #{materialId}
        where a.order_id = #{purchaseId}
        and a.is_deleted = 0
    </select>


    <select id="selectIdByPurchaseNo"
            resultType="cn.pinming.microservice.material.management.biz.entity.PurchaseOrder">
        select id ,supplier_id, order_no,contract_id from d_purchase_order where order_no = #{purchaseNo}
    </select>

    <select id="findDecisionFactor"
            resultType="cn.pinming.microservice.material.management.biz.dto.ContractDecisionDTO">
        select c.id,c.material_id,c.unit as settlementUnit,c.conversion_rate,c.deviation_ceiling,c.deviation_floor
        from d_purchase_order a
        left join d_purchase_contract b on b.id = a.contract_id and b.is_deleted = 0
        left join d_purchase_contract_detail c on c.contract_id = b.id and c.is_deleted = 0
        where a.order_no = #{purchaseNo}
    </select>

    <select id="selectPurchaseAccount" resultType="java.math.BigDecimal">
        select sum(b.count)
        from d_purchase_order a
        left join d_purchase_order_detail b on b.order_id = a.id and b.is_deleted = 0
        left join d_purchase_contract_detail c on b.contract_detail_id = c.id and c.is_deleted = 0
        where a.company_id = #{companyId}
        and c.unit = #{unit}
        <if test="projectIdList !=null and projectIdList.size > 0">
            and a.receiver_project in
            <foreach collection="projectIdList" item="project" open="(" close=")" separator="," index="">
                #{project}
            </foreach>
        </if>
        <if test="categoryIds !=null and categoryIds.size > 0">
            and c.category_id in
            <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="receiveStartDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m%d') <![CDATA[ >= ]]> DATE_FORMAT(#{receiveStartDate},'%Y%m%d')
        </if>
        <if test="receiveEndDate != null">
            AND DATE_FORMAT(a.receive_time,'%Y%m%d') <![CDATA[ <= ]]> DATE_FORMAT(#{receiveEndDate},'%Y%m%d')
        </if>
        and a.is_deleted = 0
    </select>

    <select id="listForRevise" resultType="cn.pinming.microservice.material.management.biz.vo.PurchaseForReviseVO">
        select id as purchaseOrderId,order_no,create_id,create_time,position
        from d_purchase_order a
        where a.contract_id = #{contractId} and a.status in (3,4)
        <if test="projectId!=null">
            and find_in_set(#{projectId},receiver_project)
        </if>
        and a.is_deleted = 0
        order by a.create_time desc
    </select>
    <select id="selectOrderByPruNo" resultType="cn.pinming.microservice.material.management.biz.entity.PurchaseOrder">
        select * from d_purchase_order
        where receiver_project = #{projectId}
        and order_no = #{purNo}
    </select>

    <select id="getPurchaseHistory" resultType="cn.pinming.microservice.material.management.biz.vo.PurchaseSimpleVO">
        select a.material_id,b.id,ifnull(b.supplier_id,d.supplier_id) as supplierId,b.order_no,b.create_id,b.receiver,b.receiver_project,b.remark,b.create_time,a.contract_detail_id,d.name
        ,d.id as contractId
        from d_material_data a
        left join d_purchase_order b on a.purchase_order_id = b.id and b.is_deleted = 0 and b.status in (3,4)
        left join d_purchase_contract_detail c on a.contract_detail_id = c.id and c.is_deleted = 0
        left join d_purchase_contract d on c.contract_id = d.id and d.is_deleted = 0
        where a.is_deleted = 0
        and a.company_id = #{companyId}
        and a.project_id = #{projectId}
        and a.update_id = #{updateId}
        and (a.purchase_order_id is not null or a.contract_detail_id is not null)
        and date_format(NOW(),'%Y%m%d') - date_format(a.update_time,'%Y%m%d') <![CDATA[ <= ]]> 2
        order by a.update_time desc
        limit 1
    </select>

    <select id="lastReceiveInfo" resultType="cn.pinming.microservice.material.management.biz.vo.PurchaseOrderReceiveInfoVO">
        SELECT
            t1.receiver lastReceiveName,
            t1.receiver_tel lastReceivePhone
        FROM
            ( SELECT #{userId} create_id, max( create_time ) create_time FROM d_purchase_order WHERE is_deleted = 0 and create_id = #{userId} ) t
                LEFT JOIN d_purchase_order t1 ON t.create_id = t1.create_id
                AND t.create_time = t1.create_time
    </select>

    <select id="queryPlantId" resultType="java.lang.Integer">
        select plant_id from s_supplier where id = #{supplierId}
    </select>

    <select id="selectDetail" resultType="cn.pinming.microservice.material.management.biz.vo.PurchasePdfDetailVO">
        select a.remark as position,b.material_id,a.company_id,a.project_id,a.id as purchaseOrderDetailId,b.id as contractDetailId,b.unit
        from d_purchase_order_detail a
        left join  d_purchase_contract_detail b on b.id = a.contract_detail_id and b.is_deleted = 0
        where a.order_id = #{id}
        and a.is_deleted = 0
    </select>
    <select id="getPurchaseOrderDetail"
            resultType="cn.pinming.microservice.material.management.biz.vo.PurchaseOrderDetailVO">
        select a.contract_detail_id,
        a.id as purchaseOrderDetailId,
        a.order_id,
        a.brand,
        a.count,
        a.remark,
        a.unit,
        a.parameter_requirements,
        b.order_no,
        b.supplier_id,
        b.order_time,
        b.receiver_project,
        b.receiver_address,
        b.receiver,
        b.receiver_tel,
        b.receive_time,
        b.create_id,
        b.company_id,b.order_ext_no
        from d_purchase_order_detail a
        left join d_purchase_order b on b.id = a.order_id and b.is_deleted = 0
        where a.id in
        <foreach collection="list" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
    </select>
</mapper>
