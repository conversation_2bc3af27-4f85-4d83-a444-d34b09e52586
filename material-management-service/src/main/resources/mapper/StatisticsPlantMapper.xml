<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.StatisticsPlantMapper">

    <select id="queryStatisticsPlantCategory" resultType="cn.pinming.microservice.material.management.biz.dto.StatisticsPlantCategoryDTO">
        SELECT
            IF
                ( p1.purchase_order_detail_id IS NOT NULL, c.category_id, p1.category_id ) categoryId,
            IF
                ( p1.purchase_order_detail_id IS NOT NULL, p2.unit, p1.unit ) unit,
            IF
                ( p1.purchase_order_detail_id IS NOT NULL, c.material_id, p1.material_id ) materialId
        FROM
            (
                SELECT
                    t1.*
                FROM
                        (
                            <foreach collection="projectIds" item="id" separator=" union all ">
                                select #{id} project_id
                            </foreach>
                        ) t
                            LEFT JOIN ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 and status != 7 ) t1 ON t.project_id = t1.project_id
            ) p1
                LEFT JOIN ( SELECT * FROM d_purchase_order_detail WHERE is_deleted = 0 ) p2 ON p1.purchase_order_detail_id = p2.id
                LEFT JOIN ( SELECT * FROM d_purchase_contract_detail WHERE is_deleted = 0 ) c ON p2.contract_detail_id = c.id
        WHERE
                IF
                    ( p1.purchase_order_detail_id IS NOT NULL, c.category_id, p1.category_id ) IN
                        <foreach collection="categoryIds" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
    </select>

    <select id="queryPlanCount" resultType="cn.pinming.microservice.material.management.biz.dto.StatisticsPlantDTO">
        SELECT
            detailId,
            projectId,
            receiveTime,
            if(planCount is null, 0, planCount) planCount,
            materialId,
            unit
        FROM
            (
                SELECT
                    t1.id detailId,
                    t.project_id projectId,
                    date_format( IF ( t1.purchase_order_detail_id IS NOT NULL, t4.receive_time, o.receive_time ), '%Y-%m-%d' ) receiveTime,
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t2.count, t1.count ) planCount,
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t2.unit, t1.unit ) unit,
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t3.material_id, t1.material_id ) materialId
        FROM
                        (
                            <foreach collection="projectIds" item="id" separator=" union all ">
                                select #{id} project_id
                            </foreach>
                        ) t
                            LEFT JOIN ( SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0 ) o ON t.project_id = o.plant_id
                            LEFT JOIN ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 and status != 7 ) t1 ON t1.mixing_plant_order_id = o.id
                            LEFT JOIN ( SELECT * FROM d_purchase_order_detail WHERE is_deleted = 0 ) t2 ON t1.purchase_order_detail_id = t2.id
                            LEFT JOIN ( SELECT * FROM d_purchase_contract_detail WHERE is_deleted = 0 ) t3 ON t2.contract_detail_id = t3.id
                            LEFT JOIN ( SELECT * FROM d_purchase_order WHERE is_deleted = 0 ) t4 ON t2.order_id = t4.id
                        where t1.id is not null
            ) t5
        WHERE 1 = 1
              <if test="unit != null and unit !=''">
                  AND unit = #{unit}
              </if>
              <if test="materialIds != null and materialIds.size > 0">
                  AND materialId IN
                  <foreach collection="materialIds" item="materialId" open="(" close=")" separator=",">
                      #{materialId}
                  </foreach>
              </if>
    </select>

    <select id="queryExitCount" resultType="cn.pinming.microservice.material.management.biz.dto.StatisticsPlantDTO">
        SELECT
            t.project_id projectId,
            date_format( t2.create_time, '%Y-%m-%d' ) exitTime,
            if(t2.actual_count is null, 0, t2.actual_count) exitCount,
            t2.material_id materialId,
            t2.unit
        FROM
                    (
                        <foreach collection="projectIds" item="id" separator=" union all ">
                            select #{id} project_id
                        </foreach>
                    ) t
                    LEFT JOIN ( SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0 ) o ON t.project_id = o.plant_id
                    LEFT JOIN ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 and status != 7 ) t1 ON t1.mixing_plant_order_id = o.id
                    LEFT JOIN (
                SELECT
                    *
                FROM
                    d_mixing_plant_exit_ticket
                WHERE
                    is_deleted = 0
                    <if test="unit != null and unit !=''">
                        AND unit = #{unit}
                    </if>
                    <if test="materialIds != null and materialIds.size > 0">
                        AND material_id IN
                        <foreach collection="materialIds" item="materialId" open="(" close=")" separator=",">
                            #{materialId}
                        </foreach>
                    </if>
            ) t2 ON t2.mixing_plant_order_detail_id = t1.id
                    LEFT JOIN ( SELECT * FROM d_purchase_order_detail WHERE is_deleted = 0 ) t3 ON t1.purchase_order_detail_id = t3.id
                    LEFT JOIN ( SELECT * FROM d_purchase_order WHERE is_deleted = 0 ) t4 ON t3.order_id = t4.id
        WHERE
            t2.id IS NOT NULL
    </select>

    <select id="queryCutoffSomedayPlanCount" resultType="java.math.BigDecimal">
        SELECT
            if(sum(planCount) is null, 0, sum(planCount))
        FROM
            (
                SELECT
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t4.receiver_project, o.receiver_project ) projectId,
                    date_format( IF ( t1.purchase_order_detail_id IS NOT NULL, t4.receive_time, o.receive_time ), '%Y-%m-%d' ) receiveTime,
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t2.count, t1.count ) planCount,
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t2.unit, t1.unit ) unit,
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t3.material_id, t1.material_id ) materialId
        FROM
                        (
                            <foreach collection="projectIds" item="id" separator=" union all ">
                                select #{id} project_id
                            </foreach>
                        ) t
                            LEFT JOIN ( SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0 ) o ON t.project_id = o.plant_id
                            LEFT JOIN ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 and status != 7 ) t1 ON t1.mixing_plant_order_id = o.id
                            LEFT JOIN ( SELECT * FROM d_purchase_order_detail WHERE is_deleted = 0 ) t2 ON t1.purchase_order_detail_id = t2.id
                            LEFT JOIN ( SELECT * FROM d_purchase_contract_detail WHERE is_deleted = 0 ) t3 ON t2.contract_detail_id = t3.id
                            LEFT JOIN ( SELECT * FROM d_purchase_order WHERE is_deleted = 0 ) t4 ON t2.order_id = t4.id
            ) t5
        WHERE
              receiveTime &lt; #{time}
          AND unit = #{unit}
          AND materialId IN
            <foreach collection="materialIds" item="materialId" open="(" close=")" separator=",">
                #{materialId}
            </foreach>
    </select>

    <select id="queryCutoffSomedayExitCount" resultType="java.math.BigDecimal">
        SELECT
            if(sum(t2.actual_count) is null, 0, sum(t2.actual_count))
        FROM
                    (
                        <foreach collection="projectIds" item="id" separator=" union all ">
                            select #{id} project_id
                        </foreach>
                    ) t
                    LEFT JOIN ( SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0 ) o ON t.project_id = o.plant_id
                    LEFT JOIN ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 and status != 7 ) t1 ON t1.mixing_plant_order_id = o.id
                    LEFT JOIN (
                SELECT
                    *
                FROM
                    d_mixing_plant_exit_ticket
                WHERE
                    is_deleted = 0
                  AND date_format(create_time, '%Y-%m-%d') &lt; #{time}
                  AND unit = #{unit}
                  AND material_id IN
                    <foreach collection="materialIds" item="materialId" open="(" close=")" separator=",">
                        #{materialId}
                    </foreach>
            ) t2 ON t2.mixing_plant_order_detail_id = t1.id
        WHERE
            t2.id IS NOT NULL
    </select>

    <select id="queryCutoffSomedayPlanCountGroupByPlant" resultType="cn.pinming.microservice.material.management.biz.dto.StatisticsPlantDTO">
        SELECT
            projectId,
            if(sum(planCount) is null, 0, sum(planCount)) planCount
        FROM
            (
                SELECT
                    t.project_id projectId,
                    date_format( IF ( t1.purchase_order_detail_id IS NOT NULL, t4.receive_time, o.receive_time ), '%Y-%m-%d' ) receiveTime,
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t2.count, t1.count ) planCount,
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t2.unit, t1.unit ) unit,
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t3.material_id, t1.material_id ) materialId
        FROM
                        (
                            <foreach collection="projectIds" item="id" separator=" union all ">
                                select #{id} project_id
                            </foreach>
                        ) t
                            LEFT JOIN ( SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0 ) o ON t.project_id = o.plant_id
                            LEFT JOIN ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 and status != 7 ) t1 ON t1.mixing_plant_order_id = o.id
                            LEFT JOIN ( SELECT * FROM d_purchase_order_detail WHERE is_deleted = 0 ) t2 ON t1.purchase_order_detail_id = t2.id
                            LEFT JOIN ( SELECT * FROM d_purchase_contract_detail WHERE is_deleted = 0 ) t3 ON t2.contract_detail_id = t3.id
                            LEFT JOIN ( SELECT * FROM d_purchase_order WHERE is_deleted = 0 ) t4 ON t2.order_id = t4.id
            ) t5
        WHERE
              receiveTime &lt; #{time}
          AND unit = #{unit}
          AND materialId IN
            <foreach collection="materialIds" item="materialId" open="(" close=")" separator=",">
                #{materialId}
            </foreach>
        group by projectId
    </select>

    <select id="queryCutoffSomedayExitCountGroupByPlant" resultType="cn.pinming.microservice.material.management.biz.dto.StatisticsPlantDTO">
        SELECT
            t.project_id projectId,
            if(sum(t2.actual_count) is null, 0, sum(t2.actual_count)) exitCount
        FROM
                    (
                        <foreach collection="projectIds" item="id" separator=" union all ">
                            select #{id} project_id
                        </foreach>
                    ) t
                    LEFT JOIN ( SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0 ) o ON t.project_id = o.plant_id
                    LEFT JOIN ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 and status != 7 ) t1 ON t1.mixing_plant_order_id = o.id
                    LEFT JOIN (
                SELECT
                    *
                FROM
                    d_mixing_plant_exit_ticket
                WHERE
                    is_deleted = 0
                  AND date_format(create_time, '%Y-%m-%d') &lt; #{time}
                  AND unit = #{unit}
                  AND material_id IN
                    <foreach collection="materialIds" item="materialId" open="(" close=")" separator=",">
                        #{materialId}
                    </foreach>
            ) t2 ON t2.mixing_plant_order_detail_id = t1.id
        WHERE
            t2.id IS NOT NULL
        group by t.project_id
    </select>

    <select id="queryTimeRangePlanCount" resultType="cn.pinming.microservice.material.management.biz.dto.StatisticsPlantDTO">
        SELECT
            receiveTime,
            if(sum(planCount) is null, 0, sum(planCount)) planCount,
            if(sum(productionCount) is null, 0, sum(productionCount)) productionCount
        FROM
            (
                SELECT
                    IF
                        ( l.id IS NOT NULL, IF ( t1.purchase_order_detail_id IS NOT NULL, t2.count, t1.count ), 0 ) productionCount,
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t4.receiver_project, o.receiver_project ) projectId,
                    date_format( IF ( t1.purchase_order_detail_id IS NOT NULL, t4.receive_time, o.receive_time ), '%Y-%m-%d' ) receiveTime,
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t2.count, t1.count ) planCount,
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t2.unit, t1.unit ) unit,
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t3.material_id, t1.material_id ) materialId
        FROM
                        (
                            <foreach collection="projectIds" item="id" separator=" union all ">
                                select #{id} project_id
                            </foreach>
                        ) t
                            LEFT JOIN ( SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0 ) o ON t.project_id = o.plant_id
                            LEFT JOIN ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 and status != 7 ) t1 ON t1.mixing_plant_order_id = o.id
                            LEFT JOIN ( SELECT * FROM d_purchase_order_detail WHERE is_deleted = 0 ) t2 ON t1.purchase_order_detail_id = t2.id
                            LEFT JOIN ( SELECT * FROM d_purchase_contract_detail WHERE is_deleted = 0 ) t3 ON t2.contract_detail_id = t3.id
                            LEFT JOIN ( SELECT * FROM d_purchase_order WHERE is_deleted = 0 ) t4 ON t2.order_id = t4.id
                            LEFT JOIN ( SELECT * FROM d_ingredient_list WHERE is_deleted = 0 and status in(2, 3, 4) ) l ON t1.id = l.mixing_plant_order_detail_id
                        where t1.id is not null
            ) t5
        WHERE
              receiveTime between #{start} and #{end}
          AND unit = #{unit}
          AND materialId IN
            <foreach collection="materialIds" item="materialId" open="(" close=")" separator=",">
                #{materialId}
            </foreach>
        group by receiveTime
    </select>

    <select id="queryTimeRangeExitCount" resultType="cn.pinming.microservice.material.management.biz.dto.StatisticsPlantDTO">
        SELECT
            date_format( t2.create_time, '%Y-%m-%d' ) exitTime,
            if(sum(t2.actual_count) is null, 0, sum(t2.actual_count)) exitCount
        FROM
                    (
                        <foreach collection="projectIds" item="id" separator=" union all ">
                            select #{id} project_id
                        </foreach>
                    ) t
                    LEFT JOIN ( SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0 ) o ON t.project_id = o.plant_id
                    LEFT JOIN ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 and status != 7 ) t1 ON t1.mixing_plant_order_id = o.id
                    LEFT JOIN (
                SELECT
                    *
                FROM
                    d_mixing_plant_exit_ticket
                WHERE
                    is_deleted = 0
                  AND date_format(create_time, '%Y-%m-%d') between #{start} and #{end}
                  AND unit = #{unit}
                  AND material_id IN
                    <foreach collection="materialIds" item="materialId" open="(" close=")" separator=",">
                        #{materialId}
                    </foreach>
            ) t2 ON t2.mixing_plant_order_detail_id = t1.id
        WHERE
            t2.id IS NOT NULL
        group by date_format( t2.create_time, '%Y-%m-%d' )
    </select>

    <select id="queryOverviewExitCount" resultType="cn.pinming.microservice.material.management.biz.dto.StatisticsOverviewPlantExitDTO">
        SELECT
            t2.material_id materialId, t2.unit,
            sum(case when date_format(t2.create_time, '%Y-%m-%d') = date_format(date_sub(now(), interval 1 day), '%Y-%m-%d') then t2.actual_count else 0 end) yesterdayCount,
            sum(case when date_format(t2.create_time, '%Y-%m') = date_format(now(), '%Y-%m') then t2.actual_count else 0 end) currentMonthCount,
            sum(case when date_format(t2.create_time, '%Y') = date_format(now(), '%Y') then t2.actual_count else 0 end) currentYearCount
        FROM
                    (
                        <foreach collection="projectIds" item="id" separator=" union all ">
                            select #{id} project_id
                        </foreach>
                    ) t
                    LEFT JOIN ( SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0 ) o ON t.project_id = o.plant_id
                    LEFT JOIN ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 and status != 7 ) t1 ON t1.mixing_plant_order_id = o.id
                    LEFT JOIN (
                SELECT
                    id, create_time, material_id, unit, if(actual_count is null, 0, actual_count) actual_count, mixing_plant_order_detail_id
                FROM
                    d_mixing_plant_exit_ticket
                WHERE
                    is_deleted = 0
                  AND date_format(create_time, '%Y-%m-%d') between
                        -- 本年包含本月，但不一定包含昨日。兼容1月1号
                        if(date_format(now(), '%Y-%m-%d') = concat(year(now()), '-01-01'), date_format(date_sub(now(), interval 1 day), '%Y-%m-%d'), concat(year(now()), '-01-01')) and
                        date_format(now(), '%Y-%m-%d')
            ) t2 ON t2.mixing_plant_order_detail_id = t1.id
        WHERE
            t2.id IS NOT NULL
        group by t2.material_id, t2.unit
    </select>

    <select id="querySomedayPlanCount" resultType="cn.pinming.microservice.material.management.biz.dto.StatisticsPlantDTO">
        SELECT
            projectId,
            if(sum(planCount) is null, 0, sum(planCount)) planCount,
            if(sum(productionCount) is null, 0, sum(productionCount)) productionCount
        FROM
            (
                SELECT
                    t.project_id projectId,
                    IF
                        ( l.id IS NOT NULL, IF ( t1.purchase_order_detail_id IS NOT NULL, t2.count, t1.count ), 0 ) productionCount,
                    date_format( IF ( t1.purchase_order_detail_id IS NOT NULL, t4.receive_time, o.receive_time ), '%Y-%m-%d' ) receiveTime,
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t2.count, t1.count ) planCount,
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t2.unit, t1.unit ) unit,
                    IF
                        ( t1.purchase_order_detail_id IS NOT NULL, t3.material_id, t1.material_id ) materialId
        FROM
                        (
                            <foreach collection="projectIds" item="id" separator=" union all ">
                                select #{id} project_id
                            </foreach>
                        ) t
                            LEFT JOIN ( SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0 ) o ON t.project_id = o.plant_id
                            LEFT JOIN ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 and status != 7 ) t1 ON t1.mixing_plant_order_id = o.id
                            LEFT JOIN ( SELECT * FROM d_purchase_order_detail WHERE is_deleted = 0 ) t2 ON t1.purchase_order_detail_id = t2.id
                            LEFT JOIN ( SELECT * FROM d_purchase_contract_detail WHERE is_deleted = 0 ) t3 ON t2.contract_detail_id = t3.id
                            LEFT JOIN ( SELECT * FROM d_purchase_order WHERE is_deleted = 0 ) t4 ON t2.order_id = t4.id
                            LEFT JOIN ( SELECT * FROM d_ingredient_list WHERE is_deleted = 0 and status in(2, 3, 4) ) l ON t1.id = l.mixing_plant_order_detail_id
                        where t1.id is not null
            ) t5
        WHERE
              receiveTime = #{time}
          AND unit = #{unit}
          AND materialId IN
            <foreach collection="materialIds" item="materialId" open="(" close=")" separator=",">
                #{materialId}
            </foreach>
        group by projectId
    </select>

    <select id="querySomedayExitCount" resultType="cn.pinming.microservice.material.management.biz.dto.StatisticsPlantDTO">
        SELECT
            t.project_id projectId,
            if(sum(t2.actual_count) is null, 0, sum(t2.actual_count)) exitCount
        FROM
                    (
                        <foreach collection="projectIds" item="id" separator=" union all ">
                            select #{id} project_id
                        </foreach>
                    ) t
                    LEFT JOIN ( SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0 ) o ON t.project_id = o.plant_id
                    LEFT JOIN ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 and status != 7 ) t1 ON t1.mixing_plant_order_id = o.id
                    LEFT JOIN (
                SELECT
                    *
                FROM
                    d_mixing_plant_exit_ticket
                WHERE
                    is_deleted = 0
                  AND date_format(create_time, '%Y-%m-%d') = #{time}
                  AND unit = #{unit}
                  AND material_id IN
                    <foreach collection="materialIds" item="materialId" open="(" close=")" separator=",">
                        #{materialId}
                    </foreach>
            ) t2 ON t2.mixing_plant_order_detail_id = t1.id
        WHERE
            t2.id IS NOT NULL
        group by t.project_id
    </select>

    <select id="queryMachineCapacity"
            resultType="cn.pinming.microservice.material.management.biz.dto.MachineCapacityDTO">
        select plant_machine_id as machineId, date_format(create_time, '%m/%d') as date, sum(actual_count) as num
        from d_mixing_plant_exit_ticket
        where is_deleted = 0 and unit = #{query.unit}
        AND material_id IN
        <foreach collection="materialIds" item="materialId" open="(" close=")" separator=",">
            #{materialId}
        </foreach>
        and date_format(create_time, '%Y%m%d') &gt;= date_format(#{query.beginTime}, '%Y%m%d')
        and date_format(create_time, '%Y%m%d') &lt;= date_format(#{query.endTime}, '%Y%m%d')
        and project_id in
        <foreach collection="projectIds" item="projectId" separator="," open="(" close=")">
            #{projectId}
        </foreach>
        group by plant_machine_id, date_format(create_time, '%Y%m%d')
        order by date_format(create_time, '%Y%m%d');
    </select>

    <select id="queryMachineExitDetail"
            resultType="cn.pinming.microservice.material.management.biz.vo.MachineExitDetailVO">
        select material_id,
               sum(if(DATEDIFF(create_time, now()) = 0, actual_count, 0))                              todayNum,
               sum(if(DATEDIFF(create_time, now()) = -1, actual_count, 0))                             yesterdayNum,
               sum(if(date_format(create_time, '%Y%m') = date_format(now(), '%Y%m'), actual_count, 0)) monthlyNum,
               sum(if(date_format(create_time, '%Y') = date_format(now(), '%Y'), actual_count, 0))     yearNum,
               sum(actual_count)                                                                       total
        from d_mixing_plant_exit_ticket
        where unit = #{query.unit} and is_deleted = 0
        AND material_id IN
        <foreach collection="materialIds" item="materialId" open="(" close=")" separator=",">
            #{materialId}
        </foreach>
        and plant_machine_id in
        <foreach collection="query.machineIds" item="machineId" separator="," open="(" close=")">
            #{machineId}
        </foreach>
        and project_id in
        <foreach collection="projectIds" item="projectId" separator="," open="(" close=")">
            #{projectId}
        </foreach>
        group by material_id
        order by material_id;
    </select>

    <select id="queryRawMaterialDemand" resultType="cn.pinming.microservice.material.management.biz.vo.RawMaterialDemandStockVO">
        SELECT a.materialId, if(sum((a.planCount - a.actualCount) * a.unitUsage) &lt; 0, 0, sum((a.planCount - a.actualCount) * a.unitUsage)) demand,
                sum(a.actualCount * a.unitUsage) consumption
        FROM
        (
        SELECT
            IF
            (
            t.purchase_order_detail_id IS NOT NULL,
            IF
            ( t1.count IS NULL, 0, t1.count ),
            IF
            ( t.count IS NULL, 0, t.count )
            ) planCount,
            t4.material_id materialId,
            IF
            ( t4.unit_usage IS NULL, 0, t4.unit_usage ) unitUsage,
            IF
            ( t5.actual_count IS NULL, 0, t5.actual_count ) actualCount
        FROM
        (
        SELECT
            *
        FROM
            d_mixing_plant_order_detail
        WHERE
            is_deleted = 0
            AND project_id IN
            <foreach collection="projectIds" item="projectId" separator="," open="(" close=")">
                #{projectId}
            </foreach>) t
        LEFT JOIN ( SELECT * FROM d_purchase_order_detail WHERE is_deleted = 0 ) t1 ON t.purchase_order_detail_id = t1.id
        LEFT JOIN ( SELECT * FROM d_ingredient_list WHERE is_deleted = 0 ) t2 ON t.id = t2.mixing_plant_order_detail_id
        LEFT JOIN ( SELECT * FROM d_ingredient_notice WHERE is_deleted = 0 and no is not null ) t3 ON t2.id = t3.ingredient_list_id
        LEFT JOIN ( SELECT * FROM d_ingredient_notice_detail WHERE is_deleted = 0 AND material_id IS NOT NULL ) t4 ON t3.id = t4.ingredient_notice_id
        LEFT JOIN ( SELECT sum(actual_count) actual_count, mixing_plant_order_detail_id FROM d_mixing_plant_exit_ticket WHERE is_deleted = 0 group by mixing_plant_order_detail_id ) t5 ON t.id = t5.mixing_plant_order_detail_id
        LEFT JOIN ( SELECT * FROM d_purchase_order WHERE is_deleted = 0 ) t6 ON t1.order_id = t6.id
        LEFT JOIN ( SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0 ) t7 ON t.mixing_plant_order_id = t7.id
        WHERE
            t4.id IS NOT NULL
            AND date_format( IF ( t.purchase_order_detail_id IS NOT NULL, t6.receive_time, t7.receive_time ), '%Y-%m-%d' ) &lt;= #{deadline}
        ) a
        GROUP BY
            a.materialId
    </select>

    <select id="queryRawMaterialDailyConsumption" resultType="cn.pinming.microservice.material.management.biz.dto.PlantRawMaterialDailyConsumptionDTO">
        SELECT
            material_id materialId,
            create_time time,
            if(sum( actual_count * unit_usage ) is null, 0, sum( actual_count * unit_usage )) dailyConsumption
        FROM
            (
            SELECT
            t1.actual_count,
            t4.material_id,
            IF
            ( t4.unit_usage IS NULL, 0, t4.unit_usage ) unit_usage,
            date_format( t1.create_time, '%Y-%m-%d' ) create_time
            FROM
            (
            SELECT
            *
            FROM
            d_ingredient_list
            WHERE
            is_deleted = 0
            AND project_id IN
            <foreach collection="projectIds" item="projectId" separator="," open="(" close=")">
                #{projectId}
            </foreach>) t2
            LEFT JOIN ( SELECT * FROM d_ingredient_notice WHERE is_deleted = 0 and no is not null ) t3 ON t2.id = t3.ingredient_list_id
            LEFT JOIN ( SELECT * FROM d_ingredient_notice_detail WHERE is_deleted = 0 AND material_id IS NOT NULL ) t4 ON t3.id = t4.ingredient_notice_id
            LEFT JOIN ( SELECT * FROM d_mixing_plant_exit_ticket WHERE is_deleted = 0 ) t1 ON t1.mixing_plant_order_detail_id = t2.mixing_plant_order_detail_id
            WHERE
            date_format( t1.create_time, '%Y-%m-%d' ) BETWEEN #{start}
            AND #{end}
            AND t4.material_id IS NOT NULL
            ) a
        GROUP BY
            material_id,
            create_time
    </select>

    <select id="queryRawMaterialSendReceive" resultType="cn.pinming.microservice.material.management.biz.vo.RawMaterialSendReceiveVO">
        SELECT
            materialId,
            sum( CASE WHEN time IS NOT NULL AND time = date_format( date_sub( now(), INTERVAL 1 DAY ), '%Y-%m-%d' ) THEN dailyConsumption ELSE 0 END ) yesterdayConsumption,
            sum( CASE WHEN time IS NOT NULL AND date_format( time, '%Y-%m' ) = date_format( now(), '%Y-%m' ) THEN dailyConsumption ELSE 0 END ) currentMonthConsumption,
            sum( CASE WHEN time IS NOT NULL AND date_format( time, '%Y' ) = date_format( now(), '%Y' ) THEN dailyConsumption ELSE 0 END ) currentYearConsumption,
            round(
                avg(
                    CASE
                    WHEN time IS NOT NULL
                    AND time BETWEEN date_format( date_sub( now(), INTERVAL 7 DAY ), '%Y-%m-%d' )
                    AND date_format( now(), '%Y-%m-%d' ) THEN
                    dailyConsumption ELSE NULL
                    END
                ), 2
            ) lastSevenDayConsumptionAvg,
            sum( dailyConsumption ) allConsumption
        FROM
            (
                SELECT
                    material_id materialId,
                    create_time time,
			IF
				(
					sum( actual_count * unit_usage ) IS NULL,
					0,
				sum( actual_count * unit_usage )) dailyConsumption
                FROM
                    (
                    SELECT
                    t1.actual_count,
                    t4.material_id,
                    IF
                    ( t4.unit_usage IS NULL, 0, t4.unit_usage ) unit_usage,
                    date_format( t1.create_time, '%Y-%m-%d' ) create_time
                    FROM
                    (
                    SELECT
                    *
                    FROM
                    d_ingredient_list
                    WHERE
                    is_deleted = 0
                    AND project_id IN
                    <foreach collection="projectIds" item="projectId" separator="," open="(" close=")">
                        #{projectId}
                    </foreach>) t2
                    LEFT JOIN ( SELECT * FROM d_ingredient_notice WHERE is_deleted = 0 and no is not null ) t3 ON t2.id = t3.ingredient_list_id
                    LEFT JOIN ( SELECT * FROM d_ingredient_notice_detail WHERE is_deleted = 0 AND material_id IS NOT NULL ) t4 ON t3.id = t4.ingredient_notice_id
                    LEFT JOIN ( SELECT * FROM d_mixing_plant_exit_ticket WHERE is_deleted = 0 ) t1 ON t1.mixing_plant_order_detail_id = t2.mixing_plant_order_detail_id
                    LEFT JOIN ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 ) t ON t1.mixing_plant_order_detail_id = t.id
                    WHERE
                    t4.material_id IS NOT NULL
                    ) a
                GROUP BY
                    material_id,
                    create_time
            ) a
        GROUP BY
            a.materialId
    </select>

    <select id="queryRawMaterialPaperInventory" resultType="cn.pinming.microservice.material.management.biz.dto.RawMaterialPaperInventoryDTO">
        SELECT
            t4.material_id materialId,
            sum(
                        IF
                            ( t1.actual_count IS NULL, 0, t1.actual_count ) *
                        IF
                            ( t4.unit_usage IS NULL, 0, t4.unit_usage )) allConsumption
        FROM
            (
                SELECT
                    *
                FROM
                    d_mixing_plant_exit_ticket
                WHERE
                    is_deleted = 0
                  AND project_id IN
                    <foreach collection="projectIds" item="projectId" separator="," open="(" close=")">
                        #{projectId}
                    </foreach>
                  AND create_time BETWEEN #{start} AND #{end}
            ) t1
                LEFT JOIN ( SELECT * FROM d_ingredient_list WHERE is_deleted = 0 ) t2 ON t1.mixing_plant_order_detail_id = t2.mixing_plant_order_detail_id
                LEFT JOIN ( SELECT * FROM d_ingredient_notice WHERE is_deleted = 0 and no is not null ) t3 ON t2.id = t3.ingredient_list_id
                LEFT JOIN ( SELECT * FROM d_ingredient_notice_detail WHERE is_deleted = 0 AND material_id IS NOT NULL ) t4 ON t3.id = t4.ingredient_notice_id
        WHERE t4.material_id IN
            <foreach collection="materialIds" item="materialId" separator="," open="(" close=")">
                #{materialId}
            </foreach>
        GROUP BY
            t4.material_id
    </select>

    <select id="queryAllIngredientRawMaterialPaperInventory" resultType="cn.pinming.microservice.material.management.biz.dto.RawMaterialPaperInventoryDTO">
        SELECT
            t.material_id materialId,
            sum(
                        IF
                            ( t1.actual_count IS NULL, 0, t1.actual_count ) *
                        IF
                            ( t4.unit_usage IS NULL, 0, t4.unit_usage )) allConsumption
        FROM
            (
                <foreach collection="materialTimes" item="item" separator=" union all ">
                    select #{item.materialId} material_id, #{item.start} startTime, #{item.end} endTime
                </foreach>
            ) t
                LEFT JOIN ( SELECT * FROM d_mixing_plant_exit_ticket WHERE is_deleted = 0 AND project_id IN
                            <foreach collection="projectIds" item="projectId" separator="," open="(" close=")">
                                #{projectId}
                            </foreach>
                          ) t1 ON t1.create_time BETWEEN t.startTime AND t.endTime
                LEFT JOIN ( SELECT * FROM d_ingredient_list WHERE is_deleted = 0 ) t2 ON t1.mixing_plant_order_detail_id = t2.mixing_plant_order_detail_id
                LEFT JOIN ( SELECT * FROM d_ingredient_notice WHERE is_deleted = 0 AND NO IS NOT NULL ) t3 ON t2.id = t3.ingredient_list_id
                LEFT JOIN ( SELECT * FROM d_ingredient_notice_detail WHERE is_deleted = 0 AND material_id IS NOT NULL ) t4 ON t3.id = t4.ingredient_notice_id AND t.material_id = t4.material_id
        GROUP BY
            t.material_id
    </select>

    <select id="queryRawMaterialPaperInventoryPeriods" resultType="cn.pinming.microservice.material.management.biz.dto.PaperInventoryPeriodsDTO">
        SELECT
            t4.material_id materialId,
            t1.create_time ingredientTime,
            IF( t1.actual_count IS NULL, 0, t1.actual_count ) * IF( t4.unit_usage IS NULL, 0, t4.unit_usage ) allConsumption
        FROM
            (
                SELECT
                    *
                FROM
                    d_mixing_plant_exit_ticket
                WHERE
                    is_deleted = 0
                    AND project_id IN
                    <foreach collection="projectIds" item="projectId" separator="," open="(" close=")">
                        #{projectId}
                    </foreach>
                    AND create_time BETWEEN #{start} AND #{end}
            ) t1
        LEFT JOIN ( SELECT * FROM d_ingredient_list WHERE is_deleted = 0 ) t2 ON t1.mixing_plant_order_detail_id = t2.mixing_plant_order_detail_id
        LEFT JOIN ( SELECT * FROM d_ingredient_notice WHERE is_deleted = 0 and no is not null ) t3 ON t2.id = t3.ingredient_list_id
        LEFT JOIN ( SELECT * FROM d_ingredient_notice_detail WHERE is_deleted = 0 AND material_id IS NOT NULL ) t4 ON t3.id = t4.ingredient_notice_id
        WHERE t4.material_id IN
            <foreach collection="materialIds" item="materialId" separator="," open="(" close=")">
                #{materialId}
            </foreach>
    </select>
    <select id="queryMachineList" resultType="string">
        SELECT
        plant_machine_id
        FROM
        d_mixing_plant_exit_ticket
        WHERE
        is_deleted = 0
        and unit = #{unit}
        AND project_id IN
        <foreach collection="projectIds" item="projectId" separator="," open="(" close=")">
            #{projectId}
        </foreach>
        and material_id IN
        <foreach collection="materialIds" item="materialId" separator="," open="(" close=")">
            #{materialId}
        </foreach>
    </select>

</mapper>
