<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MaterialWarningConfigMapper">

    <select id="getWarningTypeConfig" resultType="cn.pinming.microservice.material.management.biz.entity.MaterialWarningConfig">
        select *
        from d_material_warning_config
        where is_deleted = 0
        <if test="projectId == null">
            and company_id = #{companyId} and project_id is null
        </if>
        <if test="projectId != null">
            and project_id = #{projectId}
        </if>
    </select>

</mapper>
