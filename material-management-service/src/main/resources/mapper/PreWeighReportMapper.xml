<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.PreWeighReportMapper">
    <select id="selectPreReport" resultType="cn.pinming.microservice.material.management.biz.dto.PreReportDTO">
        select * from (select a.id,a.no,a.status,a.purchase_order_id,a.create_id,a.update_time,
        b.receiver_project as projectId,b.supplier_id,b.position,
        e.category_name,e.material_name,e.material_spec,
        min(f.arrive_time) as arriveTime,
        max(f.arrive_time) as lastTime,
        count(f.pre_weigh_report_id) as planCount,
        count(if(f.material_data_id is not null, 1, null)) as actualCount,
        a.create_time
        from d_pre_weigh_report a
        left join d_purchase_order b on a.purchase_order_id = b.id
        left join d_pre_material_report c on a.id = c.pre_weigh_report_id and c.is_deleted = 0
        left join d_purchase_order_detail d on c.purchase_order_detail_id = d.id
        left join d_purchase_contract_detail e on e.id = d.contract_detail_id and e.is_deleted = 0
        left join d_pre_truck_report_detail f on a.id = f.pre_weigh_report_id
        where a.company_id = #{param2.currentCompanyId}
        <if test="query.status != null and query.status != 0">
            and a.status = #{param1.status}
        </if>
        <if test="query.categoryId != null and query.categoryId != ''">
            and e.category_id = #{param1.categoryId}
        </if>
        <if test="query.weighType != null and query.weighType != 0">
            and a.weigh_type = #{param1.weighType}
        </if>
        <if test="query.no != null and query.no != ''">
            and a.no like concat('%', #{param1.no},'%')
        </if>
        <if test="query.projectId != null and query.projectId != 0">
            and a.project_id = #{param1.projectId}
        </if>
        <if test="user.currentProjectId != null and user.currentProjectId != 0">
            and a.project_id = #{param2.currentProjectId}
        </if>
        <if test="query.startTime != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{param1.startTime}
        </if>
        <if test="query.endTime != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{param1.endTime}
        </if>
        and a.is_deleted = 0 and a.create_id = #{param2.id} and a.status = 1
        group by a.id, a.status, a.create_time
        union
        select a.id,a.no,a.status,a.purchase_order_id,a.create_id,a.update_time,
        b.receiver_project,b.supplier_id,b.position,
        e.category_name,e.material_name, e.material_spec,
        min(f.arrive_time) as arriveTime,
        max(f.arrive_time) as lastTime,
        count(f.pre_weigh_report_id) as planCount,
        count(if(f.material_data_id is not null, 1, null)) as actualCount,
        a.create_time
        from d_pre_weigh_report a
        left join d_purchase_order b on a.purchase_order_id = b.id
        left join d_pre_material_report c on a.id = c.pre_weigh_report_id and c.is_deleted = 0
        left join d_purchase_contract_detail e on e.id = c.contract_detail_id and e.is_deleted = 0
        left join d_pre_truck_report_detail f on a.id = f.pre_weigh_report_id
        where a.company_id = #{param2.currentCompanyId}
        <if test="query.status != null and query.status != 0">
            and a.status = #{param1.status}
        </if>
        <if test="query.categoryId != null and query.categoryId != ''">
            and e.category_id = #{param1.categoryId}
        </if>
        <if test="query.weighType != null and query.weighType != 0">
            and a.weigh_type = #{param1.weighType}
        </if>
        <if test="query.no != null and query.no != ''">
            and a.no like concat('%', #{param1.no},'%')
        </if>
        <if test="query.projectId != null and query.projectId != 0">
            and a.project_id = #{param1.projectId}
        </if>
        <if test="user.currentProjectId != null and user.currentProjectId != 0">
            and a.project_id = #{param2.currentProjectId}
        </if>
        <if test="query.startTime != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{param1.startTime}
        </if>
        <if test="query.endTime != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{param1.endTime}
        </if>
        and a.is_deleted = 0 and a.status = 2
        group by a.id, a.status, a.create_time) x
        order by FIELD(x.status, 1, 2), x.create_time desc
    </select>

    <select id="selectDetail" resultType="cn.pinming.microservice.material.management.biz.vo.PreReportDetailVO">
        select a.purchase_order_id,
               a.status,
               a.no,
               a.create_id,
               a.create_time,
               b.purchase_order_detail_id,
               b.brand as sendBrand,
               b.count,
               b.conversion_rate
        from d_pre_weigh_report a
                 left join d_pre_material_report b on a.id = b.pre_weigh_report_id and b.is_deleted = 0
        where a.id = #{id}
          and a.is_deleted = 0
    </select>

    <select id="selectPreReportByPurchaseId" resultType="cn.pinming.microservice.material.management.biz.vo.PreReportVO">
        select *
		from
			(
				select a.no,a.id,a.status,a.create_time,a.create_id
				from d_pre_weigh_report a
				where a.purchase_order_id = #{purchaseId}
				and a.type = 1
				and a.weigh_type = 1
				and a.is_deleted = 0
				and a.status = 1 and a.create_id = #{memberId}
				union
				select a.no,a.id,a.status,a.create_time,a.create_id
				from d_pre_weigh_report a
				where a.purchase_order_id = #{purchaseId}
				and a.type = 1
				and a.weigh_type = 1
				and a.is_deleted = 0
				and a.status = 2
			)x
		order by field(x.status,1,2),x.create_time

    </select>

    <select id="selectVehicleList"
            resultType="cn.pinming.microservice.material.management.biz.dto.VehicleInfoDTO">
        select *
        from (select a.exit_ticket_id as reserve5,
                     g.arrive_time,
                     g.id,
                     f.truck_no,
                     d.material_id,
                     d.material_name,
                     d.material_spec,
                     e.order_no         as reserve1,
                     f.capacity         as reserve2,
                     d.unit             as reserve3,
                     b.conversion_rate  as reserve4,
                     e.supplier_id,
                     e.receiver_project as projectId
              from d_pre_weigh_report a
                       left join d_pre_material_report b on a.id = b.pre_weigh_report_id and b.is_deleted = 0
                       left join d_purchase_order e on a.purchase_order_id = e.id
                        left join d_purchase_order_detail c on b.purchase_order_detail_id = c.id
                       left join d_purchase_contract_detail d on c.contract_detail_id = d.id
                       left join d_pre_truck_report f on a.id = f.pre_weigh_report_id and f.is_deleted = 0
                       left join d_pre_truck_report_detail g on f.id = g.pre_truck_report_id
              where a.company_id = #{companyId}
                and a.project_id = #{projectId}
                and a.status = 2
                and a.type = 1
                and g.status = 1
                and g.material_data_id is null
                and a.is_deleted = 0
              order by a.create_time, g.arrive_time,g.create_time) x
        group by x.truck_no
    </select>

    <select id="selectExitTicketId" resultType="java.lang.String">
        select id
        from d_pre_weigh_report
        where exit_ticket_id = #{exitTicketId}
        and is_deleted = 0
    </select>
</mapper>
