<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MixingPlantExitTicketMapper">

    <select id="selectPages" resultType="cn.pinming.microservice.material.management.biz.dto.ExitTicketPageDTO">
        select a.id,a.ticket_no,a.truck_no,a.actual_count,a.unit,a.create_time as leaveTime,a.project_id,a.mixing_plant_order_detail_id
        ,b.order_no
        ,( case when c.purchase_order_detail_id is not null then e.material_id else c.material_id end ) as materialId
        ,( case when c.purchase_order_detail_id is not null then d.parameter_requirements else c.parameter_requirements end ) as parameterRequirements
        ,( case when c.purchase_order_detail_id is not null then d.project_id else b.receiver_project end ) as receiverProject
        ,f.machine_name
        from d_mixing_plant_exit_ticket a
        left join d_mixing_plant_order b on b.id = a.mixing_plant_order_id and b.is_deleted = 0
        left join d_mixing_plant_order_detail c on c.id = a.mixing_plant_order_detail_id and c.is_deleted = 0
        left join d_purchase_order_detail d on d.id = c.purchase_order_detail_id and d.is_deleted = 0
        left join d_purchase_contract_detail e on e.id = d.contract_detail_id and e.is_deleted = 0
        left join s_mixing_plant_machine f on a.plant_machine_id = f.id and f.is_deleted = 0
        where a.company_id = #{companyId}
        and a.project_id = #{projectId}
        and a.is_deleted = 0
        order by a.create_time desc
    </select>

    <select id="selectDetail" resultType="cn.pinming.microservice.material.management.biz.dto.ExistTicketDetailDTO">
        select a.id,a.ticket_no,a.unit,a.project_id, a.actual_count, a.create_time as leaveTime
        ,b.id as mixingPlantOrderId,b.order_no
		,c.id as mixingPlantOrderDetailId
        ,( case when c.purchase_order_detail_id is not null then e.material_id else c.material_id end ) as materialId
        ,( case when c.purchase_order_detail_id is not null then d.parameter_requirements else c.parameter_requirements end ) as parameterRequirements
		,( case when c.purchase_order_detail_id is not null then f.order_no else null end) as purchaseOrderNo
		,( case when c.purchase_order_detail_id is not null then d.create_id else b.create_id end ) as createId
		,( case when c.purchase_order_detail_id is not null then d.create_time else b.create_time end ) as createTime
		,( case when c.purchase_order_detail_id is not null then d.count else c.count end ) as count
		,( case when c.purchase_order_detail_id is not null then d.remark else c.position end ) as position
		,( case when c.purchase_order_detail_id is not null then d.project_id else b.receiver_project end ) as receiverProject
		,( case when c.purchase_order_detail_id is not null then f.receiver else b.receiver end ) as receiver
		,( case when c.purchase_order_detail_id is not null then f.receiver_tel else b.receiver_tel end ) as receiverTel
		,( case when c.purchase_order_detail_id is not null then f.receiver_address else b.receiver_address end ) as receiverAddress
		,( case when c.purchase_order_detail_id is not null then f.receive_time else b.receive_time end ) as receiveTime
		,( case when c.purchase_order_detail_id is not null then f.remark else b.remark end ) as remark
		,g.machine_name
        from d_mixing_plant_exit_ticket a
        left join d_mixing_plant_order b on b.id = a.mixing_plant_order_id and b.is_deleted = 0
        left join d_mixing_plant_order_detail c on c.id = a.mixing_plant_order_detail_id and c.is_deleted = 0
        left join d_purchase_order_detail d on d.id = c.purchase_order_detail_id and d.is_deleted = 0
        left join d_purchase_contract_detail e on e.id = d.contract_detail_id and e.is_deleted = 0
		left join d_purchase_order f on d.order_id = f.id and f.is_deleted = 0
		left join s_mixing_plant_machine g on a.plant_machine_id = g.id and g.is_deleted = 0
        where a.id = #{id}
		and a.is_deleted = 0
    </select>


    <select id="selectSendTotalCountByOrderId" resultType="java.lang.String">
        select sum(actual_count)
        from d_mixing_plant_exit_ticket
        where mixing_plant_order_id = #{orderId} and project_id = #{projectId}
    </select>

    <select id="statistics" resultType="cn.pinming.microservice.material.management.biz.dto.ExistTicketDetailStatisticsDTO">
        select count(id) as truckCount,sum(actual_count) as sendCount
        from d_mixing_plant_exit_ticket
        where mixing_plant_order_detail_id = #{id}
        and is_deleted = 0
    </select>

    <select id="history" resultType="cn.pinming.microservice.material.management.biz.vo.ExistTicketHistoryVO">
        select a.truck_no,a.actual_count,a.create_time as leaveTime,b.template_name as printTemplateName,a.print_template_id
        from d_mixing_plant_exit_ticket a
        left join s_leave_template b on a.print_template_id = b.id
        where a.mixing_plant_order_detail_id = #{id}
        order by a.create_time desc
        limit 1
    </select>

    <select id="selectTruckNoHistory" resultType="java.lang.String">
        select distinct truck_no
        from d_mixing_plant_exit_ticket
        where is_deleted = 0
        and company_id = #{companyId}
        and project_id = #{projectId}
        <if test="truckNo != null and truckNo != ''">
            and truck_no like concat ('%', #{truckNo},'%')
        </if>
        order by create_time desc
    </select>

    <select id="selectIsCanceled"
            resultType="cn.pinming.microservice.material.management.biz.entity.MixingPlantExitTicket">
        select *
        from d_mixing_plant_exit_ticket
        where mixing_plant_order_detail_id = #{id}
        and is_deleted = 0
    </select>

    <select id="statisticsByIdS" resultType="cn.pinming.microservice.material.management.biz.dto.ExistTicketDetailStatisticsDTO">
        select mixing_plant_order_detail_id as id,count(id) as truckCount,sum(actual_count) as sendCount
        from d_mixing_plant_exit_ticket
        where mixing_plant_order_detail_id in
        <foreach collection="idList" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
        and is_deleted = 0
        group by mixing_plant_order_detail_id
    </select>

    <select id="historys" resultType="cn.pinming.microservice.material.management.biz.vo.ExistTicketHistoryVO">
        select *
		from (
				select a.mixing_plant_order_detail_id,a.truck_no,a.actual_count,a.create_time as leaveTime,a.print_template_id
				from d_mixing_plant_exit_ticket a
				where mixing_plant_order_detail_id in
                <foreach collection="list" item="id" open="(" close=")" separator="," index="">
                    #{id}
                </foreach>
				order by a.create_time desc
				limit 999999
		)m
		group by m.mixing_plant_order_detail_id
    </select>

    <select id="getPreTruckNoByTicketNo" resultType="java.lang.String">
        select truck_no
        from d_mixing_plant_exit_ticket
        where mixing_plant_order_id = #{orderId}
        and create_time &lt; (select create_time
        from d_mixing_plant_exit_ticket
        where mixing_plant_order_id = #{orderId}
        and ticket_no = #{ticketNo})
        order by create_time desc
        limit 1


    </select>
</mapper>
