<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MaterialDataMapper">

    <update id="updatePushState">
        <if test="ids != null and ids.size > 0">
            update d_material_data set push_state = #{status}
            where id in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="updateDataByWeighIds">
        update d_material_data a
        left join d_material_send_receive b on a.receive_id =  b.id  and b.is_deleted = 0
        left join d_material_revise c on c.material_data_id =  a.id  and a.is_deleted = 0
        left join d_material_warning d on d.warning_source_id =  a.id  and d.is_deleted = 0
        left join d_material_verify_relation e on e.receive_data_id =  a.id  and e.is_deleted = 0
        set a.is_deleted = 1,b.is_deleted = 1,c.is_deleted = 1,d.is_deleted = 1
        where  e.id  is NULL
        and a.weigh_id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectWeightDetail"
            resultType="cn.pinming.microservice.material.management.biz.dto.MaterialDataDetailDTO">
        select a.project_id  as receiverProject
             , a.weight_unit as unit
             , a.supplier_id
             , a.supplier_name
             , a.receive_id
             , a.weight_gross
             , a.weight_tare
             , a.weight_net
             , a.weight_deduct as weightDeduction
             , a.weight_actual
             , a.moisture_content
             , a.purchase_order_id as purchaseId
             , a.is_device
             , a.enter_time
             , a.enter_pic
             , a.leave_time
             , a.leave_pic
             , b.receive_no
             , b.receiver
             , a.receive_time
             , b.type_detail
             , b.is_addition
             , b.ext_no
             , b.order_no
             , b.truck_no
             , a.document_pic
             , e.no,
            a.receive_mode,
            a.material_validity,
            f.position,
            f.create_id,
            f.remark,
            f.id as orderId
        from d_material_data a
                 left join d_material_send_receive b
                           on a.receive_id = b.id and b.type = 1
                 left join d_pre_truck_report_detail e on a.id = e.material_data_id
                 left join d_purchase_order f on b.purchase_id = f.id
        where a.id = #{id}
          and a.company_id = #{companyId}
          and a.is_deleted = 0

    </select>

    <select id="selectWeighDetailsWithPurchase"
            resultType="cn.pinming.microservice.material.management.biz.vo.MaterialDatasVO">

        select a.ratio            as conversionRate
             , a.weight_send
             , a.actual_count
             , a.weight_unit      as unit
             , a.material_name
             , a.material_id
             , a.category_id
             , a.category_name    as materialCategoryName
             , a.deviation_rate   as deviation
             , a.deviation_status as deviationStatusByte
             , a.id
             , a.actual_receive
             , a.moisture_content
             , d.deviation_floor
             , d.deviation_ceiling
             , e.count
             , e.brand
        from d_material_data a
                 left join d_purchase_contract_detail d on d.is_deleted = 0 and a.contract_detail_id = d.id
                 left join d_purchase_order_detail e on d.id = e.contract_detail_id and e.order_id = a.purchase_order_id and e.is_deleted = 0
        where a.id = #{id}
          and a.is_deleted = 0
    </select>

    <select id="queryWeightReceiveInfo"
            resultType="cn.pinming.microservice.material.management.biz.dto.MaterialWeighInfoDTO">
        SELECT
        a.weight_gross as gWeight,
        a.weight_tare as tWeight,
        a.weight_deduct as bWeight,
        a.weight_net as nWeight,
        a.enter_time as gTime,
        a.moisture_content,
        a.position,
        b.receive_no as sNo,
        a.leave_time as tTime,
        a.weigh_id as tUuid,
        b.truck_no as tNo,
        a.weight_actual,
        a.id,
        a.purchase_order_id purchaseId,
        a.contract_detail_id contractDetailId,
        a.receive_id,
        a.actual_receive,
        a.receive_mode,
        a.material_validity,
        a.weight_send,
        a.actual_count,
        a.weight_unit AS unit,
        a.supplier_id,
        a.supplier_name,
        a.material_id,
        a.ratio,
        a.material_name,
        a.category_id,
        a.category_name,
        a.push_state,
        a.project_id AS receiveProject,
        a.deviation_rate,
        a.deviation_status,
        a.is_weight_integrality,
        a.is_contract_rate_used,
        a.is_contract_unit_used,
        a.material_exist,
        a.is_revise,
        b.receive_no,
        b.receiver,
        b.is_addition,
        a.receive_time,
        b.purchase_id,
        b.ext_no,
        b.type_detail,
        b.truck_no,
        d.order_no,
        e.deviation_ceiling,
        e.deviation_floor,
        e.contract_id contractId,
        IF(c.id is not NULL, 1, 2) as isVerify
        FROM
        d_material_data a
        LEFT JOIN d_material_send_receive b ON a.receive_id = b.id
        LEFT JOIN d_material_verify_relation c ON a.id = c.receive_data_id and c.is_deleted = 0
        left join d_purchase_order d on d.id = a.purchase_order_id and d.is_deleted = 0
        left join d_purchase_contract_detail e on e.id = a.contract_detail_id
        where a.company_id = #{companyId} and a.is_deleted = 0
        and b.is_deleted = 0 and b.type = 1
        and a.weight_gross is not null
        <if test="receiveMode != null and receiveMode == 1">
            and b.material_validity = 1
            and b.receive_mode = 1
        </if>
        <if test="receiveMode != null and receiveMode == 2">
            and b.material_validity = 1
            and b.receive_mode = 2
        </if>
        <if test="receiveMode != null and receiveMode == 3">
            and b.material_validity = 1
            and b.receive_mode = 3
        </if>
        <if test="receiveMode != null and receiveMode == 0">
            and (b.material_validity = 0 or b.material_validity = 2)
        </if>
        <if test="receiveMode != null and receiveMode == 1">
            and a.material_validity = 1
            and a.receive_mode = 1
        </if>
        <if test="receiveMode != null and receiveMode == 2">
            and a.material_validity = 1
            and a.receive_mode = 2
        </if>
        <if test="receiveMode != null and receiveMode == 3">
            and a.material_validity = 1
            and a.receive_mode = 3
        </if>
        <if test="receiveMode != null and receiveMode == 0">
            and (a.material_validity = 0 or a.material_validity = 2)
        </if>
        <if test="receiveNo != null and receiveNo != '' and searchMethod != 1">
            and b.receive_no like concat('%',#{receiveNo},'%')
        </if>
        <if test="receiveNo != null and receiveNo != '' and searchMethod == 1">
            and b.receive_no = #{receiveNo}
        </if>
        <if test="projectId != null and projectId != ''">
            and a.project_id = #{projectId}
        </if>
        <if test="projectIds != null and projectIds.size != 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="supplierId != null and supplierId != ''">
            and a.supplier_id = #{supplierId}
        </if>
        <if test="truckNo != null and truckNo !=''">
            and b.truck_no like concat ('%', #{truckNo},'%')
        </if>
        <if test="materialName != null and materialName != ''">
            and (a.material_name like concat('%', #{materialName}, '%')
            or a.category_name like concat('%', #{materialName}, '%'))
        </if>
        <if test="deviationStatus != null and deviationStatus != 99">
            and a.deviation_status = #{deviationStatus}
        </if>
        <if test="deviationStatus == 99">
            and a.deviation_status is null
        </if>
        <if test="isWeightIntegrality != null and isWeightIntegrality != ''">
            and a.is_weight_integrality = #{isWeightIntegrality}
        </if>
        <if test="isContractRateUsed != null and isContractRateUsed != ''">
            and a.is_contract_rate_used = #{isContractRateUsed}
        </if>
        <if test="isContractUnitUsed != null and isContractUnitUsed != ''">
            and a.is_contract_unit_used = #{isContractUnitUsed}
        </if>
        <if test="materialExist != null and materialExist != ''">
            and a.material_exist = #{materialExist}
        </if>
        <if test="isUsedPurchaseOrder != null and isUsedPurchaseOrder != ''">
            <if test="isUsedPurchaseOrder == 1">
                and b.purchase_id <![CDATA[ <> ]]> ''
            </if>
            <if test="isUsedPurchaseOrder == 2">
                and (b.purchase_id = '' or b.purchase_id is null)
            </if>
        </if>
        <if test="isRevise != null and isRevise !=''">
            and a.is_revise = #{isRevise}
        </if>
        <if test="startTime != null">
            and a.receive_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and a.receive_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="isVerify != null and isVerify != ''">
            <if test="isVerify == 1">
                and c.id is not null
            </if>
            <if test="isVerify == 2">
                and c.id is null
            </if>
        </if>
        <if test="isAddition != null and isAddition != ''">
             and b.is_addition = #{isAddition}
        </if>
        <if test="sort != 2">
            order by a.receive_time desc
        </if>
        <if test="sort == 2">
            order by a.receive_time asc
        </if>
    </select>

    <select id="queryCrccWeightReceiveInfo"
            resultType="cn.pinming.microservice.material.management.biz.dto.CrccMaterialWeighInfoDto">
        SELECT
        a.weight_gross as gWeight,
        a.weight_tare as tWeight,
        a.weight_deduct as bWeight,
        a.weight_net as nWeight,
        a.enter_time as gTime,
        a.enter_pic as enterPic,
        a.moisture_content,
        a.position,
        b.receive_no as sNo,
        a.leave_time as tTime,
        a.leave_pic as leavePic,
        a.document_pic as documentPic,
        a.weigh_id as tUuid,
        b.truck_no as tNo,
        a.weight_actual,
        a.id,
        a.purchase_order_id purchaseId,
        a.contract_detail_id contractDetailId,
        a.receive_id,
        a.actual_receive,
        a.receive_mode,
        a.material_validity,
        a.weight_send,
        a.actual_count,
        a.weight_unit AS unit,
        a.supplier_id,
        d.name as supplierName,
        a.create_id,
        a.material_id,
        a.ratio,
        a.material_name,
        a.category_id,
        a.category_name,
        a.push_state,
        a.project_id AS receiveProject,
        a.deviation_rate,
        a.deviation_status,
        a.is_weight_integrality,
        a.is_contract_rate_used,
        a.is_contract_unit_used,
        a.material_exist,
        a.is_revise,
        a.company_id,
        b.receive_no,
        b.receiver,
        b.is_addition,
        a.receive_time,
        b.purchase_id,
        b.ext_no,
        b.type_detail,
        b.truck_no,
        c.id contractId,
        c.ext_code contractExtCode,
        c.name contractName
        FROM
        d_material_data a
        LEFT JOIN d_material_send_receive b ON a.receive_id = b.id
        left join d_purchase_contract_detail e on e.id = a.contract_detail_id
        left join d_purchase_contract c on c.id = e.contract_id
        left join s_supplier d on d.id = a.supplier_id and d.is_deleted = 0
        where a.is_deleted = 0
        and b.is_deleted = 0 and b.type = 1
        and a.weight_gross is not null
        <if test="projectId != null">
            and a.project_id = #{projectId}
        </if>
        <if test="materialId != null">
            and a.material_id = #{materialId}
        </if>
        <if test="query.startTime != null">
            and a.receive_time &gt; #{query.startTime}
        </if>
        <if test="query.endTime != null">
            and a.receive_time &lt; #{query.endTime}
        </if>
        <if test="query.truckNo != null and query.truckNo != ''">
            and b.truck_no like concat('%', #{query.truckNo},'%')
        </if>
        <if test="query.supplier != null and query.supplier != ''">
            and d.name like concat('%', #{query.supplier},'%')
        </if>
        order by a.create_time desc
    </select>
    <select id="selectSupplierAnalysisByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierAnalysisVO">
        select sum(weight_send) as weightSendAmount ,sum(actual_count) as weightActualAmount,sum(actual_receive) as actualReceive
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity
        = 1
        where a.is_deleted = 0 and a.receive_mode in (1, 2) and a.material_validity = 1 and b.type = 1
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="materialIds !=null and materialIds.size > 0">
            and material_id in
            <foreach collection="materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="supplierId != null">
            and a.supplier_id = #{supplierId}
        </if>
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
        and a.weight_unit = #{unit}
    </select>

    <select id="selectDeviationByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.ReceiveDeviationVO">
        select count(IF(b.deviation_status = 0, 1, null))    as normal,
        count(IF(b.deviation_status = 2, 1, null))    as positive,
        count(IF(b.deviation_status = 1, 1, null))    as negative,
        count(IF(b.deviation_status is null, 1, null)) as unidentified,
        date_format(b.receive_time,'%Y-%m')           as `date`
        from d_material_send_receive a left join d_material_data b on a.id = b.receive_id and b.receive_time is not null
        <if test="receiveModes != null and receiveModes.size > 0">
            and b.receive_mode in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator="," index="">
                #{mode}
            </foreach>
        </if>
        and b.material_validity = 1
        where a.type = 1 and b.material_exist = 1
        <if test="receiveModes != null and receiveModes.size > 0">
            and a.receive_mode in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator=",">
                #{mode}
            </foreach>
        </if>
        and a.material_validity = 1
        and a.company_id = #{query.companyId}
        <if test="query.projectIds !=null and query.projectIds.size > 0">
            and a.project_id in
            <foreach collection="query.projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.materialIds !=null and query.materialIds.size > 0">
            and b.material_id in
            <foreach collection="query.materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.materialIds !=null and query.materialIds.size > 0">
            and b.material_id in
            <foreach collection="query.materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(b.receive_time,'%Y-%m') <![CDATA[ >= ]]> date_format(#{query.startDate},'%Y-%m')
        </if>
        <if test="query.endDate != null">
            and date_format(b.receive_time,'%Y-%m') <![CDATA[ <= ]]> date_format(#{query.endDate},'%Y-%m')
        </if>
        group by `date` order by `date`
    </select>

    <select id="selectSupplierAnalysisPageVO"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierAnalysisDetailVO">
        select a.supplier_id, sum(a.weight_send) as weightSendAmount, sum(a.actual_count) as weightActualAmount,sum(a.actual_receive) as actualReceive
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity
        = 1
        where a.is_deleted = 0 and a.receive_mode in (1, 2) and a.material_validity = 1 and b.type = 1 and
        a.material_exist = 1
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="materialIds !=null and materialIds.size > 0">
            and a.material_id in
            <foreach collection="materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and a.weight_unit = #{unit}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
        <if test="supplierIds !=null and supplierIds.size > 0">
            and a.supplier_id in
            <foreach collection="supplierIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by a.supplier_id
    </select>
    <select id="selectDeviationSummaryByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.ReceiveDeviationSummaryVO">
        select b.deviation_status , count(1) as count
        from d_material_send_receive a
        left join d_material_data b on a.id = b.receive_id
        <if test="receiveModes != null and receiveModes.size > 0">
            and a.receive_mode in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator=",">
                #{mode}
            </foreach>
        </if> and b.material_validity = 1
        where a.type = 1 and b.material_exist = 1
        <if test="receiveModes != null and receiveModes.size > 0">
            and b.receive_mode in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator=",">
                #{mode}
            </foreach>
        </if>
        and a.material_validity = 1
        <if test="query.projectIds !=null and query.projectIds.size > 0">
            and a.project_id in
            <foreach collection="query.projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.materialIds !=null and query.materialIds.size > 0">
            and b.material_id in
            <foreach collection="query.materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>

        </if>
        <if test="query.startDate != null">
            and date_format(b.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(b.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by b.deviation_status
    </select>
    <select id="selectReceiveListByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.CategoryReceiveVO">
        select e.category_name, sum(a.weight_send) as weightSend, sum(a.actual_count) as actualCount
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity
        = 1
        left join d_purchase_contract_detail e on e.id = a.contract_detail_id and e.is_deleted = 0
        where a.is_deleted = 0 and a.receive_mode in (1, 2) and a.material_validity = 1 and b.type = 1 and
        a.material_exist = 1
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and a.weight_unit = #{unit}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{startDate}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endDate}
        and e.category_name is not null
        <if test="type != null and type == 11">
            and a.receive_mode = 2
        </if>
        <if test="type != null and type == 12">
            and a.receive_mode = 1
        </if>
        group by category_name
        order by actualCount desc;
    </select>

    <select id="showWeightInfoWithPurchase"
            resultType="cn.pinming.microservice.material.management.biz.dto.MaterialWeighInfoDTO">

        select a.receive_id,
               a.weight_send,
               a.actual_count,
               a.weight_unit as unit,
               a.supplier_id,
               a.supplier_name,
               a.material_id,
               a.project_id  as receiveProject,
               b.receive_no,
               b.receiver,
               a.receive_time,
               b.purchase_id,
               d.deviation_floor
        from d_material_data a
                 left join d_material_send_receive b
                           on a.receive_id = b.id and b.type = 1 and b.receive_mode in (1, 2) and
                              b.material_validity = 1
                 left join d_purchase_contract_detail d on d.id = a.contract_detail_id and d.is_deleted = 0
        where a.id = #{id}
          and a.is_deleted = 0
          and a.receive_mode in (1, 2)
          and a.material_validity = 1

    </select>

    <select id="selectWeighDetailsWithoutPurchase"
            resultType="cn.pinming.microservice.material.management.biz.vo.MaterialDatasVO">

        select a.weight_send, a.actual_count, a.weight_unit as unit, a.material_id
        from d_material_data a
        where a.company_id = #{companyId} and a.receive_id = #{receiveId}
          and a.is_deleted = 0
          and a.receive_mode in (1
            , 2)
          and a.material_validity = 1

    </select>
    <select id="selectUnitListByCompanyId" resultType="java.lang.String">

        select distinct weight_unit
        from d_material_data
        where company_id = #{companyId}
          and is_deleted = 0
          and receive_mode in (1, 2)
          and material_validity = 1

    </select>
    <select id="selectWeightInfo"
            resultType="cn.pinming.microservice.material.management.biz.dto.ActTruckReportInfoDTO">
        select a.weight_send,
               a.weight_gross,
               a.weight_tare,
               a.weight_deduct,
               a.weight_net,
               a.weight_actual,
               a.ratio,
               a.actual_count,
               deviation_status,
               a.receive_time,
               b.receiver,
               a.enter_pic,
               a.enter_time,
               a.leave_pic,
               a.leave_time,
               receive_id,
               receive_no
        from d_material_data a
                 left join d_material_send_receive b
                           on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity = 1
        where a.id = #{id}
          and a.receive_mode in (1, 2)
          and a.material_validity = 1
    </select>

    <select id="selectSupplierAnalysisUnionByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierAnalysisVO">
        select sum(weightSendAmount) as weightSendAmount ,sum(weightActualAmount) as weightActualAmount ,sum(actualReceive) as actualReceive
        from
        (select sum(weight_send) as weightSendAmount ,sum(actual_count) as weightActualAmount,sum(actual_receive) as actualReceive
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity
        = 1
        where a.is_deleted = 0 and a.receive_mode in (1, 2) and a.material_validity = 1 and b.type = 1
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="materialIds !=null and materialIds.size > 0">
            and material_id in
            <foreach collection="materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
        and a.weight_unit = #{unit}
        <if test="supplierId != null">
            and a.supplier_id = #{supplierId}
        </if>
        union all
        select sum(b.send_settlement_total) as weightSendAmount,sum(actual_settlement_total) as weightActualAmount,sum(actual_settlement_total) as actualReceive
        from d_mobile_receive a
        left join d_mobile_receive_total b on b.receive_id = a.receive_id
        where a.is_deleted = 0
        and b.settlement_unit = #{unit}
        and a.receive_type in (1,2)
        <if test="supplierId != null">
            and a.supplier_id = #{supplierId}
        </if>
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="receiveStartDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
        </if>
        <if test="receiveEndDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
        </if>
        <if test="categoryIds.size != 0">
            and b.category_id in
            <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        ) x
    </select>

    <select id="selectSupplierUnionAnalysisPageVO"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierAnalysisDetailVO">
        select supplier_id,sum(weightSendAmount) as weightSendAmount,sum(weightActualAmount) as weightActualAmount,sum(actualReceive) as actualReceive
        from
        (select a.supplier_id, sum(a.weight_send) as weightSendAmount, sum(a.actual_count) as weightActualAmount,sum(a.actual_receive) as actualReceive
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity
        = 1
        where a.is_deleted = 0 and a.receive_mode in (1, 2) and a.material_validity = 1 and b.type = 1 and
        a.material_exist = 1
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="materialIds !=null and materialIds.size > 0">
            and a.material_id in
            <foreach collection="materialIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and a.weight_unit = #{unit}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
        <if test="supplierIds !=null and supplierIds.size > 0">
            and a.supplier_id in
            <foreach collection="supplierIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by a.supplier_id

        union all

        select a.supplier_id,sum(b.send_settlement_total) as weightSendAmount,sum(actual_settlement_total) as weightActualAmount,sum(actual_settlement_total) as actualReceive
        from d_mobile_receive a
        left join d_mobile_receive_total b on b.receive_id = a.receive_id
        where a.is_deleted = 0 and b.settlement_unit = #{unit}
        and a.receive_type in (1,2)
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="receiveStartDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
        </if>
        <if test="receiveEndDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
        </if>
        <if test="categoryIds.size != 0">
            and b.category_id in
            <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by a.supplier_id
        ) x
        group by supplier_id having supplier_id is not null
    </select>
    <select id="queryWeighbridgeSendInfo"
            resultType="cn.pinming.microservice.material.management.biz.dto.WeighbridgeSendDTO"
            parameterType="cn.pinming.microservice.material.management.biz.query.WeighbridgeSendQuery">
        select b.id as id,
        a.id as sendId,
        a.receive_no as sendNo,
        a.receiver as sender,
        a.truck_no,
        a.remark as remark,
        b.project_id as sendProjectId,
        b.material_id,
        b.material_name,
        b.weight_gross,
        b.weight_tare, b.weight_deduct, b.weight_actual, b.is_weight_integrality,
        b.supplier_name as receiverName,
        b.receive_time as sendTime,
        b.create_id,
        a.is_addition
        from d_material_data b
        left join d_material_send_receive a on b.receive_id = a.id and a.is_deleted = 0
        where b.is_deleted = 0 and a.type = 2 and b.company_id = #{companyId}
        <if test="sendNo != null and sendNo != ''">
            and a.receive_no like concat('%', #{sendNo}, '%')
        </if>
        <if test="truckNo != null and truckNo != ''">
            and a.truck_no like concat('%', #{truckNo}, '%')
        </if>
        <if test="sendProject != null and sendProject != ''">
            and a.project_id = #{sendProject}
        </if>
        <if test="receiveProject != null and receiveProject != ''">
            and b.supplier_name = #{receiveProject}
        </if>
        <if test="materialName != null and materialName != ''">
            and (b.material_name like concat('%', #{materialName}, '%')
            or b.category_name like concat('%', #{materialName}, '%'))
        </if>
        <if test="materialSpec != null and materialSpec != ''">
            and (b.material_name like concat('%', #{materialSpec}, '%')
            or b.category_name like concat('%', #{materialSpec}, '%'))
        </if>
        <if test="isWeightIntegrality != null and isWeightIntegrality != 0">
            and b.is_weight_integrality = #{isWeightIntegrality}
        </if>
        <if test="materialValidity != null and materialValidity != 0">
            and a.material_validity = #{materialValidity}
        </if>
        <if test="startTime != null">
            and b.receive_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and b.receive_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="projectId != null and projectId != ''">
            and a.project_id = #{projectId}
        </if>
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="isAddition != null and isAddition != ''">
            and a.is_addition = #{isAddition}
        </if>
        order by b.receive_time desc
    </select>
    <select id="selectWeighbridgeSendDetail"
            resultType="cn.pinming.microservice.material.management.biz.dto.WeighbridgeSendDetailDTO">
        SELECT a.id            AS sendId,
               a.receive_no    AS sendNo,
               a.create_id      AS sender,
               a.truck_no,
               a.remark        AS remark,
               b.project_id    AS sendProjectId,
               b.weight_gross  AS weight_gross,
               b.weight_tare   AS weight_tare,
               b.weight_deduct as weight_deduct,
               b.weight_actual as weight_actual,
               b.weight_net    as weight_net,
               b.receive_time  AS sendTime,
               b.enter_time    AS weightGrossTime,
               b.enter_pic     AS weightTarePic,
               b.leave_time    AS weightTareTime,
               b.leave_pic     AS weightGrossPic,
               b.supplier_name AS receiverName
        FROM d_material_send_receive a
                 LEFT JOIN d_material_data b ON a.id = b.receive_id
        where b.is_deleted = 0
          and a.type = 2
          and a.is_deleted = 0
          and b.id = #{id}
    </select>

    <select id="selectWeighbridgeSendMaterialDetail"
            resultType="cn.pinming.microservice.material.management.biz.dto.MaterialSendDetailDTO">
        select a.material_id,
               a.material_name,
               a.weight_send,
               a.weight_unit,
               a.weight_actual,
               a.document_pic,
               a.is_device
        from d_material_data a
                 left join d_material_send_receive b on a.receive_id = b.id
        where b.type = 2
          and a.id = #{id}
          and b.is_deleted = 0
          and a.is_deleted = 0
    </select>

    <select id="getDataForFuYang" resultType="cn.pinming.microservice.material.management.biz.dto.MaterialDataForFY">
        select a.*, b.truck_no
        from d_material_data a
                 left join d_material_send_receive b on a.receive_id = b.id
        where a.company_id = 80159
          and a.project_id = 15741
          and a.is_deleted = 0
    </select>

    <select id="checkByWeighId" resultType="cn.pinming.microservice.material.management.biz.dto.PicDTO">
        select a.id, a.document_pic,enter_pic,leave_pic,company_id,project_id
        from d_material_data a
        where a.weigh_id = #{weighId}
          and a.is_deleted = 0
    </select>

    <select id="selectByWeighId" resultType="cn.pinming.microservice.material.management.biz.dto.InfoByWeighIdDTO">
        select a.id,a.receive_id
        from d_material_data a
        where a.weigh_id = #{weighId}
          and a.weight_gross is null
          and a.is_deleted = 0
    </select>

    <select id="findWeighDataByWeighId" resultType="java.lang.Integer">
        select count(a.id)
        from d_material_data a
        left join d_material_verify_relation b on b.receive_data_id = a.id and b.is_deleted = 0
        left join d_material_verify c on c.id = b.verify_id and c.is_deleted = 0
        where a.weigh_id = #{weighId}
          and (a.weight_gross is not null or c.status = 0)
          and a.is_deleted = 0
    </select>

    <select id="findAllWeighId" resultType="java.lang.String">
        select distinct weigh_id
        from d_material_data
        where is_deleted = 0
          and weight_gross is not null
          and weigh_id is not null
    </select>
    <select id="listSummaryDeliveryByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SummaryDeliveryVO">
        select a.category_id,a.category_name,ifnull(sum(a.weight_actual), 0)  as accumulationCount,
        ifnull(sum(if(date_format(a.receive_time, '%Y-%m') = date_format(now(), '%Y-%m'), a.weight_actual, 0)),0)      AS monthlyCount,
        a.weight_unit as unit
        from d_material_data a
        left join d_material_send_receive b on b.id = a.receive_id
        where b.material_validity = 1 and a.material_id is not null
        and b.type = 2
        and a.category_id is not null
        and a.company_id = #{companyId}
        <if test="startDate != null">
            and date_format(b.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate != null">
            and date_format(b.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endDate}
        </if>
        <if test="projectIdList.size != 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by a.category_id
    </select>

    <select id="listCategoryIdList"  resultType="integer">
        select distinct a.category_id
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id
        where a.category_id is not null
        and a.company_id = #{companyId}
        and b.type = 2
        <if test="startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endDate}
        </if>
        <if test="projectIdList != null and projectIdList.size != 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="listDetailList"
            resultType="cn.pinming.microservice.material.management.biz.dto.SummaryDeliverySecondDetailDTO">

        select a.category_id,a.category_name, sum(a.weight_actual) as accumulationCount,
        sum(if(date_format(a.receive_time,'%Y-%m') = date_format(now(), '%Y-%m'), a.weight_actual, 0)) as monthlyCount
        from d_material_data a
        left join d_material_send_receive b on b.id = a.receive_id
        where b.material_validity = 1 and a.material_id is not null
        and b.type = 2
        and a.company_id = #{query.companyId}
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        <if test="query.projectIdList.size != 0">
            and a.project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="categoryIdList.size != 0">
            and a.category_id in
            <foreach collection="categoryIdList" item="categoryId" open="(" close=")" separator="," index="">
                #{categoryId}
            </foreach>
        </if>
        group by a.category_id
    </select>

    <select id="selectReceiveOverviewSecondByQuery"
            resultType="cn.pinming.microservice.material.management.biz.dto.ReceiveOverviewSecondDTO">
        select x.time, sum(x.total) as total, sum(x.monthRise) as monthRise
        from (
        select DATE_FORMAT(a.receive_time, '%Y/%m') as time,
        count(*) as total,
        count(case when date_format(a.receive_time, "%Y/%m") = date_format(now(), "%Y/%m") then 1 or null end) AS
        monthRise
        from d_material_send_receive b
        left join d_material_data a on a.receive_id = b.id
        where a.is_deleted = 0  and b.type = #{type}
        and b.company_id = #{query.companyId}
        <if test="query.projectIdList.size != 0">
            and a.project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by time  having time is not null
        <if test="type != 2">
            UNION ALL
            SELECT
            DATE_FORMAT( create_time, '%Y/%m' ) AS time,
            count( 1 ) AS total,
            count(case date_format( create_time, '%Y%m' ) when date_format( now(), '%Y%m' ) then 1 else  NULL end) AS monthRise
            FROM
            d_mobile_receive
            WHERE
            is_deleted = 0 and company_id = #{query.companyId}
            <if test="query.projectIdList.size != 0">
                and project_id in
                <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                    #{id}
                </foreach>
            </if>
            GROUP BY time  having time is not null
        </if>
        ) x
        group by x.time
        order by x.time asc
    </select>

    <select id="selectReceiveOverviewListByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.ReceiveOverviewMaterialVO">
        select e.category_id, e.category_name, e.material_name ,sum(a.weight_send) as weightSend, sum(a.actual_count) as
        actualCount
        from d_material_data a
        left join d_material_send_receive b on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity
        = 1
        left join d_purchase_contract_detail e on e.id = a.contract_detail_id and e.is_deleted = 0
        where a.is_deleted = 0 and a.receive_mode in (1, 2) and a.material_validity = 1 and b.type = 1 and
        a.material_exist = 1
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and a.weight_unit = #{unit}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{startDate}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endDate}
        and e.category_name is not null
        <if test="type != null and type == 11">
            and a.receive_mode = 2
        </if>
        <if test="type != null and type == 12">
            and a.receive_mode = 1
        </if>
        group by e.material_name
        order by actualCount desc;
    </select>

    <select id="selectDeviationSecondByQuery"
            resultType="cn.pinming.microservice.material.management.biz.dto.DeviationOverviewSecondDTO">
        SELECT
        sum(x.countNormalCarsNum) as countNormalCarsNum,
        sum(x.countMinusCarsNum) as countMinusCarsNum,
        sum(x.countPositiveCarsNum) as countPositiveCarsNum,
        x.time

        from (

        SELECT
        count(case when deviation_status = 0 then 1 else null end ) as countNormalCarsNum,
        count(case when deviation_status = 1 then 1 else null end ) AS countMinusCarsNum,
        count(case when deviation_status = 2 then 1 else null end ) as countPositiveCarsNum,
        DATE_FORMAT(a.receive_time, '%Y/%m') as time
        FROM
        d_material_data a
        LEFT JOIN d_material_send_receive b ON a.receive_id = b.id
        where
        a.receive_time is not null
        AND a.is_deleted = 0
        AND a.company_id = #{query.companyId}
        <if test="query.projectIdList !=null and query.projectIdList.size > 0">
            AND a.project_id IN
            <foreach collection="query.projectIdList" item="project" open="(" close=")" separator="," index="">
                #{project}
            </foreach>
        </if>
        GROUP BY time

        UNION ALL

        SELECT
        count(case when deviation_status = 0 then 1 else null end ) as countNormalCarsNum,
        count(case when deviation_status = 1 then 1 else null end ) AS countMinusCarsNum,
        count(case when deviation_status = 2 then 1 else null end ) as countPositiveCarsNum,
        DATE_FORMAT(create_time, '%Y/%m') as time
        FROM
        d_mobile_receive
        WHERE is_deleted = 0 and company_id = #{query.companyId}
        and create_time is not null
        <if test="query.projectIdList != null and query.projectIdList.size != 0">
            and project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        GROUP BY time
        ) x
        GROUP BY x.time
        ORDER BY x.time
    </select>

    <select id="negativeFrequencyRankListByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierRankVO">
        select a.supplier_id, count(a.id) as num, a.deviation_status
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity = 1
        where a.company_id = #{query.companyId}
        and a.deviation_status in (1, 2)
        and a.receive_mode in (1, 2)
        and a.material_validity = 1
        and a.material_exist = 1
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id, a.deviation_status
        order by num desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>
    </select>
    <select id="negativeFrequencyMobileRankListByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierRankVO">
        select a.supplier_id, count(b.id) as num, b.deviation_status
        from d_mobile_receive_total b
        left join d_mobile_receive a on b.receive_id = a.receive_id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.is_deleted = 0
        and b.deviation_status in (1, 2)
        and a.receive_type in (1, 2)
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null ">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        and supplier_id is not null
        group by a.supplier_id, b.deviation_status
        order by num desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>
    </select>
    <select id="negativeFrequencyAllRankListByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierRankVO">
        select x.supplier_id, sum(x.num) as num, x.deviation_status
        from (select a.supplier_id, count(a.id) as num, a.deviation_status
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity = 1
        where a.company_id = #{query.companyId}
        and a.deviation_status in (1, 2)
        and a.receive_mode in (1, 2)
        and a.material_validity = 1
        and a.material_exist = 1
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id, a.deviation_status
        union all
        select a.supplier_id, count(b.id) as num, b.deviation_status
        from d_mobile_receive_total b
        left join d_mobile_receive a on b.receive_id = a.receive_id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.is_deleted = 0
        and b.deviation_status in (1, 2)
        and a.receive_type in (1, 2)
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        and supplier_id is not null
        group by a.supplier_id, b.deviation_status) x
        group by x.supplier_id, x.deviation_status order by num desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>
    </select>
    <select id="negativeFrequencyProportionByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierRankVO">
        select a.supplier_id,count(*) as num, a.deviation_status
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity = 1
        where a.company_id = #{query.companyId}
        and a.receive_mode in (1, 2)
        and a.material_validity = 1
        and a.material_exist = 1
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id, a.deviation_status
        order by num desc
<!--        <if test="query.limit != null and query.limit != 0">-->
<!--            limit #{query.limit}-->
<!--        </if>-->
    </select>
    <select id="negativeFrequencyMobileProportionByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierRankVO">
        select a.supplier_id, count(*) as num, b.deviation_status
        from d_mobile_receive_total b
        left join d_mobile_receive a on b.receive_id = a.receive_id
        where a.company_id = #{query.companyId}
        and a.is_deleted = 0
        and b.is_deleted = 0
        and a.receive_type in (1, 2)
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null ">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null ">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        and a.supplier_id is not null
        group by a.supplier_id, b.deviation_status
        order by num desc
<!--        <if test="query.limit != null and query.limit != 0">-->
<!--            limit #{query.limit}-->
<!--        </if>-->
    </select>
    <select id="negativeFrequencyAllProportionByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierRankVO">
        select x.supplier_id, sum(x.num) as num, x.deviation_status
        from (select a.supplier_id ,count(*) as num, a.deviation_status
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity = 1
        where a.company_id = #{query.companyId}
        and a.receive_mode in (1, 2)
        and a.material_validity = 1
        and a.material_exist = 1
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id, a.deviation_status
        union all
        select a.supplier_id, count(*) as num, a.deviation_status
        from d_mobile_receive_total b
        left join d_mobile_receive a on a.receive_id = b.receive_id and a.is_deleted = 0
        where b.company_id = #{query.companyId}
        and b.is_deleted = 0
        and a.receive_type in (1, 2)
        <if test="query.projectId.size != 0">
            and b.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(b.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(b.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        and a.supplier_id is not null
        group by a.supplier_id,a.deviation_status) x
        group by x.supplier_id, x.deviation_status order by num desc
<!--        <if test="query.limit != null and query.limit != 0">-->
<!--            limit #{query.limit}-->
<!--        </if>-->
    </select>
    <select id="deductRankByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierRankVO">
        select a.supplier_id, count(a.id) as num
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity = 1
        where a.company_id = #{query.companyId}
        and a.weight_deduct != 0
        and a.material_validity = 1
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id
        order by num desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>
    </select>
    <select id="deductProportionByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierRankVO">
        select a.supplier_id, round(count(if (a.weight_deduct != 0, a.weight_deduct, null))*100/count(*),2) as num
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity = 1
        where a.company_id = #{query.companyId}
        and a.material_validity = 1
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id
        order by num desc
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>
    </select>
    <select id="deductTotalRankByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierRankVO">
        select a.supplier_id, - sum(a.weight_deduct) as num
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity = 1
        where a.company_id = #{query.companyId}
        and a.weight_deduct != 0
        and a.material_validity = 1
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id
        order by num
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>
    </select>
    <select id="deductTotalProportionByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierRankVO">
        select a.supplier_id, - round(sum(a.weight_deduct) * 100 / sum(a.weight_send / a.ratio), 2) as num
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity = 1
        where a.company_id = #{query.companyId}
        and a.weight_deduct != 0
        and a.material_validity = 1
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by supplier_id
        order by num
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>
    </select>
    <select id="negativeTotalRankByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierRankDeviationVO">
        select x.supplier_id, round(sum(x.negativeNum) / 100, 2) as negativeNum, round(sum(x.positiveNum) / 100, 2) as positiveNum, x.deviation_status from
        (select a.supplier_id, (a.deviation_rate - c.deviation_floor) * a.weight_send as negativeNum, (a.deviation_rate - c.deviation_ceiling) * a.weight_send as positiveNum, a.deviation_status
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity = 1
        left join d_purchase_contract_detail c on a.contract_detail_id = c.id
        where a.company_id = #{query.companyId}
        and a.deviation_status in (1, 2)
        and a.receive_mode in (1, 2)
        and a.material_validity = 1
        and a.material_exist = 1
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        ) x
        group by x.supplier_id, x.deviation_status
        order by negativeNum
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>
    </select>
    <select id="negativeTotalMobileRankByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierRankDeviationVO">
        select x.supplier_id, round(sum(negativeNum) / 100, 2) as negativeNum, round(sum(positiveNum) / 100, 2) as positiveNum, x.deviation_status
        from (select b.supplier_id,
        sum((a.deviation_rate - c.deviation_floor) *
        a.send_settlement_total) as negativeNum,
        sum((a.deviation_rate - c.deviation_ceiling) *
        a.send_settlement_total) as positiveNum, a.deviation_status
        from d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        left join d_purchase_contract_detail c on c.contract_id = b.contract_id and c.material_id = a.material_id and c.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.contract_id is not null
        and a.is_deleted = 0
        and a.deviation_status in (1, 2)
        and b.receive_type in (1, 2)
        and b.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by b.supplier_id, a.deviation_status

        union all

        select b.supplier_id,
        sum((a.deviation_rate - d.deviation_floor) * a.send_settlement_total) as negativeNum,  sum((a.deviation_rate - d.deviation_floor) * a.send_settlement_total) as positiveNum, a.deviation_status
        from d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        left join d_purchase_order c on b.purchase_id = c.id and c.is_deleted = 0
        left join d_purchase_contract_detail d on d.contract_id = c.contract_id and d.material_id = a.material_id and d.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.contract_id is null
        and b.purchase_id is not null
        and a.is_deleted = 0
        and a.deviation_status in (1, 2)
        and b.receive_type in (1, 2)
        and b.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        group by b.supplier_id, a.deviation_status
        ) x
        group by x.supplier_id, x.deviation_status
        order by negativeNum
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>
    </select>
    <select id="negativeTotalAllRankByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierRankDeviationVO">
        select y.supplier_id, sum(y.negativeNum) / 100 as negativeNum, sum(y.positiveNum) / 100 as positiveNum, y.deviation_status from (
        select x.supplier_id, round(sum(x.negativeNum), 2) as negativeNum, round(sum(x.positiveNum), 2) as positiveNum, x.deviation_status from
        (select a.supplier_id, (a.deviation_rate - c.deviation_floor) * a.weight_send as negativeNum, (a.deviation_rate - c.deviation_ceiling) * a.weight_send as positiveNum, a.deviation_status
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity = 1
        left join d_purchase_contract_detail c on a.contract_detail_id = c.id
        where a.company_id = #{query.companyId}
        and a.deviation_status in (1, 2)
        and a.receive_mode in (1, 2)
        and a.material_validity = 1
        and a.material_exist = 1
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        ) x
        group by x.supplier_id, x.deviation_status
        union all
        select x.supplier_id, round(sum(negativeNum), 2) as negativeNum, round(sum(positiveNum), 2) as positiveNum, x.deviation_status
        from (select b.supplier_id,
        (a.deviation_rate - c.deviation_floor) * a.send_settlement_total as negativeNum, (a.deviation_rate - c.deviation_ceiling) * a.send_settlement_total as positiveNum, a.deviation_status
        from d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        left join d_purchase_contract_detail c on c.contract_id = b.contract_id and c.material_id = a.material_id and c.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.contract_id is not null
        and a.is_deleted = 0
        and a.deviation_status in (1, 2)
        and b.receive_type in (1, 2)
        and b.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>

        union all

        select b.supplier_id,
        (a.deviation_rate - d.deviation_floor) * a.send_settlement_total as negativeNum, (a.deviation_rate - d.deviation_ceiling) * a.send_settlement_total as positiveNum, a.deviation_status
        from d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        left join d_purchase_order c on b.purchase_id = c.id and c.is_deleted = 0
        left join d_purchase_contract_detail d on d.contract_id = c.contract_id and d.material_id = a.material_id and d.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.contract_id is null
        and b.purchase_id is not null
        and a.is_deleted = 0
        and a.deviation_status in (1, 2)
        and b.receive_type in (1, 2)
        and b.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        ) x
        group by x.supplier_id, x.deviation_status) y
        group by y.supplier_id, y.deviation_status
        order by negativeNum
        <if test="query.limit != null and query.limit != 0">
            limit #{query.limit}
        </if>
    </select>
    <select id="negativeTotalProportionByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierRankDeviationVO">
        select a.supplier_id,
        (a.deviation_rate - c.deviation_floor) * a.weight_send as negativeNum,
        (a.deviation_rate - c.deviation_ceiling) * a.weight_send as positiveNum,
        if(a.weight_send is null, 0, a.weight_send) as total,
        if(a.deviation_status is null, -1, a.deviation_status) deviationStatus
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity = 1
        left join d_purchase_contract_detail c on a.contract_detail_id = c.id
        where a.company_id = #{query.companyId}
        and a.receive_mode in (1, 2)
        and a.material_validity = 1
        and a.material_exist = 1
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
    </select>
    <select id="negativeTotalMobileProportionByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierRankDeviationVO">
        select b.supplier_id supplierId,
        (a.deviation_rate - c.deviation_floor) * a.send_settlement_total as negativeNum,
        (a.deviation_rate - c.deviation_ceiling) * a.send_settlement_total as positiveNum,
        if(a.send_settlement_total is null, 0, a.send_settlement_total) as total,
        if(a.deviation_status is null, -1, a.deviation_status) deviationStatus
        from d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        left join d_purchase_contract_detail c on c.contract_id = b.contract_id and c.material_id = a.material_id and c.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.contract_id is not null
        and a.is_deleted = 0
        and b.receive_type in (1, 2)
        and b.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>

        union all

        select b.supplier_id supplierId,
        (a.deviation_rate - d.deviation_floor) * a.send_settlement_total as negativeNum,
        (a.deviation_rate - d.deviation_ceiling) * a.send_settlement_total as positiveNum,
        if(a.send_settlement_total is null, 0, a.send_settlement_total) as total,
        if(a.deviation_status is null, -1, a.deviation_status) deviationStatus
        from d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        left join d_purchase_order c on b.purchase_id = c.id and c.is_deleted = 0
        left join d_purchase_contract_detail d on d.contract_id = c.contract_id and d.material_id = a.material_id and d.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.contract_id is null
        and b.purchase_id is not null
        and a.is_deleted = 0
        and b.receive_type in (1, 2)
        and b.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
    </select>
    <select id="negativeTotalAllProportionByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierRankDeviationVO">
        select b.supplier_id supplierId,
        (a.deviation_rate - c.deviation_floor) * a.send_settlement_total as negativeNum,
        (a.deviation_rate - c.deviation_floor) * a.send_settlement_total as positiveNum,
        if(a.send_settlement_total is null, 0, a.send_settlement_total) as total,
        if(a.deviation_status is null, -1, a.deviation_status) deviationStatus
        from d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        left join d_purchase_contract_detail c on c.contract_id = b.contract_id and c.material_id = a.material_id and c.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.contract_id is not null
        and a.is_deleted = 0
        and b.receive_type in (1, 2)
        and b.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>

        union all

        select b.supplier_id supplierId,
        (a.deviation_rate - d.deviation_floor) * a.send_settlement_total as negativeNum,
        (a.deviation_rate - d.deviation_ceiling) * a.send_settlement_total as positveNum,
        if(a.send_settlement_total is null, 0, a.send_settlement_total) as total,
        if(a.deviation_status is null, -1, a.deviation_status) deviationStatus
        from d_mobile_receive_total a
        left join d_mobile_receive b on a.receive_id = b.receive_id
        left join d_purchase_order c on b.purchase_id = c.id and c.is_deleted = 0
        left join d_purchase_contract_detail d on d.contract_id = c.contract_id and d.material_id = a.material_id and d.is_deleted = 0
        where a.company_id = #{query.companyId}
        and b.contract_id is null
        and b.purchase_id is not null
        and a.is_deleted = 0
        and b.receive_type in (1, 2)
        and b.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>

        union all

        select a.supplier_id supplierId, (a.deviation_rate - c.deviation_floor) * a.weight_send as negativeNum,
        (a.deviation_rate - c.deviation_ceiling) * a.weight_send as positiveNum,
        if(a.weight_send is null, 0, a.weight_send) as total,
        if(a.deviation_status is null, -1, a.deviation_status) deviationStatus
        from d_material_data a
        left join d_material_send_receive b
        on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity = 1
        left join d_purchase_contract_detail c on a.contract_detail_id = c.id and c.is_deleted = 0
        where a.company_id = #{query.companyId}
        and a.receive_mode in (1, 2)
        and a.material_validity = 1
        and a.material_exist = 1
        and a.is_deleted = 0
        and b.type = 1
        and a.supplier_id is not null
        <if test="query.projectId.size != 0">
            and a.project_id in
            <foreach collection="query.projectId" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
    </select>

    <select id="showWeighCar" resultType="cn.pinming.microservice.material.management.biz.dto.WeighCarDTO">
        select sum(c.carTotal) as carTotal,sum(c.carMonthly) as carMonthly,c.project_id
        from
        (
        select ifnull(count(a.id),0) as carTotal,0 as carMonthly,a.project_id
        from d_material_data a
        where a.is_deleted = 0
        and a.project_id is not null
        <if test="query.projectIdList.size != 0">
            and a.project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m') <![CDATA[ >= ]]> date_format(#{query.startDate},'%Y-%m')
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m') <![CDATA[ <= ]]> date_format(#{query.endDate},'%Y-%m')
        </if>
        group by a.project_id

        union all

        select 0 as carTotal,ifnull(count(a.id),0) as carMonthly,a.project_id
        from d_material_data a
        where a.is_deleted = 0
        and a.project_id is not null
        <if test="query.projectIdList.size != 0">
            and a.project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m') <![CDATA[ >= ]]> date_format(#{query.startDate},'%Y-%m')
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m') <![CDATA[ <= ]]> date_format(#{query.endDate},'%Y-%m')
        </if>
        and date_format(a.create_time,'%Y%m') = date_format(curdate(),'%Y%m')
        group by a.project_id
        )c
        group by c.project_id
    </select>

    <select id="showWeighCarDetail" resultType="cn.pinming.microservice.material.management.biz.dto.WeighCarDetailDTO">
        select
        ifnull(count(case b.type when 1 then a.id end) , 0) as weighingCarNumber,
        ifnull(count(case b.type when 2 then a.id end) , 0) as sendingCarNumber,
        date_format(a.receive_time,'%Y-%m') as date,
        a.project_id
        from d_material_data a
        left join d_material_send_receive b on b.id = a.receive_id and b.is_deleted = 0
        where a.is_deleted = 0
        and a.project_id is not null
        <if test="query.startDate != null">
            and date_format(a.receive_time,'%Y-%m') <![CDATA[ >= ]]> date_format(#{query.startDate},'%Y-%m')
        </if>
        <if test="query.endDate != null">
            and date_format(a.receive_time,'%Y-%m') <![CDATA[ <= ]]> date_format(#{query.endDate},'%Y-%m')
        </if>
        <if test="query.projectIdList.size != 0">
            and a.project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by a.project_id,date_format(a.receive_time,'%Y%m')
    </select>

    <select id="listUnitByCategoryIds" resultType="cn.pinming.microservice.material.management.biz.dto.StatisticsUnitDTO">
        select distinct categoryId, unit from (
        select distinct category_id categoryId, weight_unit unit from d_material_data
        where 1 = 1
        <if test="categoryIds != null and categoryIds.size > 0">
            and category_id in
            <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        union all
        select distinct category_id categoryId, settlement_unit unit from d_mobile_receive_total
        where 1 = 1
        <if test="categoryIds != null and categoryIds.size > 0">
            and category_id in
            <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        ) t
    </select>

    <select id="wagonReceiveOverviewCard" resultType="cn.pinming.microservice.material.management.biz.dto.ReceiveOverviewCardDTO">
        select t1.category_id categoryId, t1.material_id materialId, t1.weight_unit unit,
        if(t1.weight_send is null, 0, t1.weight_send) weightSend,
        if(t1.actual_count is null, 0, t1.actual_count) actualCount,
        if(t1.actual_receive is null, 0, t1.actual_receive) actualReceive from
        (select * from d_material_send_receive
        where 1 = 1 and is_deleted = 0
        and material_validity = 1
        and purchase_id is not null
        <if test="receiveModes != null and receiveModes.size > 0">
            and receive_mode in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator="," index="">
                #{mode}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        ) t
        inner join
        (select * from d_material_data where material_id is not null and is_deleted = 0 and weight_unit is not null and weight_unit != ''
        <if test="categoryIds != null and categoryIds.size > 0">
            and category_id in
            <foreach collection="categoryIds" item="categoryId" open="(" close=")" separator="," index="">
                #{categoryId}
            </foreach>
        </if>
        <if test="start != null and start != ''">
            and date_format(receive_time,'%Y-%m-%d') between #{start} and #{end}
        </if>
        and material_validity = 1
        ) t1
        on t.id = t1.receive_id
    </select>
    <select id="mobileReceiveOverviewCard" resultType="cn.pinming.microservice.material.management.biz.dto.ReceiveOverviewCardDTO">
        select t4.category_id categoryId, t4.material_id, t4.settlement_unit unit,
        if(t4.send_settlement_total is null, 0, t4.send_settlement_total) weight_send,
        if(t4.actual_settlement_total is null, 0, t4.actual_settlement_total) actual_count,
        if(t4.actual_settlement_total is null, 0, t4.actual_settlement_total) actualReceive from
        (select * from d_mobile_receive
        where 1 = 1 and is_deleted = 0
        <if test="start != null and start != ''">
            and date_format(create_time,'%Y-%m-%d') between #{start} and #{end}
        </if>
        <if test="receiveTypes != null and receiveTypes.size > 0">
            and receive_type in
            <foreach collection="receiveTypes" item="type" open="(" close=")" separator="," index="">
                #{type}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        ) t3
        inner join
        (select * from d_mobile_receive_total where material_id is not null and is_deleted = 0 and settlement_unit is not null and settlement_unit != ''
        <if test="categoryIds != null and categoryIds.size > 0">
            and category_id in
            <foreach collection="categoryIds" item="categoryId" open="(" close=")" separator="," index="">
                #{categoryId}
            </foreach>
        </if>
        ) t4
        on t3.receive_id = t4.receive_id
    </select>

    <select id="wagonReceiveOverviewCardSecond" resultType="cn.pinming.microservice.material.management.biz.dto.ReceiveOverviewCardSecondDTO">
        select date_format(t.receive_time,'%Y/%m/%d') time, date_format(t.receive_time,'%Y/%m') month,
        t1.weight_unit unit, if(t1.weight_send is null, 0, t1.weight_send) weightSend,
        if(t1.actual_count is null, 0, t1.actual_count) actualCount,
        if(t1.actual_receive is null, 0, t1.actual_receive) actualReceive from
        (select * from d_material_send_receive
        where 1 = 1 and is_deleted = 0
        <if test="receiveModes != null and receiveModes.size > 0">
            and receive_mode in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator="," index="">
                #{mode}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        ) t
        inner join
        (select * from d_material_data where 1 = 1 and is_deleted = 0
        <if test="materialIds != null and materialIds.size > 0">
            and material_id in
            <foreach collection="materialIds" item="materialId" open="(" close=")" separator="," index="">
                #{materialId}
            </foreach>
        </if>
        <if test="unit != null and unit != ''">
            and weight_unit = #{unit}
        </if>
        <if test="start != null and start != ''">
            and date_format(receive_time,'%Y-%m-%d') between #{start} and #{end}
        </if>
        ) t1
        on t.id = t1.receive_id
    </select>
    <select id="mobileReceiveOverviewCardSecond" resultType="cn.pinming.microservice.material.management.biz.dto.ReceiveOverviewCardSecondDTO">
        select date_format(t3.create_time,'%Y/%m/%d') time, date_format(t3.create_time,'%Y/%m') month,
        if(t4.send_settlement_total is null, 0, t4.send_settlement_total) weight_send,
        if(t4.actual_settlement_total is null, 0, t4.actual_settlement_total) actual_count,
        if(t4.actual_settlement_total is null, 0, t4.actual_settlement_total) actualReceive from
        (select * from d_mobile_receive
        where 1 = 1 and is_deleted = 0
        <if test="start != null and start != ''">
            and date_format(create_time,'%Y-%m-%d') between #{start} and #{end}
        </if>
        <if test="receiveTypes != null and receiveTypes.size > 0">
            and receive_type in
            <foreach collection="receiveTypes" item="type" open="(" close=")" separator="," index="">
                #{type}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        ) t3
        inner join
        (select * from d_mobile_receive_total where 1 = 1 and is_deleted = 0
        <if test="materialIds != null and materialIds.size > 0">
            and material_id in
            <foreach collection="materialIds" item="materialId" open="(" close=")" separator="," index="">
                #{materialId}
            </foreach>
        </if>
        <if test="unit != null and unit != ''">
            and settlement_unit = #{unit}
        </if>
        ) t4
        on t3.receive_id = t4.receive_id
    </select>

    <select id="wagonReceiveOverviewOther" resultType="cn.pinming.microservice.material.management.biz.dto.ReceiveOverviewCardDTO">
        select t1.category_id categoryId, t1.material_id materialId, t1.weight_unit unit,
        if(t1.weight_send is null, 0, t1.weight_send) weightSend,
        if(t1.actual_count is null, 0, t1.actual_count) actualCount,
        if(t1.actual_receive is null, 0, t1.actual_receive) actualReceive from
        (select * from d_material_send_receive
        where 1 = 1 and is_deleted = 0
        <if test="receiveModes != null and receiveModes.size > 0">
            and receive_mode in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator="," index="">
                #{mode}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        ) t
        inner join
        (select * from d_material_data where 1=1 and is_deleted = 0 and weight_unit is not null and weight_unit != ''
        <if test="categoryIds != null and categoryIds.size > 0">
            and category_id not in
            <foreach collection="categoryIds" item="categoryId" open="(" close=")" separator="," index="">
                #{categoryId}
            </foreach>
        </if>
        <if test="start != null and start != ''">
            and date_format(receive_time,'%Y-%m-%d') between #{start} and #{end}
        </if>
        ) t1
        on t.id = t1.receive_id
    </select>
    <select id="mobileReceiveOverviewOther" resultType="cn.pinming.microservice.material.management.biz.dto.ReceiveOverviewCardDTO">
        select t4.category_id categoryId, t4.material_id, t4.settlement_unit unit,
        if(t4.send_settlement_total is null, 0, t4.send_settlement_total) weight_send,
        if(t4.actual_settlement_total is null, 0, t4.actual_settlement_total) actual_count,
        if(t4.actual_settlement_total is null, 0, t4.actual_settlement_total) actualReceive from
        (select * from d_mobile_receive
        where 1 = 1 and is_deleted = 0
        <if test="start != null and start != ''">
            and date_format(create_time,'%Y-%m-%d') between #{start} and #{end}
        </if>
        <if test="receiveTypes != null and receiveTypes.size > 0">
            and receive_type in
            <foreach collection="receiveTypes" item="type" open="(" close=")" separator="," index="">
                #{type}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        ) t3
        inner join
        (select * from d_mobile_receive_total where 1=1 and is_deleted = 0 and settlement_unit is not null and settlement_unit != ''
        <if test="categoryIds != null and categoryIds.size > 0">
            and category_id not in
            <foreach collection="categoryIds" item="categoryId" open="(" close=")" separator="," index="">
                #{categoryId}
            </foreach>
        </if>
        ) t4
        on t3.receive_id = t4.receive_id
    </select>

    <select id="wagonReceiveOverviewOtherSecond" resultType="cn.pinming.microservice.material.management.biz.dto.ReceiveOverviewCardSecondDTO">
        select date_format(t.receive_time,'%Y/%m/%d') time, date_format(t.receive_time,'%Y/%m') month,
        if(t1.weight_send is null, 0, t1.weight_send) weightSend,
        if(t1.actual_count is null, 0, t1.actual_count) actualCount,
        if(t1.actual_receive is null, 0, t1.actual_receive) actualReceive from
        (select * from d_material_send_receive
        where 1 = 1 and is_deleted = 0
        <if test="start != null and start != ''">
            and date_format(receive_time,'%Y-%m-%d') between #{start} and #{end}
        </if>
        <if test="receiveModes != null and receiveModes.size > 0">
            and receive_mode in
            <foreach collection="receiveModes" item="mode" open="(" close=")" separator="," index="">
                #{mode}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        ) t
        inner join
        (select * from d_material_data where 1=1 and is_deleted = 0
        <if test="categoryId != null and categoryId != -1">
            and category_id = #{categoryId}
        </if>
        <if test="categoryId != null and categoryId == -1">
            and (material_id is null or material_id = '')
        </if>
        ) t1
        on t.id = t1.receive_id
    </select>
    <select id="mobileReceiveOverviewOtherSecond" resultType="cn.pinming.microservice.material.management.biz.dto.ReceiveOverviewCardSecondDTO">
        select date_format(t3.create_time,'%Y/%m/%d') time, date_format(t3.create_time,'%Y/%m') month,
        if(t4.send_settlement_total is null, 0, t4.send_settlement_total) weight_send,
        if(t4.actual_settlement_total is null, 0, t4.actual_settlement_total) actual_count,
        if(t4.actual_settlement_total is null, 0, t4.actual_settlement_total) actualReceive from
        (select * from d_mobile_receive
        where 1 = 1 and is_deleted = 0
        <if test="start != null and start != ''">
            and date_format(create_time,'%Y-%m-%d') between #{start} and #{end}
        </if>
        <if test="receiveTypes != null and receiveTypes.size > 0">
            and receive_type in
            <foreach collection="receiveTypes" item="type" open="(" close=")" separator="," index="">
                #{type}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        ) t3
        inner join
        (select * from d_mobile_receive_total where 1=1 and is_deleted = 0
        <if test="categoryId != null and categoryId != -1">
            and category_id = #{categoryId}
        </if>
        <if test="categoryId != null and categoryId == -1">
            and (material_id is null or material_id = '')
        </if>
        ) t4
        on t3.receive_id = t4.receive_id
    </select>

    <select id="solve" resultType="cn.pinming.microservice.material.management.biz.dto.MaterialIdDTO">
        select a.id,a.material_id
        from d_material_data a
        where a.material_id is not null
    </select>

    <select id="selectUsageRate" resultType="java.math.BigDecimal">
        select convert(( count(distinct a.project_id ) / c.num * 100),decimal (3,0))as usageRate
        from d_material_data a
        join
        (
        select count(distinct b.project_id) as num
        from s_material_weighbridge b
        where b.is_deleted = 0
        <if test="projectIdList != null and projectIdList.size > 0">
            and b.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        )c
        where a.is_deleted = 0
        <if test="projectIdList != null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="day != null and day != 0">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> now()
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> date_sub(now(),interval #{day} day )
        </if>
    </select>

    <select id="selectUsageList" resultType="java.lang.Integer">
        select distinct a.project_id
        from d_material_data a
        where a.is_deleted = 0
        <if test="projectIdList != null and projectIdList.size > 0">
            and a.project_id in
            <foreach collection="projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="day != null and day != 0">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> now()
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> date_sub(now(),interval #{day} day )
        </if>
    </select>
    <select id="selectCountByWeighId" resultType="java.lang.Integer">
        select count(*)
        from d_material_data a
        left join d_material_verify_relation b
        on a.id = b.receive_data_id and a.company_id = b.company_id
        left join d_material_verify c
        on c.id = b.verify_id and a.company_id = b.company_id
        where a.weigh_id = #{weighId} and a.company_id = #{companyId}
        <if test="flag == true">
            and (c.status = 1 or c.status is null)
            and a.document_pic is not null
            and a.weight_gross is null
        </if>
        <if test="flag == false">
            and a.weight_gross is not null
            and (c.status = 1 or c.status is null)
        </if>
        and a.is_deleted = 0
    </select>

    <select id="selectGunByWeighId" resultType="cn.pinming.microservice.material.management.biz.entity.MaterialData">
        select *
        from d_material_data a
        <!--        left join d_material_verify_relation b on b.receive_data_id = a.id and b.is_deleted = 0-->
        <!--        left join d_material_verify c on c.id = b.verify_id and c.is_deleted = 0-->
        where a.weigh_id = #{weighId}
        and a.is_deleted = 0
<!--        and a.weight_gross is not null-->
    </select>

    <select id="selectSendReceiveByWeighId"
            resultType="cn.pinming.microservice.material.management.biz.dto.SendReceiveByWeighIdDTO">
        select b.purchase_id,b.order_no,b.id,b.receive_mode,b.supplier_id
        from d_material_data a
                 left join d_material_send_receive b on a.receive_id = b.id and b.is_deleted = 0
        where a.weigh_id = #{weighId}
          and a.weight_gross is not null
          and a.is_deleted = 0
    </select>

    <select id="selectVerifyByWeighId" resultType="int">
        select count(*)
        from d_material_data a
                 left join d_material_verify_relation b on a.id = b.receive_data_id and a.company_id = b.company_id
                 left join d_material_verify c on c.id = b.verify_id and a.company_id = b.company_id
        where a.weigh_id = #{weighId}
          and a.company_id = #{companyId}
          and a.is_deleted = 0
          and c.status = 0
    </select>

    <select id="selectFeedBack" resultType="cn.pinming.microservice.material.management.biz.dto.FeedBackDTO">
        select a.ratio,a.weight_send,a.document_pic,a.id,a.receive_id,a.supplier_id,a.material_id,a.weight_unit,a.supplier_name,a.weight_net
             ,b.order_no,a.purchase_order_id,a.contract_detail_id,a.position,a.weight_gross,a.weight_tare,a.weight_deduct,c.truck_no,a.moisture_content
             ,a.actual_count,a.deviation_rate,a.weight_actual
        from d_material_data a
                 left join d_purchase_order b on a.purchase_order_id = b.id and b.is_deleted = 0
                 left join d_material_send_receive c on c.id = a.receive_id and c.is_deleted = 0
        where a.weigh_id = #{weighId}
          and a.is_deleted = 0
          and a.weight_gross is not null
    </select>

    <select id="selectByOrderId"
            resultType="cn.pinming.microservice.material.management.biz.vo.MaterialDetailVO">
        select a.id,
               a.material_name,
               a.material_id,
               a.category_name,
               b.receive_no,
               b.truck_no,
               c.material_spec,
               a.weight_gross,
               a.weight_tare,
               a.weight_deduct,
               a.weight_actual,
               a.ratio,
               a.weight_send,
               a.actual_count,
               a.actual_receive,
               a.actual_count - weight_send as deviationCount,
               a.deviation_rate,
               a.deviation_status,
               a.enter_time,
               a.leave_time
        from
                (select * from d_material_data where purchase_order_id = #{id}) a
                    inner join d_material_send_receive b on a.receive_id = b.id and type = 1 and a.is_deleted = 0
                    left join d_purchase_contract_detail c on a.contract_detail_id = c.id
        order by material_id, enter_time desc

    </select>


    <select id="receiveDataRelation" resultType="java.lang.Integer">
        select count(t.id) from (select * from d_material_data where purchase_order_id = #{id} and is_deleted = 0) t
                                    inner join (select * from d_material_send_receive where type = 1 and is_deleted = 0) t1 on t.receive_id = t1.id
    </select>

    <select id="selectNeedPushDataIds" resultType="java.lang.String">
        select b.id
        from (select * from d_project_ext_code where is_deleted = 0 and ext_code is not null) a
                 inner join d_material_data b on a.project_id = b.project_id and b.is_deleted = 0 and b.push_state = 0
    </select>
    <select id="receiveDataRelationList"
            resultType="cn.pinming.microservice.material.management.biz.dto.ReceiveDataRelationDTO">
        select b.purchase_order_id as orderId, count(b.id) as `count`
        from d_material_send_receive a
        left join d_material_data b
        on a.id = b.receive_id and a.company_id = b.company_id and a.project_id = b.project_id
        and a.is_deleted = b.is_deleted and a.type = 1
        where a.company_id = #{companyId}
        and b.purchase_order_id in
        <foreach collection="orderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by b.purchase_order_id
    </select>

    <select id="sendCompany" resultType="string">
        select distinct supplier_name
        from d_material_data
        where company_id = #{companyId}
        <if test="projectId != null and projectId != ''">
            and project_id = #{projectId}
        </if>
        and receive_mode is null
        and supplier_name is not null
        and material_validity = 1
        and is_deleted = 0
    </select>

    <select id="selectThreshold" resultType="cn.pinming.microservice.material.management.biz.dto.ThresholdDTO">
        select b.deviation_floor,b.deviation_ceiling
        from d_material_data a
        left join d_purchase_contract_detail b on a.contract_detail_id = b.id and b.is_deleted = 0
        where a.id = #{id}
    </select>

    <select id="queryPlantDeduct" resultType="cn.pinming.microservice.material.management.biz.dto.PlantDeductDTO">
        SELECT
            t1.material_id materialId,
            sum( if(t1.weight_deduct is null, 0, if(t1.weight_unit = '吨', t1.weight_deduct * 1000, t1.weight_deduct)) ) weightDeduct
        FROM
            (
                <foreach collection="ids" separator=" union all " item="id">
                    SELECT #{id} id
                </foreach>
            ) t
                LEFT JOIN d_material_data t1 ON t.id = t1.id
                AND t1.is_deleted = 0
        GROUP BY
            t1.material_id
    </select>

    <select id="selectVerifyByDataId" resultType="int">
        select count(*)
        from d_material_data a
        left join d_material_verify_relation b on a.id = b.receive_data_id and a.company_id = b.company_id
        left join d_material_verify c on c.id = b.verify_id and a.company_id = b.company_id
        where a.id = #{id}
        and a.company_id = #{companyId}
        and a.is_deleted = 0
        and c.status = 0
    </select>

    <select id="selectContractByIds" resultType="cn.pinming.microservice.material.management.biz.dto.ContractMapDTO">
        select a.id,b.contract_id
        from d_material_data a
        left join d_purchase_contract_detail b on b.id = a.contract_detail_id and b.is_deleted = 0
        where a.id in
        <foreach collection="ids" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
        and a.is_deleted = 0
    </select>

    <select id="needPushDataList"
            resultType="cn.pinming.microservice.material.management.biz.entity.MaterialData">
        select a.*
        from d_material_data a
                 left join d_material_send_receive b
                           on a.receive_id = b.id
        where a.is_deleted = 0 and a.project_id = #{projectId} and a.company_id = #{companyId}
          and push_state = 0 and b.type = 1
    </select>

    <select id="mobileHistoryByType" resultType="cn.pinming.microservice.material.management.biz.entity.MaterialData">
        select a.*
        from d_material_data a
        left join d_material_send_receive b on b.id = a.receive_id and b.is_deleted = 0
        where a.is_deleted = 0
        and a.company_id = #{companyId}
        and a.project_id = #{projectId}
        and b.type = #{type}
        <if test="type == 1">
            and a.receive_mode in (1,2)
        </if>
        <if test="receiveType != null and receiveType != ''">
            <if test="receiveType == 2">
                and a.purchase_order_id is not null
            </if>
            <if test="receiveType == 1">
                and a.contract_detail_id is not null
                and a.purchase_order_id is  null
            </if>
        </if>
        order by a.update_time desc
        limit 1
    </select>
    <select id="selectDataIdsByRecycleId"
            resultType="cn.pinming.microservice.material.management.biz.dto.RecycleDataIdDTO">
        SELECT confirm_ext_no as recycleId, b.id as dataId, b.receive_id
        FROM d_material_send_receive a
        left join d_material_data b on a.id = b.receive_id
        WHERE a.is_deleted = 0
        and b.is_deleted = 0
        and a.project_id = #{projectId}
        and confirm_ext_no in
        <foreach collection="ids" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>

    </select>
    <select id="getCargoFromPurchase"
            resultType="cn.pinming.microservice.material.management.biz.dto.CargoDetailInfoDTO">
        select b.material_id,b.category_id,c.supplier_id,b.deviation_floor,b.deviation_ceiling,b.deviation_calculate
        from d_purchase_order_detail a
        left join d_purchase_contract_detail b on b.id = a.contract_detail_id
        left join d_purchase_contract c on c.id = b.contract_id
        where a.id = #{cargoId}
    </select>
    <select id="getCargoFromContract"
            resultType="cn.pinming.microservice.material.management.biz.dto.CargoDetailInfoDTO">
        select a.material_id,a.category_id,b.supplier_id,a.deviation_floor,a.deviation_ceiling,a.deviation_calculate
        from d_purchase_contract_detail a
        left join d_purchase_contract b on b.id = a.contract_id
        where a.id = #{cargoId}
    </select>
    <select id="getCargo" resultType="cn.pinming.microservice.material.management.biz.dto.CargoDetailInfoDTO">
        select b.material_id,b.category_id,c.supplier_id,b.deviation_floor,b.deviation_ceiling,b.deviation_calculate
        from d_purchase_order_detail a
        left join d_purchase_contract_detail b on b.id = a.contract_detail_id
        left join d_purchase_contract c on c.id = b.contract_id
        where a.id = #{cargoId}

        union all

        select k.material_id,k.category_id,g.supplier_id,j.deviation_floor,j.deviation_ceiling,j.deviation_calculate
        from d_mixing_plant_order_detail k
        left join d_purchase_order_detail f on f.id = k.purchase_order_detail_id
        left join d_purchase_contract_detail j on j.id = f.contract_detail_id
        left join d_purchase_contract g on g.id = j.contract_id
        where k.id = #{cargoId}

    </select>
    <select id="getCargoFromMixing"
            resultType="cn.pinming.microservice.material.management.biz.dto.CargoDetailInfoDTO">
        select k.material_id,k.category_id,g.supplier_id,j.deviation_floor,j.deviation_ceiling,j.deviation_calculate
        from d_mixing_plant_order_detail k
        left join d_purchase_order_detail f on f.id = k.purchase_order_detail_id
        left join d_purchase_contract_detail j on j.id = f.contract_detail_id
        left join d_purchase_contract g on g.id = j.contract_id
        where k.id = #{cargoId}

    </select>
</mapper>
