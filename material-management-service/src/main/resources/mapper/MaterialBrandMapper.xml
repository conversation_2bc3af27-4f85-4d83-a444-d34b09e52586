<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MaterialBrandMapper">


    <select id="selectNameByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.MaterialBrandVO">
        select id, brand
        from s_material_brand
        where is_deleted = 0
        <if test="projectId !=null">
            and project_id = #{projectId}
        </if>
        <if test="categoryId !=null">
            and category_id = #{categoryId}
        </if>
        order by create_time desc
    </select>
</mapper>
