<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.PurchaseContractMapper">

    <select id="selectPageDTO" resultType="cn.pinming.microservice.material.management.biz.dto.PurchaseContractDTO">
        select id, belong_project_id, name, no, type, supplier_id,create_time,category
        from d_purchase_contract
        where is_deleted = 0
        and company_id = #{query.companyId}
        <if test="query.name !=null and query.name !=''">
            and name like concat('%',#{query.name},'%')
        </if>
        <if test="query.no !=null and query.no !=''">
            and no like concat('%',#{query.no},'%')
        </if>
        <if test="query.type !=null">
            and type = #{query.type}
        </if>
        <if test="query.supplierId !=null">
            and supplier_id = #{query.supplierId}
        </if>
        <if test="query.getBelongProjectId !=null and query.getBelongProjectId.size() > 0">
            and belong_project_id regexp #{query.pjIds}
        </if>
        <if test="query.projectId != null">
            and belong_project_id regexp #{query.projectId}
        </if>
        order by create_time desc
    </select>

    <select id="selectBelongProjects" resultType="java.lang.String">
        select belong_project_id
        from d_purchase_contract
        where id = #{id}
          and is_deleted = 0
    </select>

    <select id="selectContract" resultType="cn.pinming.microservice.material.management.biz.entity.PurchaseContract">
        select id,name,supplier_id from d_purchase_contract
        where is_deleted = 0
        <if test="projectId != null">
            and find_in_set(#{projectId},belong_project_id)
        </if>
    </select>
    <select id="selectContractAndExtCode" resultType="cn.pinming.microservice.material.management.biz.entity.PurchaseContract">
        select * from d_purchase_contract
        where is_deleted = 0 and ext_code is not null
        <if test="projectId != null">
            and find_in_set(#{projectId},belong_project_id)
        </if>
        <if test="extCodeList != null and extCodeList.size > 0">
            and ext_code in <foreach collection="extCodeList" item="extCode" open="(" close=")" separator=",">#{extCode}</foreach>
        </if>
    </select>
    <select id="selectOrderStatus" resultType="cn.pinming.microservice.material.management.biz.vo.PurchaseOrderStatusVO">
        select status, count(status) as num
        from d_purchase_order
        where is_deleted = 0
        <if test="projectIds != null and projectIds.size > 0">
            and receiver_project in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator="," index="">
                #{projectId}
            </foreach>
        </if>
        group by status
    </select>
    <select id="selectSupplierIdsById"
            resultType="cn.pinming.microservice.material.management.biz.dto.SimpleSupplierDTO">
        select distinct supplier_id from d_purchase_contract
        where company_id = #{companyId}
        and is_deleted = 0
        <if test="projectId!=null">
             and find_in_set(#{projectId},belong_project_id)
        </if>
        and supplier_id is not null
    </select>

    <select id="getContract" resultType="cn.pinming.microservice.material.management.biz.entity.PurchaseContract">
        select *
        from d_purchase_contract a
        where a.id = #{id}
        and a.is_deleted = 0
    </select>

    <update id="updateInfo">
        update d_purchase_contract a
        set a.belong_project_id = #{belongProjectId}
        , a.name = #{name}
        , a.no = #{no}
        , a.type = #{type}
        , a.supplier_id = #{supplierId}
        , a.create_id = #{createId}
        , a.update_id = #{updateId}
        where a.id = #{id}
        and a.is_deleted = 0
    </update>

    <select id="listBySupplierId" resultType="cn.pinming.microservice.material.management.biz.dto.PurchaseContractDTO">
        select a.id,a.type,a.supplier_id,a.no,a.name,a.create_id,a.create_time,a.belong_project_id,a.is_push
        from d_purchase_contract a
        where a.company_id = #{companyId}
        <if test="projectId != null and projectId != ''">
            and find_in_set(#{projectId},belong_project_id)
        </if>
        <if test="query.supplierId != null and query.supplierId != ''">
            and a.supplier_id = #{param1.supplierId}
        </if>
        <if test="query.contractName != null and query.contractName != ''">
            and a.name like concat('%',#{param1.contractName},'%')
        </if>
        and a.is_deleted = 0
        order by a.create_time
    </select>

    <select id="selectMaterial" resultType="cn.pinming.microservice.material.management.biz.dto.MaterialSimpleDTO">
        select a.deviation_floor,a.deviation_ceiling
        from d_purchase_contract_detail a
        where a.contract_id = #{contractId}
        and a.material_id = #{materialId}
        and a.is_deleted = 0
    </select>

    <select id="listForRevise" resultType="cn.pinming.microservice.material.management.biz.vo.ContractForReviseVO">
        select id as contractId,name as contractName
        from d_purchase_contract
        where company_id = #{companyId}
        and is_deleted = 0
        and supplier_id = #{supplierId}
        <if test="projectId!=null">
            and find_in_set(#{projectId},belong_project_id)
        </if>
        order by create_time
    </select>

    <select id="listUsedSupplierId" resultType="java.lang.Integer">
        select distinct supplier_id
        from d_purchase_contract
        where is_deleted = 0
    </select>

    <select id="appchoose" resultType="cn.pinming.microservice.material.management.biz.vo.AppContractChooseVO">
        select a.id as contractDetailId,a.material_id,a.unit as weightUnit,a.conversion_rate,a.deviation_floor,a.deviation_ceiling,b.`name` as contractName,b.supplier_id
        from d_purchase_contract_detail a
        left join d_purchase_contract b on b.id = a.contract_id and b.is_deleted = 0
        where a.contract_id = #{contractId}
        and a.category_id = #{categoryId}
        and a.is_deleted = 0
    </select>

    <select id="selectContractAuto"
            resultType="cn.pinming.microservice.material.management.biz.entity.PurchaseContract">
            select *
            from d_purchase_contract a
            where a.belong_project_id is not null
            and a.is_deleted = 0
            and a.id not in
            (
                select b.contract_id
                from d_material_verify b
                where b.is_deleted = 0
                and b.is_origin = 1
            )
    </select>

    <select id="ocrCheck" resultType="cn.pinming.microservice.material.management.biz.entity.PurchaseContract">
        select *
        from d_purchase_contract
        where company_id = #{companyId}
          and is_deleted = 0
          and id = #{id}
          and find_in_set(#{projectId},belong_project_id)
    </select>
</mapper>
