<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MaterialWarningMapper">
    <select id="selectPageByQuery" resultType="cn.pinming.microservice.material.management.biz.dto.WarningInfoDTO" parameterType="cn.pinming.microservice.material.management.biz.query.WarningInfoQuery">

        select warning_source_no, source_project_id as sourceProjectId, min(warning_source) as warningSource, group_concat(warning_type) as warningType
        ,min(create_time) as warningTime, max(handler_name) as handlerName
        ,max(handler_id) as HandlerId, max(id) as warningId
        ,max(handler_time) as handlerTime, min(warning_status) as warningStatus
        ,min(warning_source_id) as warningSourceId
        from d_material_warning
        where is_deleted = 0
        and company_id = #{query.companyId}
        <if test="query.warningSourceNo !=null and query.warningSourceNo !=''">
            and warning_source_no like concat('%', #{query.warningSourceNo},'%')
        </if>
        group by warning_source_no, source_project_id
        having  1 = 1
        <if test="query.warningType != null">
            and find_in_set(#{query.warningType},group_concat(warning_type))
        </if>
        <if test="query.warningStatus != null and query.warningStatus != 0">
            and min(warning_status) = #{query.warningStatus}
        </if>
        <if test="query.warningSource !=null and query.warningSource !=''">
            and min(warning_source) = #{query.warningSource}
        </if>
        <if test="query.sourceProjectIds !=null and query.sourceProjectIds.size > 0">
            and min(source_project_id) in
            <foreach collection="query.sourceProjectIds" item="sourceProjectId" open="(" close=")" separator=",">
                #{sourceProjectId}
            </foreach>
        </if>
        <if test="query.startTime != null  and query.endTime != null">
            and max(create_time) <![CDATA[ >= ]]> #{query.startTime}
            and max(create_time) <![CDATA[ <= ]]> #{query.endTime}
        </if>
        order by max(warning_status) asc, min(create_time) desc
    </select>

    <select id="listSummaryWarningByQuery" resultType="cn.pinming.microservice.material.management.biz.vo.SummaryWarningVO">

        SELECT
        warning_type as type,
        sum( CASE warning_status WHEN 1 THEN 1 ELSE 0 END ) as unHandleCount,
        sum( CASE warning_status WHEN 2 THEN 1 ELSE 0 END ) as handleCount,
        sum( CASE warning_status WHEN 1 THEN 1 WHEN 2 THEN 1 ELSE 0 END ) as total
        FROM
        d_material_warning where is_deleted = 0 and company_id = #{query.companyId}
        and ((find_in_set(warning_type, (select warning_type from d_material_warning_config where company_id = #{query.companyId})) = 0
        or (find_in_set(warning_type, (select warning_type from d_material_warning_config where company_id = #{query.companyId})) is null)))
        <if test="query.startDate != null">
            and date_format(create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        <if test="query.projectIdList != null and query.projectIdList.size != 0">
            and project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        GROUP BY
        warning_type
        order by unHandleCount desc

    </select>
    <select id="listSummaryWarningSecondByQuery"
            resultType="cn.pinming.microservice.material.management.biz.vo.SummaryWarningSecondVO">
        SELECT
        warning_type as type,
        sum( CASE warning_status WHEN 1 THEN 1 ELSE 0 END ) as unHandleCount,
        sum( CASE warning_status WHEN 2 THEN 1 ELSE 0 END ) as handleCount
        FROM
        d_material_warning where  company_id = #{query.companyId} and is_deleted = 0
        and ((find_in_set(warning_type, (select warning_type from d_material_warning_config where company_id = #{query.companyId})) = 0
        or (find_in_set(warning_type, (select warning_type from d_material_warning_config where company_id = #{query.companyId})) is null)))
        <if test="query.startDate != null">
            and date_format(create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and date_format(create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{query.endDate}
        </if>
        <if test="query.projectIdList != null and query.projectIdList.size != 0">
            and project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        GROUP BY
        warning_type

    </select>
    <select id="queryWarningSummary"
            resultType="cn.pinming.microservice.material.management.biz.vo.WarningSummaryAnalysisVO">
        select count(t1.warning_source_no)                                            as warningTotal,
        count(if(t1.warningStatus = 1, 1, null))                                      as warningUnHandleCount,
        count(if(date_format(t1.time, '%Y%m') = date_format(now(), '%Y%m'), 1, null)) aS monthlyCount
        from (select warning_source_no, min(warning_status) as warningStatus, min(create_time) as time
        from d_material_warning
        where is_deleted = 0
        and company_id = #{query.companyId}
        and ((find_in_set(warning_type, (select warning_type from d_material_warning_config where company_id = #{query.companyId})) = 0
        or (find_in_set(warning_type, (select warning_type from d_material_warning_config where company_id = #{query.companyId})) is null)))
        <if test="query.projectIdList != null and query.projectIdList.size != 0">
            and project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by warning_source_no) as t1
    </select>

    <select id="queryOverviewWarningSummary"
            resultType="cn.pinming.microservice.material.management.biz.dto.WarningOverviewSecondDTO">
        select count(*)                              total,
        count(if(warningStatus = 1, 1, null)) unHandle,
        date_format(create_time, '%Y/%m')     time
        from (select warning_source_no,
        min(warning_status) as warningStatus,
        min(create_time)    as create_time
        from d_material_warning
        where is_deleted = 0
        and company_id = #{query.companyId}
        <if test="query.projectIdList != null and query.projectIdList.size != 0">
            and project_id in
            <foreach collection="query.projectIdList" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and ((find_in_set(warning_type, (select warning_type from d_material_warning_config where company_id = #{query.companyId})) = 0
        or (find_in_set(warning_type, (select warning_type from d_material_warning_config where company_id = #{query.companyId})) is null)))
        group by warning_source_no) t1
        group by date_format(create_time, '%Y/%m')
        order by time asc
    </select>

    <select id="queryOverviewWarningTips" resultType="java.lang.Integer">
        select count(distinct warning_source_id) as count
        from d_material_warning
        where is_deleted = 0 and company_id = #{companyId} and warning_status = 1
        <if test="projectIds != null and projectIds.size > 0">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        and warning_type in
        <foreach collection="warningTypeList" item="warningType" open="(" close=")" separator=",">
            #{warningType}
        </foreach>
    </select>

    <select id="queryAllMaterialWarning" resultType="cn.pinming.microservice.material.management.biz.entity.MaterialWarning">
        select * from d_material_warning where is_deleted = 0 and warning_status = 1
        and
        <foreach collection="types" item="item" open="(" close=")" separator=" or " index="">
            warning_type = #{item.type} and warning_sub_type in
            <foreach collection="item.subTypes" item="subType" open="(" close=")" separator="," index="">
                #{subType}
            </foreach>
        </foreach>
    </select>

    <select id="weighConvertOne" resultType="java.lang.String">
        SELECT
        id
        FROM
        (
        SELECT
        t.id,
        IF
        ( t2.ratio is not null and t3.conversion_rate is not null and t2.ratio = t3.conversion_rate, TRUE, FALSE ) auto
        FROM
        (<foreach collection="ids" item="id" separator=" union all ">
        SELECT #{id} id
    </foreach>) t
        LEFT JOIN (select * from d_material_warning where is_deleted = 0) t1 ON t.id = t1.id
        LEFT JOIN (select * from d_material_data where is_deleted = 0) t2 ON t1.warning_source_id = t2.id
        LEFT JOIN (select * from d_purchase_contract_detail where is_deleted = 0) t3 ON t2.contract_detail_id = t3.id
        ) t4
        WHERE
        auto = TRUE
    </select>

    <select id="weighConvertTwo" resultType="java.lang.String">
        SELECT
        id
        FROM
        (
        SELECT
        t.id,
        IF
        ( t2.ratio is not null and t2.ratio != '', TRUE, FALSE ) auto
        FROM
        (<foreach collection="ids" item="id" separator=" union all ">
        SELECT #{id} id
    </foreach>) t
        LEFT JOIN (select * from d_material_warning where is_deleted = 0) t1 ON t.id = t1.id
        LEFT JOIN (select * from d_material_data where is_deleted = 0) t2 ON t1.warning_source_id = t2.id
        LEFT JOIN (select * from d_purchase_contract_detail where is_deleted = 0) t3 ON t2.contract_detail_id = t3.id
        ) t4
        WHERE
        auto = TRUE
    </select>

    <select id="receiveNumberOne" resultType="java.lang.String">
        SELECT
        id
        FROM
        (
        SELECT
        t.id,
        IF
        ( t2.weight_send is not null and t2.weight_send != '', TRUE, FALSE ) auto
        FROM
        (<foreach collection="ids" item="id" separator=" union all ">
        SELECT #{id} id
    </foreach>) t
        LEFT JOIN (select * from d_material_warning where is_deleted = 0) t1 ON t.id = t1.id
        LEFT JOIN (select * from d_material_data where is_deleted = 0) t2 ON t1.warning_source_id = t2.id
        ) t3
        WHERE
        auto = TRUE
    </select>

    <select id="chargeUnitOne" resultType="java.lang.String">
        SELECT
        id
        FROM
        (
        SELECT
        t.id,
        IF
        ( t2.weight_unit is not null and t3.unit is not null and t2.weight_unit = t3.unit, TRUE, FALSE ) auto
        FROM
        (<foreach collection="ids" item="id" separator=" union all ">
        SELECT #{id} id
    </foreach>) t
        LEFT JOIN (select * from d_material_warning where is_deleted = 0) t1 ON t.id = t1.id
        LEFT JOIN (select * from d_material_data where is_deleted = 0) t2 ON t1.warning_source_id = t2.id
        LEFT JOIN (select * from d_purchase_contract_detail where is_deleted = 0) t3 ON t2.contract_detail_id = t3.id
        ) t4
        WHERE
        auto = TRUE
    </select>

    <select id="invalidWeightOne" resultType="java.lang.String">
        SELECT
        id
        FROM
        (
        SELECT
        t.id,
        IF
        ( t2.material_id is not null and t3.material_id is not null and t2.material_id = t3.material_id, TRUE, FALSE ) auto
        FROM
        (<foreach collection="ids" item="id" separator=" union all ">
        SELECT #{id} id
    </foreach>) t
        LEFT JOIN (select * from d_material_warning where is_deleted = 0) t1 ON t.id = t1.id
        LEFT JOIN (select * from d_material_data where is_deleted = 0) t2 ON t1.warning_source_id = t2.id
        LEFT JOIN (select * from d_purchase_contract_detail where is_deleted = 0) t3 ON t2.contract_detail_id = t3.id
        ) t4
        WHERE
        auto = TRUE
    </select>

    <select id="materialNameOne" resultType="cn.pinming.microservice.material.management.biz.dto.MaterialNameOneDTO">
        SELECT
        t.id,
        t2.material_id materialId
        FROM
        (<foreach collection="ids" item="id" separator=" union all ">
        SELECT #{id} id
    </foreach>) t
        LEFT JOIN (select * from d_material_warning where is_deleted = 0) t1 ON t.id = t1.id
        LEFT JOIN (select * from d_material_data where is_deleted = 0) t2 ON t1.warning_source_id = t2.id
    </select>

    <select id="listWarningDetail"
            resultType="cn.pinming.microservice.material.management.biz.dto.WarningDetailDTO">
        select warning_type, warning_info
        from d_material_warning
        where warning_source_no = #{query.warningSourceNo}
        and company_id = #{query.companyId}
        and project_id = #{query.projectId}
    </select>

</mapper>
