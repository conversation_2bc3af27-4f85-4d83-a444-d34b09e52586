<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.PreMaterialReportMapper">

    <update id="updateEntity">
        update d_pre_material_report a
        set a.brand = #{param1.brand}
        , a.count = #{param1.count}
        ,a.conversion_rate = #{param1.conversionRate}
        where a.purchase_order_detail_id = #{param1.purchaseOrderDetailId}
        and a.pre_weigh_report_id = #{id}
        and a.is_deleted = 0
    </update>

    <select id="selectId" resultType="java.lang.String">
        select id
        from d_pre_material_report
        where pre_weigh_report_id = #{preWeighReportId}
        and is_deleted = 0
    </select>
</mapper>
