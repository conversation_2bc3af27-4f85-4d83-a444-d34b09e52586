<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.PurchaseContractDetailMapper">

    <select id="selectByContractIds" resultType="cn.pinming.microservice.material.management.biz.dto.ContractDetailDTO">
        select contract_id, group_concat(distinct category_name, '/', material_name separator '、') as type
        from d_purchase_contract_detail
        where is_deleted = 0
        <if test="contractIds != null and contractIds.size() > 0">
            and contract_id in
            <foreach collection="contractIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        group by contract_id
    </select>


    <select id="selectByContractDetailIds"
            resultType="cn.pinming.microservice.material.management.biz.dto.ContractDetailDTO">
        select group_concat(distinct category_name, '/', material_name separator '、') as type
        from d_purchase_contract_detail
        where is_deleted = 0
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectUnitById" resultType="java.lang.String">
        select distinct unit
        from d_purchase_contract_detail a
                 left join d_purchase_contract b on a.contract_id = b.id and b.is_deleted = 0
        where a.company_id = #{companyId}
        and a.is_deleted = 0
          and find_in_set(#{projectId}, b.belong_project_id)

    </select>

    <select id="count" resultType="int">
        select count(*)
        from d_purchase_contract_detail a
        where a.contract_id = #{contractId}
        <if test="ids != null and ids.size() > 0">
            and a.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        and is_deleted = 0
    </select>

    <select id="list" resultType="cn.pinming.microservice.material.management.biz.entity.PurchaseContractDetail">
        select *
        from d_purchase_contract_detail a
        where a.contract_id = #{id}
          and a.is_deleted = 0
    </select>

    <select id="listRemoveIdsById" resultType="string">
        select a.id
        from d_purchase_contract_detail a
        where a.contract_id = #{contractId}
        <if test="ids != null and ids.size() > 0">
            and a.material_id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        and a.is_deleted = 0
    </select>

    <update id="remove">
        update d_purchase_contract_detail a
        set a.is_deleted = 1
        where a.is_deleted = 0
        <if test="ids != null and ids.size() > 0">
            and a.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </update>

    <select id="listMaterial"
            resultType="cn.pinming.microservice.material.management.biz.dto.ContractMaterialDetailDTO">
        select a.id as contractDetailId,
               a.material_id,
               a.category_id,
               a.category_name,
               a.material_name,
               a.material_spec,
               a.brand,
               a.unit,
               a.conversion_rate as ratio,
               a.deviation_ceiling,
               a.deviation_floor
        from d_purchase_contract_detail a
        where a.contract_id = #{contractId}
          and a.is_deleted = 0
    </select>

    <select id="selectInfo" resultType="cn.pinming.microservice.material.management.biz.vo.ContractMaterialVO">
        select a.material_code, a.material_name, a.material_spec, a.brand as contractBrand
        from d_purchase_contract_detail a
        where a.contract_id = #{contractId}
          and a.material_id = #{materialId}
          and a.is_deleted = 0
    </select>

    <select id="selectThreshold" resultType="cn.pinming.microservice.material.management.biz.dto.ContractDecisionDTO">
        select a.deviation_floor, a.deviation_ceiling,b.supplier_id,c.name as supplierName
        from d_purchase_contract_detail a
                 left join d_purchase_contract b on b.id = a.contract_id and b.is_deleted = 0
                 left join s_supplier c on c.id = b.supplier_id and c.is_deleted = 0
        where a.id = #{contractDetailId}
          and a.is_deleted = 0
    </select>

    <select id="selectContractDetails"
            resultType="cn.pinming.microservice.material.management.biz.vo.PurchaseContractDetailVO">
        select distinct a.id as contractDetailId,a.category_id,a.category_name,a.material_id,a.material_code,a.material_name,a.material_spec,a.unit,a.transform_unit,a.conversion_rate,a.deviation_ceiling,a.deviation_floor,a.brand as brandStr
        ,(case when b.id is null then 2 else 1 end ) as isEdit
        from d_purchase_contract_detail a
        left join d_purchase_order_detail b on b.contract_detail_id = a.id and b.is_deleted = 0
        where a.contract_id = #{id}
        and a.is_deleted = 0
    </select>

    <select id="selectByContractDetailIdList"
            resultType="cn.pinming.microservice.material.management.biz.dto.ContractDecisionDTO">
        select id as contractDetailId,material_id,unit as settlementUnit,conversion_rate,deviation_ceiling,deviation_floor,contract_id
        from d_purchase_contract_detail
        where id in
        <foreach collection="contractDetailIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and is_deleted = 0
    </select>

    <select id="history" resultType="cn.pinming.microservice.material.management.biz.vo.GoodsSimpleVO">
        select a.material_id,a.category_id,a.category_name,a.material_name,a.material_spec,a.unit,a.conversion_rate,a.deviation_floor,a.deviation_ceiling,a.id as contractDetailId
        from d_purchase_contract_detail a
        left join d_purchase_contract b on a.contract_id = b.id
        where b.id = #{contractId}
        and a.is_deleted = 0
        and b.is_deleted = 0
    </select>

    <select id="ocrFilter" resultType="cn.pinming.microservice.material.management.biz.entity.PurchaseContractDetail">
        select material_id,material_name,material_spec,conversion_rate
        from d_purchase_contract_detail
        where contract_id = #{contractId}
        <if test="name != null and name != ''">
            and material_name like concat('%',#{name},'%')
        </if>
        <if test="spec != null and spec != ''">
            and material_spec like concat('%',#{spec},'%')
        </if>
        and is_deleted = 0
    </select>
</mapper>
