<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MixingPlantOrderMapper">

    <select id="plantOrderCount" resultType="java.lang.Long">
        SELECT
            count(*)
        FROM
            (
                SELECT
                    t1.order_no,
                    t1.receiver_project,
                    t.material_id,
                    t.parameter_requirements,
                    t.count,
                    t.unit,
                    t1.create_time,
                    t1.receive_time,
                    t.STATUS,
                    t.finish_time
                FROM
                        (
                            select p.* from (SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0
                                <if test="status != null and status != ''">
                                    AND status = #{status}
                                </if>
                            ) p inner join
                            (SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0
                                <if test="projectId != null and projectId != ''">
                                    AND plant_id = #{projectId}
                                </if>
                            ) p1 on p.mixing_plant_order_id = p1.id
                        ) t
                        LEFT JOIN (select * from d_mixing_plant_order WHERE is_deleted = 0) t1 ON t.mixing_plant_order_id = t1.id
            ) t2
    </select>

    <select id="plantOrderPage" resultType="cn.pinming.microservice.material.management.biz.vo.MixingPlantOrderVO">
        SELECT
            t2.*
        FROM
            (
                SELECT
                    t.id plantOrderDetailId,
                    t1.order_no plantOrderNo,
                    IF
                        ( t1.purchase_order_id IS NOT NULL, p3.receiver_project, t1.receiver_project ) receiverProject,
                    t1.receiver_project_title receiverProjectTitle,
                    t.material_id materialId,
                    t.category_id categoryId,
                    t.parameter_requirements parameterRequirements,
                    t.count,
                    t.unit,
                    t1.create_time orderTime,
                    IF
                        ( t1.purchase_order_id IS NOT NULL, p3.receive_time, t1.receive_time ) receiveTime,
                    t.STATUS,
                    p3.order_no purchaseOrderNo,
                    t.finish_time finishTime
                FROM
                    (
                        SELECT
                            p1.mixing_plant_order_id,
                            p1.id,
                            p1.STATUS,
                            p1.finish_time,
                            IF
                                ( p1.purchase_order_detail_id IS NOT NULL, c.material_id, p1.material_id ) material_id,
                            IF
                                ( p1.purchase_order_detail_id IS NOT NULL, c.category_id, p1.category_id ) category_id,
                            IF
                                ( p1.purchase_order_detail_id IS NOT NULL, p2.parameter_requirements, p1.parameter_requirements ) parameter_requirements,
                            IF
                                ( p1.purchase_order_detail_id IS NOT NULL, p2.count, p1.count ) count,
                            IF
                                ( p1.purchase_order_detail_id IS NOT NULL, p2.unit, p1.unit ) unit
                        FROM
                            (
                                select t.* from (SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0
                                    <if test="status != null and status != ''">
                                        AND status = #{status}
                                    </if>
                                ) t inner join
                                (SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0
                                    <if test="projectId != null and projectId != ''">
                                        AND plant_id = #{projectId}
                                    </if>
                                ) t1 on t.mixing_plant_order_id = t1.id
                            ) p1
                            LEFT JOIN (select * from d_purchase_order_detail WHERE is_deleted = 0) p2 ON p1.purchase_order_detail_id = p2.id
                            LEFT JOIN (select * from d_purchase_contract_detail WHERE is_deleted = 0) c ON p2.contract_detail_id = c.id
                    ) t
                        LEFT JOIN (select * from d_mixing_plant_order WHERE is_deleted = 0) t1 ON t.mixing_plant_order_id = t1.id
                        LEFT JOIN (select * from d_purchase_order WHERE is_deleted = 0) p3 ON t1.purchase_order_id = p3.id
            ) t2
        ORDER BY
            t2.receiveTime DESC
            LIMIT #{idxStart}, #{offset}
    </select>

    <select id="queryOrderUpdateVO" resultType="cn.pinming.microservice.material.management.biz.vo.MixingPlantOrderUpdateVO">
        SELECT
            t1.id,
            t1.company_id companyId,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.receiver_project, t1.receiver_project ) receiverProject,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.receiver_address, t1.receiver_address ) receiverAddress,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.receiver, t1.receiver ) receiver,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.receiver_tel, t1.receiver_tel ) receiverTel,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.receive_time, t1.receive_time ) receiveTime,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.remark, t1.remark ) remark
        FROM  ( SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0 AND id = #{orderId}) t1
        LEFT JOIN (select * from d_purchase_order WHERE is_deleted = 0) p3 ON t1.purchase_order_id = p3.id
    </select>

    <select id="queryOrderDetailUpdateVO" resultType="cn.pinming.microservice.material.management.biz.vo.MixingPlantOrderDetailUpdateVO">
        SELECT
            t.id,
            t.material_id materialId,
            t.category_id categoryId,
            t.parameter_requirements parameterRequirements,
            t.count,
            t.unit,
            t.position
        FROM
            (
                SELECT
                    p1.id,
                    IF
                        ( p1.purchase_order_detail_id IS NOT NULL, c.material_id, p1.material_id ) material_id,
                    IF
                        ( p1.purchase_order_detail_id IS NOT NULL, c.category_id, p1.category_id ) category_id,
                    IF
                        ( p1.purchase_order_detail_id IS NOT NULL, p2.parameter_requirements, p1.parameter_requirements ) parameter_requirements,
                    IF
                        ( p1.purchase_order_detail_id IS NOT NULL, p2.count, p1.count ) count,
                    IF
                        ( p1.purchase_order_detail_id IS NOT NULL, p2.unit, p1.unit ) unit ,
                    IF
                        ( p1.purchase_order_detail_id IS NOT NULL, p2.remark, p1.position ) position
                FROM
                    ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 AND mixing_plant_order_id = #{orderId}) p1
                LEFT JOIN (select * from d_purchase_order_detail WHERE is_deleted = 0) p2 ON p1.purchase_order_detail_id = p2.id
                LEFT JOIN (select * from d_purchase_contract_detail WHERE is_deleted = 0) c ON p2.contract_detail_id = c.id
            ) t
    </select>

    <select id="queryOrderDetailByOrderId" resultType="cn.pinming.microservice.material.management.biz.vo.MixingPlantOrderDetailVO">
        SELECT
            t1.id,
            t1.company_id companyId,
            t1.order_no orderNo,
            t1.plant_id plantId,
            t1.create_id createId,
            t1.create_time orderDate,
            p3.order_no purchaseOrderNo,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.receiver_project, t1.receiver_project ) receiverProjectId,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.receiver_address, t1.receiver_address ) receiverAddress,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.receiver, t1.receiver ) receiver,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.receiver_tel, t1.receiver_tel ) receiverTel,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.receive_time, t1.receive_time ) receiveTime,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.remark, t1.remark ) remark
        FROM  ( SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0 AND id = #{orderId}) t1
                  LEFT JOIN (select * from d_purchase_order WHERE is_deleted = 0) p3 ON t1.purchase_order_id = p3.id
    </select>

    <select id="queryOrderDetailByDetailId" resultType="cn.pinming.microservice.material.management.biz.vo.MixingPlantOrderDetailVO">
        SELECT
            t1.id,
            t1.company_id companyId,
            t1.order_no orderNo,
            t1.plant_id plantId,
            t1.create_id createId,
            t1.create_time orderDate,
            p3.order_no purchaseOrderNo,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.receiver_project, t1.receiver_project ) receiverProjectId,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.receiver_address, t1.receiver_address ) receiverAddress,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.receiver, t1.receiver ) receiver,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.receiver_tel, t1.receiver_tel ) receiverTel,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.receive_time, t1.receive_time ) receiveTime,
            IF
                ( t1.purchase_order_id IS NOT NULL, p3.remark, t1.remark ) remark
        FROM  ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 AND id = #{orderDetailId}) t
                left join ( SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0) t1 on t.mixing_plant_order_id = t1.id
                  LEFT JOIN (select * from d_purchase_order WHERE is_deleted = 0) p3 ON t1.purchase_order_id = p3.id
    </select>

    <select id="queryOrderDetailItemsById" resultType="cn.pinming.microservice.material.management.biz.vo.MixingPlantOrderDetailItemVO">
        SELECT
            t.id,
            t.status,
            t.material_id materialId,
            t.category_id categoryId,
            t.parameter_requirements parameterRequirements,
            t.count,
            t.unit,
            t.position,
            sum( e.actual_count ) weightSend,
            sum( t3.actual_count ) actualCount,
            sum( t3.weight_send ) weightReceive,
            sum( t3.actual_receive ) actualReceive,
            sum( t3.actual_count - t3.weight_send ) deviationCount,
            round((sum(t3.actual_count)-sum(t3.weight_send))/sum(t3.weight_send)*100, 2) deviationRate
        FROM
            (
                SELECT
                    p1.id,
                    p1.status,
                    IF
                        ( p1.purchase_order_detail_id IS NOT NULL, c.material_id, p1.material_id ) material_id,
                    IF
                        ( p1.purchase_order_detail_id IS NOT NULL, c.category_id, p1.category_id ) category_id,
                    IF
                        ( p1.purchase_order_detail_id IS NOT NULL, p2.parameter_requirements, p1.parameter_requirements ) parameter_requirements,
                    IF
                        ( p1.purchase_order_detail_id IS NOT NULL, p2.count, p1.count ) count,
                    IF
                        ( p1.purchase_order_detail_id IS NOT NULL, p2.unit, p1.unit ) unit,
                    IF
                        ( p1.purchase_order_detail_id IS NOT NULL, p2.remark, p1.position ) position
                FROM
                    ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 AND id = #{orderDetailId} ) p1
                    LEFT JOIN ( SELECT * FROM d_purchase_order_detail WHERE is_deleted = 0 ) p2 ON p1.purchase_order_detail_id = p2.id
                    LEFT JOIN ( SELECT * FROM d_purchase_contract_detail WHERE is_deleted = 0 ) c ON p2.contract_detail_id = c.id
            ) t
                left join ( select * from d_mixing_plant_exit_ticket where is_deleted = 0 ) e on t.id = e.mixing_plant_order_detail_id
                LEFT JOIN ( SELECT * FROM d_pre_weigh_report WHERE is_deleted = 0 ) t1 ON e.id = t1.exit_ticket_id
                LEFT JOIN ( SELECT * FROM d_pre_truck_report_detail WHERE is_deleted = 0 ) t2 ON t1.id = t2.pre_weigh_report_id
                LEFT JOIN ( SELECT * FROM d_material_data WHERE is_deleted = 0 ) t3 ON t2.material_data_id = t3.id
        group by t.id
    </select>

    <select id="queryPlantOrderSendDetailsById" resultType="cn.pinming.microservice.material.management.biz.vo.MixingPlantOrderSendDetailVO">
        SELECT
            t.id,
            t.material_id materialId,
            t.ticket_no ticketNo,
            t.truck_no truckNo,
            t.actual_count weightSendCount,
            t.create_time sendTime,
            t3.weight_gross weightGross,
            t3.weight_tare weightTare,
            t3.weight_deduct weightDeduct,
            t3.weight_actual weightActual,
            t3.weight_send weightReceive,
            t3.ratio,
            t3.actual_count actualCount,
            t3.actual_receive actualReceive,
            t3.actual_count - t3.weight_send deviationCount,
            t3.deviation_rate deviationRate,
            t3.deviation_status deviationStatus,
            t3.enter_time enterTime,
            t3.leave_time leaveTime
        FROM
            ( SELECT * FROM d_mixing_plant_exit_ticket WHERE is_deleted = 0 AND mixing_plant_order_detail_id = #{orderDetailId} ) t
                LEFT JOIN ( SELECT * FROM d_pre_weigh_report WHERE is_deleted = 0 ) t1 ON t.id = t1.exit_ticket_id
                LEFT JOIN ( SELECT * FROM d_pre_truck_report_detail WHERE is_deleted = 0 ) t2 ON t1.id = t2.pre_weigh_report_id
                LEFT JOIN ( SELECT * FROM d_material_data WHERE is_deleted = 0 ) t3 ON t2.material_data_id = t3.id
        ORDER BY t.create_time DESC
    </select>

    <select id="orderStatusCount" resultType="cn.pinming.microservice.material.management.biz.vo.MixingPlantOrderStatusCountVO">
        SELECT
            t.STATUS,
            count( t.id ) count
        FROM
                ( SELECT * FROM d_mixing_plant_order_detail WHERE is_deleted = 0 ) t
                    INNER JOIN ( SELECT * FROM d_mixing_plant_order WHERE is_deleted = 0
                                    <if test="projectId != null and projectId != ''">
                                        and plant_id = #{projectId}
                                    </if>
                               ) t1 ON t.mixing_plant_order_id = t1.id
        GROUP BY
            t.STATUS
    </select>

    <select id="customer" resultType="string">
        select a.receiver_project_title
        from d_mixing_plant_order a
        left join d_mixing_plant_exit_ticket b on a.id = b.mixing_plant_order_id and b.is_deleted = 0
        where a.company_id = #{companyId}
        and a.plant_id = #{projectId}
        and a.is_deleted = 0
        and a.receiver_project_title is not null
        order by b.create_time DESC
    </select>
</mapper>
