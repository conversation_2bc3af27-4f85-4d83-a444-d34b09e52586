<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.PurchaseOrderDetailMapper">

    <select id="selectOrderDetailById"
            resultType="cn.pinming.microservice.material.management.biz.dto.PurchaseOrderDetailDTO">
        select a.contract_detail_id,
               if(c.id is null, 0, 1)                                                               receiveDataRelation,
               a.id orderDetailId,
               a.brand,
               count,
               a.remark,
               b.category_id categoryId,
               b.category_name,
               b.material_name,
               b.material_spec,
               b.material_id,
               b.unit,
               a.parameter_requirements parameterRequirements,
               transform_unit,
               sum(c.weight_send)                                                                as sendAmount,
               sum(c.actual_count)                                                               as actualCount,
               sum(c.actual_receive)                                                             as actualAmount,
               sum(c.actual_count) - sum(c.weight_send)                                          as deviationCount,
               round(((sum(c.actual_count) - sum(c.weight_send)) / sum(c.weight_send)) * 100, 2) as deviationRate
        from d_purchase_order_detail a
                 left join d_purchase_contract_detail b on a.contract_detail_id = b.id and a.is_deleted = 0
                 left join d_material_data c on c.purchase_order_id = a.order_id and c.contract_detail_id = b.id
                 left join d_material_send_receive d on c.receive_id = d.id and d.type = 1 and a.is_deleted = 0
        where order_id = #{id}
          and a.is_deleted = 0
        group by material_id
        order by material_id
    </select>
    <select id="selectPurchaseAmountByQuery" resultType="java.math.BigDecimal">
        select sum(count) as purchaseAmount
        from d_purchase_order a
        left join d_purchase_order_detail b on a.id = b.order_id
        left join d_purchase_contract_detail c on c.id = b.contract_detail_id
        where a.is_deleted = 0
        and b.is_deleted = 0
        and c.is_deleted = 0
        <if test="projectIds !=null and projectIds.size > 0">
            and a.receiver_project in
            <foreach collection = "projectIds" item = "id" open ="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="startDate != null">
            and date_format(a.receive_time,'%Y%m') <![CDATA[ >= ]]> date_format(#{startDate},'%Y%m')
        </if>
        <if test="endDate != null">
            and date_format(a.receive_time,'%Y%m') <![CDATA[ <= ]]> date_format(#{endDate},'%Y%m')
        </if>
        <if test="categoryId != null">
            and c.category_id = #{categoryId}
        </if>
        <if test="categoryIds != null and categoryIds.size>0">
            and c.category_id in
            <foreach collection = "categoryIds" item = "id" open ="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="unit != null">
            and c.unit = #{unit}
        </if>
        <if test="supplierId != null">
            and a.supplier_id = #{supplierId}
        </if>
    </select>
    <select id="selectSupplierAnalysisPageVO"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierAnalysisDetailVO">
        select a.supplier_id, sum(b.count) as purchaseAmount
        from d_purchase_order a
        left join d_purchase_order_detail b on a.id = b.order_id
        left join d_purchase_contract_detail c on b.contract_detail_id = c.id and c.is_deleted = 0
        where a.is_deleted = 0
        and a.company_id = #{companyId}
        <if test="projectIds !=null and projectIds.size > 0">
            and a.receiver_project in
            <foreach collection = "projectIds" item = "id" open ="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="categoryIds != null and categoryIds.size>0">
            and c.category_id in
            <foreach collection = "categoryIds" item = "id" open ="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and c.unit = #{unit}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{startDate}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endDate}
        <if test="supplierIds !=null and supplierIds.size > 0">
            and a.supplier_id in
            <foreach collection="supplierIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        group by a.supplier_id
    </select>
    <select id="selectSupplierAnalysisDetailPageVO"
            resultType="cn.pinming.microservice.material.management.biz.vo.MaterialReceiveVO">
        select a.id ,
               b.receive_no,
               a.supplier_id,
               a.project_id,
               a.weight_send,
               a.actual_count,
               a.material_id,
               a.weight_unit,
               b.truck_no,
               '地磅收料' as receiveType,
               a.weight_gross,
               a.weight_tare,
               a.weight_net,
               a.weight_deduct,
               a.weight_actual,
               c.conversion_rate,
               a.ratio as actualRatio,
               c.unit,
               a.actual_receive,
               a.deviation_status,
               a.enter_time,
               a.leave_time
        from d_material_data a left join d_material_send_receive b on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity = 1
        left join d_purchase_contract_detail c on a.contract_detail_id = c.id and c.is_deleted = 0
        where a.is_deleted = 0 and a.receive_mode in (1, 2) and a.material_validity = 1
          and b.type = 1 and a.material_exist = 1
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection = "projectIds" item = "id" open ="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="materialIds !=null and materialIds.size > 0">
            and a.material_id in
            <foreach collection = "materialIds" item = "id" open ="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and a.weight_unit = #{unit}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{startDate}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endDate}
        and a.supplier_id = #{supplierId}
        order by b.receive_no
    </select>

    <select id="selectThreshold" resultType="cn.pinming.microservice.material.management.biz.vo.ThresholdVO">
        select distinct b.deviation_ceiling,b.deviation_floor,b.unit
        from d_purchase_order_detail a
        left join d_purchase_contract_detail b on a.contract_detail_id = b.id and b.material_id = #{materialId}
        where a.order_id = #{purchaseId}
        and a.is_deleted = 0
        and b.is_deleted = 0
    </select>

    <select id="selectMaterialInfo" resultType="cn.pinming.microservice.material.management.biz.vo.GoodsSimpleVO">
        select a.count,b.conversion_rate,a.id as purchaseOrderDetailId,b.category_name,b.material_name,b.material_spec,b.material_id,a.brand as purchaseBrand,a.remark,b.unit,
               b.deviation_floor,
               b.deviation_ceiling,
               b.id as contract_detail_id,
               b.category_id
        from d_purchase_order_detail a
        inner join d_purchase_contract_detail b on a.contract_detail_id = b.id
        where a.order_id = #{orderId}
        and a.is_deleted = 0
    </select>

    <select id="listGoods" resultType="cn.pinming.microservice.material.management.biz.dto.PreGoodsDTO">
        select a.id as purchaseOrderDetailId,a.count,a.remark,b.material_id,b.category_name,b.material_name,b.material_spec,a.brand,b.unit,
               b.conversion_rate,b.category_id,b.deviation_ceiling,b.deviation_floor,b.id as contractDetailId
        from d_purchase_order_detail a
        left join d_purchase_contract_detail b on a.contract_detail_id = b.id
        where a.order_id = #{orderId}
        and a.is_deleted = 0
    </select>

    <select id="selectInfo" resultType="cn.pinming.microservice.material.management.biz.vo.GoodsSimpleVO">
        select a.brand as purchaseBrand,a.remark,b.category_name,b.material_name,b.material_spec
        from d_purchase_order_detail a
        right join d_purchase_contract_detail b on a.contract_detail_id = b.id and b.material_id = #{materialId}
        where a.order_id = #{purchaseId}
        and a.is_deleted = 0
    </select>

    <select id="selectSupplierAnalysisDetailMobilePageVO"
            resultType="cn.pinming.microservice.material.management.biz.vo.MaterialReceiveVO">
        select a.receive_id as id,
               a.receive_no,
               a.supplier_id,
               a.project_id,
               b.send_settlement_total as weight_send,
               b.actual_settlement_total as actual_count,
               b.material_id,
               b.settlement_unit as weight_unit,
               c.truck_no,
               '移动收料' as receiveType,
               '/' as weightGross,
               '/' as weightTare,
               '/' as weightNet,
               '/' as weightDeduct,
               '/' as weightActual,
               '/' as conversionRate,
               '/' as actualRatio,
               '/' as  unit,
               '/' as actualReceive,
               '/' as enterTime,
               '/' as leaveTime,
               b.deviation_status
        from d_mobile_receive a
        left join d_mobile_receive_total b on a.receive_id = b.receive_id
        left join d_mobile_receive_truck c on c.receive_id = a.receive_id
        where a.is_deleted = 0
        and a.supplier_id = #{supplierId}
        and b.settlement_unit = #{unit}
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection = "projectIds" item = "id" open ="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endDate}
        </if>
        <if test="categoryIds.size != 0">
            and b.category_id in
            <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        order by a.receive_no
    </select>

    <select id="selectSupplierAnalysisDetailUnionPageVO"
            resultType="cn.pinming.microservice.material.management.biz.vo.MaterialReceiveVO">
        select * from
        (select a.id ,
                b.receive_no,
                a.supplier_id,
                a.project_id,
                a.weight_send,
                a.actual_count,
                a.material_id,
                a.weight_unit,
                b.truck_no,
                '地磅收料' as receiveType,
                a.weight_gross,
                a.weight_tare,
                a.weight_net,
                a.weight_deduct,
                a.weight_actual,
                c.conversion_rate,
                a.ratio as actualRatio,
                c.unit,
                a.actual_receive,
                a.deviation_status,
                a.enter_time,
                a.leave_time
        from d_material_data a left join d_material_send_receive b on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity = 1
        left join d_purchase_contract_detail c on a.contract_detail_id = c.id
        where a.is_deleted = 0 and a.receive_mode in (1, 2) and a.material_validity = 1
          and a.material_exist = 1  and b.type = 1
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection = "projectIds" item = "id" open ="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="materialIds !=null and materialIds.size > 0">
            and a.material_id in
            <foreach collection = "materialIds" item = "id" open ="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        and a.weight_unit = #{unit}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{startDate}
        and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endDate}
        and a.supplier_id = #{supplierId}
        union all
        select a.receive_id as id,a.receive_no,a.supplier_id,a.project_id,
        b.send_settlement_total as weight_send,
        b.actual_settlement_total as actual_count,
        b.material_id,
        b.settlement_unit as weight_unit,
        c.truck_no,
        '移动收料' as receiveType,
        '/' as weightGross,
        '/' as weightTare,
        '/' as weightNet,
        '/' as weightDeduct,
        '/' as weightActual,
        '/' as conversionRate,
        '/' as actualRatio,
        '/' as  unit,
        '/' as actualReceive,
        '/' as enterTime,
        '/' as leaveTime,
        b.deviation_status
        from d_mobile_receive a
        left join d_mobile_receive_total b on a.receive_id = b.receive_id
        left join d_mobile_receive_truck c on c.receive_id = a.receive_id
        where a.is_deleted = 0
        and a.supplier_id = #{supplierId}
        and b.settlement_unit = #{unit}
        <if test="projectIds !=null and projectIds.size > 0">
            and a.project_id in
            <foreach collection = "projectIds" item = "id" open ="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="startDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate != null">
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{endDate}
        </if>
        <if test="categoryIds.size != 0">
            and b.category_id in
            <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        ) x
        order by receive_no
    </select>
    <select id="selectSupplierPageVO"
            resultType="cn.pinming.microservice.material.management.biz.vo.SupplierAnalysisDetailVO">
<!--        select a.supplier_id-->
<!--        from d_purchase_order a-->
<!--        left join d_purchase_order_detail b on a.id = b.order_id-->
<!--        left join d_purchase_contract_detail c on b.contract_detail_id = c.id and c.is_deleted = 0-->
<!--        where a.is_deleted = 0-->
<!--        <if test="projectIds !=null and projectIds.size > 0">-->
<!--            and a.receiver_project in-->
<!--            <foreach collection = "projectIds" item = "id" open ="(" close=")" separator="," index="">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="categoryIds != null and categoryIds.size>0">-->
<!--            and c.category_id in-->
<!--            <foreach collection = "categoryIds" item = "id" open ="(" close=")" separator="," index="">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        and c.unit = #{unit}-->
<!--        and a.company_id = #{companyId}-->
<!--        <if test="supplierId !=null and supplierId !=''">-->
<!--            and a.supplier_id = #{supplierId}-->
<!--        </if>-->
<!--        group by a.supplier_id-->
        select distinct m.supplier_id
        from
        (
            select a.supplier_id
            from d_material_data a
            left join d_material_send_receive b on a.receive_id = b.id and b.receive_mode in (1, 2) and b.material_validity = 1
            where a.is_deleted = 0 and a.receive_mode in (1, 2) and a.material_validity = 1 and b.type = 1
            <if test="projectIds !=null and projectIds.size > 0">
                and a.project_id in
                <foreach collection = "projectIds" item = "id" open ="(" close=")" separator="," index="">
                    #{id}
                </foreach>
            </if>
            <if test="categoryIds != null and categoryIds.size>0">
                and a.category_id in
                <foreach collection = "categoryIds" item = "id" open ="(" close=")" separator="," index="">
                    #{id}
                </foreach>
            </if>
            and a.weight_unit = #{unit}
            and a.company_id = #{companyId}
            <if test="supplierId !=null and supplierId !=''">
                and a.supplier_id = #{supplierId}
            </if>
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
            and date_format(a.receive_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
            group by a.supplier_id

            union all

            select a.supplier_id
            from d_mobile_receive a
            left join d_mobile_receive_total b on b.receive_id = a.receive_id
            where a.is_deleted = 0
            and a.company_id = #{companyId}
            and b.settlement_unit = #{unit}
            and a.receive_type in (1,2)
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{receiveStartDate}
            and date_format(a.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{receiveEndDate}
            <if test="supplierId != null">
                and a.supplier_id = #{supplierId}
            </if>
            <if test="projectIds !=null and projectIds.size > 0">
                and a.project_id in
                <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                    #{id}
                </foreach>
            </if>
            <if test="categoryIds.size != 0">
                and b.category_id in
                <foreach collection="categoryIds" item="id" open="(" close=")" separator="," index="">
                    #{id}
                </foreach>
            </if>
            group by a.supplier_id
        ) m

    </select>

    <select id="purchaseDetail" resultType="cn.pinming.microservice.material.management.biz.dto.PurchaseOrderDetailDTO">
        select x.count,x.remark
				,x.material_id,x.category_name,x.material_name,x.material_spec,x.unit
				,sum(x.sendAccount) as sendAmount,sum(x.actualAmount) as actualAmount
	    from

	    (
			select
				a.count,a.remark
				,b.material_id,b.category_name,b.material_name,b.material_spec,b.unit
				,sum(c.weight_send) as sendAccount,sum(c.actual_count) as actualAmount
			from d_purchase_order_detail a
			left join d_purchase_contract_detail b on a.contract_detail_id = b.id and b.is_deleted = 0
			left join d_material_data c on c.material_id = b.material_id and c.purchase_order_id = a.order_id
			where a.order_id = #{purchaseId}
			and a.is_deleted = 0
			group by b.material_id

			union all

			select
				a.count,a.remark
				,b.material_id,b.category_name,b.material_name,b.material_spec,b.unit
				,sum(c.send_settlement_total) as sendAccount ,sum(c.actual_settlement_total) as actualAmount
			from d_purchase_order_detail a
			left join d_purchase_contract_detail b on a.contract_detail_id = b.id and b.is_deleted = 0
			left join d_mobile_receive d on d.purchase_id = a.order_id and d.receive_status = 1
			left join d_mobile_receive_total c on d.receive_id = c.receive_id and c.material_id = b.material_id
			where a.order_id = #{purchaseId}
			and a.is_deleted = 0
			group by b.material_id
	    )x group by x.material_id
    </select>

    <select id="selectContractDetailId" resultType="java.lang.String">
        select a.contract_detail_id
        from d_purchase_order_detail a
        left join d_purchase_contract_detail b on b.id = a.contract_detail_id and b.is_deleted = 0
        where a.id = #{purchaseOrderDetailId}
        and a.is_deleted = 0
    </select>

    <select id="listForRevise" resultType="cn.pinming.microservice.material.management.biz.vo.GoodsForReviseVO">
        select a.remark,a.contract_detail_id,a.unit as weightUnit,b.material_id,b.conversion_rate as ratio,b.category_name,b.material_name,b.material_spec,b.deviation_floor,b.deviation_ceiling,b.category_id
        from d_purchase_order_detail a
        left join d_purchase_contract_detail b on b.id = a.contract_detail_id and b.is_deleted = 0
        where a.order_id = #{purchaseOrderId}
        and a.is_deleted = 0
    </select>
    <select id="selectOrderDetailByIds"
            resultType="cn.pinming.microservice.material.management.biz.dto.PurchaseOrderDetailDTO">
        select order_id,a.contract_detail_id,
               if(c.id is null, 0, 1)                                                               receiveDataRelation,
               a.brand,
               count,
               a.remark,
               b.category_name,
               b.material_name,
               b.material_spec,
               b.material_id,
               b.unit,
               transform_unit,
               sum(c.weight_send)                                                                as sendAmount,
               sum(c.actual_count)                                                               as actualCount,
               sum(c.actual_receive)                                                             as actualAmount,
               sum(c.actual_count) - sum(c.weight_send)                                          as deviationCount,
               round(((sum(c.actual_count) - sum(c.weight_send)) / sum(c.weight_send)) * 100, 2) as deviationRate
        from d_purchase_order_detail a
                 left join d_purchase_contract_detail b on a.contract_detail_id = b.id and a.is_deleted = b.is_deleted
                 left join d_material_data c on c.purchase_order_id = a.order_id and a.company_id = c.company_id and
                                                a.project_id = c.project_id and c.contract_detail_id = b.id
                 left join d_material_send_receive d on c.receive_id = d.id and d.type = 1 and a.is_deleted = 0
        where order_id in
        <foreach collection="orderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
          and a.is_deleted = 0
        group by order_id, material_id
    </select>

    <select id="queryHistoryUsePartByProjectId" resultType="java.lang.String">
        select distinct remark from d_purchase_order_detail where is_deleted = 0 and remark is not null and remark != ''
        <if test="projectId != null and projectId != ''">
            and project_id = #{projectId}
        </if>
        <if test="remark != null and remark != ''">
            and remark like concat('%', #{remark}, '%')
        </if>
    </select>
    <select id="selectMaterialDetailByPurchaseOrderIdAndMaterialId"
            resultType="cn.pinming.microservice.material.management.biz.dto.PurchaseOrderDetailSimpleDTO">
        select a.id as contractDetailId,
        b.order_id,
        a.category_id,
        a.category_name,
        a.material_id,
        a.material_code,
        a.material_name,
        a.material_spec,
        a.unit,
        a.transform_unit,
        a.conversion_rate,
        a.deviation_ceiling,
        a.deviation_floor,
        a.brand
        from d_purchase_contract_detail a
        left join d_purchase_order_detail b on b.contract_detail_id = a.id and b.is_deleted = 0
        where b.order_id = #{purchaseOrderId}
        and a.material_id = #{materialId}
        and a.is_deleted = 0
    </select>
</mapper>
