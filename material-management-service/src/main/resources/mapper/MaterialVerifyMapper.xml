<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.pinming.microservice.material.management.biz.mapper.MaterialVerifyMapper">

    <update id="updateFileStatusById">
        update d_material_verify set status = 0, file_time = CURRENT_TIMESTAMP() where id = #{id}
    </update>

    <select id="selectMaterialVerify"
            resultType="cn.pinming.microservice.material.management.biz.dto.MaterialDataVerifyDTO">
        select id,
               verify_no,
               verify_project_id,
               supplier_id as verifySupplierId,
               contract_id,
               status,
               create_id as verifyPersonId,
               create_time,
               file_time
        from d_material_verify
        where is_deleted = 0
        <if test="verifyNo != null and verifyNo != ''">
            and verify_no  like concat('%', #{verifyNo}, '%')
        </if>
        <if test="verifyProjectId != null and verifyProjectId != ''">
            and project_id  = #{verifyProjectId}
        </if>
        <if test="projectIds != null and projectIds.size != 0">
            and project_id in
            <foreach collection="projectIds" item="id" open="(" close=")" separator="," index="">
                #{id}
            </foreach>
        </if>
        <if test="verifyPerson != null and verifyPerson != ''">
            and create_id  = #{verifyPerson}
        </if>
        <if test="verifyStatus != null">
            and status= #{verifyStatus}
        </if>
        order by create_time desc
    </select>

    <!-- 对账明细（临时收料&报备收料） -->
    <select id="tempAndReportVerifyReceiveDetail" resultType="cn.pinming.microservice.material.management.biz.dto.MaterialVerifyReceiveDetailDTO">
        SELECT
            c.id receiveId,
            c.id receiveDataId,
            c.receive_time receiveTime,
            t.receive_no receiveNo,
            t.truck_no truckNo,
            c.material_id materialId,
            c.material_name materialName,
            c.category_id categoryId,
            c.category_name categoryName,
            c.weight_send weightSend,
            c.actual_count actualCount,
            c.actual_receive actualReceive,
            c.deviation_rate deviationRate,
            c.deviation_status deviationStatus,
            IF
                ( r.verify_id = #{verifyId}, TRUE, FALSE ) isChoose
        FROM
            d_material_send_receive t
                LEFT JOIN d_material_data c ON t.id = c.receive_id
                LEFT JOIN (SELECT * FROM d_purchase_order WHERE is_deleted = 0) d ON c.purchase_order_id = d.id
                LEFT JOIN ( SELECT * FROM d_material_verify_relation WHERE is_deleted = 0 ) r ON c.id = r.receive_data_id
        WHERE
            c.is_deleted = 0
          AND c.id is not null
          AND t.is_deleted = 0
          AND t.receive_mode = #{receiveMode}
          AND t.material_validity = 1
          AND t.type = 1
          AND t.supplier_id = #{supplierId}
          AND t.project_id = #{projectId}
          AND d.contract_id = #{contractId}
          AND c.reconciliation_id is null
        <if test="start != null and start != ''">
            AND c.receive_time BETWEEN #{start} AND #{end}
        </if>
    </select>

    <!-- 对账明细（无归属收料&无效称重） -->
    <select id="unbelognAndInvalidVerifyReceiveDetail" resultType="cn.pinming.microservice.material.management.biz.dto.MaterialVerifyReceiveDetailDTO">
        SELECT
            c.id receiveId,
            c.id receiveDataId,
            c.receive_time receiveTime,
            t.receive_no receiveNo,
            t.truck_no truckNo,
            t.order_no orderNo,
            c.material_id materialId,
            c.material_name materialName,
            c.category_id categoryId,
            c.category_name categoryName,
            c.weight_send weightSend,
            c.actual_count actualCount,
            c.actual_receive actualReceive,
            c.deviation_rate deviationRate,
            c.deviation_status deviationStatus,
            d.contract_id contractId,
            IF
                ( r.verify_id = #{verifyId}, TRUE, FALSE ) isChoose
        FROM
            d_material_send_receive t
                LEFT JOIN d_material_data c ON t.id = c.receive_id
                LEFT JOIN ( SELECT * FROM d_purchase_order WHERE is_deleted = 0 ) d ON c.purchase_order_id = d.id
                LEFT JOIN ( SELECT * FROM d_material_verify_relation WHERE is_deleted = 0 ) r ON c.id = r.receive_data_id
        WHERE
            t.type = 1
        <if test="receiveMode == 0">
            AND t.material_validity = 2
        </if>
        <if test="receiveMode == 3">
            AND t.material_validity = 1
        </if>
          AND t.project_id = #{projectId}
          AND t.is_deleted = 0
        <if test="receiveMode == 3">
            AND t.receive_mode = 3
        </if>
          AND c.is_deleted = 0
          AND c.id IS NOT NULL
        <if test="start != null and start != ''">
            AND c.receive_time BETWEEN #{start} AND #{end}
        </if>
    </select>

    <select id="verifyReceiveList" resultType="cn.pinming.microservice.material.management.biz.dto.MaterialVerifyReceiveDTO">
        SELECT
            t1.id weighRecordId,
            t2.id receiveId,
            t2.id receiveDataId,
            t2.receive_time receiveTime,
            t1.receive_no receiveNo,
            t1.truck_no truckNo,
            t2.enter_time enterTime,
            t2.leave_time leaveTime,
            t2.material_id materialId,
            t2.material_name materialName,
            t2.category_id categoryId,
            if(t2.material_id IS NULL OR t2.material_id = '', t2.material_name, t2.category_name) categoryName,
            t2.contract_detail_id contractDetailId,
            t2.weight_gross weightGross,
            t2.weight_tare weightTare,
            t2.weight_Deduct weightDeduct,
            t2.ratio,
            t2.weight_unit weightUnit,
            t2.weight_send weightSend,
            t2.actual_count actualCount,
            t2.actual_receive actualReceive,
            t2.deviation_rate deviationRate,
            t2.deviation_status deviationStatus,
            t2.is_pushed,
            if(t2.is_revise = 1, 1, 0) isRevise,
            t3.warning_source_no warningSourceNo,
            if(min( if(t3.warning_source_id is null, 2, t3.warning_status) ) = 1, false, true) warningStatus
        FROM
            ( SELECT receive_data_id FROM d_material_verify_relation WHERE is_deleted = 0 AND verify_id = #{verifyId} ) t
                LEFT JOIN d_material_data t2 ON t2.is_deleted = 0
                AND t2.id = t.receive_data_id
                LEFT JOIN d_material_send_receive t1 ON t2.receive_id = t1.id
                LEFT JOIN d_material_warning t3 ON t3.is_deleted = 0
                AND t3.warning_source_id = t2.id
        GROUP BY
            t2.id
        order by t2.enter_time desc
    </select>

    <select id="verifyReceiveMaterialList" resultType="cn.pinming.microservice.material.management.biz.dto.MaterialVerifyMaterialDTO">
        SELECT
        d.*,
        sum( pd.count ) purchaseTotal
        FROM
        (
        SELECT
        p.id orderId,
        t.contract_detail_id contractDetailId,
        t.material_id materialId,
        IF
        ( p.id IS NULL, 0, 1 ) isPurchase,
        IF
        ( t.material_id IS NULL OR t.material_id = '', t.material_name, if(t.category_name is null or t.category_name = '', t.material_name, t.category_name) ) categoryName,
        sum( t.weight_gross ) weightGrossTotal,
        sum( t.weight_tare ) weightTareTotal,
        sum( t.weight_net ) weightNetTotal,
        sum( t.weight_deduct ) weightDeductTotal,
        sum( t.weight_actual ) weightActualTotal,
        sum( t.weight_send ) weightSendTotal,
        sum( t.actual_count ) actualCountTotal,
        sum( t.actual_receive ) actualReceiveTotal,
        sum( t.actual_count - t.weight_send ) deviationTotal,
        round(( sum( t.actual_count )- sum( t.weight_send ))/ sum( t.weight_send )* 100, 2 ) deviationRateTotal,
        pcd.unit
        FROM
        ( SELECT * FROM d_material_data WHERE id IN
        <foreach collection="dataIds" item="id" open="(" close=")" separator="," index="">
            #{id}
        </foreach>
        AND is_deleted = 0 ) t
        LEFT JOIN d_purchase_contract_detail pcd ON t.contract_detail_id = pcd.id
        LEFT JOIN d_purchase_order p ON t.purchase_order_id = p.id
        GROUP BY if(t.material_id is null or t.material_id = '', t.material_name, t.material_id),
        IF
        ( p.id IS NULL, 0, 1 )) d
        LEFT JOIN d_purchase_order_detail pd ON d.orderId = pd.order_id and d.contractDetailId = pd.contract_detail_id
        GROUP BY
        if(d.materialId is null or d.materialId = '', d.categoryName, d.materialId),
        d.isPurchase
    </select>

    <select id="receiveDetail" resultType="cn.pinming.microservice.material.management.biz.dto.MaterialVerifyReceiveDetailScanDTO">
        SELECT
            t2.*,
            t4.contract_id contractId,
            t4.deviation_ceiling deviationCeiling,
            t4.deviation_floor deviationFloor,
            t3.verify_id verifyId,
            t3.verify_no verifyNo,
            IF
            ( t3.verify_no IS NULL, FALSE, TRUE ) isVerify
        FROM
            (
                SELECT
                    t.*,
                    IF
                    ( t1.deviation_status = 1, TRUE, FALSE ) deviation,
                    IF
                    ( min( IF ( w.warning_source_id IS NULL, 2, w.warning_status ) ) = 1, TRUE, FALSE ) warning,
                    t1.id receiveId,
                    t1.id receiveDataId,
                    t1.material_id materialId,
                    t1.receive_time receiveTime,
                    t1.material_name materialName,
                    t1.category_id categoryId,
                    t1.category_name categoryName,
                    t1.weight_send weightSend,
                    t1.actual_count actualCount,
                    t1.actual_receive actualReceive,
                    t1.deviation_rate deviationRate,
                    t1.deviation_status deviationStatus,
                    t1.contract_detail_id contractDetailId
                FROM
                        (
                        SELECT
                            id weighRecordId,
                            type,
                            order_no orderNo,
                            receive_no receiveNo,
                            truck_no truckNo,
                            project_id projectId,
                            company_id companyId,
                            IF
                            ( material_validity = 1, 1, 0 ) validity,
                            IF
                            ( receive_mode = 3, 1, 0 ) affiliation
                        FROM
                            d_material_send_receive
                        WHERE
                            is_deleted = 0
                            <if test="receiveNo != null">
                                AND receive_no = #{receiveNo}
                            </if>
                    ) t
                INNER JOIN (select * from d_material_data where is_deleted = 0
                            <if test="weighId != null">
                                AND weigh_id = #{weighId}
                            </if>
                    ) t1 ON t.weighRecordId = t1.receive_id
                LEFT JOIN d_material_warning w ON w.is_deleted = 0
                AND w.warning_source_id = t1.id
                GROUP BY
                    t1.id
            ) t2
        LEFT JOIN ( SELECT vr.receive_data_id, vr.verify_id, v.verify_no FROM d_material_verify_relation vr LEFT JOIN d_material_verify v ON vr.verify_id = v.id WHERE vr.is_deleted = 0 ) t3 ON t2.receiveId = t3.receive_data_id
        LEFT JOIN ( SELECT * FROM d_purchase_contract_detail WHERE is_deleted = 0 ) t4 ON t2.contractDetailId = t4.id
    </select>

    <select id="scanSubmitCheck" resultType="cn.pinming.microservice.material.management.biz.dto.MaterialVerifyReceiveDetailScanCheckDTO">
        SELECT
            t3.id receiveId,
            t3.receive_no receiveNo,
            t2.verify_no verifyNo
        FROM
            (
                <foreach collection="receiveIds" item="id" separator=" union all ">
                    select #{id} receive_data_id
                </foreach>
            ) t
                INNER JOIN ( SELECT * FROM d_material_verify_relation WHERE is_deleted = 0 ) t1 ON t.receive_data_id = t1.receive_data_id
                LEFT JOIN d_material_verify t2 ON t1.verify_id = t2.id
                LEFT JOIN d_material_data d ON d.id = t1.receive_data_id
                LEFT JOIN d_material_send_receive t3 ON d.receive_id = t3.id
        WHERE
            t3.id IS NOT NULL
    </select>

    <select id="selectPushed" resultType="java.lang.String">
        select a.id
        from d_material_verify a
        left join d_purchase_contract b on b.id = a.contract_id and b.is_deleted = 0
        where a.contract_id is not null
        and a.status = 0
        and a.is_deleted = 0
        and b.is_upload = 1
    </select>


    <select id="selectVerifyPerson" resultType="java.lang.String">
        select distinct create_id
        from d_material_verify
        where is_deleted = 0
        <if test="companyId != null and companyId != 0">
            and company_id = #{companyId}
        </if>
        <if test="projectId != null and projectId != 0">
            and project_id = #{projectId}
        </if>
        and create_id is not null
    </select>
</mapper>
