spring.application.name=material-management

spring.profiles.active=dev

dubbo.scan.base-packages=cn.pinming.microservice.material.management

spring.jackson.deserialization.fail-on-unknown-properties=false
spring.jackson.default-property-inclusion=non_null

mybatis-plus.mapper-locations=classpath:mapper/*.xml
mybatis-plus.type-aliases-package=cn.pinming.microservice.material.management.biz.entity
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.global-config.db-config.logic-delete-field=isDeleted
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0

cn.pinming.gateway.proxy.aliasName=materialManagement

cn.pinming.openapi.proxy.aliasName=material-management

logging.level.org.springframework.security=debug

