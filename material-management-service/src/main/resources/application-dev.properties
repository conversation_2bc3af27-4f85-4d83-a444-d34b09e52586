server.port=8080

server.tomcat.min-spare-threads=20
server.tomcat.max-threads=800

spring.datasource.url=***************************************************************************************************************************************************************************************
spring.datasource.username=zzsa
spring.datasource.password=RymQLf8Vq7gxiLlIJjz1
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

logging.level.root=info
logging.level.cn.pinming.microservice.material.management.biz.mapper=debug
#logging.level.cn.pinming.synshowdoc=error

spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

dubbo.protocol.name=dubbo
dubbo.protocol.port=12745
#specify dubbo ip for multiple network car
#dubbo.protocol.host=127.0.0.1
dubbo.registry.id=registry
#dubbo.registry.address=zookeeper://************:2181
#dubbo.consumer.group=ztcj-test
#dubbo.provider.group=ztcj-test
dubbo.registry.address=zookeeper://************:2181?client=curator&timeout=60000
dubbo.consumer.group=pms_k8s_test05
dubbo.provider.group=zh
dubbo.consumer.timeout=30000
dubbo.provider.timeout=30000
dubbo.consumer.check=false
dubbo.metadata-report.address=${dubbo.registry.address}
dubbo.config-center.timeout=10000

cookie.domain=zz-test03.pinming.org

#redis
spring.redis.host=***********
spring.redis.port=6379
spring.redis.database=6


spring.kafka.bootstrap-servers=***********:9092
spring.kafka.consumer.group-id=${spring.application.name}
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.enable-auto-commit=false
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer

iot.url=https://iot-opensite-test05.pinming.org/

category=[{"name":"æ··åå","categoryId":[38021,38023,38025,38027,38028,38029,38030],"unit":["ç«æ¹ç±³"]},{"name":"é¢ç­","categoryId":[10101],"unit":["å¨"]},{"name":"æ¨¡æ¿","categoryId":[13501],"unit":["ç"]}]
# ç»è®¡-æåç±»çé»è®¤category
statistics.tag.default.category={"CONCRETE":"38021,38023,38025,38027,38028,38029,38030","REBAR":"10101","TEMPLATE":"13501"}
# ç»è®¡-æ åç±»çé»è®¤category
statistics.default.category=38021,10101

#temporary.path=/Users/<USER>/Desktop/

temporary.path=/Users/<USER>/Desktop/client

weighbridge.push.info={2320:{"serviceSupply":"sevenBureanSerivce", "url":"http://**************:8083","projectCode":"005","appKey":"EEB61009C8E34D539CE2A4E45681860F","secret":"EEB6108878E34D539CE2A4E45681860F"}}

yongyou.push.address=http://*************:8777/service/puInstorService
push.switch=false
warning.auto.process.switch=true
algs.push.address=http://***********:8307/api/material/loadometer/add
algs.push.switch=false
algs.companyId=11346
algs.projectId=5128

#qrCodeuri = https://zhuang.pinming.cn/material-management-h5/#/pages/material/pages/driverWeighingPreReport/purhaseId={}/code={}
qrCodeuri = https://zzpre.pinming.org/smallapp/material-management-h5/#/pages/material/pages/driverWeighingPreReport?purchaseId={}&code={}
qrCodeUrl = https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx8037b61f15ed66c3&redirect_uri={}&response_type=code&scope=snsapi_base#wechat_redirect

#common.ctcj.token.clientId=chengjian-crucg-report
#common.ctcj.token.clientSecret=q3cVdiWG6hYR40XSlnedIap2Co7tohtoiu2HFEce
#common.ctcj.token.apiUrl=https://reg.crcc.cn

common.ctcj.token.clientId=crucg-zhgdpt
common.ctcj.token.clientSecret=Sz7oh5KlgD6vMBs36oAaYLt3WIZzF9iuDxRZPIP7
common.ctcj.token.apiUrl=https://reg.crcc.cn


imatchocr.url = http://************:8080/api/words/match
sdk.host=localhost:8080
sdk.appKey=1
sdk.appSecretKey=05AAA84A77322877778A199C4F273CBC