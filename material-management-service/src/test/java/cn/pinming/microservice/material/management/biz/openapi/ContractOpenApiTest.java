package cn.pinming.microservice.material.management.biz.openapi;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.management.biz.form.PurchaseContractDetailForm;
import cn.pinming.microservice.material.management.biz.form.PurchaseContractForm;
import cn.pinming.microservice.material.management.biz.query.PurchaseContractQuery;
import cn.pinming.microservice.material.management.common.util.FormData;
import cn.pinming.microservice.material.management.common.util.SecretUtils;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

class ContractOpenApiTest {
    private final static String URL = "https://openapi-test03.pinming.org/api/openapi/material-management/";
    // 企业开发者秘钥
    private final static String SECRET_KEY = "d3395dd55136a9cb9f26276eeaaf0ddb";
    // 企业编码（非companyId）
    private final static String cono = "14766374";
    /**
     * 保存合同
     */
    @Test
    void saveContract() {
        //1.组装业务数据对象
        List<PurchaseContractDetailForm> list = new ArrayList<>();
        PurchaseContractDetailForm dto = PurchaseContractDetailForm.builder()
                .categoryId(11)
                .categoryName("测试")
                .materialId(12)
                .materialCode("1231")
                .materialName("材料")
                .materialSpec("规格")
                .unit("米")
                .transformUnit("立方米")
                .conversionRate(new BigDecimal("2"))
                .deviationCeiling(new BigDecimal("12.23"))
                .deviationFloor(new BigDecimal("12.23"))
                .brand("dasd")
                .build();
        list.add(dto);
        PurchaseContractForm build = PurchaseContractForm.builder()
                .createId("12")
                .updateId("123")
                .id("d2cb42b72ea59122852938c23f17bb21")
                .belongProjectId("10052")
                .name("合同名称")
                .no("19191919")
                .type("1")
                .supplierId(12)
                .build();
        build.setList(list);

        //2.对象转json
        String jsonStr = JSONUtil.toJsonStr(build);
        //3.字符串编码
        String encodeStr = SecretUtils.encode(jsonStr);
        //4.aes加密
        String encryptStr = SecretUtils.aesEncrypt(SECRET_KEY, encodeStr);
        //5.组装请求对象
        FormData data = new FormData();
        data.setItype("/test/saveContract");
        data.setTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        data.setCono(cono);
        data.setData(encryptStr);
        //加密类型：1-AES 	2-BASE64
        data.setEtype(1);
        // 开发者类型： 1企业开发者 2平台开发者，默认为空代表企业开发者
        data.setDtype(1);

        //6.按字段顺序md5加密
        String originalString = data.getItype() + data.getTime() + data.getCono() + data.getData()+SECRET_KEY;
        String sign = SecretUtils.md5Encrypt(originalString);
        data.setSign(sign);

        //7.对象转map
        Map<String, Object> map = BeanUtil.beanToMap(data);
        //8.请求
        String result = HttpUtil.post(URL, map);
        System.out.println(JSONUtil.toJsonPrettyStr(result));
    }

    /**
     * 删除合同
     */
    @Test
    void deleteContract() {
//        1. 组装业务数据对象
        String id = "990674d07dc74c46bdbcae246a7b51b8";
        //2.对象转json
        String jsonStr = JSONUtil.toJsonStr(id);
        //3.字符串编码
        String encodeStr = SecretUtils.encode(jsonStr);
        //4.aes加密
        String encryptStr = SecretUtils.aesEncrypt(SECRET_KEY, encodeStr);
        //5.组装请求对象
        FormData data = new FormData();
        data.setItype("/test/delContract");
        data.setTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        data.setCono(cono);
        data.setData(encryptStr);
        //加密类型：1-AES 	2-BASE64
        data.setEtype(1);
        // 开发者类型： 1企业开发者 2平台开发者，默认为空代表企业开发者
        data.setDtype(1);

        //6.按字段顺序md5加密
        String originalString = data.getItype() + data.getTime() + data.getCono() + data.getData() + SECRET_KEY;
        String sign = SecretUtils.md5Encrypt(originalString);
        data.setSign(sign);

        //7.对象转map
        Map<String, Object> map = BeanUtil.beanToMap(data);
        //8.请求
        String result = HttpUtil.post(URL, map);
        System.out.println(JSONUtil.toJsonPrettyStr(result));
    }

    /**
     * 合同列表
     */
    @Test
    void listContract() {
        //        1. 组装业务数据对象
        PurchaseContractQuery query = PurchaseContractQuery.builder()
                .companyId(11346)
                .build();
        //2.对象转json
        String jsonStr = JSONUtil.toJsonStr(query);
        //3.字符串编码
        String encodeStr = SecretUtils.encode(jsonStr);
        //4.aes加密
        String encryptStr = SecretUtils.aesEncrypt(SECRET_KEY, encodeStr);
        //5.组装请求对象
        FormData data = new FormData();
        data.setItype("/test/listContract");
        data.setTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        data.setCono(cono);
        data.setData(encryptStr);
        //加密类型：1-AES 	2-BASE64
        data.setEtype(1);
        // 开发者类型： 1企业开发者 2平台开发者，默认为空代表企业开发者
        data.setDtype(1);

        //6.按字段顺序md5加密
        String originalString = data.getItype() + data.getTime() + data.getCono() + data.getData() + SECRET_KEY;
        String sign = SecretUtils.md5Encrypt(originalString);
        data.setSign(sign);

        //7.对象转map
        Map<String, Object> map = BeanUtil.beanToMap(data);
        //8.请求
        String result = HttpUtil.post(URL, map);
        System.out.println(JSONUtil.toJsonPrettyStr(result));
    }

    /**
     * 查看合同详情
     */
    @Test
    void detailContract() {
//        1. 组装业务数据对象
        String id = "0f5a54d2b305b019e47b53416790ca24";
        //2.对象转json
        String jsonStr = JSONUtil.toJsonStr(id);
        //3.字符串编码
        String encodeStr = SecretUtils.encode(jsonStr);
        //4.aes加密
        String encryptStr = SecretUtils.aesEncrypt(SECRET_KEY, encodeStr);
        //5.组装请求对象
        FormData data = new FormData();
        data.setItype("/test/detailContract");
        data.setTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        data.setCono(cono);
        data.setData(encryptStr);
        //加密类型：1-AES 	2-BASE64
        data.setEtype(1);
        // 开发者类型： 1企业开发者 2平台开发者，默认为空代表企业开发者
        data.setDtype(1);

        //6.按字段顺序md5加密
        String originalString = data.getItype() + data.getTime() + data.getCono() + data.getData() + SECRET_KEY;
        String sign = SecretUtils.md5Encrypt(originalString);
        data.setSign(sign);

        //7.对象转map
        Map<String, Object> map = BeanUtil.beanToMap(data);
        //8.请求
        String result = HttpUtil.post(URL, map);
        System.out.println(JSONUtil.toJsonPrettyStr(result));
    }

    /**
     * 采购合同-所属项目
     */
    @Test
    void belongContract() {
//        1. 组装业务数据对象
        String id = "1ff47671c9b8296642f5d0a54a32ee77";
        //2.对象转json
        String jsonStr = JSONUtil.toJsonStr(id);
        //3.字符串编码
        String encodeStr = SecretUtils.encode(jsonStr);
        //4.aes加密
        String encryptStr = SecretUtils.aesEncrypt(SECRET_KEY, encodeStr);
        //5.组装请求对象
        FormData data = new FormData();
        data.setItype("/test/belongContract");
        data.setTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        data.setCono(cono);
        data.setData(encryptStr);
        //加密类型：1-AES 	2-BASE64
        data.setEtype(1);
        // 开发者类型： 1企业开发者 2平台开发者，默认为空代表企业开发者
        data.setDtype(1);

        //6.按字段顺序md5加密
        String originalString = data.getItype() + data.getTime() + data.getCono() + data.getData() + SECRET_KEY;
        String sign = SecretUtils.md5Encrypt(originalString);
        data.setSign(sign);

        //7.对象转map
        Map<String, Object> map = BeanUtil.beanToMap(data);
        //8.请求
        String result = HttpUtil.post(URL, map);
        System.out.println(JSONUtil.toJsonPrettyStr(result));
    }



}