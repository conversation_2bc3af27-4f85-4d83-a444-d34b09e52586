package cn.pinming.microservice.material.management.biz.aspect;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class AspectTest {

    @Pointcut("@annotation(cn.pinming.microservice.material.management.biz.common.annotation.Log)")
    public void logPointCut(){

    }

    @Before("logPointCut()")
    public void before(JoinPoint joinPoint){
        System.out.println("前置通知");
    }
}
