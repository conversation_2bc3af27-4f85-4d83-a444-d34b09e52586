//package cn.pinming.microservice.material.management;
//
//import com.baomidou.mybatisplus.annotation.FieldFill;
//import com.baomidou.mybatisplus.annotation.IdType;
//import com.baomidou.mybatisplus.generator.AutoGenerator;
//import com.baomidou.mybatisplus.generator.config.*;
//import com.baomidou.mybatisplus.generator.config.builder.GeneratorBuilder;
//import com.baomidou.mybatisplus.generator.config.converts.MySqlTypeConvert;
//import com.baomidou.mybatisplus.generator.config.rules.DateType;
//import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
//import com.baomidou.mybatisplus.generator.config.rules.IColumnType;
//import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
//import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
//import com.baomidou.mybatisplus.generator.fill.Column;
//import org.jetbrains.annotations.NotNull;
//import org.junit.Test;
//
//public class GenTest {
//
//    @Test
//    public void generate() {
//        //全局配置
//        GlobalConfig globalConfig = getGlobalConfig();
//        //包配置
//        PackageConfig packageConfig = getPackageConfig();
//        //数据库配置
//        DataSourceConfig dataSourceConfig = getDataSourceConfig();
//        //填充策略
//        StrategyConfig strategyConfig = getStrategyConfig();
//        //模板配置
//        TemplateConfig templateConfig = getTemplateConfig();
//        // 代码生成器
//        new AutoGenerator(dataSourceConfig)
//                .global(globalConfig)
//                .packageInfo(packageConfig)
//                .template(templateConfig)
//                .strategy(strategyConfig)
//                .execute(new FreemarkerTemplateEngine());
//    }
//
//    private TemplateConfig getTemplateConfig() {
//        TemplateConfig templateConfig = new TemplateConfig.Builder().build();
//        return templateConfig;
//    }
//
//    @NotNull
//    private StrategyConfig getStrategyConfig() {
//        StrategyConfig strategyConfig = new StrategyConfig.Builder().addTablePrefix("d_").addTablePrefix("s_")
//            .addInclude("d_ingredient_list","d_ingredient_apply","d_ingredient_apply_detail","d_ingredient_notice","d_ingredient_notice_detail")
//                .entityBuilder()// 实体配置构建者
//                .enableLombok()// 开启lombok模型
//                .enableSerialVersionUID()
//                .enableTableFieldAnnotation()
//                .logicDeleteColumnName("is_deleted")
//                .idType(IdType.ASSIGN_UUID)
//                //.versionColumnName("version") //乐观锁数据库表字段
//                .naming(NamingStrategy.underline_to_camel)// 数据库表映射到实体的命名策略
//                .addTableFills(new Column("company_id", FieldFill.INSERT))    //基于数据库字段填充
//                .addTableFills(new Column("project_id", FieldFill.INSERT))
//                .addTableFills(new Column("department_id", FieldFill.INSERT))
//                .addTableFills(new Column("create_id", FieldFill.INSERT))
//                .addTableFills(new Column("update_id", FieldFill.INSERT_UPDATE))
//                .addTableFills(new Column("create_time", FieldFill.INSERT))
//                .addTableFills(new Column("update_time", FieldFill.INSERT_UPDATE))
//                //.addTableFills(new Property("updateTime", FieldFill.INSERT_UPDATE))    //基于模型属性填充
//                .controllerBuilder() //控制器属性配置构建
//                .enableRestStyle()
//                .build();
//        return strategyConfig;
//    }
//
//    private DataSourceConfig getDataSourceConfig() {
//        DataSourceConfig dataSourceConfig = new DataSourceConfig
//                .Builder("*****************************************************************************************************************************", "zzsa", "RymQLf8Vq7gxiLlIJjz1")
//                .typeConvert(new MySqlTypeConvert() {
//                    @Override
//                    public IColumnType processTypeConvert(GlobalConfig globalConfig, String fieldType) {
//                        if (fieldType.toLowerCase().contains("tinyint")) {
//                            return DbColumnType.BYTE;
//                        }
//                        if (fieldType.toLowerCase().contains("datetime")) {
//                            return DbColumnType.LOCAL_DATE_TIME;
//                        }
//                        return super.processTypeConvert(globalConfig, fieldType);
//                    }
//                })
//                .build();
//        return dataSourceConfig;
//    }
//
//    private PackageConfig getPackageConfig() {
//        PackageConfig packageConfig = new PackageConfig.Builder()
//                .parent("cn.pinming.microservice.material.management")
//                .moduleName("biz")
//                .build();
//        return packageConfig;
//    }
//
//    private GlobalConfig getGlobalConfig() {
//        String property = System.getProperty("user.dir");
//        GlobalConfig globalConfig = GeneratorBuilder.globalConfigBuilder()
//                .openDir(false)
//                .outputDir(property + "/src/main/java").enableSwagger()
//                .author("luo hao").dateType(DateType.TIME_PACK).commentDate("yyyy-MM-dd HH:mm:ss")
//                .build();
//        return globalConfig;
//    }
//}
