package cn.pinming.microservice.material.management.biz.openapi;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.management.biz.form.StandardMaterialDataForm;
import cn.pinming.microservice.material.management.biz.form.StandardMaterialDataItemFrom;
import cn.pinming.microservice.material.management.biz.query.MaterialWeighQuery;
import cn.pinming.microservice.material.management.common.util.FormData;
import cn.pinming.microservice.material.management.common.util.SecretUtils;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

public class ReceiveOpenApiTest {
    private final static String URL = "https://openapi-test03.pinming.org/api/openapi/material-management/";
    // 企业开发者秘钥
    private final static String SECRET_KEY = "c708f52755b21b98417cad0e96434e1e";
    // 企业编码（非companyId）
    private final static String cono = "15352206";

    /**
     * 收料列表
     */
    @Test
    void listReceive() {
//        1. 组装业务数据对象
        Map<String,String> formData = new HashMap<>();
        formData.put("projectId","5128");
        formData.put("mode","2");
        formData.put("sort","2");
        formData.put("size","10");
        formData.put("current","1");


        //2.对象转json
        String jsonStr = JSONUtil.toJsonStr(formData);
        //3.字符串编码
        String encodeStr = SecretUtils.encode(jsonStr);
        //4.aes加密
        String encryptStr = SecretUtils.aesEncrypt(SECRET_KEY, encodeStr);
        //5.组装请求对象
        FormData data = new FormData();
        data.setItype("/test/listReceive");
        data.setTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        data.setCono(cono);
        data.setData(encryptStr);
        //加密类型：1-AES 	2-BASE64
        data.setEtype(1);
        // 开发者类型： 1企业开发者 2平台开发者，默认为空代表企业开发者
        data.setDtype(1);

        //6.按字段顺序md5加密
        String originalString = data.getItype() + data.getTime() + data.getCono() + data.getData() + SECRET_KEY;
        String sign = SecretUtils.md5Encrypt(originalString);
        data.setSign(sign);

        //7.对象转map
        Map<String, Object> map = BeanUtil.beanToMap(data);
        //8.请求
        String result = HttpUtil.post(URL, map);
        System.out.println(JSONUtil.toJsonPrettyStr(result));
    }

    /**
     * 收料详情
     */
    @Test
    void detailReceive() {
//        1. 组装业务数据对象
        MaterialWeighQuery query = MaterialWeighQuery.builder()
                .receiveId("19c6eff89a17f5fb525ab09c220d4656")
                .build();
        //2.对象转json
        String jsonStr = JSONUtil.toJsonStr(query);
        //3.字符串编码
        String encodeStr = SecretUtils.encode(jsonStr);
        //4.aes加密
        String encryptStr = SecretUtils.aesEncrypt(SECRET_KEY, encodeStr);
        //5.组装请求对象
        FormData data = new FormData();
        data.setItype("/test/detailReceive");
        data.setTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        data.setCono(cono);
        data.setData(encryptStr);
        //加密类型：1-AES 	2-BASE64
        data.setEtype(1);
        // 开发者类型： 1企业开发者 2平台开发者，默认为空代表企业开发者
        data.setDtype(1);

        //6.按字段顺序md5加密
        String originalString = data.getItype() + data.getTime() + data.getCono() + data.getData() + SECRET_KEY;
        String sign = SecretUtils.md5Encrypt(originalString);
        data.setSign(sign);

        //7.对象转map
        Map<String, Object> map = BeanUtil.beanToMap(data);
        //8.请求
        String result = HttpUtil.post(URL, map);
        System.out.println(JSONUtil.toJsonPrettyStr(result));
    }

    /**
     * 收料
     */
    @Test
    void receive() {
        List<StandardMaterialDataForm> list = new ArrayList<>();
//        1. 组装业务数据对象
        StandardMaterialDataItemFrom from = StandardMaterialDataItemFrom.builder()
                .weighId("15")
                .materialId(3951)
                .clientMaterialId("11")
                .materialName("钢筋")
                .weightUnit("吨")
                .weightSend(BigDecimal.valueOf(100))
                .weightGross(BigDecimal.valueOf(34.32))
                .weightTare(BigDecimal.valueOf(23.54))
                .weightDeduct(BigDecimal.valueOf(2.32))
                .weightNet(BigDecimal.valueOf(10.78))
                .weightActual(BigDecimal.valueOf(8.46))
                .ratio(BigDecimal.valueOf(2))
                .actualCount(BigDecimal.valueOf(16.92))
                .actualReceiveCount(BigDecimal.valueOf(17))
                .unitPrice(BigDecimal.valueOf(3))
                .totalPrice(BigDecimal.valueOf(51))
                .grossTime(LocalDateTime.of(2023,7,28,9,48,32))
                .grossPoundId("1")
                .grossPoundName("毛重地磅")
                .tareTime(LocalDateTime.of(2023,7,28,11,48,32))
                .tarePicList(Arrays.asList("https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fsafe-img.xhscdn.com%2Fbw1%2F553ee40a-cd06-4932-adf6-0bb43ce942ce%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fsafe-img.xhscdn.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1693967489&t=38e5cbcf789aa73beb280a6271b5cbd1","https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fsafe-img.xhscdn.com%2Fbw1%2F92f752ad-84ad-4bcf-b98f-26298d15306c%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fsafe-img.xhscdn.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1693967489&t=2c855b27e3efaa61f61e4b0ee56dcf46"))
                .grossPicList(Arrays.asList("https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fsafe-img.xhscdn.com%2Fbw1%2F553ee40a-cd06-4932-adf6-0bb43ce942ce%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fsafe-img.xhscdn.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1693967489&t=38e5cbcf789aa73beb280a6271b5cbd1"))
                .tarePoundId("2")
                .tarePoundName("皮重地磅")
                .build();

        StandardMaterialDataForm dataForm = StandardMaterialDataForm.builder()
                .dataId("15")
                .deviceNo("xxx123")
                .companyId(11346)
                .projectId(5128)
                .clientProjectId("2333")
                .supplierId(String.valueOf(32))
                .supplierName("供应商")
                .clientSupplierId("32")
                .type((byte) 1)
                .receiveNo("23023")
                .receiver("收料人")
                .truckNo("浙A12323")
                .operator("司磅员")
                .driver("司机")
                .picList(Arrays.asList("https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fsafe-img.xhscdn.com%2Fbw1%2F553ee40a-cd06-4932-adf6-0bb43ce942ce%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fsafe-img.xhscdn.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1693967489&t=38e5cbcf789aa73beb280a6271b5cbd1"))
                .materialDataList(Arrays.asList(from))
                .build();
        list.add(dataForm);

        //2.对象转json
        String jsonStr = JSONUtil.toJsonStr(list);
        //3.字符串编码
        String encodeStr = SecretUtils.encode(jsonStr);
        //4.aes加密
        String encryptStr = SecretUtils.aesEncrypt(SECRET_KEY, encodeStr);
        //5.组装请求对象
        FormData data = new FormData();
        data.setItype("/material/receive");
        data.setTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        data.setCono(cono);
        data.setData(encryptStr);
        //加密类型：1-AES 	2-BASE64
        data.setEtype(1);
        // 开发者类型： 1企业开发者 2平台开发者，默认为空代表企业开发者
        data.setDtype(1);

        //6.按字段顺序md5加密
        String originalString = data.getItype() + data.getTime() + data.getCono() + data.getData() + SECRET_KEY;
        String sign = SecretUtils.md5Encrypt(originalString);
        data.setSign(sign);

        //7.对象转map
        Map<String, Object> map = BeanUtil.beanToMap(data);
        //8.请求
        String result = HttpUtil.post(URL, map);
        System.out.println(JSONUtil.toJsonPrettyStr(result));
    }


}
