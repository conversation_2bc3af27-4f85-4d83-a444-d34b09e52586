package cn.pinming.microservice.material.management;

import cn.pinming.microservice.material_unit.api.material.dto.MaterialDto;
import cn.pinming.microservice.material_unit.api.material.service.MaterialService;

import java.util.List;

import org.apache.dubbo.config.annotation.Reference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class MaterialServiceTests {

    @Reference
    private MaterialService materialService;

    @Test
    public void materialServiceTest() {
        int companyId = 11346;
        List<MaterialDto> materialDtos = materialService.listAllMaterialByCategoryId(companyId, 10101);
        System.out.println(materialDtos);
    }

}
