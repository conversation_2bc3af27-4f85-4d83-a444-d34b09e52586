package cn.pinming.microservice.material.management;

import cn.hutool.core.util.NumberUtil;
import cn.pinming.microservice.material.management.biz.enums.DeviationStatusEnum;
import cn.pinming.microservice.material.management.common.util.NoUtil;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

//@SpringBootTest
class UtilTests {

    @Resource
    private NoUtil noUtil;

    @Test
    public void noUtilTest() {
        String purchaseOrderNo = noUtil.getPurchaseOrderNo(3426);
        System.out.println(purchaseOrderNo);
    }

    @Test
    public void compare(){
        BigDecimal actual = new BigDecimal(9);
        BigDecimal weightsend = new BigDecimal(9);
        BigDecimal floor = new BigDecimal(2.0000);
        BigDecimal cell = new BigDecimal(-2.0000);

        BigDecimal result = NumberUtil.mul(NumberUtil.div(NumberUtil.sub(actual, weightsend), actual, 4),100);
        byte deviationStatus = DeviationStatusEnum.NORMAL.value();
        if (NumberUtil.isGreater(result, floor)) {
            deviationStatus = DeviationStatusEnum.POSITIVEDIFFERENCE.value();
        } else if (NumberUtil.isLess(result, cell)) {
            deviationStatus = DeviationStatusEnum.NEGATIVEDIFFERENCE.value();
        }
        System.out.println(deviationStatus);
    }
}
