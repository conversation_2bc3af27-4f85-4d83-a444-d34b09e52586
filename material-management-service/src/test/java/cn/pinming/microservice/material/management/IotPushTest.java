package cn.pinming.microservice.material.management;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.management.biz.dto.MaterialDataDTO;
import cn.pinming.microservice.material.management.biz.dto.MaterialWeightDTO;
import cn.pinming.microservice.material.management.biz.dto.PushDataDTO;
import cn.pinming.microservice.material.management.biz.entity.MaterialData;
import cn.pinming.microservice.material.management.biz.entity.MaterialSendReceive;
import cn.pinming.microservice.material.management.biz.entity.MaterialWeighbridge;
import cn.pinming.microservice.material.management.biz.entity.MaterialWeight;
import cn.pinming.microservice.material.management.biz.entity.PurchaseContractDetail;
import cn.pinming.microservice.material.management.biz.entity.TruckReport;
import cn.pinming.microservice.material.management.biz.enums.DeviationStatusEnum;
import cn.pinming.microservice.material.management.biz.mapper.MaterialWeighbridgeMapper;
import cn.pinming.microservice.material.management.biz.mapper.PurchaseOrderMapper;
import cn.pinming.microservice.material.management.biz.mapper.TruckReportMapper;
import cn.pinming.microservice.material.management.biz.service.IMaterialDataService;
import cn.pinming.microservice.material.management.biz.service.IMaterialSendReceiveService;
import cn.pinming.microservice.material.management.biz.service.IMaterialWeightService;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * This class is for xxxx.
 *
 * <AUTHOR>
 * @version 2021/9/10 1:41 下午
 */
@SpringBootTest
public class IotPushTest {

    String message = "{\"dataSourceType\":450005,\"deviceSn\":\"xxx123\",\"enterTime\":1631602337000,\"heartFree\":10,\"leaveTime\":1631602337000,\"materialDataList\":[{\"actualCount\":3,\"material\":\"螺纹钢筋\",\"materialCode\":00003,\"materialId\":37,\"ratio\":1,\"totalPrice\":1800,\"unitPrice\":600,\"weightActual\":3,\"weightDeduct\":0,\"weightGross\":3,\"weightNet\":3,\"weightSend\":200,\"weightTare\":0,\"weightUnit\":\"米\"}],\"materialWeightList\":[{\"weighbridgeId\":\"817a8e84742e4af287c39b7d67453105\",\"weight\":3,\"weightNo\":1,\"weightTime\":1631602337000},{\"weighbridgeId\":\"817a8e84742e4af287c39b7d67453105\",\"weight\":0,\"weightNo\":2,\"weightTime\":1631602337000}],\"purchaseId\":\"CGXQ-2690-20210914004\",\"receiveNo\":\"2109140003\",\"receiveTime\":1631602337000,\"receiver\":\"管理员\",\"truckNo\":\"浙A77886\",\"type\":1}";

    @Resource
    private IMaterialSendReceiveService sendReceiveService;
    @Resource
    private MaterialWeighbridgeMapper weighbridgeMapper;
    @Resource
    private TruckReportMapper truckReportMapper;
    @Resource
    private IMaterialDataService materialDataService;
    @Resource
    private IMaterialWeightService weightService;
    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;

    //@Test
    //public void listen() {
    //    PushDataDTO pushDataDTO = JSONUtil.toBean(message, PushDataDTO.class);
    //    //根据设备sn获取项目和企业信息
    //    MaterialWeighbridge weighbridge = this.getWeighbridge(pushDataDTO.getDeviceSn());
    //    Integer companyId = weighbridge.getCompanyId();
    //    Integer projectId = weighbridge.getProjectId();
    //
    //    TruckReport truckInfo = getTruckInfo(pushDataDTO.getTruckNo(), companyId, projectId);
    //    //保存收料单信息
    //    MaterialSendReceive sendReceive = this.getMaterialSendReceive(pushDataDTO, companyId, projectId, truckInfo);
    //    //保存收料信息
    //    this.saveMaterialData(pushDataDTO, companyId, projectId, sendReceive);
    //    //保存过磅信息
    //    this.saveWeight(pushDataDTO, sendReceive);
    //}
    //
    //public void saveWeight(PushDataDTO pushDataDTO, MaterialSendReceive sendReceive) {
    //    List<MaterialWeightDTO> materialWeightList = pushDataDTO.getMaterialWeightList();
    //    List<MaterialWeight> weightList = materialWeightList.stream().map(obj -> {
    //        MaterialWeight weight = new MaterialWeight();
    //        BeanUtils.copyProperties(obj, weight);
    //        weight.setReceiveId(sendReceive.getId());
    //        return weight;
    //    }).collect(Collectors.toList());
    //    weightService.saveBatch(weightList);
    //}
    //
    //public void saveMaterialData(PushDataDTO pushDataDTO, Integer companyId, Integer projectId,
    //                             MaterialSendReceive sendReceive) {
    //    List<MaterialDataDTO> materialDataList = pushDataDTO.getMaterialDataList();
    //    String purchaseId = pushDataDTO.getPurchaseId();
    //    Map<Integer, PurchaseContractDetail> collectMap = new HashMap<>();
    //    if (StrUtil.isNotBlank(purchaseId)) {
    //        List<PurchaseContractDetail> contractDetails = purchaseOrderMapper.selectMaterialIdInfo(pushDataDTO.getPurchaseId(), projectId);
    //        collectMap = contractDetails.stream().collect(Collectors.toMap(PurchaseContractDetail::getMaterialId, e -> e));
    //    }
    //
    //    Map<Integer, PurchaseContractDetail> finalCollectMap = collectMap;
    //    List<MaterialData> list = materialDataList.stream().map(obj -> {
    //        MaterialData data = new MaterialData();
    //        BeanUtils.copyProperties(obj, data);
    //        data.setReceiveId(sendReceive.getId());
    //        data.setSupplierId(sendReceive.getSupplierId());
    //        data.setCompanyId(companyId);
    //        data.setProjectId(projectId);
    //
    //        PurchaseContractDetail detail = finalCollectMap.get(obj.getMaterialId());
    //        if (detail != null) {
    //            BigDecimal result =
    //                NumberUtil.div(NumberUtil.sub(obj.getActualCount(), obj.getWeightSend()),
    //                    obj.getActualCount(), 2);
    //            byte deviationStatus = DeviationStatusEnum.NORMAL.value();
    //            if (NumberUtil.isGreater(result, detail.getDeviationFloor())) {
    //                deviationStatus = DeviationStatusEnum.POSITIVEDIFFERENCE.value();
    //            } else if (NumberUtil.isLess(result, detail.getDeviationCeiling())) {
    //                deviationStatus = DeviationStatusEnum.NEGATIVEDIFFERENCE.value();
    //            }
    //            data.setDeviationStatus(deviationStatus);
    //        }
    //        return data;
    //    }).collect(Collectors.toList());
    //    materialDataService.saveBatch(list);
    //}
    //
    //public MaterialSendReceive getMaterialSendReceive(PushDataDTO pushDataDTO, Integer companyId,
    //                                                  Integer projectId, TruckReport truckInfo) {
    //    //根据单号查询id
    //    MaterialSendReceive receive = getReceiveIdByPurchaseNo(pushDataDTO.getPurchaseId());
    //
    //    MaterialSendReceive sendReceive = new MaterialSendReceive();
    //    BeanUtils.copyProperties(pushDataDTO, sendReceive);
    //    sendReceive.setCompanyId(companyId);
    //    sendReceive.setProjectId(projectId);
    //    sendReceive.setTruckId(truckInfo.getId());
    //    sendReceive.setSupplierId(receive.getSupplierId());
    //    sendReceive.setPurchaseId(receive.getId());
    //    sendReceiveService.save(sendReceive);
    //    return sendReceive;
    //}
    //
    //private MaterialSendReceive getReceiveIdByPurchaseNo(String purchaseNo) {
    //    MaterialSendReceive sendReceive = sendReceiveService.getReceiveIdByNo(purchaseNo);
    //    if (Objects.isNull(sendReceive)) {
    //        throw new BOException(BOExceptionEnum.ORDER_IS_NOT_EXIST);
    //    }
    //    return sendReceive;
    //}
    //
    //
    //public TruckReport getTruckInfo(String truckNo, Integer companyId, Integer projectId) {
    //    TruckReport truckReport = truckReportMapper.selectTruckInfoById(projectId, truckNo);
    //    if (Objects.isNull(truckReport)) {
    //        truckReport = new TruckReport();
    //        truckReport.setTruckNo(truckNo);
    //        truckReport.setCompanyId(companyId);
    //        truckReport.setProjectId(projectId);
    //        truckReportMapper.insert(truckReport);
    //    }
    //    return truckReport;
    //}
    //
    //private MaterialWeighbridge getWeighbridge(String deviceSn) {
    //    MaterialWeighbridge weighbridge = weighbridgeMapper.selectByDeviceSn(deviceSn);
    //    if (Objects.isNull(weighbridge)) {
    //        throw new BOException(BOExceptionEnum.WEIGHBRIDGE_IS_NOT_EXIST);
    //    }
    //    return weighbridge;
    //}


}
