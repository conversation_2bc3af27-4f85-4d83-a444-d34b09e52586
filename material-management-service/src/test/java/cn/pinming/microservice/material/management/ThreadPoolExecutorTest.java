package cn.pinming.microservice.material.management;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class ThreadPoolExecutorTest {
    private static final int taskCount = 11; //任务数

    public static void main(String[] args) throws InterruptedException{
        AtomicInteger integer = new AtomicInteger();
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                10,//核心线程数
                20,//最大线程数
                5,//空闲超时回收时间
                TimeUnit.SECONDS,//时间单位
                new ArrayBlockingQueue<>(1)//任务队列最大容量
        );
        System.out.println("总任务数:" + taskCount);

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < taskCount; i++) {
            Thread thread = new Thread(() -> {
                try {
                    Thread.sleep(500);
                    System.out.println("已执行" + integer.addAndGet(1) + "个任务");
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            });

//          如果不try catch，会放弃任务并抛错
            try{
                executor.execute(thread);
            }
            catch (Exception e){
                e.printStackTrace();
            }
        }


        long endTime = 0;
        while (executor.getCompletedTaskCount() < 11){
            endTime = System.currentTimeMillis();
        }

        System.out.println("任务总耗时：" + (endTime - startTime));
    }
}
