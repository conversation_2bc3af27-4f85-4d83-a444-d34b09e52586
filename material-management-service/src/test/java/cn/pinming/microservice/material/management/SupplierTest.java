//package cn.pinming.microservice.material.management;
//
//import cn.hutool.core.bean.BeanUtil;
//import cn.hutool.core.codec.Base64;
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.http.HttpRequest;
//import cn.hutool.http.HttpResponse;
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import cn.pinming.core.common.util.StringUtil;
//import cn.pinming.microservice.material.management.biz.dto.CCRCResponseDTO;
//import cn.pinming.microservice.material.management.biz.dto.CCRCSupplierDTO;
//import cn.pinming.microservice.material.management.biz.entity.Supplier;
//import cn.pinming.microservice.material.management.biz.service.ISupplierService;
//import cn.pinming.microservice.material.management.common.util.RedisUtil;
//import cn.pinming.yfzx.cxptz.thirdbutt.api.service.TokenService;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.concurrent.TimeUnit;
//
///**
// * This class is for xxxx.
// *
// * <AUTHOR> hao
// * @version 1.0.0
// * @since 2022/12/15 09:31
// */
//@SpringBootTest
//public class SupplierTest {
//
//    //common.ztcj.url.domain=https://api.crccep.com/crcc-supplier-api
//    //common.ctcj.token.clientId=chengjian-crucg-report
//    //common.ctcj.token.clientSecret=q3cVdiWG6hYR40XSlnedIap2Co7tohtoiu2HFEce
//    //common.ctcj.token.apiUrl=https://reg.crcc.cn
//
//    @Resource
//    private RedisUtil redisUtil;
//
//    @Resource
//    @Qualifier("ztcjTokenService")
//    private TokenService tokenService;
//
//    private static final String URL = "http://api.panguzd.com/crcc-supplier-api/api/supInfo/querySupplierInfoPage?pageSize=500&pageNum={}";
//
//    @Resource
//    private ISupplierService supplierService;
//
//    @Test
//    public void getToken() {
//        String token = getTokenByOwnHttpInTest();
//        //String token = tokenService.getToken();
//        System.out.println(token);
//
//        for (int i = 1; i < 100; i++) {
//            HttpResponse response = HttpRequest.get(StrUtil.format(URL, i)).header("Authorization", "Bearer " + token).execute();
//            String body = response.body();
//            CCRCResponseDTO ccrcResponseDTO = JSONUtil.toBean(body, CCRCResponseDTO.class);
//            System.out.println(ccrcResponseDTO);
//            if (CollUtil.isEmpty(ccrcResponseDTO.getData().getList())) {
//                break;
//            } else {
//                List<CCRCSupplierDTO> list = ccrcResponseDTO.getData().getList();
//                List<Supplier> suppliers = BeanUtil.copyToList(list, Supplier.class);
//                if (CollUtil.isNotEmpty(suppliers)) {
//                    supplierService.saveBatch(suppliers);
//                }
//            }
//        }
//    }
//
//    //create table s_supplier
//    //(
//    //    id          int auto_increment
//    //        primary key,
//    //    name        varchar(64)                  not null comment '名称',
//    //    credit_code varchar(64)                  null comment '组织机构代码',
//    //    ext_code    varchar(512)                 null comment '外部系统代码',
//    //    plant_id    varchar(32)                  null comment '拌合站站点ID',
//    //    company_id  int                          null comment '企业id',
//    //    project_id  int                          null comment '项目id',
//    //    create_id   varchar(32)                  null comment '创建者id',
//    //    update_id   varchar(32)                  null comment '修改者id',
//    //    create_time datetime                     null comment '创建时间',
//    //    update_time datetime                     null comment '修改时间',
//    //    is_enable   tinyint          default 0   null comment '是否启用 0 启用 1 禁用',
//    //    is_deleted  tinyint unsigned default '0' not null comment '逻辑删除标记 0 正常； 1删除'
//    //)
//    //    comment '供应商表';
//
//
//
//
//    private String getTokenByOwnHttpInTest() {
//        String tokenKey = "ztcj:api:test:token";
//        String tokenFromRedis = redisUtil.get(tokenKey, String.class);
//        if (StringUtil.isBlank(tokenFromRedis)) {
//            String encode = Base64.encode("crucg-zhgdpt:Sz7oh5KlgD6vMBs36oAaYLt3WIZzF9iuDxRZPIP7");
//            HttpResponse res = HttpRequest.get("https://regtest.crcc.cn/oauth/token?grant_type=client_credentials").header("Authorization", "Basic " + encode).execute();
//            JSONObject jsonObject = JSONUtil.parseObj(res.body());
//            String access_token = jsonObject.get("access_token", String.class);
//            redisUtil.set(tokenKey, access_token, 24 * 3600);
//            return access_token;
//        }
//        return tokenFromRedis;
//    }
//
//}
