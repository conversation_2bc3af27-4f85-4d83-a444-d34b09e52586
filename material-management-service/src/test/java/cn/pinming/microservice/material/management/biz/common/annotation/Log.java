package cn.pinming.microservice.material.management.biz.common.annotation;

import cn.pinming.microservice.material.management.common.enums.BusinessType;
import cn.pinming.microservice.material.management.common.enums.OperatorType;

import java.lang.annotation.*;

/**
 * 日志
 * <AUTHOR>
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {
    /**
     * 模块
     */
    String value() default "";
}
