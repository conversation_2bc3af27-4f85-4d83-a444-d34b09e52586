package cn.pinming.microservice.material.management.biz.openapi;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.management.biz.form.PurchaseOrderDetailForm;
import cn.pinming.microservice.material.management.biz.form.PurchaseOrderForm;
import cn.pinming.microservice.material.management.biz.query.PurchaseOrderQuery;
import cn.pinming.microservice.material.management.common.util.FormData;
import cn.pinming.microservice.material.management.common.util.SecretUtils;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class PurchaseOpenApiTest {
    // 测试URL
//    private final static String URL = "https://openapi-test03.pinming.org/api/openapi/material-management/";
    private final static String URL = "https://openapi.pinming.cn/api/openapi/material-management/";
    // 企业开发者秘钥
    private final static String SECRET_KEY = "5722391b5d2a389ede04656283116085";
    // 企业编码（非companyId）
    private final static String cono = "7841092";
    /**
     * 保存采购单
     */
    @Test
    void savePurchase() {
        //1.组装业务数据对象

        List<PurchaseOrderDetailForm> list = new ArrayList<>();
        PurchaseOrderDetailForm dto = new PurchaseOrderDetailForm();
        dto.setContractDetailId("f88b9cf5cb900f3cec3a1d6be3db4f19");
        dto.setBrand("12");
        dto.setCount(BigDecimal.valueOf(31));
        list.add(dto);
        PurchaseOrderForm form = PurchaseOrderForm.builder()
                .createId("6666")
                .updateId("222")
                .projectId(12439)
                .orderName("泵送商品砼采购单2021-11-02")
                .contractId("75f4e34c45a976a255f84feee81b1a1b")
                .supplierId(360007)
                .receiverProject(12439)
                .receiverAddress("桐乡市文华路与秋华路交叉口")
                .receiverTel("13605836677")
                .receiver("钟如清")
                .build();
        form.setList(list);
        //2.对象转json
        String jsonStr = JSONUtil.toJsonStr(form);
        //3.字符串编码
        String encodeStr = SecretUtils.encode(jsonStr);
        //4.aes加密
        String encryptStr = SecretUtils.aesEncrypt(SECRET_KEY, encodeStr);
        //5.组装请求对象
        FormData data = new FormData();
        data.setItype("/test/savePurchase");
        data.setTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        data.setCono(cono);
        data.setData(encryptStr);
        //加密类型：1-AES 	2-BASE64
        data.setEtype(1);
        // 开发者类型： 1企业开发者 2平台开发者，默认为空代表企业开发者
        data.setDtype(1);

        //6.按字段顺序md5加密
        String originalString = data.getItype() + data.getTime() + data.getCono() + data.getData()+SECRET_KEY;
        String sign = SecretUtils.md5Encrypt(originalString);
        data.setSign(sign);

        //7.对象转map
        Map<String, Object> map = BeanUtil.beanToMap(data);
        //8.请求
        String result = HttpUtil.post(URL, map);
        System.out.println(JSONUtil.toJsonPrettyStr(result));
    }

    /**
     * 取消采购单
     */
    @Test
    void cancelPurchase() {
//        1. 组装业务数据对象
        String id = "4e43a146be1081154c5292785a0f3df9";
        //2.对象转json
        String jsonStr = JSONUtil.toJsonStr(id);
        //3.字符串编码
        String encodeStr = SecretUtils.encode(jsonStr);
        //4.aes加密
        String encryptStr = SecretUtils.aesEncrypt(SECRET_KEY, encodeStr);
        //5.组装请求对象
        FormData data = new FormData();
        data.setItype("/test/cancelPurchase");
        data.setTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        data.setCono(cono);
        data.setData(encryptStr);
        //加密类型：1-AES 	2-BASE64
        data.setEtype(1);
        // 开发者类型： 1企业开发者 2平台开发者，默认为空代表企业开发者
        data.setDtype(1);

        //6.按字段顺序md5加密
        String originalString = data.getItype() + data.getTime() + data.getCono() + data.getData() + SECRET_KEY;
        String sign = SecretUtils.md5Encrypt(originalString);
        data.setSign(sign);

        //7.对象转map
        Map<String, Object> map = BeanUtil.beanToMap(data);
        //8.请求
        String result = HttpUtil.post(URL, map);
        System.out.println(JSONUtil.toJsonPrettyStr(result));
    }

    /**
     * 查看采购单列表
     */
    @Test
    void listPurchase() {
//        1. 组装业务数据对象
        PurchaseOrderQuery query = PurchaseOrderQuery.builder()
                .projectId(7777)
                .build();
        //2.对象转json
        String jsonStr = JSONUtil.toJsonStr(query);
        //3.字符串编码
        String encodeStr = SecretUtils.encode(jsonStr);
        //4.aes加密
        String encryptStr = SecretUtils.aesEncrypt(SECRET_KEY, encodeStr);
        //5.组装请求对象
        FormData data = new FormData();
        data.setItype("/test/listPurchase");
        data.setTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        data.setCono(cono);
        data.setData(encryptStr);
        //加密类型：1-AES 	2-BASE64
        data.setEtype(1);
        // 开发者类型： 1企业开发者 2平台开发者，默认为空代表企业开发者
        data.setDtype(1);

        //6.按字段顺序md5加密
        String originalString = data.getItype() + data.getTime() + data.getCono() + data.getData() + SECRET_KEY;
        String sign = SecretUtils.md5Encrypt(originalString);
        data.setSign(sign);

        //7.对象转map
        Map<String, Object> map = BeanUtil.beanToMap(data);
        //8.请求
        String result = HttpUtil.post(URL, map);
        System.out.println(JSONUtil.toJsonPrettyStr(result));
    }

    /**
     * 采购单详情
     */
    @Test
    void detailPurchase() {
//        1. 组装业务数据对象
        String id = "474432d4d7a7dd887b4d732918f4db0c";
        //2.对象转json
        String jsonStr = JSONUtil.toJsonStr(id);
        //3.字符串编码
        String encodeStr = SecretUtils.encode(jsonStr);
        //4.aes加密
        String encryptStr = SecretUtils.aesEncrypt(SECRET_KEY, encodeStr);
        //5.组装请求对象
        FormData data = new FormData();
        data.setItype("/test/detailPurchase");
        data.setTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        data.setCono(cono);
        data.setData(encryptStr);
        //加密类型：1-AES 	2-BASE64
        data.setEtype(1);
        // 开发者类型： 1企业开发者 2平台开发者，默认为空代表企业开发者
        data.setDtype(1);

        //6.按字段顺序md5加密
        String originalString = data.getItype() + data.getTime() + data.getCono() + data.getData() + SECRET_KEY;
        String sign = SecretUtils.md5Encrypt(originalString);
        data.setSign(sign);

        //7.对象转map
        Map<String, Object> map = BeanUtil.beanToMap(data);
        //8.请求
        String result = HttpUtil.post(URL, map);
        System.out.println(JSONUtil.toJsonPrettyStr(result));
    }

    /**
     * 所属合同列表
     */
    @Test
    void belongPurchase() {
//        1. 组装业务数据对象
        String id = "1ff47671c9b8296642f5d0a54a32ee77";
        //2.对象转json
        String jsonStr = JSONUtil.toJsonStr(id);
        //3.字符串编码
        String encodeStr = SecretUtils.encode(jsonStr);
        //4.aes加密
        String encryptStr = SecretUtils.aesEncrypt(SECRET_KEY, encodeStr);
        //5.组装请求对象
        FormData data = new FormData();
        data.setItype("/test/belongPurchase");
        data.setTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        data.setCono(cono);
        data.setData(encryptStr);
        //加密类型：1-AES 	2-BASE64
        data.setEtype(1);
        // 开发者类型： 1企业开发者 2平台开发者，默认为空代表企业开发者
        data.setDtype(1);

        //6.按字段顺序md5加密
        String originalString = data.getItype() + data.getTime() + data.getCono() + data.getData() + SECRET_KEY;
        String sign = SecretUtils.md5Encrypt(originalString);
        data.setSign(sign);

        //7.对象转map
        Map<String, Object> map = BeanUtil.beanToMap(data);
        //8.请求
        String result = HttpUtil.post(URL, map);
        System.out.println(JSONUtil.toJsonPrettyStr(result));
    }

}
