package cn.pinming.microservice.material.management;

import cn.hutool.core.collection.CollUtil;
import cn.pinming.microservice.material.management.biz.vo.ProjectVO;
import cn.pinming.v2.project.api.dto.BasicConstructionProjectDto;
import cn.pinming.v2.project.api.dto.ConstructionProjectQueryDto;
import cn.pinming.v2.project.api.service.ConstructionProjectService;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class ProjectTest {

    @Reference
    private ConstructionProjectService projectService;

    @Test
    public void test(){
        ConstructionProjectQueryDto queryDto = new ConstructionProjectQueryDto();
        queryDto.setCompanyId(11523);
        queryDto.setStatus((byte)2);
        List<BasicConstructionProjectDto> basicProjectList = projectService.findBasicProjectList(queryDto);
        List<ProjectVO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(basicProjectList)) {
            list = basicProjectList.parallelStream().filter(obj -> obj.getStatus() == 2).map(obj -> {
                ProjectVO vo = new ProjectVO();
                BeanUtils.copyProperties(obj, vo);
                return vo;
            }).collect(Collectors.toList());
        }
        System.out.println(list);
    }
}
