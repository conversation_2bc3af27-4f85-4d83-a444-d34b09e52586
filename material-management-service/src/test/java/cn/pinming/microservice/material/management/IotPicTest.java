package cn.pinming.microservice.material.management;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.core.upload.AliyunOssUploadComponent;
import cn.pinming.core.upload.OssFile;
import cn.pinming.core.upload.UploadConfig;
import cn.pinming.core.upload.domain.enums.FileTypeEnums;
import cn.pinming.model.FileInfos;
import cn.pinming.v2.common.api.service.FileService;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2021/9/10 1:41 下午
 */
@SpringBootTest
public class IotPicTest {

    @Reference(group = "pms_k8s_test02")
    private FileService fileService;
    @Reference(group = "pms_k8s_test02")
    private FileInfos fileInfos;
    @Value("${temporary.path}")
    private String temporaryPath;

    @Test
    public void pic() {
        Image image = ImgUtil.read("/Users/<USER>/Desktop/111.png");
        String str = ImgUtil.toBase64(image,ImgUtil.IMAGE_TYPE_PNG);
        System.out.println(getImgUUID(Arrays.asList(str,"","")));
//        BufferedImage bufferedImage = ImgUtil.toImage(str);
//        String filePath = temporaryPath + StrUtil.format("{}.png", System.currentTimeMillis());
//        FileOutputStream outputStream = new FileOutputStream(filePath);
//        ImgUtil.writeJpg(bufferedImage,outputStream);
//        File file = new File(filePath);
//        AliyunOssUploadComponent aliyunOssUploadComponent = new AliyunOssUploadComponent(fileInfos);
//        OssFile ossFile = new OssFile(file,"plain/text", FileTypeEnums.IMAGE);
//        UploadConfig uploadConfig = new UploadConfig("ff8080815afee284015afee408680001");
//        String uuid = aliyunOssUploadComponent.uploadFile(ossFile,uploadConfig);
//        fileService.confirmFile(uuid, "material-management");
    }


    private String getImgUUID(List<String> picList) {
        String picStr = "";
        if (CollUtil.isNotEmpty(picList)) {
            CopyOnWriteArrayList<String> copyOnWriteArrayList = new CopyOnWriteArrayList<>(picList);
            picStr = copyOnWriteArrayList.parallelStream().map(obj -> {
                String uuid = "";
                try {
                    if (StrUtil.isNotBlank(obj)) {
                        uuid = uploadImg(obj);
                    }
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                }
                return uuid;
            }).filter(StrUtil::isNotBlank).collect(Collectors.joining(","));
        }
        return picStr;
    }

    private String uploadImg(String base64) throws FileNotFoundException {
        BufferedImage bufferedImage = ImgUtil.toImage(base64);
        String filePath = temporaryPath + StrUtil.format("{}.png", System.currentTimeMillis());
        FileOutputStream outputStream = new FileOutputStream(filePath);
        ImgUtil.writeJpg(bufferedImage,outputStream);
        File file = new File(filePath);
        AliyunOssUploadComponent aliyunOssUploadComponent = new AliyunOssUploadComponent(fileInfos);
        OssFile ossFile = new OssFile(file,"plain/text", FileTypeEnums.IMAGE);
        UploadConfig uploadConfig = new UploadConfig("ff8080815afee284015afee408680001");
        String uuid = aliyunOssUploadComponent.uploadFile(ossFile,uploadConfig);
        fileService.confirmFile(uuid, "material-management");
        return uuid;
    }

    @Test
    public void img(){
        //ada5014f-f94a-2c92-d698-06b86ff522c3

        List<String> split = StrUtil.split("ada5015d-10d4-2c92-d698-06f2702a4cc6,,", ",");
        split = split.parallelStream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        List<String> list = fileService.fileDownloadUrlByUUIDs(split);

//        String s = fileService.fileDownloadUrlByUUID("ada50139-959f-2c92-d698-06b466a10684");
//        System.out.println(s);

    }
}
