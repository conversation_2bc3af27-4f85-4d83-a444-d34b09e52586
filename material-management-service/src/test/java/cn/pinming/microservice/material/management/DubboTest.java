package cn.pinming.microservice.material.management;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.core.common.model.PageList;
import cn.pinming.microservice.material.management.biz.enums.ClientStatusEnum;
import cn.pinming.microservice.material.management.biz.service.IClientLogService;
import cn.pinming.microservice.material.management.biz.vo.ExpirePageVO;
import cn.pinming.microservice.material.management.common.AppConstant;
import cn.pinming.microservice.material.management.common.exception.BOException;
import cn.pinming.microservice.material.management.common.exception.BOExceptionEnum;
import cn.pinming.microservice.material.management.common.util.RedisUtil;
import cn.pinming.microservice.material.management.proxy.FileServiceProxy;
import cn.pinming.model.dto.FilePreviewDto;
import cn.pinming.v2.project.api.dto.BasicConstructionProjectDto;
import cn.pinming.v2.project.api.dto.ConstructionProjectDto;
import cn.pinming.v2.project.api.dto.ConstructionProjectQueryDto;
import cn.pinming.v2.project.api.dto.SimpleConstructionProjectDto;
import cn.pinming.v2.project.api.service.ConstructionProjectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@SpringBootTest
@Slf4j
class test {
    @Reference
    private ConstructionProjectService constructionProjectService;

    @Test
    public void test() throws InterruptedException {
        ConstructionProjectQueryDto queryDto = new ConstructionProjectQueryDto();
        queryDto.setCompanyId(11346);
        queryDto.setPage(0);
        queryDto.setPageSize(Integer.MAX_VALUE);
        List<ConstructionProjectDto> projectListWithOutPage = constructionProjectService.findProjectListWithOutPage(queryDto);
        System.out.println(projectListWithOutPage);
    }


    @Test
    public void simpleProjectByCompanyId() {
        ConstructionProjectQueryDto projectQueryDto = new ConstructionProjectQueryDto();
        projectQueryDto.setCompanyId(11346);
        List<BasicConstructionProjectDto> basicProjectList = constructionProjectService.findBasicProjectList(projectQueryDto);
        System.out.println(basicProjectList);
    }

    @Test
    public void simpleProjectIds() {
        List<Integer> projectIds = Arrays.asList(2315, 2318, 2319, 2382, 2536, 2604, 2690, 3058, 3065, 3102, 3172, 3176, 3632, 3641, 3658, 3844, 3867, 3868, 3879, 3880, 3884, 3885, 3929, 3930, 4190, 4290, 4360, 4439, 4471, 4627, 4652, 4659, 4714, 4745, 4747, 4920, 4926, 4960, 5048, 5128, 5586, 6052, 6195, 6203, 8079, 8101);
        List<SimpleConstructionProjectDto> projects = constructionProjectService.findProjectsByProjectIds(projectIds);
        System.out.println(projects);
    }

    @Resource
    private RedisUtil redisUtil;


    @Test
    public void generateExternalPageInfo() {
        String code = IdUtil.fastSimpleUUID();
        String id = IdUtil.fastSimpleUUID();
        ExpirePageVO vo = new ExpirePageVO();
        vo.setId(id);
        vo.setCode(code);
        vo.setProjectId(61);
        vo.setCommit(0);
        String viewKey = StrUtil.format(AppConstant.PAGE_VIEW_PREFIX, code);
        redisUtil.hmset(viewKey, BeanUtil.beanToMap(vo));
        redisUtil.set(StrUtil.format(AppConstant.PAGE_EXP_PREFIX, code), id, 60 * 20);
        System.out.println(vo);
    }

    @Test
    public void checkExternalCodeExpire() {
        String hashKey = StrUtil.format(AppConstant.PAGE_EXP_PREFIX, "adb650c92acc43b5b6939eda7bd37a47");
        long expire = redisUtil.getExpire(hashKey);
        if (expire < 0) {
            redisUtil.del(StrUtil.format(AppConstant.PAGE_VIEW_PREFIX, "adb650c92acc43b5b6939eda7bd37a47"));
            throw new BOException(BOExceptionEnum.EXT_PAGE_EXPIRE);
        }
    }


    @Test
    public void checkExternalCodeView() {
        //保存判断页面请求次数
        String viewKey = StrUtil.format(AppConstant.PAGE_VIEW_PREFIX, "adb650c92acc43b5b6939eda7bd37a47");
        synchronized (this) {
            Map<Object, Object> map = redisUtil.hmget(viewKey);
            if (Objects.isNull(map)) {
                throw new BOException(BOExceptionEnum.EXT_PAGE_VIEW);
            }
            ExpirePageVO vo = BeanUtil.fillBeanWithMap(map, new ExpirePageVO(), true);
            if (vo.getCommit() > 0) {
                throw new BOException(BOExceptionEnum.EXT_PAGE_VIEW);
            }
            redisUtil.hincr(viewKey, "commit", 1);
        }
    }

    @Resource
    private FileServiceProxy fileServiceProxy;

    @Test
    public void filePreview() {
        FilePreviewDto preview = fileServiceProxy.findFilePreview("e4bf383bc36a5a54d7f0759c698bc2a4");
        System.out.println(preview.getFileUrl());
    }


    @Test
    public void test3() {
        ConstructionProjectQueryDto queryDto = new ConstructionProjectQueryDto();
        queryDto.setProjectIds(Arrays.asList(2315, 2318, 2319, 2320, 2382, 2536, 2604, 2690, 3058, 3065, 3102, 3172, 3176, 3632, 3641, 3658, 3844, 3867, 3868, 3879, 3880, 3884, 3885, 3929, 3930, 4190, 4290, 4360, 4439, 4471, 4627, 4652, 4659, 4714, 4745, 4747, 4920, 4926, 4960, 5048, 5128, 5586, 6052, 6195, 6203, 8079, 8101, 11466, 11708, 12422, 12423, 12424));
        //queryDto.setStatus((byte) 0);
        PageList<ConstructionProjectDto> pageList = constructionProjectService.findProjects(queryDto);
        System.out.println(JSONUtil.toJsonStr(pageList.getDataList()));


        List<ConstructionProjectDto> list = constructionProjectService.findProjectListWithOutPage(queryDto);
        System.out.println(JSONUtil.toJsonStr(list));

    }

}
