//package cn.pinming.microservice.material.management;
//
//
//import cn.pinming.microservice.material.management.biz.entity.CustomString;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.annotation.EnableAspectJAutoProxy;
//
//import java.util.HashSet;
//import java.util.Set;
//
////@SpringBootTest
//@EnableAspectJAutoProxy(proxyTargetClass = true,exposeProxy = true)
//@Slf4j
//public class Test {
//
//
//    @org.junit.jupiter.api.Test
//    public void test() throws InterruptedException {
//
//        CustomString a = new CustomString("b");
//        CustomString b = new CustomString("b");
//
//        String c = new String("a");
//        String d = new String("a");
//
//        System.out.println(c.equals(d));
//        System.out.println(a.equals(b));
//
//        Set set = new HashSet<>();
//        set.add(a);
//        set.add(b);
//        set.add(c);
//        set.add(d);
//
//        System.out.println(set);
//
//
//    }
//}
