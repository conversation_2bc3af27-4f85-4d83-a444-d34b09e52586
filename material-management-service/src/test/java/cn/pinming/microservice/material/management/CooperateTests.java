package cn.pinming.microservice.material.management;

import cn.pinming.microservice.material_unit.api.enterprise.dto.CooperateEnterpriseDto;
import cn.pinming.microservice.material_unit.api.enterprise.service.ApiCooperateEnterpriseService;
import java.util.List;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class CooperateTests {

    @Reference
    private ApiCooperateEnterpriseService cooperateEnterpriseService;

    @Test
    public void supplierTest(){
        List<CooperateEnterpriseDto> list =
            cooperateEnterpriseService.findByDictExtendInfo("2", 11346);
        System.out.println(list);
    }
}
