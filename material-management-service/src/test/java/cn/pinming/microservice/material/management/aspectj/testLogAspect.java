package cn.pinming.microservice.material.management.aspectj;

import cn.hutool.json.JSONUtil;
import cn.pinming.microservice.material.management.annotation.TestLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Slf4j
@Aspect
@Component
@EnableAspectJAutoProxy(proxyTargetClass = true,exposeProxy = true)
public class testLogAspect {

    @Pointcut("@annotation(cn.pinming.microservice.material.management.annotation.TestLog)")
    public void logPointCut(){
    }

    @BeforeEach()
    public void before(){
        System.out.println("前置通知");
    }

    @Around("logPointCut()")
    public Object process(ProceedingJoinPoint joinPoint) throws Throwable {
        log.info("环绕通知");
        Object object = null;
        try {
            object = joinPoint.proceed();
        } catch (Exception e) {
            handleLog(joinPoint, e);
        }
        return object;
    }

    protected void handleLog(final JoinPoint joinPoint, final Throwable e) {
        try {
            // 获得注解
            TestLog controllerLog = getAnnotationLog(joinPoint);
            if (controllerLog == null) {
                return;
            }
            System.out.println(JSONUtil.toJsonStr(joinPoint.getArgs()[0]));
            if (e != null) {
                System.out.println(StringUtils.substring(e.getMessage(), 0, 2000));
            }
//            AsyncManager.me().execute(AsyncFactory.recordIotOper(log));
        } catch (Exception exp) {
            log.error("异常信息:{}", exp.getMessage());
            exp.printStackTrace();
        }
    }
    /**
     * 是否存在注解，如果存在就获取
     */
    private TestLog getAnnotationLog(JoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        if (method != null) {
            return method.getAnnotation(TestLog.class);
        }
        return null;
    }


}
