<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.pinming.microservice</groupId>
    <artifactId>material-management</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <modules>
        <module>material-management-service</module>
        <module>material-management-api</module>
    </modules>
    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.2.8.RELEASE</version>
        <relativePath/>
    </parent>

    <repositories>
        <repository>
            <id>pms-nexus-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://nexus.pinming.org/repository/maven-releases/</url>
            <releases><enabled>true</enabled></releases>
            <snapshots><enabled>false</enabled></snapshots>
        </repository>
        <repository>
            <id>pms-nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://nexus.pinming.org/repository/maven-snapshots/</url>
            <releases><enabled>false</enabled></releases>
            <snapshots><enabled>true</enabled></snapshots>
        </repository>
        <repository>
            <id>pms-nexus-thirdparty</id>
            <name>PMS thirdparty Repository</name>
            <url>http://nexus.pinming.org/repository/3rd-party/</url>
            <releases><enabled>true</enabled></releases>
            <snapshots><enabled>true</enabled></snapshots>
        </repository>

    </repositories>

    <distributionManagement>
        <repository>
            <id>pms-nexus-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://nexus.pinming.org/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>pms-nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://nexus.pinming.org/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <pluginRepositories>
        <pluginRepository>
            <id>nexus</id>
            <url>http://nexus.pinming.org/repository/maven-public/</url>
            <releases><enabled>true</enabled></releases>
            <snapshots><enabled>true</enabled></snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>repo-spring</id>
            <url>https://repo.spring.io/plugins-release/</url>
            <releases><enabled>true</enabled></releases>
            <snapshots><enabled>true</enabled></snapshots>
        </pluginRepository>
    </pluginRepositories>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <zhuang-api.version>2024.5.26.ztcj</zhuang-api.version>
        <pinming.version>2.2.4</pinming.version>
        <platform-gateway-spring-boot-starter.version>2.1.0</platform-gateway-spring-boot-starter.version>
        <model-api.version>2.2.1-RELEASE</model-api.version>
        <lombok.version>1.16.22</lombok.version>
        <jetbrains-annotations.version>15.0</jetbrains-annotations.version>
        <dubbo.version>2.7.15</dubbo.version>
        <dubbo-spring-boot-starter.version>2.7.15</dubbo-spring-boot-starter.version>
        <spring-boot.version>2.2.13.RELEASE</spring-boot.version>
        <nacos-config-spring.version>0.2.12</nacos-config-spring.version>
        <mybatis-plus-boot-starter.version>3.4.3.2</mybatis-plus-boot-starter.version>
        <mybatis-plus-generator.version>3.5.0</mybatis-plus-generator.version>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <login.api>1.1.2-SNAPSHOT</login.api>
        <hutool.version>5.8.9</hutool.version>
        <upload.version>2.5.0-SNAPSHOT</upload.version>
        <algModelApi.version>0.0.5</algModelApi.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--  pinming  start -->
            <dependency>
                <groupId>cn.pinming</groupId>
                <artifactId>zhuang</artifactId>
                <version>${zhuang-api.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.pinming.yfzx.cxptz</groupId>
                <artifactId>thirdbutt-api</artifactId>
                <version>1.0.3-ztcj-20240711.053521-4</version>
            </dependency>
            <dependency>
                <groupId>cn.pinming.microservice</groupId>
                <artifactId>login-api</artifactId>
                <version>${login.api}</version>
            </dependency>
            <dependency>
                <groupId>cn.pinming</groupId>
                <artifactId>service-permission-spring-boot-starter</artifactId>
                <version>${pinming.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.pinming</groupId>
                <artifactId>platform-gateway-spring-boot-starter</artifactId>
                <version>${platform-gateway-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.pinming</groupId>
                <artifactId>core-web</artifactId>
                <version>${pinming.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.github.pagehelper</groupId>
                        <artifactId>pagehelper</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.pinming</groupId>
                <artifactId>common</artifactId>
                <version>${pinming.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.pinming</groupId>
                <artifactId>probe-servlet</artifactId>
                <version>${pinming.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.pinming</groupId>
                <artifactId>model-api</artifactId>
                <version>${model-api.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.pinming</groupId>
                <artifactId>cookie</artifactId>
                <version>${pinming.version}</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>com.alibaba</groupId>-->
            <!--                <artifactId>fastjson</artifactId>-->
            <!--                <version>1.2.83</version>-->
            <!--            </dependency>-->
            <!--  pinming  end -->

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus-generator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains</groupId>
                <artifactId>annotations</artifactId>
                <version>${jetbrains-annotations.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nacos-config-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-bom</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.pinming</groupId>
                <artifactId>weaponx-wrapper</artifactId>
                <version>0.1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>cn.pinming</groupId>
                <artifactId>upload</artifactId>
                <version>${upload.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.pinming.ai</groupId>
                <artifactId>algModelApi-api</artifactId>
                <version>${algModelApi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-freemarker</artifactId>
                <version>2.7.3</version>
            </dependency>
            <dependency>
                <groupId>org.xhtmlrenderer</groupId>
                <artifactId>core-renderer</artifactId>
                <version>R8</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>gui.ava</groupId>-->
<!--                <artifactId>html2image</artifactId>-->
<!--                <version>2.0.1</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>cn.pinming.microservice</groupId>
                <artifactId>warehouse-management-api</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>material-management</finalName>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.0.1</version>
                    <configuration>
                        <encoding>UTF-8</encoding>
                        <additionalOptions>
                            -Xdoclint:none
                        </additionalOptions>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.4</version>
                    <!-- 进行package命令时就可以将源码同时进行打包 -->
                    <!-- 所以我们需要绑定source插件到我们default生命周期的package阶段 -->
                    <executions>
                        <execution>
                            <!-- 定义一个阶段，这个阶段是package -->
                            <phase>package</phase>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>1.8</source>
                        <target>1.8</target>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
        </plugins>
    </build>


</project>
